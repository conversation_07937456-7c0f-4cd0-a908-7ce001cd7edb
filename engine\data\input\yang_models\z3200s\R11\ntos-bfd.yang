module ntos-bfd {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:bfd";
  prefix ntos-bfd;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-routing {
    prefix ntos-rt;
  }
  import ntos-ospf {
    prefix ntos-ospf;
  }
  import ntos-ospf6 {
    prefix ntos-ospf6;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-pm {
    prefix ntos-pm;
  }
  import ntos-tracker {
    prefix ntos-tracker;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS routing BFD. This module is based on the FRR BFD yang.";

  revision 2019-06-28 {
    description
      "Disable echo-mode on multi-hop session (RFC 5883 section 3 for details).";
    reference "";
  }
  revision 2019-06-19 {
    description
      "Initial version.";
    reference
      "RFC 5880: Bidirectional Forwarding Detection (BFD).
       RFC 5881: Bidirectional Forwarding Detection (BFD)
                 for IPv4 and IPv6 (Single Hop).
       RFC 5882: Bidirectional Forwarding Detection (BFD) for Multihop Paths.";
  }

  identity rtg-bfd {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Routing BFD tracker.";
    ntos-ext:nc-cli-identity-name "routing bfd";
  }

  identity bfd {
    base ntos-types:RTG_AUTO_TRACKER;
    description
      "Automatically create the BFD tracker.";
  }

  typedef bfd-state {
    type enumeration {
      enum admin-down {
        value 0;
        description
          "Administratively down.";
      }
      enum down {
        value 1;
        description
          "Down.";
      }
      enum init {
        value 2;
        description
          "Initializing.";
      }
      enum up {
        value 3;
        description
          "Up.";
      }
    }
    description
      "BFD session state.";
  }

  typedef bfd-diag {
    type enumeration {
      enum ok {
        value 0;
        description
          "Ok.";
      }
      enum control-expired {
        value 1;
        description
          "Control timer expired.";
      }
      enum echo-failed {
        value 2;
        description
          "Echo function failed.";
      }
      enum neighbor-down {
        value 3;
        description
          "Neighbor signaled session down.";
      }
      enum forwarding-reset {
        value 4;
        description
          "Forwarding plane reset.";
      }
      enum path-down {
        value 5;
        description
          "Path down.";
      }
      enum concatenated-path-down {
        value 6;
        description
          "Concatenated path down.";
      }
      enum administratively-down {
        value 7;
        description
          "Administratively down.";
      }
      enum reverse-concat-path-down {
        value 8;
        description
          "Reverse concatenated path down.";
      }
    }
    description
      "BFD session diagnostic.";
  }

  grouping bfd-default-conf {
    description
      "BFD options configurable for BGP/OSPF and static routes sessions.";

    leaf detection-multiplier {
      type uint8 {
        range "1..255";
      }
      default "3";
      description
        "Local session detection multiplier.";
    }

    leaf desired-transmission-interval {
      type uint32 {
        range "10000..4294967295";
      }
      units "microseconds";
      default "300000";
      description
        "Minimum desired control packet transmission interval.";
    }

    leaf required-receive-interval {
      type union {
        type uint32 {
          range "10000..4294967295";
        }
        type enumeration {
          enum disable {
            description
              "Ths system will not receive any periodic BFD control packets.";
          }
        }
      }
      units "microseconds";
      default "300000";
      description
        "Minimum required control packet receive interval (use disable to not
         receive any control packet).";
    }

    leaf desired-echo-transmission-interval {
      type uint32 {
        range "10000..4294967295";
      }
      units "microseconds";
      description
        "Minimum desired control packet transmission interval.";
    }
  }

  grouping bfd-session-conf {
    description
      "BDF session configuration options.";

    leaf name {
      type ntos-tracker:tracker-name;
      must 'not(/ntos:config/ntos-tracker:tracker/ntos-pm:icmp[ntos-pm:name = current()])' {
        error-message "An ICMP tracker has the same name.";
      }
      must "current() != 'bfd'" {
        error-message
          "'bfd' keyword is reserved for sessions automatically configured by
           routing protocols.";
      }
      description
        "BFD tracker's name.";
    }

    leaf type {
      type enumeration {
        enum single-hop {
          description
            "Single-hop session.";
        }
        enum multi-hop {
          description
            "Multi-hop session.";
        }
      }
      default "single-hop";
      description
        "Session type.";
    }

    leaf source {
      type ntos-inet:ip-address;
      description
        "Local IP address.";
    }

    leaf address {
      type ntos-inet:ip-address;
      mandatory true;
      description
        "IP address of the peer.";
    }

    leaf interface {
      type ntos-types:ifname;
      description
        "Interface to use to contact peer.";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
    }

    leaf vrf {
      type ntos:vrf-name;
      mandatory true;
      description
        "VRF name.";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos:name";
    }

    leaf echo-mode {
      type boolean;
      description
        "Use echo packets to detect failures.";
    }
    uses bfd-default-conf;
  }

  grouping bfd-session-state {
    description
      "BFD operational state data.";

    leaf discriminator {
      type uint32 {
        range "1..4294967295";
      }
      description
        "Local session identifier.";
    }

    leaf state {
      type bfd-state;
      description
        "Local session state.";
    }

    leaf diagnostic {
      type bfd-diag;
      description
        "Local session diagnostic.";
    }

    container remote {
      description
        "BFD remote operational state data.";

      leaf discriminator {
        type uint32;
        description
          "Remote session identifier.";
      }

      leaf diagnostic {
        type bfd-diag;
        description
          "Local session diagnostic.";
      }

      leaf multiplier {
        type uint8 {
          range "2..255";
        }
        description
          "Remote session detection multiplier.";
      }
    }

    container negociated {
      description
        "BFD negociated operational state data.";

      leaf transmission-interval {
        type uint32;
        units "microseconds";
        description
          "Negotiated transmit interval.";
      }

      leaf receive-interval {
        type uint32;
        units "microseconds";
        description
          "Negotiated receive interval.";
      }

      leaf echo-transmission-interval {
        type uint32;
        units "microseconds";
        description
          "Negotiated echo transmit interval.";
      }
    }
    /* Statistics. */

    leaf last-down-time {
      type uint64;
      description
        "Time and date of the last time session was down (in seconds).";
    }

    leaf last-up-time {
      type uint64;
      description
        "Time and date of the last time session was up (in seconds).";
    }

    leaf session-down-count {
      type uint32;
      description
        "Amount of time the session went down.";
    }

    leaf session-up-count {
      type uint32;
      description
        "Amount of time the session went up.";
    }

    leaf control-packet-input-count {
      type uint64;
      description
        "Amount of control packets received.";
    }

    leaf control-packet-output-count {
      type uint64;
      description
        "Amount of control packets sent.";
    }

    leaf echo-packet-input-count {
      type uint64;
      description
        "Amount of echo packets received.";
    }

    leaf echo-packet-output-count {
      type uint64;
      description
        "Amount of echo packets sent.";
    }

    leaf zebra-notification-count {
      type uint64;
      description
        "Amount of zebra notifications.";
    }
  }

  grouping bfd-ospf-config {
    description
      "Common BFD configuration for OSPF, OSPFv3 and static routes.";

    leaf track {
      type enumeration {
        enum bfd {
          description
            "Let OSPF create the BFD sessions automatically.";
        }
      }
      description
        "Enable BFD tracker.";
    }
  }

  rpc show-bfd {
    description
      "Show BFD information.";
    input {

      leaf vrf {
        type ntos:vrf-name;
        description
          "Specify the VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf address {
        type ntos-inet:ip-address;
        description
          "IP address of the peer.";
      }

      leaf hop-type {
        type enumeration {
          enum single-hop {
            description
              "Show single-hop session.";
          }
          enum multi-hop {
            description
              "Show multi-hop session.";
          }
        }
        must "../address" {
          error-message "address is mandatory when hop-type is specified";
        }
        description
          "Show single or multi hop session.";
        ntos-ext:nc-cli-no-name;
      }

      leaf source {
        type union {
          type ntos-inet:ip-address;
          type enumeration {
            enum any {
              description
                "Accept any source addresses.";
            }
          }
        }
        must "../address" {
          error-message "address is mandatory when source is specified";
        }
        description
          "Local IP address.";
      }

      leaf interface {
        type ntos-types:ifname;
        must "../address" {
          error-message "address is mandatory when interface is specified";
        }
        description
          "Interface used to contact peer.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos-tracker:tracker/ntos-bfd:bfd/ntos-bfd:interface";
      }

      leaf counters {
        type empty;
        description
          "Show BFD session counters information.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "bfd";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos-tracker:tracker" {
    description
      "BFD configuration.";

    list bfd {
      must 'count(../bfd[address = current()/address and vrf = current()/vrf and not(source) and
                  not(current()/source) and not(interface) and not(current()/interface)]) <= 1 and
            count(../bfd[address = current()/address and vrf = current()/vrf and
                  source = current()/source and not(interface) and
                  not(current()/interface)]) <= 1 and
            count(../bfd[address = current()/address and vrf = current()/vrf and
                  not(source) and not(current()/source) and
                  interface = current()/interface]) <= 1 and
            count(../bfd[address = current()/address and vrf = current()/vrf and
                  source = current()/source and interface = current()/interface]) <= 1 and
            count(../bfd[address = current()/address and vrf = current()/vrf and
                  source = current()/source and interface = current()/interface]) <= 1 and
            count(../bfd[address = current()/address and vrf = current()/vrf and
                  source = current()/source and not(interface) and
                  current()/interface]) <= 1 and
            count(../bfd[address = current()/address and vrf = current()/vrf and
                  not(source) and not(current()/source) and interface = current()/interface]) <= 1' {
        error-message "This session already exists.";
      }
      must "current()/type != 'multi-hop' or current()/source" {
        error-message "The source address is mandatory for multi-hop trackers.";
      }
      must "not((echo-mode = 'true' or desired-echo-transmission-interval) and type = 'multi-hop')" {
        error-message "This option is not available on multi-hop session.";
      }
      key "name";
      description
        "Configure a BFD tracker session.";
      uses bfd-session-conf;
    }
  }

  augment "/ntos:state/ntos-tracker:tracker" {
    description
      "BFD state.";

    list bfd {
      key "name";
      description
        "Configure a BFD tracker session.";
      uses bfd-session-conf;
      uses bfd-session-state;
    }
  }

  augment "/ntos:config/ntos-rt:routing" {
    description
      "BFD global configuration.";

    container bfd {
      description
        "BFD global configuration for BGP, OSPF and static routes.";
      ntos-ext:feature "product";
      uses bfd-default-conf;
    }
  }

  augment "/ntos:state/ntos-rt:routing" {
    description
      "BFD global operational state.";

    container bfd {
      description
        "BFD global state for BGP, OSPF and static routes.";
      ntos-ext:feature "product";
      uses bfd-default-conf;
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ip/ntos-ospf:ospf" {
    description
      "BFD support for OSPF.";
    uses bfd-ospf-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ip/ntos-ospf:ospf" {
    description
      "BFD state of OSPF.";
    uses bfd-ospf-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ipv6/ntos-ospf6:ospf6" {
    description
      "BFD support for OSPFv3.";
    uses bfd-ospf-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ipv6/ntos-ospf6:ospf6" {
    description
      "BFD state of OSPFv3.";
    uses bfd-ospf-config;
  }
}
