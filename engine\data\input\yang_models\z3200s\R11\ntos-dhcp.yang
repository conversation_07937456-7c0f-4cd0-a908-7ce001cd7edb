module ntos-dhcp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dhcp";
  prefix ntos-dhcp;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ietf-yang-types {
    prefix ietf-yang;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS DHCP server and relay.";

  revision 2022-08-03 {
    description
      "Add dhcpv6 subnet and options.";
    reference "";
  }
  revision 2022-02-20 {
    description
      "Add new features for NTOS.";
    reference "";
  }
  revision 2019-02-12 {
    description
      "Rework and enable relay.";
    reference "";
  }
  revision 2018-10-29 {
    description
      "Initial version.";
    reference "";
  }

  identity dhcp-server {
    base ntos-types:SERVICE_LOG_ID;
    description
      "DHCP Server service.";
  }

  identity dhcp-relay {
    base ntos-types:SERVICE_LOG_ID;
    description
      "DHCP Relay service.";
  }

  typedef binding-state {
    type enumeration {
      enum active {
        description
          "The IPv4 or IPv6 lease is active.";
      }
      enum free {
        description
          "The IPv4 lease is free.";
      }
      enum abandoned {
        description
          "The IPv4 lease is abandoned.";
      }
      enum expired {
        description
          "The IPv6 lease is expired.";
      }
      enum unknown {
        description
          "The lease state is unknown.";
      }
    }
    description
      "Enumeration for a lease binding state.";
  }

  typedef binding-type {
    type enumeration {
      enum static {
        description
          "The IPv4 or IPv6 lease binding type is static.";
      }
      enum dynamic {
        description
          "The IPv4 or IPv6 lease binding type is dynamic.";
      }
    }
    description
      "Enumeration for a lease binding type.";
  }

  typedef handle-option {
    type enumeration {
      enum append {
        description
          "Append our own set of relay options to the packet, leaving the
           supplied option field intact.";
      }
      enum replace {
        description
          "Replace the existing agent option field.";
      }
      enum forward {
        description
          "Forward the packet unchanged.";
      }
      enum discard {
        description
          "Discard the packet.";
      }
    }
    description
      "Handling of DHCPv4 packets which already contain relay agent options.";
  }

  typedef hardware-type-enum {
    type enumeration {
      enum ethernet {
        description "ethernet";
      }
      enum ieee802 {
        description "ieee802";
      }
      enum fddi {
        description "fddi";
      }
    }
    description "hardware type.";
  }

  grouping subnet-share-config {
    description
      "DHCPv4 and DHCPv6 share these configurations.";

    leaf default-lease-time {
      type uint32 {
        range "10..31536000";
      }
      description
        "Default network address lease time assigned to DHCP clients for this
          subnet (in seconds, at least 180s).";
    }

    leaf max-lease-time {
      type uint32 {
        range "10..31536000";
      }
      description
        "Maximum network address lease time assigned to DHCP clients for this
          subnet (in seconds, at least 180s or the default-lease value).";
    }

    leaf lease-id-format {
        type enumeration {
          enum octal {
            description
              "Format with octal.";
          }
          enum hex {
            description
              "Format with hex.";
          }
        }
        default "hex";
        description
          "This parameter governs the format used to write certain values to lease files.";
      }

    leaf warning-high-threshold {
      type uint8 {
        range "0..99";
      }
      default "90";
      description
        "The configure are used to control when a message is output about pool usage.
        A warning message is output once the pool usage passes that level.";
    }

    leaf warning-low-threshold {
      type uint8 {
        range "0..99";
      }
      default "80";
      description
        "After the pool usage falls below the low threshold, A warning message is output again
        when the pool usage passes that level.";
    }
  }

  grouping dhcpv4-option {
    description
      "DHCPv4 configuration options.";

    leaf dhcp-server-identifier {
      type ntos-inet:ipv4-address;
      description
        "DHCP server identifier (IPv4 address) used in DHCP messages to allow
         the client to distinguish between lease offers.";
    }

    leaf domain-name {
      type string;
      description
        "Name of the domain.";
    }

    leaf-list domain-name-server {
      max-elements 10;
      type ntos-inet:ipv4-address;
      ordered-by user;
      description
        "Domain name server (IPv4 address) listed in order of preference.";
    }

    leaf-list ntp-server {
      type ntos-inet:ipv4-address;
      ordered-by user;
      description
        "NTP server (IPv4 address) listed in order of preference.";
    }

    leaf interface-mtu {
      type uint16 {
        range "0..65535";
      }
      description
        "Minimum Transmission Unit (MTU) of the interface.";
    }

    leaf-list netbios-name-server {
      type ntos-inet:ip-address;
      ordered-by user;
      description
        "NETBIOS name server listed in order of preference.";
    }

    leaf netbios-node-type {
      type enumeration {
        enum B-node {
          description
            "Broadcast - no WINS.";
        }
        enum P-node {
          description
            "Peer - WINS only.";
        }
        enum M-node {
          description
            "Mixed - broadcast, then WINS.";
        }
        enum H-node {
          description
            "Hybrid - WINS, then broadcast.";
        }
      }
      description
        "NETBIOS node type.";
    }

    leaf netbios-scope {
      type string;
      description
        "NETBIOS over TCP/IP scope parameter for the client as specified in RFC
         1001/1002.";
    }

    leaf time-offset {
      type int32;
      description
        "Time offset in seconds from UTC.";
    }

    container vendor-specific {
      choice value-type {
        case string {
          leaf val-str {
            type string {
              length "1..254";
            }
            description
              "String value of the option.";
          }
        }
        case ip-array {
          leaf-list val-ip-array {
            max-elements 16;
            type ntos-inet:ipv4-address;
            description
              "Array of ip address.";
          }
        }
      }
    }

    leaf-list capwap-ac-v4 {
      max-elements 16;
      type ntos-inet:ipv4-address;
      description
        "Value of the option 138.";
    }
  }

  grouping dhcpv6-option {
    description
      "DHCPv6 configuration options.";

    leaf dhcp6-server-id {
      type string {
        length "1..64";
      }
      description
        "This configure specifies the server's DUID identifier.";
    }

    leaf-list dhcp6-name-servers {
      max-elements 10;
      type ntos-inet:ipv6-address;
      ordered-by user;
      description
        "Domain name server (IPv6 address) listed in order of preference.";
    }
  }

  grouping dhcp-server-config {
    description
      "Configuration data for DHCP server.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable/Disable DHCP server on this VRF.";
    }

    leaf default-lease-time {
      type uint32 {
        range "10..31536000";
      }
      default "43200";
      description
        "Default network address lease time assigned to DHCP clients (in
         seconds, at least 180s).";
    }

    leaf max-lease-time {
      type uint32 {
        range "10..31536000";
      }
      default "86400";
      description
        "Maximum network address lease time assigned to DHCP clients (in
         seconds, at least 180s or the default-lease value).";
    }

    container dhcp-options {
      description
        "Default DHCP options configuration.";
      uses dhcpv4-option;
      uses dhcpv6-option;
    }

    list subnet {
      max-elements 1024;
      must '(not(default-lease-time) and not(max-lease-time)) or
            ((default-lease-time and not(max-lease-time) and default-lease-time <= ../max-lease-time)) or
            ((not(default-lease-time) and max-lease-time and ../default-lease-time <= max-lease-time)) or
            ((default-lease-time and max-lease-time and default-lease-time <= max-lease-time))' {
        error-message "Default-lease must be less or equal to max-lease.";
      }
      key "prefix";
      ordered-by user;
      description
        "Subnet configuration.";

      leaf prefix {
        type ntos-inet:ipv4-prefix;
        description
          "Network prefix of the subnet on which the DHCP server
           listens.";
      }

      leaf-list interface {
        type ntos-types:ifname;
        max-elements 1;
        description
          "Interface on which the DHCP server should listen.";
        ntos-extensions:nc-cli-completion-xpath
          "../../../ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf description {
        type string {
          length "0..128";
        }
        description "A comment to describe the subnet.";
      }

      leaf-list default-gateway {
        type ntos-inet:ipv4-address;
        ordered-by user;
        description
          "IPv4 address of the gateway listed in order of preference.";
      }

      list exclude {
        key "start-ip end-ip";
        description
          "IPv4 exclusion range.";
        ntos-extensions:nc-cli-one-liner;

        leaf start-ip {
          type ntos-inet:ipv4-address;
          description
            "Starting IPv4 Address of a exclusion range.";
        }

        leaf end-ip {
          type ntos-inet:ipv4-address;
          description
            "Last IPv4 Address of a exclusion range.";
        }
      }

      list range {
        max-elements 20;
        key "start-ip end-ip";
        description
          "IPv4 range.";
        ntos-extensions:nc-cli-one-liner;

        leaf start-ip {
          type ntos-inet:ipv4-address;
          description
            "Starting IPv4 Address of a range.";
        }

        leaf end-ip {
          type ntos-inet:ipv4-address;
          description
            "Last IPv4 Address of a range.";
        }
      }

      leaf ping-check {
        type boolean;
        default "true";
        description
          "Enable/Disable sends an ICMP Echo request (a ping) to check ip. default timeout is 1s.";
      }

      leaf ping-timeout {
        type uint32;
        units "ms";
        description
          "specify the ping timeout in milliseconds.";
      }

      uses subnet-share-config;

      list host {
        key "host-name";
        description
          "Mapping from MAC address to IP address.";
        ntos-extensions:nc-cli-one-liner;

        leaf host-name {
          type string {
            length "1..253";
            pattern '((([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9]\.)*' +
                    '([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9]\.?)|\.';
          }
          description
            "Host name for static MAC to IP address mapping.";
        }

        leaf mac-address {
          type ntos-if:mac-address;
          mandatory true;
          description
            "MAC address of the host.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf ip-address {
          type ntos-inet:ipv4-address;
          mandatory true;
          description
            "IPv4 address of the host.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container dhcp-options {
        description
          "DHCPv4 options specific to this subnet.";
        uses dhcpv4-option;
      }
    }

    list subnet6 {
      must 'count(prefix6-r) + count(prefix6-p) <= 1' {
        error-message "At maximum one of prefix6-r or prefix6-p must be specified.";
      }
      max-elements 1024;
      must '(not(default-lease-time) and not(max-lease-time)) or
            ((default-lease-time and not(max-lease-time) and default-lease-time <= ../max-lease-time)) or
            ((not(default-lease-time) and max-lease-time and ../default-lease-time <= max-lease-time)) or
            ((default-lease-time and max-lease-time and default-lease-time <= max-lease-time))' {
        error-message "Default-lease must be less or equal to max-lease.";
      }
      key "prefix";
      ordered-by user;
      description
        "Subnet6 configuration.";

      leaf prefix {
        type ntos-inet:ipv6-prefix;
        description
          "Network prefix of the subnet on which the DHCPv6 server
           listens.";
      }

      leaf-list interface {
        type ntos-types:ifname;
        max-elements 1;
        description
          "Interface on which the DHCPv6 server should listen.";
        ntos-extensions:nc-cli-completion-xpath
          "../../../ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf description {
        type string {
          length "0..128";
        }
        description "A comment to describe the subnet6.";
      }

      uses subnet-share-config;

      container server-duid {
        ntos-extensions:nc-cli-one-liner;
        leaf duid-type {
          ntos-extensions:nc-cli-no-name;
          type enumeration {
            enum LLT {
              description
                "DUID type 1, based on Link-Layer Address Plus Time (DUID-LLT).Default.";
            }
            enum EN {
              description
                "DUID type 2, assigned by vendor based on Enterprise Number (DUID-EN).";
            }
            enum LL {
              description
                "DUID type 3, based on Link-Layer Address (DUID-LL).";
            }
          }
          description
            "dhcpv6 server duid type.";
        }

        leaf hardware-type {
          when "../duid-type = 'LLT' or ../duid-type = 'LL'";
          type hardware-type-enum;
          description
            "dhcpv6 server duid LLT or LL hardware type.";
        }
        leaf timestamp {
          when "../duid-type = 'LLT'";
          type ietf-yang:timestamp;
          description
            "dhcpv6 server duid LLT timestamp.";
        }
        leaf hardware-address {
          when "../duid-type = 'LLT' or ../duid-type = 'LL'";
          type ntos-if:mac-address;
          description
            "dhcpv6 server duid LLT or LL hardware address.";
        }
        leaf enterprise-number {
          when "../duid-type = 'EN'";
          type string;
          description
            "dhcpv6 server duid EN enterprise number.";
        }
        leaf enterprise-identifier {
          when "../duid-type = 'EN'";
          type string;
          description
            "dhcpv6 server duid EN enterprise identifier.";
        }
      }

      list range {
        max-elements 20;
        key "start-ip end-ip";
        description
          "IPv6 range low-address high-address.";
        ntos-extensions:nc-cli-one-liner;

        leaf start-ip {
          type ntos-inet:ipv6-address;
          description
            "Starting IPv6 Address of a range.";
        }

        leaf end-ip {
          type ntos-inet:ipv6-address;
          description
            "Last IPv6 Address of a range.";
        }
      }

      list range-num {
        max-elements 1;
        key "prefix";
        description
          "IPv6 range subnet6-number.";
        ntos-extensions:nc-cli-one-liner;

        leaf prefix {
          type ntos-inet:masked-ipv6-address;
          description
            "Starting IPv6 Address of a range.";
        }
      }

      list range-num-tmp {
        key "prefix";
        description
          "IPv6 subnet6-number temporary.";
        ntos-extensions:nc-cli-one-liner;

        leaf prefix {
          type ntos-inet:masked-ipv6-address;
          description
            "Starting IPv6 Address of a range.";
        }
      }

      list range-tmp {
        key "addr";
        description
          "IPv6 address temporary.";
        ntos-extensions:nc-cli-one-liner;

        leaf addr {
          type ntos-inet:ipv6-address;
          description
            "Starting IPv6 Address of a range.";
        }
      }

      list prefix6-r {
        description
          "DHCPv6 prefix6 with range.";
        ntos-extensions:nc-cli-one-liner;
        max-elements 1;
        key "start-ip end-ip bits";

        leaf start-ip {
          type ntos-inet:ipv6-address;
          description
            "Starting IPv6 prefix of a range.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf end-ip {
          type ntos-inet:ipv6-address;
          description
            "Last IPv6 prefix of a range.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf bits {
          type uint16 {
            range "1..127";
            ntos-extensions:nc-cli-shortdesc "<1-127>";
          }
          description
            "prefix length.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      list prefix6-p {
        description
          "DHCPv6 prefix6 with prefix.";
        ntos-extensions:nc-cli-one-liner;
        key "prefix bits";
        max-elements 1;

        leaf prefix {
          type ntos-inet:ipv6-prefix;
          description
            "Network prefix.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf bits {
          type uint16 {
            range "1..127";
            ntos-extensions:nc-cli-shortdesc "<1-127>";
          }
          description
            "prefix length.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      list exclude {
        key "start-ip end-ip";
        description
          "IPv6 exclusion range.";
        ntos-extensions:nc-cli-one-liner;

        leaf start-ip {
          type ntos-inet:ipv6-address;
          description
            "Starting IPv6 Address of a exclusion range.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf end-ip {
          type ntos-inet:ipv6-address;
          description
            "Last IPv6 Address of a exclusion range.";
          ntos-extensions:nc-cli-no-name;
        }
      }

      container dhcp-options {
        description
          "DHCPv6 options specific to this subnet6.";
        uses dhcpv6-option;
      }

      list host {
        key "host-name";
        description
          "Mapping from MAC address to IP address.";
        ntos-extensions:nc-cli-one-liner;

        leaf host-name {
          type string {
            length "1..253";
            pattern '((([a-zA-Z0-9_]([a-zA-Z0-9\-_:]){0,61})?[a-zA-Z0-9]\.)*' +
                    '([a-zA-Z0-9_]([a-zA-Z0-9\-_:]){0,61})?[a-zA-Z0-9]\.?)|\.';
          }
          description
            "Host name for static MAC to IPv6 address mapping.";
        }

        leaf mac-address {
          type ntos-if:mac-address;
          mandatory true;
          description
            "MAC address of the host.";
          ntos-extensions:nc-cli-no-name;
        }

        leaf ip-address {
          type ntos-inet:ipv6-address;
          mandatory true;
          description
            "IPv6 address of the host.";
          ntos-extensions:nc-cli-no-name;
        }
      }
    }
  }

  grouping dhcp-server-leases {
    description
      "lease data for DHCP server.";

    list dhcp-server-leases {
      key "address";
      description
        "State of leases for DHCP server.";

      leaf address {
        type ntos-inet:ipv4-address;
        description
          "Leased IP address.";
        ntos-extensions:nc-cli-show-key-name;
      }

      leaf binding-type {
        type binding-type;
        description
          "Lease's binding type.";
      }

      leaf starts {
        type ntos-types:date-and-time;
        mandatory true;
        description
          "Lease start time.";
      }

      leaf ends {
        type string;
        mandatory true;
        description
          "Lease end time.";
      }

      leaf cltt {
        type ntos-types:date-and-time;
        mandatory true;
        description
          "Client last transaction time.";
      }

      leaf hw-mac-address {
        type string;
        mandatory true;
        description
          "MAC address of the network interface on which the lease will be
           used.";
      }

      leaf uid {
        type string;
        description
          "Client identifier used by the client to acquire the lease.";
      }

      leaf client-hostname {
        type string;
        description
          "Client host name sent using client-hostname statement.";
      }

      leaf binding-state {
        type binding-state;
        description
          "Lease's binding state.";
      }

      leaf next-binding-state {
        type binding-state;
        description
          "State the lease will move to when the current state expires.";
      }

      leaf option-agent-circuit-id {
        type string;
        description
          "Circuit ID option sent by the relay agent.";
      }

      leaf option-agent-remote-id {
        type string;
        description
          "Remote ID option sent by the relay agent.";
      }

      leaf vendor-class-identifier {
        type string;
        description
          "Client-supplied Vendor Class Identifier option.";
      }
    }

    list dhcp6-server-leases {
      key "duid ia-type addr";
      description
        "State of leases for DHCPv6 server.";

      leaf duid {
        type string;
        description
          "dhcp client unique identifier.";
      }

      leaf ia-type {
        type enumeration {
          enum ia-na {
            description
              "IA_NA.";
          }
          enum ia-ta {
            description
              "IA_TA.";
          }
          enum ia-pd {
            description
              "IA_PD.";
          }
        }
        description
          "Identity Association.";
      }
      leaf addr {
        type string;
        description
          "lease's statement (IPv6 addr or IPv6 prefix).";
      }
      leaf binding-type {
        type binding-type;
        description
          "Lease's binding type.";
      }
      leaf binding-state {
        type binding-state;
        description
          "Dhcp IPv6 Lease's binding state.";
      }
      leaf cltt {
        type ntos-types:date-and-time;
        description
          "Lease start time.";
      }
      leaf preferred-life {
        type uint32;
        description
          "The IPv6 preferred lifetime associated with this address, in seconds.";
      }
      leaf max-life {
        type uint32;
        description
          "The valid lifetime associated with this address, in seconds.";
      }
      leaf ends {
        type string;
        description
          "The end time of the lease.";
      }
    }
  }

  grouping dhcp-relay-server-param {
    description
      "Configuration of DHCP relay server param.";

    leaf handle-option {
      type handle-option;
      description
        "Handling of DHCPv4 packets that already contain relay agent options.
          Override the matching option in root context.";
    }

    leaf drop-unmatched {
      type boolean;
      description
        "If true, drop packets from upstream servers if they were generated in
          response to a different relay agent. Override the matching option in
          root context.";
    }

    leaf hop-count {
      type uint32 {
        range "0..255";
        ntos-extensions:nc-cli-shortdesc "<0-255>";
      }
      description
        "Maximum hop count before packets are discarded. Override the matching
          option in root context.";
    }

    leaf max-size {
      type uint32 {
        range "64..1400";
        ntos-extensions:nc-cli-shortdesc "<64-1400>";
      }
      description
        "Maximum packet size to send to a DHCPv4 server. If a DHCP packet size
          surpasses this value it will be forwarded without appending relay agent
          information. Override the matching option in root context.";
    }
  }

  grouping dhcp-relay-server-config {
    description
      "Configuration data for DHCP relay server.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable/Disable DHCP relay for this server.";
    }

    leaf address {
      type ntos-inet:ipv4-address;
      description
        "IP address of DHCP server to which DHCP queries should be relayed.";
    }

    leaf-list interface {
      max-elements 20;
      type ntos-types:ifname;
      description
        "Interface(s) on which to listen to DHCPv4 queries. If ommitted, DHCP
          relay will listen on all broadcast interfaces.";
      ntos-extensions:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*/*[local-name()='name']";
    }

    uses dhcp-relay-server-param;
  }

  grouping dhcp-relay-config {
    description
      "Configuration data for DHCP relay.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable/Disable DHCP relay on this VRF.";
    }

    uses dhcp-relay-server-param;

    list dhcp-server {
      key "address";
      description
        "Configuration of DHCP server to which DHCP queries should be relayed.";

      uses dhcp-relay-server-config;
    }
  }

  rpc show-dhcp-server-leases {
    description
      "Show DHCP server leases.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf lease-type {
        type enumeration {
          enum 4 {
            description
              "ipv4 leases.";
          }
          enum 6 {
            description
              "ipv6 leases.";
          }
        }
        default "4";
        description
          "Get ipv4 or ipv6 lease.";
      }
      leaf page {
        type uint32;
        default "0";
        description
          "Page number.";
      }
      leaf num {
        type uint32;
        default "19";
        description
          "Max number in one page.";
      }
      leaf search {
        type string {
          length "1..64";
        }
        description
          "search lease with this key.";
      }
      leaf subnet {
        type ntos-inet:ip-prefix;
        description
          "Network prefix of the subnet on which the DHCP server.";
      }
      leaf output-type {
        type enumeration {
          enum node-tree {
            description
              "Output format to yang module data.";
          }
          enum json-string {
            description
              "Output format to json.";
          }
        }
        default "node-tree";
        description
          "Output format to yang module data or json string.";
      }
    }
    output {
      choice output-type {
        case node-tree {
          leaf enabled {
            type boolean;
            description
              "Enable/Disable DHCP for server.";
          }
          leaf total-num {
            type int32;
            description
              "total leases.";
          }
          uses dhcp-server-leases;
        }
        case json-string {
          leaf data {
            type string;
            description
              "The command output buffer.";
            ntos-extensions:nc-cli-stdout;
            ntos-extensions:nc-cli-hidden;
          }
        }
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-server-leases";
    ntos-api:internal;
  }

  rpc show-dhcp-client-active-num {
    description
      "Show DHCP client active num.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf lease-type {
        type enumeration {
          enum 4 {
            description
              "ipv4 leases.";
          }
          enum 6 {
            description
              "ipv6 leases.";
          }
        }
        default "4";
        description
          "Get ipv4 or ipv6 lease.";
      }
      leaf subnet {
        type ntos-inet:ip-prefix;
        description
          "Network prefix of the subnet on which the DHCP server.";
      }
      leaf output-type {
        type enumeration {
          enum node-tree {
            description
              "Output format to yang module data.";
          }
          enum json-string {
            description
              "Output format to json.";
          }
        }
        default "node-tree";
        description
          "Output format to yang module data or json string.";
      }
    }
    output {
      choice output-type {
        case node-tree {
          leaf active-num {
            type int32;
            description
              "total leases num.";
          }
          container detail {
            list subnet {
              key "prefix";
              ntos-extensions:nc-cli-one-liner;
              leaf prefix {
                type string;
                description
                  "lease file name.";
                ntos-extensions:nc-cli-no-name;
              }
              leaf interface {
                type string;
                description
                  "The interface where the dhcp pool is located.";
                ntos-extensions:nc-cli-no-name;
              }
              leaf total-num {
                type int32;
                description
                  "total lease num in config.";
              }
              leaf static-num {
                type int32;
                description
                  "static lease num in config.";
              }
              leaf dynamic-num {
                type int32;
                description
                  "dynamic lease num in db.";
              }
              leaf remain-num {
                type int32;
                description
                  "remain lease num.";
              }
              leaf usage-rate {
                type string;
                description
                  "Address utilization rate.";
              }
            }
            list subnet6 {
              key "prefix";
              ntos-extensions:nc-cli-one-liner;
              leaf prefix {
                type string;
                description
                  "lease file name.";
                ntos-extensions:nc-cli-no-name;
              }
              leaf interface {
                type string;
                description
                  "The interface where the dhcpv6 pool is located.";
                ntos-extensions:nc-cli-no-name;
              }
              leaf total-na {
                type string;
                description
                  "total na lease num in config.";
              }
              leaf na {
                type uint32;
                description
                  "na lease num in db.";
              }
              leaf remain-na {
                type string;
                description
                  "remain na lease num.";
              }
              leaf na-usage-rate {
                type string;
                description
                  "na lease utilization rate.";
              }
              leaf total-pd {
                type string;
                description
                  "total pd lease num in config.";
              }
              leaf pd {
                type int32;
                description
                  "pd lease num in db.";
              }
              leaf remain-pd {
                type string;
                description
                  "remain pd lease num.";
              }
              leaf pd-usage-rate {
                type string;
                description
                  "pd lease utilization rate.";
              }
            }
          }
        }
        case json-string {
          leaf data {
            type string;
            description
              "The command output buffer.";
            ntos-extensions:nc-cli-stdout;
            ntos-extensions:nc-cli-hidden;
          }
        }
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-client-active-num";
    ntos-api:internal;
  }

  rpc show-dhcp-hosts{
    description
      "Show DHCP active and static hosts number.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf subnet {
        type ntos-inet:ip-prefix;
        description
          "Network prefix of the subnet on which the DHCP server.";
      }
      leaf start {
        type uint32;
        default "0";
        description
          "index start.";
      }
      leaf end {
        type uint32;
        default "9";
        description
          "index end.";
      }
      leaf search-conf {
        type boolean;
        default "false";
        description
          "Search leases in config. default search leases with subnets state";
      }
    }
    output {

      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-hosts";
    ntos-api:internal;
  }

  rpc show-dhcpv6-hosts{
    description
      "Show DHCPv6 active and static hosts number.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf subnet {
        type ntos-inet:ip-prefix;
        description
          "Network prefix of the subnet on which the DHCP server.";
      }
      leaf start {
        type uint32;
        default "0";
        description
          "index start.";
      }
      leaf end {
        type uint32;
        default "9";
        description
          "index end.";
      }
      leaf search-conf {
        type boolean;
        default "false";
        description
          "Search leases in config. default search leases with subnets state";
      }
    }
    output {

      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcpv6-hosts";
    ntos-api:internal;
  }

  rpc show-dhcp-subnet-prefix {
      description
      "Show DHCP subnet prefix.";
      input {
        leaf vrf {
          type string;
          default "main";
          description
             "Specify the VRF.";
          ntos-extensions:nc-cli-completion-xpath
             "/ntos:state/ntos:vrf/ntos:name";
        }
        container host {
          description
             "Mapping from MAC address to IP address.";
          leaf mac-address {
             type ntos-if:mac-address;
             mandatory true;
             description
               "MAC address of the host.";
             ntos-extensions:nc-cli-no-name;
          }
          leaf ip-address {
             type ntos-inet:ip-address;
             mandatory true;
             description
               "IPv4 or IPv6 address of the host.";
             ntos-extensions:nc-cli-no-name;
          }
        }
      }
      output {
        leaf data {
           type string;
           description
              "The command output buffer.";
           ntos-extensions:nc-cli-stdout;
           ntos-extensions:nc-cli-hidden;
        }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-subnet-prefix";
    ntos-extensions:nc-cli-hidden;
    ntos-api:internal;
  }

  rpc show-dhcp-server-state {
    description
      "Show DHCP server state.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf start {
        type uint32;
        default "0";
        description
          "index start.";
      }
      leaf end {
        type uint32;
        default "19";
        description
          "index end.";
      }
      leaf ip-type {
        type enumeration {
          enum 4 {
            description
              "ipv4.";
          }
          enum 6 {
            description
              "ipv6.";
          }
        }
        description
          "dhcp server type.";
      }
    }

    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-server-state";
    ntos-extensions:nc-cli-hidden;
    ntos-api:internal;
  }

  rpc show-dhcp-server-config {
    description
      "Show DHCP server config.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf start {
        type uint32;
        default "0";
        description
          "index start.";
      }
      leaf end {
        type uint32;
        default "100";
        description
          "index end.";
      }
      choice search {
        description
          "search with key";

        case prefix {
          leaf prefix {
            type ntos-inet:ip-prefix;
            description
              "Network prefix of the subnet.";
          }
        }
        case interface {
          leaf interface {
            type ntos-types:ifname;
            description
              "Interface on the subnet.";
          }
        }
        case key-word {
          leaf key-word {
            type string {
              length "1..64";
            }
            description
              "key word in subnet.";
          }
        }
      }
      leaf server-type {
        type enumeration {
          enum 4 {
            description
              "ipv4 dhcp server.";
          }
          enum 6 {
            description
              "ipv6 dhcp server.";
          }
        }
        description
          "dhcp server type.";
      }
    }

    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-server-config";
    ntos-extensions:nc-cli-hidden;
    ntos-api:internal;
  }

  rpc show-dhcp-server-statistics {
    description
      "Show dhcp server statistics information.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf server {
        type enumeration {
          enum dhcpv4 {
            description
              "dhcpv4.";
          }
          enum dhcpv6 {
            description
              "dhcpv6.";
          }
        }
        description
          "dhcpv4 server or dhcpv6 server.";
        ntos-extensions:nc-cli-no-name;
      }
      leaf submodule {
        type enumeration {
          enum packet {
            description
              "packet.";
          }
          enum ha {
            description
              "ha.";
          }
          enum sync {
            description
              "sync to other process.";
          }
          enum collect {
            description
              "call libcollect.";
          }
          enum lease-err {
            description
              "lease some err.";
          }
          enum libev-q {
            description
              "lease some err.";
          }
        }
        default "packet";
        description
          "The submodule in dhcpv4 server or dhcpv6 server.";
        ntos-extensions:nc-cli-no-name;
      }
    }

    output {
        leaf data {
        type string;
        description
            "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
        }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-server stats";
    ntos-api:internal;
  }

  rpc show-dhcp-server-status {
    description
      "Show dhcp server status information.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf server {
        type enumeration {
          enum dhcpv4 {
            description
              "dhcpv4.";
          }
          enum dhcpv6 {
            description
              "dhcpv6.";
          }
        }
        description
          "dhcpv4 server or dhcpv6 server.";
        ntos-extensions:nc-cli-no-name;
      }
      leaf submodule {
        type enumeration {
          enum ha {
            description
              "ha.";
          }
        }
        default "ha";
        description
          "The submodule in dhcpv4 server or dhcpv6 server.";
        ntos-extensions:nc-cli-no-name;
      }
    }

    output {
        leaf data {
        type string;
        description
            "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
        }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-server status";
    ntos-api:internal;
  }

  rpc show-dhcp-server-lease-files {
    description
      "show dhcp server lease files.";
    input {
      leaf search {
        type string{
          length "1..63";
        }
        description
          "search key in dhcp server leases file name.";
      }
    }
    output {
      leaf-list lease-files-name {
        type string;
        description
          "dhcp server lease files list.";
        ntos-extensions:nc-cli-no-name;
      }
    }
    ntos-extensions:nc-cli-show "dhcp-server-lease-files";
    ntos-api:internal;
  }

  rpc flush-dhcp-server-lease-file {
    description
      "Flush specify dhcp server lease.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      choice lease {
        case all-leases {
          leaf all-leases {
            type enumeration {
              enum all {
                description
                  "Specify dhcp server IPv4 and IPv6.";
              }
            }
            ntos-extensions:nc-cli-no-name;
          }
        }
        case specify-lease {
          leaf lease {
            type string {
              length "1..253";
            }
            description
              "Specify dhcp server one leases file.";
          }
        }
      }
      leaf flush-type {
        type enumeration {
          enum lease {
            description
              "Specify flush dhcp server lease table.";
          }
          enum file {
            description
              "Specify flush dhcp server db file.";
          }
        }
        default "lease";
      }
    }
    output {
      leaf-list message {
        type string;
        description
          "flush lease files result.";
        ntos-extensions:nc-cli-no-name;
      }
    }
    ntos-extensions:nc-cli-flush "dhcp-server-lease";
    ntos-extensions:nc-cli-hidden;
  }

  rpc show-dhcp-relay-server-config {
    description
      "Show dhcp relay server configuration.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf page {
        type uint32{
          range "0..10000";
        }
        default "0";
        description
          "Page number.";
      }
      leaf num {
        type uint32{
          range "1..100";
        }
        default "10";
        description
          "Max number in one page.";
      }
      leaf search {
        type string {
          length "1..64";
        }
        description
          "search server config with this key.";
      }
      leaf output-type {
        type enumeration {
          enum node-tree {
            description
              "Output format to yang module data.";
          }
          enum json-string {
            description
              "Output format to json.";
          }
        }
        default "node-tree";
        description
          "Output format to yang module data or json string.";
      }
    }
    output {
      choice output-type {
        case node-tree {
          leaf number {
            type int32;
            description
              "total domain name number.";
          }
          list dhcp-server {
            key "address";
            description
              "Configuration of DHCP server to which DHCP queries should be relayed.";

            uses dhcp-relay-server-config;
          }
        }
        case json-string {
          leaf data {
            type string;
            description
              "The command output buffer.";
            ntos-extensions:nc-cli-stdout;
            ntos-extensions:nc-cli-hidden;
          }
        }
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-relay server-config";
    ntos-api:internal;
  }

  rpc show-dhcp-relay-statistics {
    description
      "Show dhcp relay statistics information on interface.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf page {
        type uint32{
          range "0..100";
        }
        default "0";
        description
          "Page number.";
      }
      leaf num {
        type uint32{
          range "1..10";
        }
        default "5";
        description
          "Max number in one page.";
      }
      leaf search {
        type string {
          length "1..64";
        }
        description
          "search interfaces statistics.";
      }
      leaf output-type {
        type enumeration {
          enum node-tree {
            description
              "Output format to yang module data.";
          }
          enum json-string {
            description
              "Output format to json.";
          }
        }
        default "node-tree";
        description
          "Output format to yang module data or json string.";
      }
    }
    output {
      choice output-type {
        case node-tree {
          list statistics {
            key "interface";
            description
              "statistics with dhcp relay on the interfaces.";

            leaf interface {
                type ntos-types:ifname;
                description
                    "Interface on which dhcp relay statistics.";
                ntos-extensions:nc-cli-completion-xpath
                    "../../../ntos-interface:interface/*/*[local-name()='name']";
            }
            leaf recv-rep-num {
                type uint32;
                description
                    "The number of dhcp relay receive reply packet on the interfaces.";
            }
            leaf recv-req-num {
                type uint32;
                description
                    "The number of dhcp relay receive request packet on the interfaces.";
            }
            leaf send-rep-num {
                type uint32;
                description
                    "The number of dhcp relay send reply packet on the interfaces.";
            }
            leaf send-req-num {
                type uint32;
                description
                    "The number of dhcp relay send request packet on the interfaces.";
            }
            leaf send-rep-err-num {
                type uint32;
                description
                    "The number of dhcp relay send reply packet error on the interfaces.";
            }
            leaf send-req-err-num {
                type uint32;
                description
                    "The number of dhcp relay send request packet error on the interfaces.";
            }
          }
        }
        case json-string {
          leaf data {
            type string;
            description
              "The command output buffer.";
            ntos-extensions:nc-cli-stdout;
            ntos-extensions:nc-cli-hidden;
          }
        }
      }
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "dhcp-relay statistics";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "DHCP server and relay configuration.";

    container dhcp {
      presence "DHCP server and relay configuration";
      description
        "DHCP server and relay configuration.";
      ntos-extensions:feature "product";

      container server {
        must 'default-lease-time <= max-lease-time' {
          error-message "Default-lease must be less or equal to max-lease.";
        }
        presence "DHCP server configuration";
        description
          "DHCP server configuration.";
        uses dhcp-server-config;
      }

      container relay {
        presence "DHCP relay configuration";
        description
          "DHCP relay configuration.";
        uses dhcp-relay-config;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "DHCP server and relay operational state data.";

    container dhcp {
      presence "DHCP server and relay operational state data.";
      description
        "DHCP server and relay operational state data.";
      ntos-extensions:feature "product";

      container server {
        presence "DHCP server operational state data.";
        description
          "DHCP server operational state data.";
        uses dhcp-server-config {

          augment "subnet" {
            description
              "Augment subnet with state leaf.";

            leaf state {
              type enumeration {
                enum active {
                  description
                    "The subnet is active.";
                }
                enum wait-interface-up {
                  description
                    "The subnet is inactive (the interface is down).";
                }
                enum wait-ip {
                  description
                    "The subnet is inactive (No IP address corresponding for
                     this subnet was found).";
                }
                enum wait-iface-ready {
                  description
                    "The subnet is inactive (No IP address corresponding for
                     this subnet was found or iface is not up).";
                }
              }
              description
                "Subnet state.";
            }
          }

          augment "subnet6" {
            description
              "Augment subnet with state leaf.";

            leaf state {
              type enumeration {
                enum active {
                  description
                    "The subnet is active.";
                }
                enum wait-interface-up {
                  description
                    "The subnet is inactive (the interface is down).";
                }
                enum wait-ip {
                  description
                    "The subnet is inactive (No IP address corresponding for
                      this subnet was found).";
                }
              }
              description
                "Subnet state.";
            }
          }
        }
      }

      container relay {
        presence "DHCP relay operational state data.";
        description
          "DHCP relay operational state data.";
        uses dhcp-relay-config;
      }
    }
  }
}