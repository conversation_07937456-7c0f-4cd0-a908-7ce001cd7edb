   module ietf-factory-default {
     yang-version 1.1;
     namespace "urn:ietf:params:xml:ns:yang:ietf-factory-default";
     prefix fd;

     import ietf-datastores {
       prefix ds;
       reference
         "RFC 8342: Network Management Datastore Architecture
          (NMDA)";
     }
     import ietf-netconf-acm {
       prefix nacm;
       reference
         "RFC 8341: Network Configuration Access Control Model";
     }

     organization
       "IETF Network Modeling (netmod) Working Group";
     contact
       "WG Web:   <https://datatracker.ietf.org/wg/netmod/>
        WG List:  <mailto:<EMAIL>>

        Editor:   <PERSON>
                  <mailto:<EMAIL>>

        Editor:   <PERSON><PERSON><PERSON><PERSON> Lengyel
                  <mailto:<EMAIL>>

        Editor:   <PERSON> <PERSON><PERSON>
                  <mailto:<EMAIL>>";
     description
       "This module provides functionality to reset a server to its
        factory default configuration and, when supported, to
        discover the factory default configuration contents
        independently of resetting the server.

        Copyright (c) 2020 IETF Trust and the persons identified as
        authors of the code.  All rights reserved.

        Redistribution and use in source and binary forms, with or
        without modification, is permitted pursuant to, and subject
        to the license terms contained in, the Simplified BSD License
        set forth in Section 4.c of the IETF Trust's Legal Provisions
        Relating to IETF Documents
        (https://trustee.ietf.org/license-info).

        This version of this YANG module is part of RFC 8808; see the
        RFC itself for full legal notices.";

     revision 2020-08-31 {
       description
         "Initial revision.";
       reference
         "RFC 8808: A YANG Data Model for Factory Default Settings";
     }

     feature factory-default-datastore {
       description
         "Indicates that the factory default configuration is
          available as a datastore.";
     }

     rpc factory-reset {
       nacm:default-deny-all;
       description
         "The server resets all datastores to their factory
          default contents and any nonvolatile storage back to
          factory condition, deleting all dynamically
          generated files, including those containing keys,
          certificates, logs, and other temporary files.

          Depending on the factory default configuration, after
          being reset, the device may become unreachable on the
          network.";
     }

     identity factory-default {
       if-feature "factory-default-datastore";
       base ds:datastore;
       description
         "This read-only datastore contains the factory default
          configuration for the device that will be used to replace
          the contents of the read-write conventional configuration
          datastores during a 'factory-reset' RPC operation.";
     }
   }
