module ntos-config-mgmt {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:config-mgmt";
  prefix ntos-config-mgmt;

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-api {
    prefix ntos-api;
  }

  import ntos-commands {
    prefix ntos-cmd;
  }

  import ietf-netconf-acm {
    prefix nacm;
  }

  import ntos-inet-types {
    prefix ntos-inet-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS config management module.";

  revision 2022-04-19 {
    description
      "Add new RPCs.";
    reference
      "";
  }

  revision 2021-11-04 {
    description
      "Set nacm:default-deny-all.";
    reference
      "";
  }

  revision 2021-09-15 {
    description
      "Modify config import and export.";
    reference
      "";
  }

  revision 2021-06-08 {
    description
      "Initial version.";
    reference
      "";
  }

  rpc export-pub-key {
    nacm:default-deny-all;
    ntos-ext:nc-cli-cmd "export-pub-key";
    ntos-ext:nc-cli-command-no-pager;
    description
      "Export ssh Public key.";
    input {
      container remote {
        leaf host {
          type ntos-inet-types:host;
          ntos-ext:nc-cli-no-name;
        }
        leaf usr {
          type string {
            length "1..32";
          }
          ntos-ext:nc-cli-no-name;
        }
        leaf pwd {
          type string {
            length "1..128";
          }
          ntos-ext:nc-cli-no-name;
        }
        leaf host-type {
          type enumeration {
            enum "Unix";
            enum "Windows";
          }
          default "Unix";
          description
            "Unix or Windows.";
          ntos-ext:nc-cli-no-name;
        }
        description
          "Export to remote host.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
  }

  rpc export-config {
    nacm:default-deny-all;
    ntos-ext:nc-cli-cmd "export-config";
    ntos-ext:nc-cli-command-no-pager;
    description
      "Export config to the specified location.";
    input {
      leaf location {
        type string {
          pattern '[a-zA-Z0-9.\-/]+.tar.gz';
        }
        default "/tmp/config/export-startup.tar.gz";
        description
          "Path of the exported config file, with suffix '.tar.gz'.";
      }
      container remote {
        leaf host {
          type ntos-inet-types:host;
          ntos-ext:nc-cli-no-name;
        }
        leaf dir {
          type string;
          ntos-ext:nc-cli-no-name;
        }
        leaf usr {
          type string {
            length "1..32";
          }
          ntos-ext:nc-cli-no-name;
        }
        leaf pwd {
          type string {
            length "1..128";
          }
          ntos-ext:nc-cli-no-name;
        }
        description
          "Export to remote host.";
      }
      leaf unencrypted {
        type empty;
        description
          "Using the 'unencrypted' operation to export config.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
  }

  rpc import-config {
    nacm:default-deny-all;
    ntos-ext:nc-cli-cmd "import-config";
    ntos-ext:nc-cli-command-no-pager;
    description
      "Import config from the specified location.";
    input {
      leaf location {
        type string {
          pattern '[a-zA-Z0-9.\-/]+.tar.gz';
        }
        default "/tmp/config/import-startup.tar.gz";
        description
          "Path of the imported config file, with suffix '.tar.gz'.";
      }
      leaf merge {
        type empty;
        description
          "Using the 'merge' operation to import config.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
  }
  
  rpc backup-config {
    nacm:default-deny-all;
    ntos-ext:nc-cli-cmd "backup-config";
    ntos-ext:nc-cli-command-no-pager;
    description
      "Backup config to the specified location.";
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
  }
  
  rpc get-backup-config {
    nacm:default-deny-all;
    ntos-ext:nc-cli-cmd "get-backup-config";
    ntos-ext:nc-cli-command-no-pager;
    description
      "Get backup config to the specified location.";
    output {
      leaf path {
          type string {
            pattern '^(/)$|((/[\w-]+)+)';
          }
      description
          "Path of the backup config file.";
      }
      container file-list {
        description
            "All of the backup config file, maximum of 5 files can be backed up";

        leaf-list file {
          type string {
            pattern '[a-zA-Z0-9.\-/]+.tar.gz';
          }
          description
            "Path of the backup config file, with suffix '.tar.gz'.";
        }
      }
    }
  }

  rpc reset-factory-settings {
    nacm:default-deny-all;
    ntos-ext:nc-cli-cmd "reset-factory-settings";
    description
      "Reset config to factory settings.";
    input {
      leaf reboot {
        type boolean;
        default "true";
        description
          "Whether to reboot the system after restoring factory settings.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-api:internal;
  }
  
  rpc set-mgmt-perf-enabled {
    ntos-ext:nc-cli-cmd "set-mgmt-perf-enabled";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
    description
      "Set mgmt performance function enabled.";
    input {
      leaf enabled {
        type boolean;
        default "true";
        description
          "Performance function enable.";
      }
    }
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
  }
  
  rpc show-mgmt-perf-status {
    description
      "Show sysrepo plugins datastore rds server infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-show "mgmt-perf-status";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
  }

  rpc copy-startup-running {
    ntos-ext:nc-cli-cmd "copy-startup-running";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
    description
      "Copy startup to running.";
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
  }

  rpc async-copy-config {
    input {
      container target {
        description
          "Particular configuration to copy to.";

        choice config-target {
          mandatory true;
          description
            "The configuration target of the copy operation.";

          leaf startup {
            type empty;
            description
              "The running configuration is the config target.
               This is optional-to-implement on the server.";
          }
        }
      }

      container source {
        description
          "Particular configuration to copy from.";

        choice config-source {
          mandatory true;
          description
            "The configuration source for the copy operation.";

          leaf running {
            type empty;
            description
              "The startup configuration is the config source.";
          }

        }
      }
    }
    ntos-ext:nc-cli-cmd "async-copy";
  }
}
