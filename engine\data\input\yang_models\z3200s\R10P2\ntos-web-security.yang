module ntos-web-security {
  yang-version 1.1;

  namespace "urn:ruijie:ntos:params:xml:ns:yang:web-security";

  prefix ntos-web-security;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-inet-types {
    prefix ntos-inet;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS web security module.";

  revision 2023-05-05 {
    description
      "Initial revision.";
    reference
      "";
  }

  typedef exception-action-type {
    type enumeration {
      enum sig-action {
        description "Indicate the type of signature.";
      }
      enum bypass {
        description "Indicate the type of bypass.";
      }

      enum alert {
        description "Indicate the type of alert.";
      }

      enum block {
        description "Indicate the type of block.";
      }
    }

    description "Action when an intrusion is detected.";
  }

  typedef profile-action-type {
    type enumeration {
      enum sig-action {
        description "Indicate the type of signature.";
      }

      enum alert {
        description "Indicate the type of alert.";
      }

      enum block {
        description "Indicate the type of block.";
      }
    }

    description "Action when an intrusion is detected.";
  }

  typedef semantic-action-type {
    type enumeration {
      enum alert {
        description "Indicate the type of alert.";
      }

      enum block {
        description "Indicate the type of block.";
      }
    }

    description "Action when an intrusion is detected.";
  }


  typedef mode-type {
    type enumeration {
      enum high-coverage {
        description "High detection mode.";
      }

      enum low-false-positive {
        description "Low false alarm mode.";
      }
    }

    description "Protect mode.";
  }

  typedef exception-type {
    type enumeration {
      enum rule-engine {
        description "Rule engine.";
      }

      enum semantic-engine {
        description "Semantic engine.";
      }
    }

    description "Exception type.";
  }

  typedef severity-type {
    type enumeration {
      enum informational {
        description
          "Informational severity.";
      }
      enum low {
        description
          "Low severity.";
      }
      enum medium {
        description
          "Medium severity.";
      }
      enum high {
        description
          "High severity.";
      }
    }
    description
      "Level of the severity.";
  }

  grouping websec-template {
    description "The grouping of the web security template.";

    list template {
      key "name";
      description "The list of the web security template.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description "The name of the web security template.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description "The description of the web security template.";
      }

      leaf-list severity {
        type severity-type;
        description
          "Level of the severity filtering conditions of the web security template.";
      }

      choice category-choice {
        default all-category-choice;
        description
          "Indicate the type of template category.";
        case all-category-choice {
          leaf all-category {
            type empty;
            description
              "Indicate whether to select all of categories.";
          }
        }
        case specified-category-choice {
          list specified-category {
            key "name";
            description
              "The specified category filtering conditions of the specified-category.";

            leaf name {
              type string;
              description
                "The specified category filtering conditions of the specified-category.";
            }

            leaf all-sub-category {
              type boolean;
              description
                "Indicate whether to select all sub-category.";
            }

            leaf-list specified-sub-category {
              when "../all-sub-category = 'false'";
              type string;
              description
                "The specified sub-category filtering conditions of the specified-sub-category.";
            }
          }
        }
      }

      list semantic {
        key "name";
        description "The list of the semantic engine.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of the semantic engine.";
        }

        leaf mode {
          type mode-type;
          default low-false-positive;
          description "Semantic engine protect mode.";
        }

        leaf action {
          type semantic-action-type;
          default alert;
          description "Action when hit the semantic engine.";
        }
      }

      list exception-config {
        key "url except-type";
        description "Web security exception signatures.";

        leaf url {
          type string {
            pattern "[^`!@$(){};,'\\\\<>|#]*" {
              error-message "Can not include character: ^`!@$(){};,'\\<>|#";
            }
          }

          description "The specific URL.";
        }

        leaf except-type {
          type exception-type;
          description "Exception type.";
        }

        leaf source-network {
          type union {
            type ntos-inet:ipv4-address;
            type ntos-inet:ipv6-address;
          }
          description "The leaf of source network.";
        }

        list semantic-exception {
          when "../except-type = 'semantic-engine'";
          key "name";
          description "The list of the semantic engine exception signatures.";

          leaf name {
            type ntos-types:ntos-obj-name-type;
            description "The name of semantic engine type.";
          }

          leaf action {
            type exception-action-type;
            default bypass;
            description "Action when hit the exception signature.";
          }
        }

        list rule-exception {
          when "../except-type = 'rule-engine'";
          key "id";
          description "The list of the web security rule exception signatures.";

          leaf id {
            type uint32;
            description "Id of signature.";
          }

          leaf action {
            type exception-action-type;
            default bypass;
            description "Action when hit the exception signature.";
          }
        }
      }

      container reference-list {
        container security-policy {
          description "Reference list of security policy.";

          leaf-list id {
            type uint32;
            description "Id of security policy.";
          }
        }

        container sim-security-policy {
          description "Reference list of simulative security policy.";

          leaf-list id {
            type uint32;
            description "Id of simulative security policy.";
          }
        }

        config false;

        description "The policy references of the template.";
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description "Web security configuration.";

    container websec-config {
      description "Web security configuration.";

      list predefined-signatures {
        key "id";
        description "List of predefined signature.";

        leaf id {
          type uint32;
          description "Id of predefined signature.";
        }

        leaf state {
          type boolean;
          description "State of predefined signature.";
        }

        leaf interval {
          type uint32;
          description "The interval time of predefined signature, in seconds.";
        }

        leaf threshold {
          type uint32;
          description "The threshold value of predefined signature.";
        }

        leaf block-time {
          type uint32;
          description "The blocking time of predefined signature, in minutes.";
        }
      }

      uses websec-template;

      list profile {
        key name;
        description "List of profile.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "Profile name.";
        }

        leaf template-name {
          type ntos-types:ntos-obj-name-type;
          mandatory true;
          description "Template name.";
        }

        leaf action {
          type profile-action-type;
          mandatory true;
          description "The action of the profile.";
        }
      }

      container global-config {
        description
          "The information of Web security global configuration.";

        leaf blacklist-enabled {
          type boolean;
          description
            "Set status of blacklist-enabled.";
        }
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description "State of web security.";

    container websec-state {
      config false;
      description "Web security state.";

      list predefined-signatures {
        key "id";
        description "List of predefined signature.";

        leaf id {
          type uint32;
          description "Id of predefined signature.";
        }

        leaf state {
          type boolean;
          description "State of predefined signature.";
        }

        leaf interval {
          type uint32;
          description "The interval time of predefined signature, in seconds.";
        }

        leaf threshold {
          type uint32;
          description "The threshold value of predefined signature.";
        }

        leaf block-time {
          type uint32;
          description "The blocking time of predefined signature, in minutes.";
        }
      }

      uses websec-template;

      list profile {
        key name;
        description "List of profile.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "Profile name.";
        }

        leaf template-name {
          type ntos-types:ntos-obj-name-type;
          mandatory true;
          description "Template name.";
        }

        leaf action {
          type profile-action-type;
          mandatory true;
          description "The action of the profile.";
        }
      }
    }
  }

  rpc show-websec-all-category {
    description "Show category supported.";

    output {
      list category {
        description "The list of category.";

        leaf name {
          type string;
          description "The name of category.";
        }

        leaf id {
          type uint32;
          description "The id of category.";
        }

        list sub-category {
          description
            "The list of sub-category.";

          leaf name {
            type string;
            description
              "The name of sub-category.";
          }

          leaf id {
            type uint32;
            description
              "The id of sub-category.";
          }
        }
      }
    }

    ntos-ext:nc-cli-show "websec all-category";
  }

  rpc show-websec-template {
    description "Show defined templates.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description "The name of VRF.";
      }

      leaf start {
        type uint32;
        description "The start offset.";
      }

      leaf end {
        type uint32;
        must "current() >= ../start" {
          error-message "The end value must be larger than the start value.";
        }
        description "The end offset.";
      }

      leaf predefined {
        type boolean;
        default false;
        description "Show predefined templates or custom templates.";
      }

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description "The name of template.";
        ntos-ext:nc-cli-group "subcommand1";
      }

      leaf filter {
        type ntos-types:ntos-obj-name-type;
        description "Whether to perform fuzzy match.";
        ntos-ext:nc-cli-group "subcommand1";
      }
    }

    output {
      leaf template-sum {
        type uint32;
        description "The total number of templates.";
      }

      uses websec-template;
    }

    ntos-ext:nc-cli-show "websec template";
  }

  rpc update-websec-signature {
    description "Update web security signature.";

    input {
      leaf package-name {
        type string;
        description
          "Name of upgrade package, filename format: web_full_sign_YYYYMMDDnnnn.zip.";
      }
    }

    output {
      leaf id {
        type uint32;
        description "Id of tracing upgrade.";
      }

      leaf progress {
        type uint32;
        description "Progress of upgrade.";
      }

      leaf error-code {
        type uint32;
        description "Error code.";
      }

      leaf buf {
        type string;
        description "Error code information.";
      }
    }

    ntos-ext:nc-cli-cmd "websec signature-update";
  }

  rpc websec-signature-state {
    description "Show web security signature state.";

    input {
      leaf id {
        type uint32;
        description
          "Id of upgrade.";
      }
    }

    output {
      leaf progress {
        type uint32;
        description "Progress of upgrade.";
      }

      leaf error-code {
        type uint32;
        description "Error code.";
      }

      leaf buf {
        type string;
        description "Error code information.";
      }
    }

    ntos-ext:nc-cli-show "websec signature-state";
  }

  rpc show-websec-signature-version {
    description "Show web security signature version.";

    output {
      leaf version {
        type string;
        description "Version of the signature.";
      }
    }

    ntos-ext:nc-cli-show "websec signature-version";
  }

  rpc websec-signature-rollback {
    description
      "Rollback web security signature.";

    input {
      leaf module {
        type string;
        description
          "Module of rollback process.";
      }

      leaf id {
        type uint32;
        description
          "Id of rollback process.";
      }

      leaf rollback-type {
        type string;
        description
          "Type of rollback process.";
      }
    }

    output {
      leaf module {
        type string;
        description
          "Module of tracing rollback.";
      }

      leaf result {
        type uint32;
        description
          "Result of tracing rollback.";
      }

      leaf progress {
        type uint32;
        description
          "Progress of rollback.";
      }

      leaf error-code {
        type uint32;
        description
          "Error code.";
      }
    }

    ntos-ext:nc-cli-cmd "websec signature-rollback";
  }

  rpc websec-rollback-version {
    output {
      leaf buffer {
        type string;
        description
          "Signature version of rollback.";
      }
    }
  }

  rpc update-websec-semantic-engine {
    description "Update web security semantic engine.";

    input {
      leaf package-name {
        type string;

        description
          "Name of upgrade package, filename format: websec_rse_sig_YYYYMMDDnnnn.zip.";
      }
    }

    output {
      leaf count {
        type uint32;
        description "Count of semantic upgrade.";
      }

      leaf id {
        type uint32;
        description "Id of semantic upgrade.";
      }

      leaf error-code {
        type uint8;
        description "Error code.";
      }

      leaf buf {
        type string;
        description "Error code information.";
      }

    }

    ntos-ext:nc-cli-cmd "websec semantic-engine update";
  }

  rpc websec-semantic-engine-upgrade-state {
    description "Show web security semantic engine upgrade state.";

    input {
      leaf id {
        type uint32;
        description
          "Id of upgrade.";
      }
    }

    output {
      leaf count {
        type uint32;
        description "Count of semantic upgrade.";
      }

      leaf id {
        type uint32;
        description "Id of semantic upgrade.";
      }

      leaf error-code {
        type uint32;
        description "Error code.";
      }

      leaf buf {
        type string;
        description "Error code information.";
      }

    }

    ntos-ext:nc-cli-show "websec semantic-engine state";
  }

  rpc show-websec-semantic-status {
    description "Show web security semantic status.";

    output {
      leaf version {
        type string;
        description "Version of the engine.";
      }

      leaf format-version {
        type string;
        description "Format Version of the engine.";
      }

      leaf status {
        type string;
        description "Status of the engine.";
      }
    }

    ntos-ext:nc-cli-show "websec semantic status";
  }

  rpc websec-semantic-engine-rollback {
    description
      "Rollback web security semantic engine.";

    input {
      leaf module {
        type string;
        description
          "Module of rollback process.";
      }

      leaf id {
        type uint32;
        description
          "Id of rollback process.";
      }

      leaf rollback-type {
        type string;
        description
          "Type of rollback process.";
      }
    }

    output {
      leaf module {
        type string;
        description
          "Module of tracing rollback.";
      }

      leaf result {
        type uint32;
        description
          "Result of tracing rollback.";
      }

      leaf progress {
        type uint32;
        description
          "Progress of rollback.";
      }

      leaf error-code {
        type uint32;
        description
          "Error code.";
      }
    }

    ntos-ext:nc-cli-cmd "websec semantic engine-rollback";
  }

  rpc websec-semantic-rollback-version {
    output {
      leaf buffer {
        type string;
        description
          "Websec semantic engine version of rollback.";
      }
    }
  }

  rpc websec-show-blacklist-enabled {
    description
      "Status of blacklist-enabled.";

    output {
      leaf blacklist-enabled {
        type boolean;
        description
          "Status of blacklist-enabled.";
      }
    }
    ntos-ext:nc-cli-show "websec blacklist-enabled";
  }

}
