module ntos-snmp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:snmp";
  prefix ntos-snmp;

  import ietf-netconf-acm {
    prefix nacm;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS SNMP.";

  revision 2022-11-24 {
    description
      "Add debug rpc and snmptools rpc.";
    reference "";
  }
  revision 2019-12-06 {
    description
      "Support monitoring of several VRFs.";
    reference "";
  }
  revision 2019-06-25 {
    description
      "Support list of source addresses in community definitions.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity snmp {
    base ntos-types:SERVICE_LOG_ID;
    description
      "SNMP service.";
  }

  typedef oid {
    type string;
    description
      "SNMP object identifier either as a label or numeric form.";
  }

  typedef auth-level {
    type enumeration {
      enum read-only {
        description
          "Read-only (GET and GETNEXT) access.";
      }
      enum read-write {
        description
          "Read-write (GET, GETNEXT and SET) access.";
      }
    }
    description
      "Available authentication levels.";
  }

  typedef frequency {
    type string {
      pattern '[0-9]+[smhdw]';
    }
    description
      "Value in seconds or optionnally suffixed by one of s (for seconds),
       m (for minutes), h (for hours), d (for days) or w (for weeks).";
  }

  grouping snmp {
    description
      "SNMP common options.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the SNMP engine.";
    }

    container listen {
      presence "Makes listen available.";
      description
        "Configuration of the transport endpoint on which the engine listens.";

      leaf protocols {
        type bits {
          bit udp {
            description
              "UDP.";
          }
          bit tcp {
            description
              "TCP.";
          }
          bit udp6 {
            description
              "UDPv6.";
          }
          bit tcp6 {
            description
              "TCPv6.";
          }
        }
        default "udp";
        description
          "The protocols used for connecting to the SNMP agent.";
      }

      leaf port {
        type ntos-inet:port-number;
        default "161";
        description
          "The TCP or UDP port on which the engine listens.";
      }
    }
    // container listen

    container static-info {
      description
        "Most of the information reported by the SNMP agent is retrieved from
         the underlying system. However, certain MIB objects can be
         configured with a static value.";

      leaf location {
        type string;
        description
          "System location (sysLocation.0) object value.";
      }

      leaf contact {
        type string;
        description
          "System contact (sysContact.0) object value.";
      }

      leaf name {
        type string;
        description
          "System name (sysName.0) object value.";
      }

      leaf services {
        type uint8;
        description
          "Value of the sysServices.0 object. For a host system, a good value
           is 72 (application + end-to-end layers).";
      }

      leaf description {
        type string;
        description
          "System description of the SNMP agent (sysDescr.0).";
      }

      leaf object-id {
        type oid;
        description
          "System OID (sysObjectOID.0) object value.";
      }
    }
    // container static-info

    list view {
      key "name";
      description
        "A named 'view' - a subset of the overall OID tree.";

      leaf name {
        type string;
        description
          "The name of the view.";
      }

      list subtree {
        key "oid";
        min-elements 1;
        description
          "A part of the OID tree to include or exclude from the view.";
        ext:nc-cli-one-liner;

        leaf oid {
          type oid;
          description
            "The OID root to include or exclude from the view.";
        }

        leaf included {
          type boolean;
          default "true";
          description
            "Set to false to exclude this OID from the view.";
        }
      }
    }
    // list view

    list community {
      key "name";
      description
        "An SNMPv1 or SNMPv2c community.";

      leaf name {
        type string;
        description
          "The name of the community.";
      }

      leaf authorization {
        type auth-level;
        mandatory true;
        description
          "The authorization level of the community.";
      }

      leaf-list source {
        type union {
          type ntos-inet:host;
          type ntos-inet:ip-prefix;
        }
        description
          "Restrict access to requests from the specified address or prefix list.";
      }

      leaf view {
        type leafref {
          path
            "../../view/name";
        }
        description
          "Restricts access for that community to the subtree rooted at the
           given view name. If not specified, the community has access to the
           whole OID tree.";
      }
    }
    // list community

    list monitored-vrf {
      key "name";
      description
        "Monitored VRF.";

      leaf name {
        type string;
        must 'count(/ntos:config/ntos:vrf[ntos:name=current()]) != 0' {
          error-message "The monitored VRF must be defined.";
        }
        must "/ntos:config/ntos:vrf[ntos:name=current()]/snmp/enabled = 'false'" {
          error-message "SNMP must be disabled in the monitored VRF.";
        }
        description
          "The name of the monitored VRF.";
      }

      list identifier {
        key "name";
        description
          "Identifier to access the monitored VRF, acts as a community for
           SNMPv1 or SNMPv2c and as a context for SNMPv3.";

        leaf name {
          type string;
          must "count(../../../*[local-name()='monitored-vrf']/*[local-name()='identifier']/*[local-name()='name'][text()=current()]) = 1" {
            error-message "The monitored VRF identifier must be unique.";
          }
          must "count(../../../*[local-name()='community']/*[local-name()='name'][text()=current()]) = 0" {
            error-message "The monitored VRF identifier must not be a
              community name.";
          }
          description
            "The monitored VRF identifier (community for SNMPv1 or SNMPv2c and
             context for SNMPv3).";
        }

        leaf authorization {
          type auth-level;
          mandatory true;
          description
            "The authorization level of the identifier.";
        }

        leaf-list source {
          type union {
            type ntos-inet:host;
            type ntos-inet:ip-prefix;
          }
          description
            "Restrict access to requests from the specified address or prefix
             list for SNMPv1 or SNMPv2.";
        }

        leaf view {
          type leafref {
            path
              "../../../view/name";
          }
          description
            "Restricts access to the subtree rooted at the given view name. If
             not specified, the identifier has access to the whole OID tree.";
        }
      }

      container traps {
        description
          "Active monitoring and automatic notifications configuration.";

        list destination {
          key "host";
          description
            "The destination of SNMPv1 TRAPs, SNMPv2c TRAP2s, or SNMPv2 INFORM
             notifications.";

          leaf host {
            type leafref {
              path
                "../../../../traps/destination/host";
            }
            description
              "The receiver address to use.";
          }

          leaf community {
            type leafref {
              path
                "../../../identifier/name";
            }
            mandatory true;
            description
              "The community string to use when sending traps to this
               destination.";
          }
        }
      }
    }
    // list monitored-vrf

    container access-control {
      description
        "SNMPv3 access control configuration.";

      list user {
        max-elements 1;
        key "name";
        description
          "An SNMPv3 user.";

        leaf name {
          type string {
            pattern '[a-zA-Z0-9][-_.a-zA-Z0-9]*';
          }
          description
            "The name of the user (securityName).";
        }

        leaf auth-password {
          type string {
            length "8..max";
          }
          mandatory true;
          description
            "The authentication password.";
          nacm:default-deny-all;
        }

        leaf auth-method {
          type enumeration {
            enum md5 {
              description
                "MD5.";
            }
            enum sha {
              description
                "SHA.";
            }
          }
          default "sha";
          description
            "The authentication method.";
        }

        leaf priv-password {
          type string {
            length "8..max";
          }
          description
            "The privacy (encryption) password. If not specified, it is
             assumed to be the same as the authentication password.";
          nacm:default-deny-all;
        }

        leaf priv-protocol {
          type enumeration {
            enum aes {
              description
                "AES.";
            }
            enum des {
              description
                "DES.";
            }
          }
          default "aes";
          description
            "The encryption protocol.";
        }
      }
      // list user

      list group {
        key "name";
        description
          "An SNMPv3 group.";

        leaf name {
          type string;
          description
            "The name of the group.";
        }

        leaf-list user {
          type leafref {
            path
              "../../user/name";
          }
          min-elements 1;
          description
            "Name of a user to add to this group.";
        }

        leaf security-level {
          type enumeration {
            enum auth {
              description
                "Authentication is required.";
            }
            enum priv {
              description
                "Authentication and encryption are required.";
            }
          }
          mandatory true;
          description
            "The security level enforced on this group.";
        }

        leaf view {
          type leafref {
            path
              "../../../view/name";
          }
          description
            "Restricts access for that group to the subtree rooted at the
             given view name. If not specified, the group has access to the
             whole OID tree.";
        }

        leaf authorization {
          type auth-level;
          default "read-only";
          description
            "The authorization level of this group.";
        }
      }
      // list group
    }
    // container access-control

    container traps {
      description
        "Active monitoring and automatic notifications configuration.";

      list destination {
        max-elements 5;
        key "host";
        description
          "Notification receiver that should be sent SNMPv1 TRAPs, SNMPv2c
           TRAP2s, or SNMPv2 INFORM notifications.";
        ext:nc-cli-one-liner;

        leaf host {
          type ntos-inet:host;
          description
            "The address of the receiver.";
        }

        leaf port {
          type ntos-inet:port-number;
          default "162";
          description
            "The port number of the host where to send the traps.";
        }

        leaf protocol {
          type enumeration {
            enum udp {
              description
                "UDP.";
            }
            enum tcp {
              description
                "TCP.";
            }
            enum udp6 {
              description
                "UDPv6.";
            }
            enum tcp6 {
              description
                "TCPv6.";
            }
          }
          default "udp";
          description
            "The protocol used to connect to the destination host.";
        }

        leaf notification-type {
          type enumeration {
            enum TRAP {
              description
                "Send SNMPv1 TRAPs to the specified host.";
            }
            enum TRAP2 {
              description
                "Send SNMPv2c TRAP2s to the specified host.";
            }
            enum INFORM {
              description
                "Send SNMPv2 INFORM notifications to the specified host.";
            }
          }
          mandatory true;
          description
            "The type of notifications that is to be sent to the specified
             host.";
        }

        leaf community {
          type string;
          mandatory true;
          description
            "The community string to use when sending traps to this
             destination.";
        }
      }
      // list sink

      container authfail-check {
        presence "Makes authfail-check available.";
        description
          "Monitor authentication failures.";
        ext:nc-cli-one-liner;

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable authentication failures monitoring.";
        }
      }

      // container link-status-check {
      //   presence "Makes link-status-check available.";
      //   description
      //     "Monitor network interfaces being taken up or down, triggering
      //      a linkUp or linkDown notification as appropriate.";
      //   ext:nc-cli-one-liner;

      //   leaf frequency {
      //     type frequency;
      //     default "60s";
      //     description
      //       "Check for network interfaces being taken up or down every
      //        <frequency> period.";
      //   }

      //   leaf enabled {
      //     type boolean;
      //     default "true";
      //     description
      //       "Enable or disable link status monitoring.";
      //   }
      // }

      container process-check {
        presence "Makes process-check available.";
        description
          "Monitor the important processes of the system, triggering
           a notification when one of them is not alive.";
        ext:nc-cli-one-liner;

        leaf frequency {
          type frequency;
          default "2s";
          description
            "Check for network interfaces being taken up or down every
             <frequency> period.";
        }

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable process monitoring.";
        }
      }

      container disk-space-check {
        presence "Makes disk-space-check available.";
        description
          "Enables monitoring of all disks found on the system, using the
           specified (percentage) threshold.";
        ext:nc-cli-one-liner;

        leaf threshold {
          type uint8 {
            range "1..99";
          }
          mandatory true;
          description
            "The minimum free disk space in percentage of the total space.";
        }

        leaf frequency {
          type frequency;
          default "5m";
          description
            "Check for free disk space every <frequency> period.";
        }

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable disk space monitoring.";
        }
      }

      container load-check {
        presence "Makes load-check available.";
        description
          "Enables monitoring of the load average and trigger notifications
           if it goes above the specified thresholds.";
        ext:nc-cli-one-liner;

        leaf threshold {
          type uint16;
          mandatory true;
          description
            "The maximum system load average.";
        }

        leaf enabled {
          type boolean;
          default "true";
          description
            "Enable or disable system load monitoring.";
        }
      }
    }
    // container traps
  }

  grouping tools-params {
    description
      "SNMP tools params.";

    leaf version {
      type enumeration {
        enum 1 {
          description
            "SNMPv1.";
        }
        enum 2c {
          description
            "SNMPv2c.";
        }
        enum 3 {
          description
            "SNMPv3.";
        }
      }
      default "2c";
      description
        "The version of snmp.";
    }
    leaf community {
      type string {
          pattern '[^` ~!#%^&*+\\\|{};":,/<>?]*';
      }
      mandatory true;
      description
        "An SNMPv1 or SNMPv2c community.";
    }
    leaf host {
      type ntos-inet:host;
      mandatory true;
      description
        "Restrict send to requests from the specified address.";
    }
    leaf oid {
      type string {
        pattern '[\.a-zA-Z0-9]([-:\.a-zA-Z0-9])*[0-9]';
      }
      mandatory true;
      description
        "SNMP object identifier a numeric form.";
    }
  }

  grouping oid-type-val {
    description
        "SNMP object value and value type.";

    leaf type {
      type enumeration {
        enum i {
          description
            "INTEGER.";
        }
        enum u {
          description
            "UNSIGNED.";
        }
        enum s {
          description
            "STRING.";
        }
        enum x {
          description
            "HEX STRING.";
        }
        enum d {
          description
            "DECIMAL STRING.";
        }
        enum n {
          description
            "NULLOBJ.";
        }
        enum o {
          description
            "OBJID.";
        }
        enum t {
          description
            "TIMETICKS.";
        }
        enum a {
          description
            "IPADDRESS.";
        }
        enum b {
          description
            "BITS.";
        }
      }
      mandatory true;
      description
        "SNMP object value type.";
    }
    leaf value {
      type string {
        pattern '[-_:/\\\.a-zA-Z0-9]*';
      }
      description
        "SNMP object value.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "SNMP configuration.";

    container snmp {
      presence "Makes SNMP available.";
      description
        "SNMP configuration.";
      ext:feature "product";
      uses snmp;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "SNMP operational state data.";

    container snmp {
      description
        "SNMP operational state data.";
      ext:feature "product";
      uses snmp;
    }
  }

  notification debug-set {
    leaf level {
        type string;
    }
  }

  rpc snmptools {
    description
      "exec netsnmp tool.";
    input {
      //uses tools-params;
      choice toolname {
        //status obsolete;
        description
          "choice tool";

        case snmpget{
          container snmpget {
            description
              "snmpget -v [1|2c|3] -c community host OID.";
            uses tools-params;
          }
        }
        case snmpwalk{
          container snmpwalk {
            description
              "snmpwalk -v [1|2c|3] -c community host OID.";
            uses tools-params;
          }
        }
        case snmpset{
          container snmpset {
            description
              "snmpset -v [1|2c|3] -c community host OID type value.";
            uses tools-params;
            uses oid-type-val;
          }
        }
        case snmpbulkwalk{
          container snmpbulkwalk {
            description
              "snmpbulkwalk -v [1|2c|3] -c community host OID.";
            uses tools-params;
          }
        }
        case snmpbulkget{
          container snmpbulkget {
            description
              "snmpbulkget -v [1|2c|3] -c community host OID.";
            uses tools-params;
          }
        }
        case snmptranslate{
          container snmptranslate {
            description
              "translate MIB OID names between numeric and textual forms.";
            leaf transopts {
              type enumeration {
                enum Td {
                  description
                    "Print full details of the specified OID.";
                }
                enum Tp {
                  description
                    "Print a graphical tree, rooted at the specified OID.";
                }
              }
              mandatory true;
              description
                "Translate type over the translation of the OID values.";
            }
            leaf oid {
              type string {
                pattern '(^[0-9])|(^[\.a-zA-Z0-9]([-:\.a-zA-Z0-9])*[0-9])';
              }
              mandatory true;
              description
                "SNMP object identifier a numeric form.";
            }
          }
        }
      }
    }

    output {
      leaf result {
        type string;
        description
          "The command output buffer.";
        ext:nc-cli-stdout;
      }
    }
    ext:nc-cli-cmd "snmptool";
  }

  // grouping snmp
}
