module ntos-replacement-messages {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:replacement-messages";
  prefix ntos-replacement-messages;
  
  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  
  organization 
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS replacement-messages module.";
    
  revision 2023-11-29 {
    description
      "Initial version.";
    reference
      "";
  }

  identity rm {
    base ntos-types:SERVICE_LOG_ID;
    description
      "replacement messages service.";
  }

  typedef rm-messages-type {
    type enumeration {
      enum original {
        description
          "Original messages.";
      }
      enum modified {
        description
          "Modified messages.";
      }
      enum usually {
        description
          "Usually messages.";
      }
    }
  }
  
  typedef rm-resources-type {
    type enumeration {
      enum original {
        description
          "Default resources.";
      }
      enum custom {
        description
          "Custom resources.";
      }
    }
  }
  
  grouping rm-image-resources {
    list image-configuration {
      key "resources-name";
      description
        "List of images.";
      leaf resources-name {
        type string;
		mandatory true;
        description
          "Record the name of image resources.";
      }
      leaf resources-type {
        type rm-resources-type;
		default custom;
        description
          "Determine whether image resources are default.";
      }
      //base64 string
      leaf resources-link {
        type string;
		mandatory true;
        description
          "Base64 transcoding information of image resources.";
      }
      leaf resources-enabled {
        type boolean;
		default false;
        description
          "Enable source";
      }
    }
  }
  
  grouping rm-information-edit {
    description
      "Replacement messages of editing page.";
    leaf alarm-title {
      type string;
      description
        "Replace the title field in the HTML of the messages.";
    }
    leaf alarm-description {
      type string {
        length "1..512";
      }
      mandatory true;
      description
        "Replace the description field in the HTML of the messages.";
    }
  }
  
  grouping rm-management {
    description
      "Replace the main configuration information of the information function.";
    leaf rm-enable-status {
      type boolean;
      description
        "The replacement messages function is available as a whole.";
    }
    leaf cache-enable-status {
      type boolean;
      default true;
      description
        "The cache function is available as a whole.";
    }
    leaf block-alert-enable-status {
      type boolean;
      description
        "The block alert function for black-list is available as a whole.";
    }
    list message {
      key "message-id";
      description
        "Replace messages with names of different replacement types.";
      leaf message-id {
        type uint32 {
		  range "1..16";
		}
        description
          "The id of a replacement message.";
      }
      leaf message-type {
        type rm-messages-type;
        default usually;
        description
          "Is the replacement information revised.";
      }
      container information {
        uses rm-information-edit;
        description
          "The editing information for a replacement message.";
      }
    }
    
    container image-config {
      description
        "The list of the replacement message images.";
      uses rm-image-resources;
    }
  }
  
  grouping vrf {
    leaf vrf {
      description
        "VRF name.";
        
      type string;
      default "main";
    }
  }
  
  grouping rm-base-info-type {
    list info {
      key "info-id";
      description
        "List of entry information.";
      leaf info-id {
        type uint32;
      }
      leaf info-name {
        type string;
      }
      leaf info-description {
        type string;
      }
    }
  }
  
  augment "/ntos:config/ntos:vrf" {
    description 
      "The configuration of replacement-messages.";
    container replacement-messages {
      container management {
        uses rm-management;
      }
    }
  }
  
  rpc rm-status-get {
    description
      "Get rm status.";
    input {
      uses vrf;
    }
    
    output {
      container replacement-messages {
        leaf rm-status {
          type string;
          description
            "Rm status.";
        }
        leaf cache-status {
          type string;
          description
            "Cache status.";
        }
        leaf block-alert-status {
          type string;
          description
            "block alert for black-list status.";
        }
      }
    }
    ntos-ext:nc-cli-show "rm status";
  }
  
  rpc rm-data-convert {
    description
      "Complete the conversion from replacing information ID to name.";
    input {
      uses vrf;
    }
    
    output {
      container replacement-messages {
        uses rm-base-info-type;
      }
    }
    ntos-ext:nc-cli-show "rm get-srcs";
  }
  
  rpc rm-data-export {
    description
      "Export selected replacement-messages.";
    input {
      uses vrf;
      list message {
        key "message-id";
        leaf message-id {
          type uint32;
          description
            "Indicates the serial number of the selected exported item.";
        }
      }
    }
    
    output {
      container replacement-messages {
        leaf time-stamp{
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "rm export";
  }
  
  rpc rm-data-reset-all {
    description
      "Restore all replacement information entries to their factory state.";
    input {
      uses vrf;
    }
    
    output {
      container replacement-messages {
        leaf status-code {
          type uint32;
          description
            "Status code.";
        }
      }
    }
    ntos-ext:nc-cli-cmd "rm reset-all";
  }
  
  rpc rm-data-reset {
    description
      "Restore single replacement information entries to their factory state.";
    input {
      uses vrf;
      leaf msg-id {
        type uint32;
	  }
    }
    
    output {
      container replacement-messages {
        leaf status-code {
          type uint32;
          description
            "Status code.";
        }
      }
    }
    ntos-ext:nc-cli-cmd "rm reset";
  }
  
  rpc rm-management-show {
    description
      "Get rm config.";
    
    input {
      uses vrf;
      leaf start {
        type uint32;
        description
          "The start offset.";
      }
      leaf end {
        type uint32;
        description
          "The end offset.";
      }
      leaf rm-type {
        type rm-messages-type;
        description
          "Show predefined messages or modified messages.";
      }
    }
    
    output {
      container replacement-messages {
        leaf message-num {
          type uint32;
          description
            "The total number of messages.";
        }
     
        leaf rm-enable-status {
        type boolean;
        description
          "The replacement messages function is available as a whole.";
        }
        leaf cache-enable-status {
          type boolean;
          default true;
          description
            "The cache function is available as a whole.";
        }
        leaf block-alert-enable-status {
          type boolean;
          description
            "The block alert function for black-list is available as a whole.";
        }

        list message {
          key "message-id";
          description
            "Replace messages with names of different replacement types.";
            leaf message-id {
              type uint32;
              description
                "The id of a replacement message.";
            }
            leaf message-type {
              type rm-messages-type;
              description
                "Is the replacement information revised.";
            }
            leaf alarm-title {
              type string;
              description
                "Replace the title field in the HTML of the messages.";
            }
            leaf alarm-description {
              type string {
                length "1..512";
              }
              description
                "Replace the description field in the HTML of the messages.";
            }
        }
      }
    }
    ntos-ext:nc-cli-show "rm messages";
  }
  
  rpc rm-image-show {
    description
      "Get rm img config.";
    
    input {
      uses vrf;
      leaf is-default {
        type rm-resources-type;
        description
          "Show default or custom images.";
      }
    }
    
    output {
      container replacement-messages {
        leaf image-num {
          type uint32;
          description
            "The total number of images.";
        }
     
        uses rm-image-resources;
      }
    }
    ntos-ext:nc-cli-show "rm images";
  }
  
  rpc rm-data-export-result {
    description
      "Get rm export data result.";
      
    input {
      uses vrf;
      leaf time-stamp {
        type uint32;
      }
    }
    
    output {
      container replacement-messages {
        leaf status {
          type uint32;
          description
            "The status code.";
        }
        leaf export-data-path {
          type string;
          description
            "The path to the exported compressed file.";
        }
      }
    }
	ntos-ext:nc-cli-cmd "rm export-status";
  }
}