package dpassword

import (
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"os/user"
)

const ModelName = "用户管理"

type Response struct {
	Id       uint   `json:"id"`
	Password string `json:"password"`
}

func (p *Response) ModelName() string {
	return ModelName
}

func Model() *user.User {
	return &user.User{}
}

func (p *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	list := map[string]interface{}{"items": "", "total": 0, "limit": pageSize}
	return list, nil
}

func (p *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	list := map[string]interface{}{"items": "", "total": 0, "limit": pageSize}
	return list, nil
}

func (p *Response) FindByUserName(username string) error {
	return nil
}

func (p *Response) Create(object map[string]interface{}) error {
	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (p *Response) Update(id uint, object map[string]interface{}) error {
	err := p.Find(id)
	if err != nil {
		return err
	}
	// if u.Username == "username" {
	// 	return errors.New("不能编辑管理员")
	// }
	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).UpdateColumns(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (p *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(p).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (p *Response) FindEx(col, value string) error {
	return nil
}

func (p *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}
