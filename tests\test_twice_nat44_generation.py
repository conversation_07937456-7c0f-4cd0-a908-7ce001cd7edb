#!/usr/bin/env python3
"""
twice-nat44规则生成自动化测试套件

本测试套件验证twice-nat44功能的完整工作流程，包括：
1. IP池配置的正确解析
2. 策略评估逻辑的准确性
3. twice-nat44规则的正确生成
4. 不同IP池格式的处理效果

作者: FortiGate转换系统
版本: 1.0
日期: 2025-08-08
"""

import sys
import os
import unittest
import tempfile
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine.business.models.twice_nat44_models import TwiceNat44Rule, _validate_fortigate_pools, _is_valid_ipv4

class TestTwiceNat44Generation(unittest.TestCase):
    """twice-nat44规则生成测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.test_data_dir = Path(__file__).parent.parent / "test_data"
        self.test_config_file = self.test_data_dir / "fortigate_ippool_test.conf"
        
        # 确保测试数据目录存在
        self.test_data_dir.mkdir(exist_ok=True)
        
        # 测试上下文
        self.context = {
            "ntos_version": "R11",
            "twice_nat44_threshold": 65,
            "high_confidence_threshold": 80,
            "low_confidence_threshold": 50,
            "available_pools": []
        }
        
        # 模拟VIP配置
        self.vip_configs = {
            "WEB_SERVER_VIP": {
                "name": "WEB_SERVER_VIP",
                "extip": "**************",
                "mappedip": "**************",
                "extport": "80-443",
                "mappedport": "80-443"
            },
            "MAIL_SERVER_VIP": {
                "name": "MAIL_SERVER_VIP",
                "extip": "************",
                "mappedip": "**************",
                "extport": "25",
                "mappedport": "25"
            },
            "FTP_SERVER_VIP": {
                "name": "FTP_SERVER_VIP",
                "extip": "*************",
                "mappedip": "**************",
                "extport": "21",
                "mappedport": "21"
            }
        }

    def test_ip_address_pool_format_validation(self):
        """测试IP地址格式池名称验证"""
        print("\n🧪 测试IP地址格式池名称验证...")
        
        # 测试有效的IP地址格式
        valid_ip_pools = ["**************", "************", "*************"]
        for pool in valid_ip_pools:
            self.assertTrue(_is_valid_ipv4(pool), f"IP地址 {pool} 应该被识别为有效")
        
        # 测试无效的IP地址格式
        invalid_ip_pools = ["256.1.1.1", "192.168.1", "not_an_ip", "EXTERNAL_POOL"]
        for pool in invalid_ip_pools:
            if pool != "EXTERNAL_POOL":  # EXTERNAL_POOL是有效的传统池名称
                self.assertFalse(_is_valid_ipv4(pool), f"字符串 {pool} 不应该被识别为IP地址")
        
        print("  ✅ IP地址格式验证测试通过")

    def test_fortigate_pool_validation(self):
        """测试FortiGate IP池验证功能"""
        print("\n🧪 测试FortiGate IP池验证功能...")
        
        # 测试纯IP地址格式
        ip_only_pools = ["**************", "************"]
        result = _validate_fortigate_pools(ip_only_pools)
        self.assertEqual(len(result), 2, "所有IP格式池名称都应该被验证为有效")
        
        # 测试混合格式
        mixed_pools = ["**************", "EXTERNAL_POOL_1", "invalid_name", "************"]
        available_pools = ["EXTERNAL_POOL_1", "INTERNAL_POOL"]
        result = _validate_fortigate_pools(mixed_pools, available_pools)
        
        expected_valid = ["**************", "EXTERNAL_POOL_1", "************"]
        self.assertEqual(len(result), 3, f"应该有3个有效池，实际: {result}")
        for pool in expected_valid:
            self.assertIn(pool, result, f"池 {pool} 应该在有效池列表中")
        
        print("  ✅ FortiGate IP池验证功能测试通过")

    def test_policy_evaluation_with_ip_pools(self):
        """测试使用IP池的策略评估"""
        print("\n🧪 测试使用IP池的策略评估...")
        
        test_policies = [
            {
                "name": "IP_ADDRESS_POOL_POLICY",
                "srcintf": ["wan1"],
                "dstintf": ["dmz"],
                "srcaddr": ["all"],
                "dstaddr": ["WEB_SERVER_VIP"],
                "service": ["HTTP", "HTTPS"],
                "nat": "enable",
                "ippool": "enable",
                "poolname": ["**************"]
            },
            {
                "name": "TRADITIONAL_POOL_POLICY",
                "srcintf": ["lan"],
                "dstintf": ["wan1"],
                "srcaddr": ["LAN_SUBNET"],
                "dstaddr": ["WEB_SERVER_VIP"],  # 使用VIP而不是all
                "service": ["HTTP"],
                "nat": "enable",
                "ippool": "enable",
                "poolname": ["EXTERNAL_POOL_1"]
            },
            {
                "name": "MIXED_POOL_POLICY",
                "srcintf": ["dmz"],
                "dstintf": ["wan1"],
                "srcaddr": ["DMZ_SUBNET"],
                "dstaddr": ["MAIL_SERVER_VIP"],
                "service": ["SMTP"],
                "nat": "enable",
                "ippool": "enable",
                "poolname": ["************", "EXTERNAL_POOL_1"]
            }
        ]
        
        results = []
        for policy in test_policies:
            recommendation = TwiceNat44Rule.evaluate_twice_nat44_suitability(
                policy, self.vip_configs, self.context
            )
            
            results.append({
                "policy_name": policy["name"],
                "score": recommendation.total_score,
                "recommended": recommendation.should_use,
                "confidence": recommendation.confidence_score
            })
            
            # 验证IP池策略能够获得合理评分
            self.assertGreater(recommendation.total_score, 0, 
                             f"策略 {policy['name']} 应该获得大于0的评分")
            
            print(f"    策略 {policy['name']}: 评分={recommendation.total_score}, "
                  f"推荐={recommendation.should_use}, 置信度={recommendation.confidence_score:.2f}")
        
        # 验证至少有一个策略被推荐使用twice-nat44
        recommended_count = sum(1 for r in results if r["recommended"])
        self.assertGreater(recommended_count, 0, "至少应该有一个策略被推荐使用twice-nat44")
        
        print(f"  ✅ 策略评估测试通过，{recommended_count}/{len(results)}个策略被推荐")

    def test_complex_service_penalty(self):
        """测试复杂服务的评分惩罚"""
        print("\n🧪 测试复杂服务的评分惩罚...")
        
        simple_policy = {
            "name": "SIMPLE_SERVICES",
            "srcintf": ["wan1"],
            "dstintf": ["dmz"],
            "srcaddr": ["all"],
            "dstaddr": ["WEB_SERVER_VIP"],
            "service": ["HTTP", "HTTPS"],
            "nat": "enable",
            "ippool": "enable",
            "poolname": ["**************"]
        }
        
        complex_policy = {
            "name": "COMPLEX_SERVICES",
            "srcintf": ["wan1"],
            "dstintf": ["dmz"],
            "srcaddr": ["all"],
            "dstaddr": ["FTP_SERVER_VIP"],
            "service": ["FTP", "SSH", "TELNET", "HTTP", "HTTPS", "SMTP"],
            "nat": "enable",
            "ippool": "enable",
            "poolname": ["*************"]
        }
        
        simple_result = TwiceNat44Rule.evaluate_twice_nat44_suitability(
            simple_policy, self.vip_configs, self.context
        )
        
        complex_result = TwiceNat44Rule.evaluate_twice_nat44_suitability(
            complex_policy, self.vip_configs, self.context
        )
        
        print(f"    简单服务策略评分: {simple_result.total_score}")
        print(f"    复杂服务策略评分: {complex_result.total_score}")
        
        # 复杂服务策略的评分应该低于简单服务策略
        self.assertGreater(simple_result.total_score, complex_result.total_score,
                          "简单服务策略的评分应该高于复杂服务策略")
        
        print("  ✅ 复杂服务评分惩罚测试通过")

    def test_threshold_effectiveness(self):
        """测试阈值有效性"""
        print("\n🧪 测试阈值有效性...")
        
        test_policy = {
            "name": "THRESHOLD_TEST",
            "srcintf": ["dmz"],
            "dstintf": ["wan1"],
            "srcaddr": ["DMZ_SUBNET"],
            "dstaddr": ["MAIL_SERVER_VIP", "FTP_SERVER_VIP"],  # 多个VIP降低分数
            "service": ["SMTP", "FTP", "SSH"],  # 中等复杂度服务
            "nat": "enable",
            "ippool": "enable",
            "poolname": ["************"]
        }
        
        # 测试不同阈值
        thresholds = [80, 75, 70, 65, 60]
        results = []
        
        for threshold in thresholds:
            context = self.context.copy()
            context["twice_nat44_threshold"] = threshold
            
            recommendation = TwiceNat44Rule.evaluate_twice_nat44_suitability(
                test_policy, self.vip_configs, context
            )
            
            results.append({
                "threshold": threshold,
                "score": recommendation.total_score,
                "recommended": recommendation.should_use
            })
            
            print(f"    阈值 {threshold}: 评分={recommendation.total_score}, "
                  f"推荐={recommendation.should_use}")
        
        # 验证阈值降低确实扩大了适用范围
        old_threshold_result = next(r for r in results if r["threshold"] == 80)
        new_threshold_result = next(r for r in results if r["threshold"] == 65)
        
        if old_threshold_result["score"] >= 65 and old_threshold_result["score"] < 80:
            self.assertFalse(old_threshold_result["recommended"], "旧阈值应该不推荐")
            self.assertTrue(new_threshold_result["recommended"], "新阈值应该推荐")
            print("  ✅ 阈值降低成功扩大了适用范围")
        else:
            print("  ℹ️  当前策略评分不在65-80分区间，无法验证阈值效果")
        
        print("  ✅ 阈值有效性测试完成")

    def test_vip_configuration_completeness(self):
        """测试VIP配置完整性评分"""
        print("\n🧪 测试VIP配置完整性评分...")
        
        # 完整VIP配置的策略
        complete_vip_policy = {
            "name": "COMPLETE_VIP",
            "srcintf": ["wan1"],
            "dstintf": ["dmz"],
            "srcaddr": ["all"],
            "dstaddr": ["WEB_SERVER_VIP"],  # 完整的VIP配置
            "service": ["HTTP"],
            "nat": "enable",
            "ippool": "enable",
            "poolname": ["**************"]
        }
        
        # 不完整VIP配置
        incomplete_vip_configs = {
            "INCOMPLETE_VIP": {
                "name": "INCOMPLETE_VIP",
                "extip": "**************"
                # 缺少mappedip
            }
        }
        
        incomplete_vip_policy = {
            "name": "INCOMPLETE_VIP",
            "srcintf": ["wan1"],
            "dstintf": ["dmz"],
            "srcaddr": ["all"],
            "dstaddr": ["INCOMPLETE_VIP"],
            "service": ["HTTP"],
            "nat": "enable",
            "ippool": "enable",
            "poolname": ["**************"]
        }
        
        complete_result = TwiceNat44Rule.evaluate_twice_nat44_suitability(
            complete_vip_policy, self.vip_configs, self.context
        )
        
        incomplete_result = TwiceNat44Rule.evaluate_twice_nat44_suitability(
            incomplete_vip_policy, incomplete_vip_configs, self.context
        )
        
        print(f"    完整VIP配置评分: {complete_result.total_score}")
        print(f"    不完整VIP配置评分: {incomplete_result.total_score}")
        
        # 完整VIP配置应该获得更高评分
        self.assertGreater(complete_result.total_score, incomplete_result.total_score,
                          "完整VIP配置应该获得更高评分")
        
        print("  ✅ VIP配置完整性评分测试通过")

def run_twice_nat44_generation_tests():
    """运行twice-nat44规则生成测试"""
    print("🚀 开始twice-nat44规则生成自动化测试\n")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestTwiceNat44Generation)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print(f"\n📊 测试结果摘要:")
    print(f"  总测试数: {result.testsRun}")
    print(f"  成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"  失败: {len(result.failures)}")
    print(f"  错误: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_twice_nat44_generation_tests()
    sys.exit(0 if success else 1)
