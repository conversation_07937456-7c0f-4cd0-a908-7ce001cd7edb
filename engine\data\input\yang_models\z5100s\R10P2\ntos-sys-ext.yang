module ntos-sys-ext {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:sys-ext";
  prefix ntos-sys-ext;
  
  import ntos {
    prefix ntos;
  }
  
  import ntos-types {
    prefix ntos-types;
  }
  
  import ntos-inet-types {
    prefix ntos-inet;
  }

  import ntos-if-types {
    prefix ntos-if;
  }
  
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS system management extend module.";

  revision 2022-09-03 {
    description
      "Initial version.";
    reference "";
  }

  grouping sys-base-info {
    description
      "The grouping for system base information.";

	leaf sys-up-time {
	    type ntos-types:timeticks64;
		config false;
	    description
           "The time (in hundredths of a second) since the
            network management portion of the system was last re-initialized.";
	}

	leaf sys-serialno {
	    type string {
		   length "0..255";
		}
		config false;
	    description 
           "The string of the serial number	of the device.";
	}
	
	leaf sys-hw-version {
 	    type string {
		   length "0..255";
		}
        config false;
	    description 
           "The hardware system version of the device.";        		
	}
	
	leaf sys-sw-version {
 	    type string {
		   length "0..255";
		}
        config false;
	    description 
           "The software system version of the device.";	
	}
	
	leaf sys-sw-number {
 	    type string {
		   length "0..255";
		}
        config false;
	    description 
           "The software main system version of the device.";
	}

    leaf mac {
      type ntos-if:mac-address;
      config false;
      description
        "The hw ethaddr.";
    }
  }

  augment "/ntos:state" {
      description
         "Global system extend state.";

      container system-ext {
         description
            "Global system extend operational state data.";

		 uses sys-base-info;
	  }
  }
}
