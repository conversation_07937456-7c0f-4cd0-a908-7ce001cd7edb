debug: false
loglevel: error # 日志级别 info debug disable error fatal warn 等等
host: ************** # host 地址
port: 9005
nginx:
  host: localhost
  port: 9527
  path: /api
readtimeout: 60
writetimeout: 60
maxsize: 10240 # 文件上传限制
pprof: true #  http://localhost:8086/debug/pprof/heap ...
casbin:
  prefix:
  # path: /data/goprojects/irisAdminApi/rbac_model.conf # go run main.go 运行必须指定路径
  path: /media/sf_code/config-converter/apis/rbac_model.conf #/projects/iris-admin-api/rbac_model.conf
cache:
  driver: redis # 缓存驱动 local redis
limit:
  disable: false
  limit: 20 # 每秒允许请求 1 次
  burst: 100 # 最高允许并发
admin: # 管理员账号信息，用于数据填充
  username: admin
  rolename: 超级管理员
  name: 超级管理员
  password: password
db:
  adapter: mysql # mysql postgres sqlite3 
  conn: "root:abc.123@tcp(**************:3306)/test?parseTime=True&loc=Local"
  # conn: "root:secloud@mysql@tcp(************:3306)/configtrans?parseTime=True&loc=Local" # postgres://root:password@localhost/iris?sslmode=disable
  prefix:
  encrypt: false
redis:
  host: ***********
  # host: ************
  port: 6379
  password: password
  # password: secloud@Redis
qiniu:
  enable: false
  host:
  accesskey:
  secretkey:
  bucket:
filestorage:
  # 临时以及最后存在路径win/linux均以/作为分隔符，以/为结尾
  temp: /tmp/temp/ # win/linux均以/作为分隔符，以/为结尾
  upload: /tmp/upload/

rsa:
  publickey: /media/sf_code/iris-admin-api/id_rsa.pub #/home/<USER>/.ssh/id_rsa.pub
  privatekey: /media/sf_code/iris-admin-api/id_rsa  #/home/<USER>/.ssh/id_rsa

# 多厂商配置转换工具设置
configtrans:
  upload: /tmp/configtrans_uploads/ # 通用上传文件存储路径
  tempdir: /tmp/configtrans_temp/ # 通用临时目录路径
  pythonpath: python3 # Python解释器路径，默认从环境变量获取
  enginepath: /media/sf_code/config-converter/engine # Python转换引擎路径
  mappingbasedir: data/mappings/ # 接口映射文件基础目录路径
  
  # 支持的厂商配置
  vendors:
    fortigate: # 飞塔配置转换
      mappingfile: interface_mapping.json # 接口映射文件名
      modes:
        verify: "--mode verify --cli {file}" # 验证模式命令行参数
        extract: "--mode extract --cli {file} --output-json {json}" # 提取模式命令行参数
        convert: "--mode convert --cli {file} --mapping {mapping} --model {model} --version {version} --output {output} --encrypt-output {encrypt}" # 转换模式命令行参数
