package dconfigtrans

import (
	"fmt"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"strings"
	"time"

	"github.com/gomodule/redigo/redis"
)

const (
	// 默认任务限制
	DefaultTaskLimit = 10
	// 任务计数器的Redis键前缀
	TaskCounterKeyPrefix = "config_trans:tasks:processing:"
	// 任务锁的过期时间 (秒)
	TaskCounterExpireSeconds = 7200 // 2小时
)

// TaskCounter 任务计数器结构体
type TaskCounter struct {
	redisClient *cache.RedisCluster
}

// NewTaskCounter 创建一个任务计数器实例
func NewTaskCounter() *TaskCounter {
	// 最多尝试3次获取Redis客户端
	var redisClient *cache.RedisCluster
	var healthy bool
	var err error

	for i := 0; i < 3; i++ {
		redisClient = cache.GetRedisClusterClient()

		// 检查Redis客户端是否可用
		if redisClient == nil {
			logging.ErrorLogger.Error("无法获取Redis客户端，尝试重新连接...")
			// 尝试重新初始化Redis连接
			if cache.TryReconnect() {
				logging.InfoLogger.Info("Redis重连成功")
				redisClient = cache.GetRedisClusterClient()
			}
		}

		if redisClient != nil {
			// 简单测试Redis连接是否可用
			healthy, err = cache.GetRedisHealth()
			if healthy && err == nil {
				logging.InfoLogger.Info("成功获取Redis连接")
				break
			} else {
				logging.ErrorLogger.Errorf("Redis连接不健康: %v，尝试重新连接...", err)
				// 尝试重新初始化Redis连接
				if cache.TryReconnect() {
					logging.InfoLogger.Info("Redis重连成功")
					redisClient = cache.GetRedisClusterClient()
					continue
				}
			}
		}

		if i < 2 {
			// 如果不是最后一次尝试，等待一段时间后重试
			time.Sleep(500 * time.Millisecond)
		}
	}

	if redisClient == nil {
		logging.ErrorLogger.Error("无法获取Redis客户端，任务计数器将无法正常工作")
	}

	return &TaskCounter{
		redisClient: redisClient,
	}
}

// TryAcquireTaskSlot 尝试获取任务槽位，增加重试机制
// 返回值:
// - 是否成功获取任务槽位
// - 当前正在处理的任务数量
// - 错误信息
func (tc *TaskCounter) TryAcquireTaskSlot(vendor string, limit int) (bool, int, error) {
	if limit <= 0 {
		limit = DefaultTaskLimit
	}

	key := tc.getTaskCounterKey(vendor)

	// 添加重试机制
	const maxRetries = 3
	const retryDelay = 100 * time.Millisecond

	var err error
	var count int
	var acquired bool

	for retry := 0; retry < maxRetries; retry++ {
		if retry > 0 {
			logging.DebugLogger.Infof("尝试重试获取任务槽位 (第%d次): %s", retry+1, vendor)
			time.Sleep(retryDelay * time.Duration(retry)) // 增加退避时间
		}

		acquired, count, err = tc.tryAcquireTaskSlotOnce(key, limit)
		if err == nil {
			// 成功获取或确认无法获取（槽位已满）
			return acquired, count, nil
		}

		// 判断是否需要继续重试
		if err == redis.ErrNil || strings.Contains(err.Error(), "nil returned") {
			logging.DebugLogger.Infof("Redis返回nil错误，将进行重试: %v", err)
			continue // 重试
		}

		// 其他错误则直接返回
		return false, 0, err
	}

	// 所有重试都失败
	logging.ErrorLogger.Errorf("经过%d次重试后，仍然无法获取任务槽位: %v", maxRetries, err)
	return false, 0, err
}

// tryAcquireTaskSlotOnce 尝试一次获取任务槽位
func (tc *TaskCounter) tryAcquireTaskSlotOnce(key string, limit int) (bool, int, error) {
	// 检查TaskCounter是否为空
	if tc == nil {
		logging.ErrorLogger.Error("TaskCounter对象为空")
		return false, 0, fmt.Errorf("TaskCounter对象为空")
	}

	// 检查Redis客户端是否为空
	if tc.redisClient == nil {
		logging.ErrorLogger.Error("Redis客户端为空，尝试重新连接")
		// 尝试重新连接Redis
		if cache.TryReconnect() {
			tc.redisClient = cache.GetRedisClusterClient()
			logging.InfoLogger.Info("Redis重连成功")
			if tc.redisClient == nil {
				return false, 0, fmt.Errorf("Redis客户端仍为空，无法获取任务槽位")
			}
		} else {
			return false, 0, fmt.Errorf("Redis客户端未初始化且重连失败")
		}
	}

	// 获取Redis连接
	conn := tc.redisClient.Get()
	if conn == nil {
		logging.ErrorLogger.Error("无法获取Redis连接，尝试重新连接")
		// 尝试重新连接Redis
		if cache.TryReconnect() {
			tc.redisClient = cache.GetRedisClusterClient()
			logging.InfoLogger.Info("Redis重连成功，尝试重新获取连接")
			conn = tc.redisClient.Get()
			if conn == nil {
				return false, 0, fmt.Errorf("无法获取Redis连接")
			}
		} else {
			return false, 0, fmt.Errorf("无法获取Redis连接且重连失败")
		}
	}
	defer conn.Close() // 注意这里只关闭单个连接，不是整个客户端

	// 使用Lua脚本保证原子性，替代WATCH和MULTI/EXEC事务
	luaScript := `
	local key = KEYS[1]
	local limit = tonumber(ARGV[1])
	local expire_time = tonumber(ARGV[2])
	local current = redis.call('GET', key)
	
	-- 如果键不存在，初始化为0
	if current == false then
		current = 0
	else
		current = tonumber(current)
	end
	
	-- 如果当前计数已达到限制，则放弃获取槽位
	if current >= limit then
		return current
	end
	
	-- 增加计数器并设置过期时间
	redis.call('INCR', key)
	redis.call('EXPIRE', key, expire_time)
	return current
	`

	// 执行Lua脚本，传递参数
	result, err := redis.Int(conn.Do("EVAL", luaScript, 1, key, limit, TaskCounterExpireSeconds))
	if err != nil {
		logging.ErrorLogger.Errorf("执行Redis脚本失败: %v", err)
		// 检查是否是连接问题或"closed pool"错误
		if strings.Contains(err.Error(), "get on closed pool") ||
			strings.Contains(err.Error(), "connection") {
			logging.ErrorLogger.Error("检测到Redis连接已关闭，尝试重新连接")
			// 尝试重新连接Redis
			if cache.TryReconnect() {
				logging.InfoLogger.Info("Redis重连成功")
				// 注意：不要在此处重试，让调用者处理重试逻辑
				return false, 0, fmt.Errorf("Redis连接已重新建立，请重试操作")
			}
		}
		return false, 0, err
	}

	// 如果返回值等于或超过limit，说明获取失败
	if result >= limit {
		return false, result, nil
	}

	// result表示操作前的计数，当前计数应该是result+1
	logging.DebugLogger.Infof("成功获取任务槽位，厂商: %s, 当前计数: %d", key, result+1)
	return true, result, nil
}

// ReleaseTaskSlot 释放任务槽位，增加重试机制
func (tc *TaskCounter) ReleaseTaskSlot(vendor string) error {
	// 检查Redis客户端是否为空
	if tc == nil || tc.redisClient == nil {
		logging.ErrorLogger.Error("TaskCounter或Redis客户端为空")
		return fmt.Errorf("Redis客户端未初始化")
	}

	key := tc.getTaskCounterKey(vendor)

	// 添加重试机制
	const maxRetries = 3
	const retryDelay = 100 * time.Millisecond

	var err error
	for retry := 0; retry < maxRetries; retry++ {
		if retry > 0 {
			logging.DebugLogger.Infof("尝试重试释放任务槽位 (第%d次): %s", retry+1, vendor)
			time.Sleep(retryDelay * time.Duration(retry)) // 增加退避时间
		}

		// 使用Lua脚本保证原子性
		luaScript := `
		local key = KEYS[1]
		local current = redis.call('GET', key)
		
		if current == false or tonumber(current) <= 0 then
			return 0
		end
		
		return redis.call('DECR', key)
		`

		conn := tc.redisClient.Get()
		if conn == nil {
			logging.ErrorLogger.Error("无法获取Redis连接")
			continue // 重试
		}

		_, err = conn.Do("EVAL", luaScript, 1, key)
		conn.Close()

		if err == nil {
			logging.DebugLogger.Infof("成功释放任务槽位，厂商: %s", vendor)
			return nil
		}

		if err != redis.ErrNil && !strings.Contains(err.Error(), "nil returned") {
			// 非nil错误直接返回
			break
		}
	}

	if err != nil {
		logging.ErrorLogger.Errorf("释放任务槽位失败: %v", err)
	}
	return err
}

// GetTaskCount 获取当前任务数量
func (tc *TaskCounter) GetTaskCount(vendor string) (int, error) {
	// 检查Redis客户端是否为空
	if tc == nil || tc.redisClient == nil {
		logging.ErrorLogger.Error("TaskCounter或Redis客户端为空")
		return 0, fmt.Errorf("Redis客户端未初始化")
	}

	key := tc.getTaskCounterKey(vendor)

	count, err := redis.Int(tc.redisClient.Do("GET", key))
	if err == redis.ErrNil {
		// 键不存在，表示没有处理中的任务
		return 0, nil
	} else if err != nil {
		logging.ErrorLogger.Errorf("Redis GET error: %v", err)
		return 0, err
	}

	return count, nil
}

// ResetTaskCount 重置任务计数器（仅用于管理员操作或系统重置）
func (tc *TaskCounter) ResetTaskCount(vendor string) error {
	// 检查Redis客户端是否为空
	if tc == nil || tc.redisClient == nil {
		logging.ErrorLogger.Error("TaskCounter或Redis客户端为空")
		return fmt.Errorf("Redis客户端未初始化")
	}

	key := tc.getTaskCounterKey(vendor)
	_, err := tc.redisClient.Del(key)
	if err != nil {
		logging.ErrorLogger.Errorf("Reset task counter error: %v", err)
		return err
	}

	logging.DebugLogger.Infof("Task counter for vendor %s has been reset", vendor)
	return nil
}

// RefreshTaskExpiration 刷新任务计数器的过期时间
func (tc *TaskCounter) RefreshTaskExpiration(vendor string) error {
	// 检查Redis客户端是否为空
	if tc == nil || tc.redisClient == nil {
		logging.ErrorLogger.Error("TaskCounter或Redis客户端为空")
		return fmt.Errorf("Redis客户端未初始化")
	}

	key := tc.getTaskCounterKey(vendor)

	// 检查键是否存在
	exists := tc.redisClient.Exists(key)
	if !exists {
		return nil
	}

	// 刷新过期时间
	_, err := tc.redisClient.Expire(key, TaskCounterExpireSeconds)
	if err != nil {
		logging.ErrorLogger.Errorf("Refresh task expiration error: %v", err)
		return err
	}

	return nil
}

// SyncTaskCount 同步任务计数器与实际状态（定期执行）
// 用于解决计数器与实际任务数量不一致的问题
func (tc *TaskCounter) SyncTaskCount(vendor string) error {
	// 这个函数可以定期执行，根据数据库中实际的处理中任务数量更新Redis计数器
	// 实现略复杂，取决于具体业务需求
	return nil
}

// getTaskCounterKey 获取任务计数器的Redis键
func (tc *TaskCounter) getTaskCounterKey(vendor string) string {
	return fmt.Sprintf("%s%s", TaskCounterKeyPrefix, vendor)
}

// InitTaskCounter 初始化任务计数器
// 在系统启动时调用，确保Redis计数器与数据库中实际的任务状态同步
func InitTaskCounter() {
	logging.InfoLogger.Info("初始化任务计数器...")

	// 获取计数器实例
	tc := NewTaskCounter()

	// 检查Redis客户端是否为空
	if tc == nil || tc.redisClient == nil {
		logging.ErrorLogger.Error("TaskCounter或Redis客户端为空，无法初始化任务计数器")
		return
	}

	// 检查Redis连接是否正常
	conn := tc.redisClient.Get()
	if conn == nil {
		logging.ErrorLogger.Error("无法获取Redis连接，任务计数器初始化失败")
		return
	}

	_, err := conn.Do("PING")
	conn.Close()

	if err != nil {
		logging.ErrorLogger.Errorf("Redis连接检查失败，任务计数器可能无法正常工作: %v", err)
		return
	}
	logging.InfoLogger.Info("Redis连接检查成功")

	// 先清理所有计数器（确保重置）
	vendors := []string{"fortigate"} // 可以扩展为从配置中获取所有支持的厂商
	for _, vendor := range vendors {
		// 重置任务计数器
		if err := tc.ResetTaskCount(vendor); err != nil {
			logging.ErrorLogger.Errorf("重置任务计数器失败 [%s]: %v", vendor, err)
			// 继续执行，不中断整个初始化过程
		} else {
			logging.InfoLogger.Infof("成功重置任务计数器 [%s]", vendor)
		}
	}

	// 为每个厂商从数据库中获取实际的处理中任务数量并更新计数器
	for _, vendor := range vendors {
		// 同步计数器状态
		if err := tc.SyncTaskCountFromDB(vendor); err != nil {
			logging.ErrorLogger.Errorf("同步任务计数器失败 [%s]: %v", vendor, err)
			// 继续执行，不中断整个初始化过程
		} else {
			logging.InfoLogger.Infof("成功同步任务计数器 [%s]", vendor)
		}
	}

	logging.InfoLogger.Info("任务计数器初始化完成")
}

// SyncTaskCountFromDB 从数据库同步任务计数器
func (tc *TaskCounter) SyncTaskCountFromDB(vendor string) error {
	// 检查Redis客户端是否为空
	if tc == nil || tc.redisClient == nil {
		logging.ErrorLogger.Error("TaskCounter或Redis客户端为空")
		return fmt.Errorf("Redis客户端未初始化")
	}

	// 创建一个数据模型实例
	job := &ConfigTrans{}

	// 获取当前处理中的任务数量
	list, err := job.All("", "", "", "", "0", "", vendor, "desc", "id", 1, 1000)
	if err != nil {
		logging.ErrorLogger.Errorf("获取任务列表失败: %v", err)
		return err
	}

	// 提取处理中的任务
	var processingCount int
	if items, ok := list["items"].([]*ListResponse); ok {
		processingCount = len(items)
	} else {
		logging.ErrorLogger.Errorf("解析任务列表失败，无法获取处理中的任务数: %+v", list)
	}

	key := tc.getTaskCounterKey(vendor)

	// 清除现有计数
	_, delErr := tc.redisClient.Del(key)
	if delErr != nil {
		logging.ErrorLogger.Errorf("清除现有计数失败: %v", delErr)
		// 不中断流程，继续尝试设置新值
	}

	// 只有在有处理中任务时才设置计数器
	if processingCount > 0 {
		// 最多重试3次
		var setErr error
		for retry := 0; retry < 3; retry++ {
			_, setErr = tc.redisClient.Do("SET", key, processingCount)
			if setErr == nil {
				break
			}
			time.Sleep(100 * time.Millisecond)
		}

		if setErr != nil {
			logging.ErrorLogger.Errorf("设置任务计数器失败: %v", setErr)
			return setErr
		}

		// 设置过期时间，避免任务异常时计数器永远不减少
		_, expireErr := tc.redisClient.Expire(key, TaskCounterExpireSeconds)
		if expireErr != nil {
			logging.ErrorLogger.Errorf("设置任务计数器过期时间失败: %v", expireErr)
			// 不返回错误，计数器已设置成功
		}

		logging.InfoLogger.Infof("同步任务计数器完成 [%s]: %d个处理中任务", vendor, processingCount)
	} else {
		logging.InfoLogger.Infof("当前没有处理中的任务 [%s]", vendor)
	}

	return nil
}

// GetRedisClient 返回Redis客户端实例
// 允许外部代码检查Redis客户端状态
func (tc *TaskCounter) GetRedisClient() *cache.RedisCluster {
	return tc.redisClient
}
