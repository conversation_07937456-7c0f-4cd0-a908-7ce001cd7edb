<config xmlns="urn:ruijie:ntos">
  <vrf>
    <name>main</name>
    <aaa xmlns="urn:ruijie:ntos:params:xml:ns:yang:aaad">
      <domain>
        <name>default</name>
        <authentication>
          <sslvpn>
            <method>default</method>
            <enabled>true</enabled>
          </sslvpn>
          <webauth>
            <method>default</method>
            <enabled>true</enabled>
          </webauth>
        </authentication>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
      </domain>
      <authentication>
        <sslvpn>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </sslvpn>
        <webauth>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </webauth>
      </authentication>
    </aaa>
    <dns xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns">
      <proxy>
        <enabled>false</enabled>
      </proxy>
    </dns>
    <ike xmlns="urn:ruijie:ntos:params:xml:ns:yang:ike">
      <proposal>
        <name>default</name>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>des des3 aes-128 aes-192 aes-256</encrypt-alg>
        <hash-alg>md5 sha</hash-alg>
        <dh-group>group1 group2 group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
    </ike>
    <interface xmlns="urn:ruijie:ntos:params:xml:ns:yang:interface">
      <physical>
        <name>Ge0/0</name>
        <ipv4>
          <address>
            <ip>*************/24</ip>
          </address>
        </ipv4>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/1</name>
      </physical>
      <physical>
        <name>Ge0/2</name>
      </physical>
      <physical>
        <name>Ge0/3</name>
      </physical>
      <physical>
        <name>Ge0/4</name>
      </physical>
      <physical>
        <name>Ge0/5</name>
      </physical>
      <physical>
        <name>Ge0/6</name>
      </physical>
      <physical>
        <name>Ge0/7</name>
      </physical>
      <physical>
        <name>Ge0/8</name>
      </physical>
      <physical>
        <name>Ge0/9</name>
      </physical>
      <physical>
        <name>TenGe0/0</name>
      </physical>
      <physical>
        <name>TenGe0/1</name>
      </physical>
      <physical>
        <name>TenGe0/2</name>
      </physical>
      <physical>
        <name>TenGe0/3</name>
      </physical>
      <bridge xmlns="urn:ruijie:ntos:params:xml:ns:yang:bridge">
        <name>br0</name>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>true</enabled>
        </ipv6>
      </bridge>
    </interface>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <policy>
        <name>deny_all</name>
        <enabled>true</enabled>
        <action>deny</action>
      </policy>
      <policy>
        <name>limit_local</name>
        <enabled>true</enabled>
        <action>permit</action>
        <limit>true</limit>
        <pps>1500</pps>
      </policy>
    </local-defend>
    <nat xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <alg>ftp sip-tcp sip-udp tftp dns-udp</alg>
    </nat>
    <netconf-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:netconf-server">
      <enabled>false</enabled>
    </netconf-server>
    <wba-portal xmlns="urn:ruijie:ntos:params:xml:ns:yang:portal">
      <enabled>false</enabled>
      <port>8081</port>
      <ssl-enabled>false</ssl-enabled>
      <redirection-mode>no-redirection</redirection-mode>
    </wba-portal>
    <security-zone xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-zone">
      <zone>
        <name>trust</name>
        <description>Trust Zone.</description>
        <priority>85</priority>
      </zone>
      <zone>
        <name>untrust</name>
        <description>Untrust Zone.</description>
        <priority>5</priority>
      </zone>
      <zone>
        <name>DMZ</name>
        <description>Demilitarized Zone.</description>
        <priority>50</priority>
      </zone>
    </security-zone>
    <ssh-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssh-server">
      <enabled>true</enabled>
    </ssh-server>
    <ssl-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssl-proxy">
      <profile>
        <name>default</name>
        <description>Default Template, Traffic proxy for Internet access of users.</description>
        <outbound/>
      </profile>
    </ssl-proxy>
    <time-range xmlns="urn:ruijie:ntos:params:xml:ns:yang:time-range">
      <range>
        <name>any</name>
        <description>Time range of all the time.</description>
        <period>
          <start>00:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
        </period>
      </range>
    </time-range>
    <webauth xmlns="urn:ruijie:ntos:params:xml:ns:yang:webauth">
      <authentication-options>
        <portal-authentication>
          <portal-group>
            <name>cportal</name>
            <protocol>portal</protocol>
          </portal-group>
        </portal-authentication>
      </authentication-options>
    </webauth>
  </vrf>
  <system xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
    <timezone>Asia/Shanghai</timezone>
    <auth xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:auth">
      <user>
        <name>admin</name>
        <role>admin</role>
        <password>$5$zHDpSFXXrdqxgOGh$uk/0SQ.WKEWWPXkHyuUnFA6Ym9sIEtnFX4bcHViEwZ1</password>
      </user>
      <user>
        <name>securityadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>useradmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>auditadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
    </auth>
    <wis-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </wis-service>
    <macc-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </macc-service>
    <security-cloud-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </security-cloud-service>
  </system>
</config>
