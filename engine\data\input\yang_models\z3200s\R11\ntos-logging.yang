module ntos-logging {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:logging";
  prefix ntos-logging;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS logging.";

  revision 2023-09-19 {
    description
      "Add syslog customization.";
    reference "";
  }
  revision 2022-07-20 {
    description
      "Add show log trace.";
    reference "";
  }
  revision 2022-05-10 {
    description
      "Add fp syslog.";
    reference "";
  }
  revision 2021-12-28 {
    description
      "Add syslog send statistics.";
    reference "";
  }
  revision 2019-03-22 {
    description
      "Add syslog facility filtering.";
    reference "";
  }
  revision 2019-03-13 {
    description
      "Add syslog TLS.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  typedef facilities {
    type enumeration {
      enum kernel {
        description
          "Filter kernel messages.";
      }
      enum mail {
        description
          "Filter mail system messages.";
      }
      enum news {
        description
          "Filter network news subsystem messages.";
      }
      enum user {
        description
          "Filter random user-level messages.";
      }
      enum auth {
        description
          "Filter security/authorization messages.";
      }
      enum authpriv {
        description
          "Filter security/authorization messages (private).";
      }
      enum cron {
        description
          "Filter clock daemon messages.";
      }
      enum daemon {
        description
          "Filter system daemons messages.";
      }
      enum line-printer {
        description
          "Filter line printer subsystem messages.";
      }
      enum FTP {
        description
          "Filter FTP daemon messages.";
      }
      enum syslog {
        description
          "Filter messages generated internally by the syslog daemon.";
      }
      enum uucp {
        description
          "Filter UUCP subsystem messages.";
      }
      enum local0 {
        description
          "Filter messages from local0.";
      }
      enum local1 {
        description
          "Filter messages from local1.";
      }
      enum local2 {
        description
          "Filter messages from local2.";
      }
      enum local3 {
        description
          "Filter messages from local3.";
      }
      enum local4 {
        description
          "Filter messages from local4.";
      }
      enum local5 {
        description
          "Filter messages from local5.";
      }
      enum local6 {
        description
          "Filter messages from local6.";
      }
      enum local7 {
        description
          "Filter messages from local7.";
      }
      enum any {
        description
          "Filter messages from any facilities.";
      }
    }
    description
      "Logging facilities.";
  }

  grouping logging-config {
    description
      "Logging parameters.";

    container rate-limit {
      presence "Makes rate limit available";
      description
        "Configure logging rate limiting.";

      leaf interval {
        type uint32;
        default "30";
        description
          "Amount of time that is being measured for rate limiting. A value of 0 disables rate limiting.";
      }

      leaf burst {
        type uint32;
        default "1000";
        description
          "Amount of messages that have to occur in the rate limit interval to trigger rate limiting.
           A value of 0 disables rate limiting.";
      }
    }
  }

  grouping logging-state {
    description
      "Logging state.";

    leaf disk-usage {
      type string;
      description
        "Total disk usage of all journal files.";
    }
  }

  grouping pri {
    description
      "Configure the facility and severity.";

    leaf facility {
      type facilities;
      description
        "Subsystem name that produce messages to filter.";
    }

    container level {
      must
        "((equal = 'none' or equal = 'any') and count(equal) = 1) or
          (not(equal = 'none') and not(equal = 'any'))" {
        error-message "'none' and 'any' cannot be set with other levels.";
      }
      description
        "Select messages level to send to the server.";

      leaf-list equal {
        type union {
          type ntos-types:log-level;
          type enumeration {
            enum any {
              description
                "Send all messages from this facility.";
            }
            enum none {
              description
                "Send nothing from this facility.";
            }
          }
        }
        must 'not(../not[level=current()])' {
          error-message
            "A level cannot be in 'not' and 'equal' lists at the same
              time.";
        }
        description
          "Select levels to send the server.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-order "1";
      }

      leaf greater-or-equal {
        type ntos-types:log-level;
        must "not(../equal='any')" {
          error-message "The 'any' level option overrides 'greater-or-equal'.";
        }
        description
          "Send messages with a greater or equal level than the selected one
            to the server.";
      }

      container not {
        presence
          "Enable to not send messages with a specific level to the
            server.";
        description
          "Select levels to not send to the server.";

        leaf-list level {
          type ntos-types:log-level;
          must "not(../../equal='none')" {
            error-message "The 'none' level option overrides 'not'.";
          }
          min-elements 1;
          description
            "Do not send messages with this level.";
          ntos-ext:nc-cli-no-name;
        }
      }
    }
  }

  grouping syslog-config {
    description
      "Syslog configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable syslog.";
    }

    container fp-syslog {
      description
        "fp syslog configuration.";

      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable fp syslog.";
      }
    }

    list remote-server {
      max-elements 5;
      key "host";
      description
        "Remote log server list.";

      leaf host {
        type ntos-inet:host;
        description
          "IP address or hostname of the remote log server.";
      }

      leaf protocol {
        type enumeration {
          enum udp {
            description
              "Traditional UDP transport. Extremely lossy but standard.";
          }
          enum tcp {
            description
              "Plain TCP based transport. Loses messages only during certain
               situations but is widely available.";
          }
        }
        default "udp";
        description
          "Transmission protocol.";
      }

      leaf port {
        type ntos-inet:port-number;
        default "514";
        description
          "Sets the destination port number for syslog UDP messages to
           the server.";
      }

      leaf syslog-protocol {
        type enumeration {
          enum rfc5424 {
            description
              "RFC5424 syslog standard.";
          }
          enum rfc3164 {
            description
              "RFC3164 syslog standard.";
          }
          enum user {
            description
              "User-defined syslog format.";
          }
        }
        default "rfc5424";
        description
          "Syslog protocol.";
      }

      leaf host-name {
        type string {
          length "0..63";
        }
        description
          "Configure host name.";
      }

      leaf log-timestamp {
        type enumeration {
          enum utc {
            description
              "Set the utc timestamp.";
          }
          enum local {
            description
              "Set the local timestamp.";
          }
        }
        default "utc";
        description
          "Configure timestamp.";
      }

      leaf sn-key {
        type string {
          length "0..63";
        }
        description
          "Configure SN name.";
      }

      list log-filter {
        key "facility";
        description
          "Filter messages sent to the server.";
        ntos-ext:nc-cli-one-liner;
        ntos-ext:nc-cli-show-key-name;
        uses pri;
      }

      list log-template {
          max-elements 8;
          key "log-type";

          leaf log-type {
            type string {
              length "0..63";
            }
            description
              "The name of log type.";
          }

          container header {
            description
              "Configure a user-defined protocol header.";

            list facility {
              max-elements 1;
              key "facility";
              description
              "Configure a user-defined facility and severity.";
              uses pri;
            }

            leaf timestamp-format {
              type string {
                length "0..63";
              }
              description
                "Configure the timestamp format.";
            }
            leaf user-text {
              type string {
                length "0..127";
              }
              description
                "Configure user-defined information.";
            }
            leaf separator {
                type string {
                  length "0..2";
                }
                description
                  "Configure the delimiter between fields.";
            }
          }
          list mapping {
            key "field-name";
            description
              "Configure the log field mapping table.";
            ntos-ext:nc-cli-one-liner;
            ntos-ext:nc-cli-show-key-name;
            ordered-by user;

            leaf field-name {
              type string {
                  length "0..63";
              }
              description
                "The name of field.";
            }

            leaf mapped-name {
              type string {
                  length "0..63";
              }
              description
                "The name of mapped field.";
            }
          }
      }

      list log-type-filter {
        key "log-type";
        description
          "Filter log type sent to the server.";
        ntos-ext:nc-cli-one-liner;
        ntos-ext:nc-cli-show-key-name;
        leaf log-type {
          type string {
            length "0..63";
          }
          description
            "The name of log type.";
        }
      }
      leaf log-charset {
        type enumeration {
          enum gbk {
            description
              "Gbk character set.";
          }
          enum utf-8 {
            description
              "UTF-8 character set.";
          }
        }
        default "utf-8";
        description
          "Configure character sets.";
      }
    }

    container tls {
      must 'not(certificate) or (certificate and private-key)' {
        error-message "Both certificate and private-key must be configured";
      }
      presence "Enable syslog messages encryption and server/client authentication.";
      description
        "Enable syslog messages encryption and server/client authentication.";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable/disable syslog messages encryption and server/client
           authentication.";
      }

      leaf ca-certificate {
        type string;
        mandatory true;
        description
          "PEM-encoded X509 certificate authority certificate.";
      }

      leaf certificate {
        type string;
        description
          "PEM-encoded X509 certificate.";
      }

      leaf private-key {
        type string;
        description
          "PEM-encoded X509 private key.";
      }

      container server-authentication {
        must 'count(*) = 1' {
          error-message "Select one authentication mode.";
        }
        description
          "Server authentication mode selection.";
        ntos-ext:nc-cli-one-liner;
        ntos-ext:nc-cli-exclusive;

        leaf anonymous {
          type empty;
          description
            "No authentication.";
        }

        container name {
          presence "Enable certificate validation and subject name authentication.";
          description
            "Certificate validation and subject name authentication.";

          leaf-list name {
            type string;
            min-elements 1;
            description
              "Certificate validation and subject name authentication.";
            ntos-ext:nc-cli-no-name;
          }
        }

        container fingerprint {
          presence "Enable certificate fingerprint authentication.";
          description
            "Certificate fingerprint authentication.";

          leaf-list fingerprint {
            type string;
            min-elements 1;
            description
              "Certificate fingerprint authentication.";
            ntos-ext:nc-cli-no-name;
          }
        }

        leaf certificate {
          type empty;
          description
            "Certificate validation only.";
        }
      }
    }
  }

  rpc show-log {
    description
      "Print log.";
    input {

      leaf max-lines {
        type uint16;
        default "50";
        description
          "Log max lines.";
      }

      leaf service {
        type union {
          type identityref {
            base ntos-types:SERVICE_LOG_ID;
          }

          type enumeration {
            enum yams;
          }
        }

        description
          "Filter logs by service.";
      }

      leaf vrf {
        type string;
        description
          "Filter logs by VRF.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf facility {
        type facilities;
        description
          "Filter logs by facility.";
      }

      container level {
        must "(equal = 'any' and count(equal) = 1) or not(equal = 'any')" {
          error-message "'any' cannot be set with other levels.";
        }
        description
          "Filter logs by level.";

        leaf-list equal {
          type union {
            type ntos-types:log-level;
            type enumeration {
              enum any {
                description
                  "Show all messages from this facility.";
              }
            }
          }
          must 'not(../not[level=current()])' {
            error-message
              "A level cannot be in 'not' and 'equal' lists at the same
               time.";
          }
          description
            "Select levels to show.";
          ntos-ext:nc-cli-no-name;
          ntos-ext:nc-cli-order "1";
        }

        leaf greater-or-equal {
          type ntos-types:log-level;
          must "not(../equal='any')" {
            error-message "The 'any' level option overrides 'greater-or-equal'.";
          }
          description
            "Filter messages with a greater or equal level than the selected
             one.";
        }

        container not {
          presence "Enable to not show messages with a specific level.";
          description
            "Select levels to not show.";

          leaf-list level {
            type ntos-types:log-level;
            min-elements 1;
            description
              "Do not show messages with this level.";
            ntos-ext:nc-cli-no-name;
          }
        }
      }
    }
    output {

      leaf data {
        type string;
        description
          "Log data.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "log";
    ntos-api:internal;
  }
  
  rpc show-log-trace {
  description
      "Print trace log.";
    input {
      leaf max-line {
        type uint16;
        description
          "show trace information with line.";
      }
    }
    output {
      leaf data {
        type string;
        description
          "Log data.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "log trace";
  }

  rpc show-syslog-statistics {
    description
      "Statistics syslog send details.";
    input {
      leaf syslog-server {
        type ntos-inet:ip-address;
        description
          "Syslog server address.";
      }
    }

    output {
        leaf stats-data {
        type string;
        description
          "Statistics syslog send details data.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "syslog-statistics";
  }

  rpc show-fp-syslog-statistics {
    description
      "Statistics fp syslog send details.";
    input {
      choice stats-type {
        case server {
          leaf syslog-server {
            type ntos-inet:ip-address;
            description
              "Syslog server address.";
          }
        }
        case global {
          leaf all {
            type empty;
            description
              "Show all syslog server statistical result.";
          }
        }
      }
    }

    output {
        leaf fp-stats-data {
        type string;
        description
          "Statistics fp syslog send details data.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "fp-syslog statistics";
  }

  rpc clear-fp-syslog-statistics {
    description
      "Clear fp syslog statistics result.";
    input {
      leaf all {
        type empty;
        description
          "Clear all fp syslog statistics result.";
      }
    }

    output {
        leaf result {
        type string;
        description
          "Clear cmd execution result.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "fp-syslog clear-statistics";
  }

  rpc fp-syslog {
    description
      "Execute fp syslog command in fast path.";

    input {
      leaf run {
        type string;
        description
          "Command to run.";
      }
    }

    output {
        leaf result {
        type string;
        description
          "Result of command.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "fp-syslog";
  }

  rpc show-log-reg-fields {
    description
      "Get registration log field information.";
    input {
      leaf detail {
        type empty;
        description
          "Log registration field details.";
      }
    }

    output {
        leaf log-reg-fields-data {
        type string;
        description
          "Log registration field data";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "log-reg-fields";
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Global logging configuration.";

    container logging {
      presence "Makes logging available";
      description
        "Global logging configuration.";
      ntos-ext:feature "product";
      uses logging-config;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Global logging operational state.";

    container logging {
      description
        "Global logging operational state.";
      ntos-ext:feature "product";
      uses logging-config;
      uses logging-state;
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Per-VRF logging configuration.";

    container logging {
      presence "Makes per-VRF logging available";
      description
        "Per-VRF logging configuration.";
      ntos-ext:feature "product";

      container syslog {
        must "enabled = 'false' or count(remote-server) > 0" {
          error-message "At least one remote server must be specified.";
        }
        presence "Makes syslog available";
        description
          "Syslog configuration.";
        uses syslog-config;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Per-VRF logging operational state.";

    container logging {
      description
        "Per-VRF logging operational state.";
      ntos-ext:feature "product";

      container syslog {
        description
          "Syslog operational state.";
        uses syslog-config;
      }
    }
  }
}
