module ntos-wlan-capwap {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:wlan-capwap";
  prefix ntos-wlan-capwap;

  import ntos-types {
    prefix ntos-types;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS WLAN capwap module.";

  revision 2024-07-23 {
    description
      "Initial version.";
    reference "";
  }

  grouping capwap-ac-config {
    container ac-control {
      description
        "Ac control function.";
      leaf enabled {
        type boolean;
        default "false";
      }
    }

    leaf location {
      description
        "Set ac location.";
      type ntos-types:ntos-obj-name-type;
    }

    leaf set-version {
      description
        "Set AC/AP software version.";
      type ntos-types:ntos-obj-name-type;
    }

    container ap-image {
      leaf ap-image-vir {
        type string;
        must "../ap-image-param2";
        ntos-ext:nc-cli-no-name;
      }

      leaf ap-image-param2 {
        type string;
        must "../ap-image-vir";
        ntos-ext:nc-cli-no-name;
        when "../ap-image-vir";
      }

      container auto-upgrade {
        description
          "Set AP auto upgrade.";
        leaf enabled {
          type boolean;
          default "false";
        }
      }
    }

    list ap-serial {
      key "serial-name";
      leaf serial-name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
        must "../ap-product-id";
      }

      leaf-list ap-product-id {
        type ntos-types:ntos-obj-name-type;
      }

      leaf hw-ver {
        type ntos-types:ntos-obj-name-type;
        when "../serial-name and ../ap-product-id";
      }
    }

    list active-bin-file {
      key "active-bin-file-vir";

      leaf active-bin-file-vir {
        type string {
          pattern '(sata[01]:.*|usb[01]:.*|tmp:.*|flash[2]?:.*)';
        }
      }

      leaf rgos10 {
        type empty;
      }
    }

    container capwap {
      container dtls {
        description
          "Set the capwap dtls.";
        leaf enabled {
          type boolean;
          default "true";
        }
      }

      leaf disc-concurrent {
        description
          "Set capwap discovery concurrent.";
        type uint16 {
          range "1..65535";
        }
      }

      leaf max-concurrent {
        description
          "Config the max-concurrent.";
        type uint16 {
          range "1..65535";
        }
      }

      leaf ctrl-ip {
        description
          "Specify control ip for capwap.";
        type ntos-inet:ipv4-address;
      }

      container upgrade {
        leaf max-concurrent {
          description
            "Config the max-concurrent upgrade ap num.";
          type uint8 {
            range "1..200";
          }
        }

        list group {
          key "upgrade-group-name";
          leaf upgrade-group-name {
            type ntos-types:ntos-obj-name-type;
            ntos-ext:nc-cli-no-name;
          }

          leaf max-concurrent {
            type uint8 {
              range "0..200";
            }
          }
        }
      }
    }
  }

  grouping capwap-ap-config {
    leaf location {
      description
        "Set ap's location string.";
      type ntos-types:ntos-obj-name-type;
    }

    leaf ac-domain-name {
      description
        "Set ac domain name which the Ap connect to.";
      type ntos-types:ntos-obj-name-type;
    }

    list acip {
      key "ipv4-address";
      description
        "Setup static AC ip address.";

      leaf ipv4-address {
        type ntos-inet:ipv4-address;
        ntos-ext:nc-cli-no-name;
      }
    }

    leaf echo-interval {
      description
        "Time between echo request messages.";
      type uint8 {
        range "5..255";
      }
    }

    container ip {
      description
        "Ip config.";
      leaf ap-address {
        type ntos-inet:ipv4-address;
        must "../ap-netmask and ../ap-gateway";
        ntos-ext:nc-cli-no-name;
      }
      leaf ap-netmask {
        type ntos-inet:ipv4-address;
        ntos-ext:nc-cli-no-name;
        when "../ap-address";
      }
      leaf ap-gateway {
        type ntos-inet:ipv4-address;
        ntos-ext:nc-cli-no-name;
        when "../ap-netmask";
      }
    }

    container ap-upgrade {
      leaf group {
        type ntos-types:ntos-obj-name-type;
      }

      leaf band-width {
        type uint16 {
          range "8..10240";
        }
      }
    }

    container capwap {
      leaf max-retransmit {
        type uint8 {
          range "3..60";
        }
      }

      container data-tunnel {
        container encryption {
          leaf des {
            type string;
          }

          leaf bf {
            type string;
          }
        }
      }

      leaf ctrl-mtu {
        type uint16 {
          range "256..1500";
        }
      }

      leaf mtu {
        type uint16 {
          range "110..1500";
        }
      }

      container fragment {
        leaf enabled {
          type boolean;
          default "false";
        }
      }
    }
  }

  grouping capwap-apg-config {
    container capwap {
      leaf max-retransmit {
        type uint8 {
          range "3..60";
        }
      }

      container data-tunnel {
        container encryption {
          leaf des {
            type string;
          }

          leaf bf {
            type string;
          }
        }
      }

      leaf ctrl-mtu {
        type uint16 {
          range "256..1500";
        }
      }

      leaf mtu {
        type uint16 {
          range "110..1500";
        }
      }

      container fragment {
        leaf enabled {
          type boolean;
          default "false";
        }
      }
    }
  }

  grouping show-capwap {
    container capwap {
      leaf server {
        type enumeration {
          enum state;
          enum auto-image-state;
        }
      }
    }
  }

  grouping show-ac-control {
    container ac-control {
      leaf enabled {
        type boolean;
      }
    }
  }

  grouping ap-version-com {
    leaf hardware-version {
      type string;
    }
    leaf software-version {
      type string;
    }
    leaf software-number {
      type string;
    }
    leaf serial-number {
      type string;
    }
    leaf mac-address {
      type string;
    }
  }

  grouping show-ap-version {
    container ap-version-info {
      list ap-version {
        key "ap-name";

        leaf ap-name {
          type string;
        }
        leaf product-ID {
          type string;
        }
        leaf system-uptime {
          type string;
        }
        uses ap-version-com;
        leaf patch-number {
          type string;
        }

        list module-information {
          key "slot name";
          leaf slot {
            type uint32;
          }
          leaf name {
            type string;
          }
          uses ap-version-com;
        }
      }
    }
  }
}