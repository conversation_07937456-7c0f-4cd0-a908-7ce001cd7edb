module ntos-wlan-wbs {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:wlan-wbs";
  prefix ntos-wlan-wbs;

  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS WLAN wbs module.";

  revision 2024-07-23 {
    description
      "Initial version.";
    reference "";
  }

  grouping enabled-leaf {
    leaf enabled {
      type boolean;
      default "false";
    }
  }

  grouping akm-config {
    list ciphers {
      key "type";

      leaf type {
        type enumeration {
          enum aes;
          enum tkip;
        }

        ntos-ext:nc-cli-no-name;
      }

      leaf enabled {
        type boolean;
      }
    }

    container akm {
      description
        "Setup wlan WPA keys manager.";

      container psk {
        description
          "Setup wlan RSN PSK AKM.";
        leaf enabled {
          type boolean;
        }

        container set-key {
          description
            "Setup WPA PSK AKM password.";

          choice key-type {
            case ascii {
              leaf ascii {
                description
                  "Setup WPA PSK password with ascii pattern.";
                type string;
              }
            }
            case hex {
              leaf hex {
                description
                  "Setup WPA PSK password with HEX pattern.";
                type string;
              }
            }
          }
        }
      }
    }
  }

  grouping wbs-sec-config {
    container security {
      description
        "Set wlan security.";

      container wpa {
        description
          "Set wlan WPA security.";

        uses enabled-leaf;
        uses akm-config;
      }

      container rsn {
        description
          "Set wlan RSNA security.";

        uses enabled-leaf;
        uses akm-config;
      }
    }
  }

  grouping wbs-ac-config {
    list country {
      key code;
      description
        "Set AC's country code.";
      leaf code {
        type string {
          length "2..3";
        }
      }
    }
  }

  grouping wbs-ap-config {
    list radio {
      key "radio-id";
      leaf radio-id {
          type union {
            type uint8;
            type enumeration {
              enum 802.11a;
              enum 802.11b;
              enum 6GHz;
            }
          }
      }

      leaf radio-type {
        type enumeration {
          enum 802.11a;
          enum 802.11b;
          enum 6GHz;
        }
      }

      leaf country {
        description
          "Set AP's country code.";
        type string {
          length "2..3";
        }
      }

      leaf channel {
        type union {
          type uint8;
          type enumeration {
            enum global;
          }
        }
      }

      leaf chan-width {
        type enumeration {
          enum 20 {
            description
              "Config channel width 20Mhz.";
          }
          enum 40 {
            description
              "Config channel width 40Mhz.";
          }
          enum 80 {
            description
              "Config channel width 80Mhz.";
          }
          enum 160 {
            description
              "Config channel width 160Mhz.";
          }
        }
      }

      container antenna {
        description
          "Config antenna mask.";
        leaf receive {
          type uint8;
        }

        leaf transmit {
          type uint8;
        }
      }

      leaf power-local {
        type union {
          type uint8;
          type enumeration {
            enum global;
          }
        }
      }
    }
  }

  grouping wbs-wlan-config {
    container band-select {
      description
        "Specify the band-select information.";
      uses enabled-leaf;
    }

    container schedule {
      description
        "Config wlan schedule.";
      leaf session {
        type uint8 {
          range "1..64";
        }
      }
    }

    leaf tunnel {
      description
        "Set (IEEE 802.11 Add WLAN.Tunnel Mode) and (IEEE 802.11 Add WLAN.MAC Mode).";
      type enumeration {
        enum local;
      }
      default "local";
    }
  }

  grouping show-rssi {
    leaf high {
      type uint32;
    }
    leaf good {
      type uint32;
    }
    leaf middle {
      type uint32;
    }
    leaf low {
      type uint32;
    }
  }

  grouping show-secinfo {
    leaf wlan-ssid {
      type string;
    }
    leaf security-policy {
      type string;
    }
    leaf wpa-version {
      type string;
    }
    leaf password {
      type string;
    }
    leaf wep-auth-mode {
      type string;
    }
    leaf wep-index {
      type uint32;
    }
    leaf sae-pwe {
      type string;
    }
  }
}