module ntos-snmp-client {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:snmp-client";
  prefix ntos-snmp-client;

  import ietf-netconf-acm {
    prefix nacm;
  }
  
  import ntos {
    prefix ntos;
  }
  
  import ntos-extensions {
    prefix ext;
  }

  import ntos-types {
    prefix ntos-types;
  }
  
  import ntos-inet-types {
    prefix ntos-inet;
  }
  
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS snmp client.";

  revision 2024-08-08 {
    description
      "Add snmp client.";
    reference "";
  }

  identity snmp-client {
    base ntos-types:SERVICE_LOG_ID;
    description
      "snmp-client resolver service.";
  }
  
  grouping snmp-client-params {
    description
      "SNMP snmp client config.";

    leaf version {
      type enumeration {
        enum v1 {
          description
            "SNMPv1.";
        }
        enum v2c {
          description
            "SNMPv2c.";
        }
        enum v3 {
          description
            "SNMPv3.";
        }
      }
      default "v2c";
      description
        "The version of snmp.";
    }
    leaf community {
      type string;
      description
        "SNMPv1,SNMPv2c, or SNMPv3 community.";
    }
    
    leaf protocol {
     type bits {
       bit udp {
         description
           "UDP.";
       }
       bit tcp {
         description
           "TCP.";
       }
       bit udp6 {
         description
           "UDPv6.";
       }
       bit tcp6 {
         description
           "TCPv6.";
       }
     }
     default "udp";
     description
       "The protocol used for connecting to the SNMP agent.";
    }
    
    leaf port {
      type ntos-inet:port-number;
      default "161";
      description
        "The port number of the host where to send the server.";
    }
    leaf name {
      type string;
      description
        "The name of the user (securityName).";
    }

    leaf auth-password {
      type string;
      description
        "The authentication password.";
    }

    leaf auth-method {
      type enumeration {
        enum md5 {
          description
            "MD5.";
        }
        enum sha {
          description
            "SHA.";
        }
      }
      default "sha";
      description
        "The authentication method.";
    }

    leaf priv-password {
      type string;
      description
        "The privacy (encryption) password. If not specified, it is
         assumed to be the same as the authentication password.";
    }

    leaf priv-protocol {
      type enumeration {
        enum aes {
          description
            "AES.";
        }
        enum des {
          description
            "DES.";
        }
      }
      default "aes";
      description
        "The encryption protocol.";
    }

  }

  grouping snmp-client-config {
    description
      "SNMP common options.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the SNMP Client engine.";
    }
    //container location-snooping {
    //  presence "Make location-snooping config from net-device.";
    //  description
    //    "location-snooping configuration.";
    //    list snooping-server {
    //      
    //       type leafref {
    //      path "/ntos:config/ntos:vrf/ntos-network-device:network-device/ntos-network-device:server";
    //    } 
    //  }
    //}
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "SNMP Client configuration.";

    container snmp-client {
      presence "Make SNMP client available.";
      description
        "SNMP client configuration.";
      ext:feature "product";
      uses snmp-client-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "SNMP Client operational state data.";

    container snmp-client {
      description
        "SNMP client operational state data.";
      ext:feature "product";
      uses snmp-client-config;
    }
  }

}
