module ntos-cloud-service {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:cloud-service";
  prefix ntos-cloud-service;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS cloud service module.";

  revision 2023-05-23 {
    description
      "1.Add log upload cloud service configuration and status.";
    reference "revision 2023-05-23";
  }

  revision 2022-03-16 {
    description
      "1.Add wis cloud status and configuration. 2.Add security cloud alarm info.";
    reference "revision 2022-03-16";
  }

  revision 2021-11-23 {
    description
      "1. Add security cloud status and broker type function. 2. Add security cloud configuration and debug log function.";
    reference "revision 2021-11-23";
  }

  revision 2021-10-28 {
    description
      "Initial version.";
    reference "";
  }

  rpc show-security-cloud-alarm-info {
    description
      "Show the security cloud alarm info.";
    output {
        leaf data {
          type string;
          description
            "The security cloud alarm info.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
        }
    }
    ntos-ext:nc-cli-show "security-cloud-alarm info";
    ntos-api:internal;
  }

  rpc show-log2cloud-config {
    description
      "Display log2cloud information.";

    output {
      leaf buffer {
        type string;
        description
          "Log2cloud information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "log2cloud config";
  }

  rpc log2cloud-test {
    description
      "The test commands of log2cloud.";

    output {
      leaf response {
        type string;
        description
          "The response message returned by the server.";
      }
    }
    ntos-ext:nc-cli-cmd "log2cloud test";
  }

  grouping system-wis-service-config {
    description
      "Configuration data for WIS service.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable the WIS service.";
    }
  }

  grouping system-macc-service-config {
    description
      "Configuration data for MACC service.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable the MACC service.";
    }
  }

  grouping system-security-cloud-service-config {
    description
      "Configuration data for security service.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable the security cloud service.";
    }
  }

  grouping system-wis-service-state {
    description
      "Status of WIS service.";

    leaf status {
      type string;
      description
        "The status of WIS service.";
    }
  }

  grouping system-macc-service-state {
    description
      "Status of MACC service.";

    leaf status {
      type string;
      description
        "The status of MACC service.";
    }

    leaf type {
      type string;
      description
        "The type of MACC service: regsister/business.";
    }
  }

grouping log2cloud-config
  {
    description
      "The configuration of log to cloud.";

    leaf cloud-url {
      type ntos-types:http-url;
      description
        "The url of security cloud.";
    }

    leaf session-token {
      type string {
        length "14";
        pattern
          '[0-9]*';
      }
      description
        "The token of session.";
    }

    leaf upload-interval {
      type uint16 {
        range "1..1440" {
          error-message
            "The upload-interval must >= 1 and <= 1440.";
        }
      }
      units "seconds";
      default 5;
      description
        "The interval time between two log upload.";
    }
  }

  grouping system-security-cloud-service-state {
    description
      "Status of security cloud service.";

    leaf status {
      type string;
      description
        "The status of security cloud service.";
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "MACC service configuration.";

    container wis-service {
      description
        "WIS service configuration.";
      uses system-wis-service-config;
    }

    container macc-service {
      description
        "MACC service configuration.";
      uses system-macc-service-config;
    }

    container security-cloud-service {
      description
        "Security cloud service configuration.";
      uses system-security-cloud-service-config;
    }

    container log2cloud {
      description
        "Forwarding logs to security configuration.";
      uses log2cloud-config;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "WIS service status.";

    container wis-service {
      description
        "WIS service configuration.";
      uses system-wis-service-state;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "MACC service status.";

    container macc-service {
      description
        "MACC service configuration.";
      uses system-macc-service-state;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Security cloud service status.";

    container security-cloud-service {
      description
        "Security cloud service configuration.";
      uses system-security-cloud-service-state;
    }
  }

  identity cloud-service {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Cloud service Log.";
  }
}
