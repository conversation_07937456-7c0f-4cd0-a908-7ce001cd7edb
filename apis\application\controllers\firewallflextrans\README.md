# 配置转换模块

本模块用于将不同厂商的网络设备配置文件转换为标准格式。

## 功能特性

- 支持多厂商配置文件解析
- 接口映射与转换
- 安全策略转换
- 地址对象与服务对象转换
- 生成标准XML配置
- 配置加密打包

## 日志系统

转换模块实现了三重日志系统，以确保安全性、用户友好性和系统可维护性：

### 1. 用户可见日志

用户可见日志只包含必要的信息，不包含敏感的实现细节：

- 转换过程的主要步骤
- 成功/失败状态
- 警告和错误信息（不包含内部路径和具体实现）
- 接口和规则的数量统计

用户日志文件：

- `job_id.log`：转换过程摘要日志
- `conversion_summary.log`：转换详细日志

### 2. 系统调试日志

系统调试日志包含详细的技术信息，仅供管理员使用：

- 完整的文件路径
- 详细的转换逻辑
- 接口映射详情
- 规则转换过程
- 异常堆栈跟踪

系统调试日志文件：

- `job_id.debug.log`：系统调试日志
- `job_id.engine.debug.log`：转换引擎调试日志

### 3. 完整日志

完整日志包含用户日志和系统日志的所有信息，并用标记区分不同类型的日志，方便系统管理员分析问题：

- 用户日志条目标记为`[USER]`
- 系统日志条目标记为`[SYS]`
- 包含所有日志信息，便于追踪问题
- 保存完整的转换过程

完整日志文件：

- `job_id.full.log`：完整日志

## 日志查看和下载

系统提供了多种日志查看和下载选项：

### 日志类型

- `summary`：摘要日志（默认，用户可见）
- `detail`：详细日志（用户可见）
- `all`：所有用户可见日志合并
- `debug`：系统调试日志（仅管理员可见）
- `engine`：引擎调试日志（仅管理员可见）
- `full`：完整日志（仅管理员可见）
- `full-debug`：所有调试日志合并（仅管理员可见）

### 日志查看

通过Web界面查看日志，支持语法高亮和关键词突出显示：

- 成功信息：绿色
- 错误信息：红色
- 警告信息：黄色
- 信息提示：蓝色
- 用户日志标记：青色
- 系统日志标记：橙色

### 日志下载

支持下载各种类型的日志文件，便于离线分析。

## 多厂商支持

当前支持的厂商：

- FortiGate（飞塔防火墙）

系统设计为可扩展架构，可以方便地添加新的厂商支持。

## 安全措施

- 用户日志中过滤敏感信息
- 调试日志仅管理员可见
- 完整日志仅管理员可见
- 防止路径遍历攻击
- 文件名和路径验证
- 权限检查

## 使用方法

1. 上传配置文件
2. 选择厂商和设备型号
3. 执行转换
4. 查看转换结果和日志
5. 下载转换后的配置文件

## 配置项

主要配置项在`config.yaml`中：

```yaml
ConfigTrans:
  Upload: "data/upload"
  EnginePath: "engine"
  PythonPath: "python"
  MappingBaseDir: "data/mappings"
  Vendors:
    fortigate:
      MappingFile: "fortigate_mapping.json"
      Modes:
        Verify: "--mode verify --vendor fortigate --cli {file}"
        Extract: "--mode extract --vendor fortigate --cli {file} --output-json {json}"
        Convert: "--mode convert --vendor fortigate --cli {file} --mapping {mapping} --model {model} --version {version} --output {output} --encrypt-output {encrypt}"
```
