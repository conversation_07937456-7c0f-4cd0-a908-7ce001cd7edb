# -*- coding: utf-8 -*-
"""
验证服务 - 应用服务层的配置验证协调器
保持与现有验证逻辑的完全兼容性
"""

import os
from typing import Dict, Any, Tuple, Optional
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.infrastructure.config.config_manager import ConfigManager
from engine.infrastructure.yang.yang_manager import YangManager


class ValidationService:
    """
    验证服务
    应用服务层的验证协调器，负责协调各种验证功能
    保持与现有验证函数的兼容性
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化验证服务
        
        Args:
            config_manager: 配置管理器实例，如果为None则创建新实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.yang_manager = YangManager(self.config_manager)
        
        log(_("validation_service.initialized"), "info")
    
    def validate_config_file(self, cli_file: str, vendor: str = "fortigate") -> <PERSON><PERSON>[bool, str]:
        """
        验证配置文件的有效性 - 委托给现有的验证逻辑
        
        Args:
            cli_file: 配置文件路径
            vendor: 厂商标识
            
        Returns: Tuple[bool, str]: (验证是否通过, 验证消息)
        """
        try:
            # 委托给现有的verify模块
            from engine.verify import verify_config
            
            result = verify_config(cli_file, vendor)
            
            if isinstance(result, dict):
                return result.get("success", False), result.get("message", "")
            else:
                # 处理旧格式的返回值
                return result if isinstance(result, tuple) else (False, str(result))
                
        except Exception as e:
            error_msg = _("validation_service.config_validation_failed", error=str(e))
            log(error_msg, "error")
            return False, error_msg
    
    def validate_xml_against_yang(self, xml_file: str, model: str, version: str) -> Tuple[bool, str]:
        """
        使用YANG模型验证XML配置
        
        Args:
            xml_file: XML文件路径
            model: 设备型号
            version: 设备版本
            
        Returns: Tuple[bool, str]: (验证是否通过, 验证消息)
        """
        try:
            return self.yang_manager.validate_xml_against_yang(xml_file, model, version)
        except Exception as e:
            error_msg = _("validation_service.yang_validation_failed", error=str(e))
            log(error_msg, "error")
            return False, error_msg
    
    def validate_interface_mapping(self, mapping_file: str, interfaces: list = None, model: str = None) -> Tuple[bool, str]:
        """
        验证接口映射文件的有效性

        Args:
            mapping_file: 接口映射文件路径
            interfaces: 解析后的接口列表（可选）
            model: 设备型号（可选）

        Returns: Tuple[bool, str]: (验证是否通过, 验证消息)
        """
        try:
            if not os.path.exists(mapping_file):
                return False, _("validation_service.mapping_file_not_found", file=mapping_file)

            # 如果提供了接口列表，使用完整的接口映射验证逻辑
            if interfaces is not None:
                # 加载映射文件
                import json
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    mapping_data = json.load(f)

                # 委托给现有的完整接口映射验证逻辑
                from engine.convert import validate_interface_mapping

                is_valid, invalid_mappings, warnings = validate_interface_mapping(interfaces, mapping_data, model)

                if is_valid:
                    return True, _("validation_service.mapping_valid")
                else:
                    error_details = []
                    for mapping in invalid_mappings[:3]:  # 只显示前3个错误
                        error_details.append(f"{mapping['source']} -> {mapping['target']}: {mapping['reason']}")

                    error_msg = _("validation_service.mapping_validation_failed_with_details",
                                count=len(invalid_mappings),
                                details="; ".join(error_details))
                    return False, error_msg
            else:
                # 如果没有接口列表，只验证文件格式
                import json
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    mapping_data = json.load(f)

                # 检查基本格式
                if not isinstance(mapping_data, dict):
                    return False, _("validation_service.mapping_invalid_format")

                # 检查是否有有效的映射数据
                has_mappings = False
                if "interface_mappings" in mapping_data and isinstance(mapping_data["interface_mappings"], dict):
                    has_mappings = len(mapping_data["interface_mappings"]) > 0
                elif any(isinstance(v, (str, dict)) for k, v in mapping_data.items() if k != "default_mapping"):
                    has_mappings = True

                if not has_mappings:
                    return False, _("validation_service.mapping_no_valid_mappings")

                return True, _("validation_service.mapping_format_valid")

        except Exception as e:
            error_msg = _("validation_service.mapping_validation_failed", error=str(e))
            log(error_msg, "error")
            return False, error_msg
    
    def is_yanglint_available(self) -> bool:
        """
        检查YANG验证工具是否可用
        
        Returns:
            bool: yanglint是否可用
        """
        return self.yang_manager.is_yanglint_available()
    
    def get_validation_capabilities(self) -> Dict[str, Any]:
        """
        获取当前验证能力信息
        
        Returns: Dict[str, Any]: 验证能力信息
        """
        return {
            "yang_validation": self.is_yanglint_available(),
            "config_validation": True,
            "interface_validation": True,
            "supported_vendors": list(self.config_manager.get_config("supported_vendors", {}).keys()),
            "supported_models": list(self.config_manager.get_config("supported_models", {}).keys())
        }
