module ntos-web-server {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system:web-server";
  prefix ntos-web-server;


  import ntos {
    prefix ntos;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS Web server.";

  revision 2024-05-17 {
    description
      "http auto jump to https default true.";
    reference
      "";
  }

  revision 2022-06-15 {
    description
      "smart http default false.";
    reference
      "";
  }

  revision 2022-04-08 {
    description
      "add smart http enable.";
    reference
      "";
  }

  revision 2022-01-18 {
    description
      "Initial version.";
    reference
      "";
  }

  identity web-server {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Web server service.";
  }

  grouping system-web-server-config {
    description
      "Configuration data for system web configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable the web server.";
    }

    leaf port {
      type ntos-inet:port-number;
      default "443";
      description
        "The local port number the web server
         listens on.";
    }

    leaf http-enabled {
      type boolean;
      default "true";
      description
        "Enable or disable the web server http auto jumps to https.";
    }

    leaf smart-http-enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the smart http.";
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Web server configuration.";

    container web-server {
      description
        "Web server configuration.";
      uses system-web-server-config;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Web server state.";

    container web-server {
      description
        "Web server state.";
      uses system-web-server-config;
    }
  }

}


