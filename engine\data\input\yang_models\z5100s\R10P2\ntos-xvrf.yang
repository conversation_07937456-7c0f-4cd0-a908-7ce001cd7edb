module ntos-xvrf {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:xvrf";
  prefix ntos-xvrf;

  import ntos {
    prefix ntos;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-qos {
    prefix ntos-qos;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS xvrf interfaces.";

  revision 2019-04-05 {
    description
      "Add qos context.";
    reference "";
  }
  revision 2018-12-11 {
    description
      "Initial version.";
    reference "";
  }

  identity xvrf {
    base ntos-types:INTERFACE_TYPE;
    description
      "Xvrf interface.";
  }

  grouping xvrf-config {
    description
      "Xvrf configuration container.";

    leaf link-interface {
      type leafref {
        path
          "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-xvrf:xvrf/ntos-xvrf:name";
      }
      must '. != ../name or ../link-vrf != ../../../ntos:name' {
        error-message "Cannot bind our own interface";
      }
      mandatory true;
      description
        "The other endpoint of the xvrf pair.";
    }

    leaf link-vrf {
      type string;
      must '. != ../../../ntos:name' {
        error-message "link-vrf must reference another vrf.";
      }
      mandatory true;
      description
        "The link vrf name.";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf/ntos:name";
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network xvrf configuration.";

    list xvrf {
      must "count(ntos-xvrf:link-interface) = 0 or
            count(ntos-xvrf:link-vrf) = 0 or
            count(/ntos:config/ntos:vrf[ntos:name=current()/ntos-xvrf:link-vrf]/
                  ntos-interface:interface/ntos-xvrf:xvrf
                    [ntos-xvrf:name=current()/ntos-xvrf:link-interface]) != 0" {
        error-message "Xvrf endpoint does not exist.";
      }
      must "count(ntos-xvrf:link-interface) = 0 or
            count(ntos-xvrf:link-vrf) = 0 or
            count(/ntos:config/ntos:vrf[ntos:name=current()/ntos-xvrf:link-vrf]/
                  ntos-interface:interface/ntos-xvrf:xvrf
                    [ntos-xvrf:name=current()/ntos-xvrf:link-interface]
                    [ntos-xvrf:link-interface=current()/ntos-xvrf:name]
                    [ntos-xvrf:link-vrf=current()/../../ntos:name]) != 0" {
        error-message "Xvrf endpoints should bind each other.";
      }
      key "name";
      description
        "The list of xvrf interfaces on the device.";
      ntos-extensions:feature "product";
      uses ntos-interface:nonphy-interface-config {
        refine name {
          must 'not(starts-with(string(.),"xvrf"))' {
            error-message "interface name xvrf* are reserved.";
          }
        }
      }
      uses xvrf-config;
      uses ntos-qos:logical-if-qos-config;
      ntos-extensions:data-not-sync;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network xvrf operational state data.";

    list xvrf {
      key "name";
      description
        "The list of xvrf interfaces on the device.";
      ntos-extensions:feature "product";
      uses ntos-interface:interface-state;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses xvrf-config;
      uses ntos-qos:logical-if-qos-state;
    }
  }
}
