module ntos-linux {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system:linux";
  prefix ntos-linux;

  import ntos {
    prefix ntos;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS linux management.";

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Operating system state.";

    container linux {
      description
        "Linux specific operational state data.";

      list cpu-usage {
        key "cpu";
        description
          "The list of busy percentage per CPU.";

        leaf cpu {
          type string;
          description
            "The CPU number.";
        }

        leaf busy {
          type uint16;
          description
            "The busy percentage.";
        }
      }

      container memory {
        description
          "The total and available memory.";

        leaf available {
          type uint64;
          description
            "The memory that can be given instantly to processes without the system
             going into swap.";
        }

        leaf total {
          type uint64;
          description
            "The total physical memory.";
        }
      }

      list disk-usage {
        key "name";
        description
          "The disk information per device.";

        leaf name {
          type string;
          description
            "The disk name.";
        }

        leaf label {
          type string;
          description
            "The disk label.";
        }

        leaf total {
          type uint64 {
            ntos-extensions:nc-cli-int-multiplier;
          }
          units "bytes";
          description
            "The disk total size.";
        }

        list partition {
          key "name";
          description
            "The partition information per disk.";

          leaf name {
            type string;
            description
              "The partition name.";
          }

          leaf label {
            type string;
            description
              "The partition label.";
          }

          leaf fstype {
            type string;
            description
              "The partition filesystem type.";
          }

          leaf total {
            type uint64 {
              ntos-extensions:nc-cli-int-multiplier;
            }
            units "bytes";
            description
              "The partition total size.";
          }

          leaf available {
            type uint64 {
              ntos-extensions:nc-cli-int-multiplier;
            }
            units "bytes";
            description
              "The partition free size.";
          }
        }
      }
    }
  }
}
