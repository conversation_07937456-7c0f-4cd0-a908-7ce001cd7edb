module ntos-collect {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system:collect";
  prefix ntos-collect;

  import ntos {
    prefix ntos;
  }

  import ntos-system {
    prefix ntos-system;
  }

  import ntos-types {
    prefix ntos-types;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ietf-netconf-acm {
    prefix nacm;
  }
  import ntos-api {
    prefix ntos-api;
  }
  organization
    "Ruijie Networks Co.,Ltd";

  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";

  description
    "Ruijie NTOS Log Collection Center.";

  revision 2024-11-08 {
    description
      "Add collect database SQL statistics.";
    reference "";
  }

  revision 2022-11-29 {
    description
      "Add export database data.";
    reference "";
  }

  revision 2022-06-22 {
    description
      "Add log-language switch.";
    reference "";
  }

  revision 2022-03-02 {
    description
      "Initial revision";
    reference "";
  }

  identity collect {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Collect service.";
  }

  typedef attack-dir-type {
    type enumeration {
      enum all {
        description
          "Set attack direction all.";
      }
      enum lan-to-lan {
        description
          "Set attack direction lan-to-lan.";
      }
      enum lan-to-wan {
        description
          "Set attack direction lan-to-wan.";
      }
      enum wan-to-lan {
        description
          "Set attack direction wan-to-lan.";
      }
    }
    description
      "Attack direction type.";
  }

  grouping collect-config {
    description
      "The grouping of log collection parameters.";

    leaf enabled {
      type boolean;
      default true;
      description
      "Enable log collection.";
    }

    leaf max-records {
      type uint32 {
        range "120..7168" {
          error-message
            "The max-records must >= 120 and <= 7168.";
        }
      }
      units "number";
      default 3072;
      description
        "The max records of log collection inside the device.";
    }

    leaf record-interval {
      type uint32 {
        range "100..1500" {
          error-message
            "The record-interval must >= 50 and <= 1500.";
        }
      }
      units "microsecond";
      default 300;
      description
        "The sleep interval time between two record collected.";
    }

    leaf memory-storage-threshold {
      type uint32 {
        range "75..95" {
          error-message
            "The memory storage threshold must >= 75 and <= 95.";
        }
      }
      units "number";
      default 90;
      description
        "The max memory storage threshold of log collection.";
    }

    leaf statistics-enabled {
      type boolean;
      default false;
      description
      "Enable statistics log count of collection.";
    }

    leaf record-stats-enabled {
        type boolean;
        default true;
        description
        "Enable statistics log status of collection.";
    }

    leaf flow-log-enabled {
      type boolean;
      default true;
      description
      "Enable flow log collection.";
    }

    leaf log-language {
      type enumeration {
        enum Chinese {
          description
          "Log message in Chinese.";
        }
        enum English {
          description
          "Log message in English.";
        }
      }
      description
      "Log message language type.";
    }
  }

  grouping db-query-parameters {
    description
      "Required parameters for SQL SELECT.";
    leaf table-name {
        type string;
        mandatory true;
          description
          "DB tabel name.";
    }
    leaf item-count {
        type empty;
          description
            "Number of items in the table.";
    }
    leaf item-limit {
        type uint32 {
          range "1..100";
        }
          description
            "The range of record item number limit.";
    }
    leaf item-offset {
        type uint32;
          description
            "The start item index when selecting DB records.";
    }
    leaf index-name {
        type string;
          description
            "Index of grep string.";
    }
    leaf grep-string {
        type string;
          description
            "Grep string.";
    }
    leaf more-than {
        type string;
          description
            "Filter items which value greater than this.";
    }
    leaf less-than {
        type string;
          description
            "Filter items which value less than this.";
    }
  }
  grouping export-db-parameters {

    description
      "Required parameters for SQL EXPORT.";

    leaf location {
      type string {
        pattern '/mnt/sata0/tarlist/+[a-zA-Z0-9.\-_]+.tar.gz';
      }
      default "/mnt/sata0/tarlist/export_data.tar.gz";
      ntos-ext:nc-cli-no-name;
      description
        "Path of the compressed file, with suffix '.tar.gz' and prefix '/mnt/sata0/tarlist/.'";
    }

    leaf db-name {
      type string;
      mandatory true;
      ntos-ext:nc-cli-no-name;
      description
        "SQL database name.";
    }

    leaf time-start {
      type string {
        pattern '(((2[0-1][0-9])[0-9]((0[1-9]|1[0-2])' +
                '(0[1-9]|1[0-9]|2[0-8])|(0[13-9]|1[0-2])' +
                '(29|30)|(0[13578]|1[02])31)|' +
                '([0-9]{2}(0[48]|[2468][048]|[13579][26])|' +
                '(0[48]|[2468][048]|[13579][26])00)0229)' +
                '([01][0-9]|2[0-3]))|'{
          error-message "Incorrect time/date format, expecting: YYYYMMDDHH and it must be after 2000-01-01.";
        }
        length "0|10";
      }
      mandatory true;
      ntos-ext:nc-cli-no-name;
      description
        "Input start time with format like 2022071109.";
    }

    leaf time-end {
      type string {
        pattern '(((2[0-1][0-9])[0-9]((0[1-9]|1[0-2])' +
                '(0[1-9]|1[0-9]|2[0-8])|(0[13-9]|1[0-2])' +
                '(29|30)|(0[13578]|1[02])31)|' +
                '([0-9]{2}(0[48]|[2468][048]|[13579][26])|' +
                '(0[48]|[2468][048]|[13579][26])00)0229)' +
                '([01][0-9]|2[0-3]))|'{
          error-message "Incorrect time/date format, expecting: YYYYMMDDHH and it must be after 2000-01-01.";
        }
        length "0|10";
      }
      mandatory true;
      ntos-ext:nc-cli-no-name;
      description
        "Input end time with format like 2022071115.";
    }

    leaf batch-capacity {
      type int32 {
        range "5000..10000";
      }
      default 10000;
      ntos-ext:nc-cli-no-name;
      description
        "Set the capacity of each batch, the recommended value is 5000-10000, the larger the capacity, the higher the export speed, but the higher the CPU usage.";
      }

    leaf easy-read-enabled {
      type boolean;
      default true;
      ntos-ext:nc-cli-no-name;
      description
        "Enable to enhance the legibility of the exported data.";
    }
  }

  grouping db-alarm-threshold {
    description
      "Percentage of database alarm threshold.";

    leaf disk-db-threshold {
      type uint32 {
        range "1..100";
      }
      default 90;
      description
        "The threshold of the disk database alarm threshold";
    }
  }

  grouping db-size-pct {
    description
      "Percentage of disk database size.";

    leaf flow-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of flow database size.";
    }

    leaf sys-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of system database size.";
    }

    leaf sec-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of security database size.";
    }

    leaf track-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of track database size.";
    }

    leaf sslvpn-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of sslvpn database size.";
    }

    leaf vrrp-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of vrrp database size.";
    }

    leaf ipsec-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of ipsec-vpn database size.";
    }

    leaf content-filter-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of content-filter database size.";
    }
    leaf url-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of url database size.";
    }
    leaf ca-url-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of ca-url database size.";
    }
    leaf ca-im-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of ca-im database size.";
    }
    leaf ca-web-search-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of ca-web-search database size.";
    }
    leaf ca-mail-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of ca-mail database size.";
    }
    leaf ca-bbs-mblog-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of ca-bbs-mblogdatabase size.";
    }
    leaf ca-file-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of ca-file database size.";
    }
    leaf bwlist-db {
      type decimal64 {
        fraction-digits 2;
      }
      description
        "The percentage of bwlist database size.";
    }
  }

  grouping collect-db-used-info {
    description
      "Database size used info.";

      leaf disk-total-size {
        type string;
        description
          "Show the total disk capacity available for logs.";
      }
      leaf disk-used-size {
        type string;
        description
          "Show the disk capacity used by logs.";
      }
      leaf disk-used-pct {
        type string;
        description
          "Show the percentage of disk capacity used by logs.";
      }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Configuration data for the log collection.";
    container collect {
      description
        "Log collection configuration.";
      container disk-allocate-db-size {
        description
          "Database size percentage configuration.";
        uses db-size-pct;
      }
      container alarm-threshold {
        description
          "Alarm threshold configuration.";
        uses db-alarm-threshold;
      }
      uses collect-config;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Operational state for the log collection.";
    container collect {
      config false;
      description
        "Log collection configuration.";
      container disk-allocate-db-size {
        description
          "Allocated database size percentage configuration.";
        uses db-size-pct;
        uses collect-db-used-info;
      }
      container disk-used-db-size {
        description
          "Used database size percentage configuration.";
        uses db-size-pct;
      }
      container alarm-threshold {
        description
          "Alarm threshold configuration.";
        uses db-alarm-threshold;
      }
      uses collect-config;
    }
  }

  rpc collect-show {
    description
      "Display collect information.";

    output {
      leaf buffer {
        type string;
        description
          "Collect information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "collect";
  }

  rpc collect-stats-show {
    description
      "Display collect stats information.";

    input {
      leaf log_type {
        type uint8;
        default 0;
        description
          "The log type range of statment, 0 means all log_type statment.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "Collect stat information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "collect-stats";
  }

  rpc show-collect-memory {
    description
      "Display collect memory information.";

    input {
      leaf module-name {
        type string;
        default "all";
        description
          "You can set the value of the module-name to all|had|dm_monitor|sslvpnd|vrrp|INOTIFY|licns_deamon|DMC|YAMS|SSHD|security|LOGIN|BASH.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "Collect memeory information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "collect-memory";
  }

  rpc show-collect-local-alert {
    description
      "Display collect local alert information.";

    output {
      leaf buffer {
        type string;
        description
          "Collect information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "collect local alert";
  }


  rpc show-sqlite-db {
    description
      "Display collect SQLite DB files information.";

    output {
      leaf buffer {
        type string;
        description
          "Collect SQLite DB files information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "collect-db sqlite-db";
  }

  rpc show-pg-db {
    description
      "Display collect PG DB information.";

    output {
      leaf buffer {
        type string;
        description
          "Collect PG DB information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "collect-db pg-db";
  }

  rpc show-sqlite-db-table {
    description
      "Display collect SQLite db table information.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The vrf name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf sqlite-db-name {
          type string;
          mandatory true;
            description
              "SQLite DB name.";
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Collect SQLite db table information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "collect-db sqlite-table";
  }

  rpc show-pg-db-table {
    description
      "Display collect PG DB table information.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The vrf name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf pg-db-name {
          type string;
          mandatory true;
            description
              "PG DB name.";
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Collect PG DB table information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "collect-db pg-table";
  }

  rpc show-sqlite-table-record {
    description
      "Display collect SQLite table record information.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The vrf name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf sqlite-db-name {
          type string;
          mandatory true;
            description
              "SQLite DB name.";
      }
      uses db-query-parameters;
    }

    output {
      leaf buffer {
        type string;
        description
          "Collect SQLite table record information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "collect-db sqlite-table-record";
  }

  rpc show-pg-table-record {
    description
      "Display collect PG table record information.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The vrf name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf pg-db-name {
          type string;
          mandatory true;
            description
              "PG DB name.";
      }
      uses db-query-parameters;
    }

    output {
      leaf buffer {
        type string;
        description
          "Collect PG table record information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "collect-db pg-table-record";
  }


  rpc collect-set-debug {
    ntos-ext:nc-cli-cmd "collect-set-debug";

    description
      "Set log debug level";
    input {
      leaf module-name {
        type enumeration {
          enum licns_deamon {
            description
            "Set debug module name.";
          }
          enum dm_monitor {
            description
            "Set debug module name.";
          }
          enum track {
            description
            "Set debug module name.";
          }
          enum fast-path {
            description
            "Set debug module name.";
          }
        }
        description
        "Log debugging module.";
      }

      leaf level {
        type enumeration {
          enum error {
            description
            "Set debug level to error.";
          }
          enum warn {
            description
            "Set debug level to warning.";
          }
          enum info {
            description
            "Set debug level to info.";
          }
          enum debug {
            description
            "Set debug level to debug.";
          }
          enum none {
            description
            "Set debug level to none.";
          }
        }
        default "error";
        description
        "Log debug level.";
      }
    }
  }

  rpc collect-get-debug {
    ntos-ext:nc-cli-cmd "collect-get-debug";

    description
      "Get debug log level";
    input {
      leaf module-name {
        type enumeration {
          enum licns_deamon {
            description
            "Set debug module name.";
          }
          enum dm_monitor {
            description
            "Set debug module name.";
          }
          enum track {
            description
            "Set debug module name.";
          }
          enum fast-path {
            description
            "Set debug module name.";
          }
        }
        description
        "Log debugging module.";
      }
    }
    output {
      leaf result{
        type string;
        description
          "The result of getting debug log level";
        ntos-ext:nc-cli-stdout;
      }
    }
  }
  rpc export-sq {
    ntos-ext:nc-cli-cmd "export-sq";
    nacm:default-deny-all;
    description
      "Export sqlite data to a compressed file by time. It will create a file in specified location.";
    input {
      uses export-db-parameters;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
  }
  rpc export-pg {
    ntos-ext:nc-cli-cmd "export-pg";
    nacm:default-deny-all;
    description
      "Export database data to a compressed file by time. It will create a file in specified location.";
    input {
      uses export-db-parameters;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
  }
  rpc get-export-status {
    ntos-ext:nc-cli-cmd " get-export-status";
    nacm:default-deny-all;
    description
      "Get further status of a command running in the background.";
    input {
      leaf uid {
        type uint64;
        description
          "The identifier of the command.";
      }
      leaf db-name {
        type string;
        description
          "The db name of the export.";
      }
    }
    output {
      leaf schedule {
        type int32;
        description
          "Show the export the progress.";
      }
      leaf tar-name {
        type string;
        description
          "Show the package name.";
      }
      leaf db-key {
        type string;
        description
          "Show the key word of database.";
      }
    uses ntos-cmd:long-cmd-output;
    }
    ntos-api:internal;
  }

  rpc export-stats-report{
    ntos-ext:nc-cli-cmd "export-stats-report";
    description
      "Export stats-report file.";
    input {
      list module {
        key "module-name";
        leaf module-name {
          type string;
          default "all";
          description
            "You can set the name of the stats-report module to all|threat|threat-intelligence|traffic|bandwith-app-list|url-request-list.";
        }
        leaf top {
          type int32;
          default 0;
          description
            "Top num (Currently supports 5|10|30|50)";
        }
        leaf time {
          type string;
          default "day";
          description
            "You can set time day|week.";
        }
        leaf attack-dir {
          type attack-dir-type;
          default "all";
          description
            "Attack direction.";
        }
      }
      leaf type {
        type enumeration{
          enum pdf {
            description
              "Set type in pdf.";
          }
        }
        default "pdf";
        description
          "The output file format";
      }
    }
    output {
      leaf data {
        type string;
        description
          "Data result
          Success: The name of the report file is returned.
          Failure: An error message is returned.";
      }
      //uses ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
    }
  }

  rpc get-stats-report {
    description
      "Get status of stats-report process.";
    ntos-ext:nc-cli-cmd "get-stats-report";
    input {
      leaf stats-report-name {
        type string;
        default "all";
        description
          "Stats-report name.";
      }
    }
    output {
      leaf data {
        type string;
        description
          "Status of stats-report process.";
      }
      //uses ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
    }
  }

  rpc db-stats-reset {
    ntos-ext:nc-cli-cmd "db-stats-reset";

    description
      "Clean up historical statistics.";
    
    output {
      leaf result {
          type string;
          description
              "Result of command.";
          ntos-ext:nc-cli-stdout;
      }
    }
  }

  rpc db-trackio-open {
    ntos-ext:nc-cli-cmd "db-trackio-open";

    description
      "Open database track io timing.";
    output {
      leaf result {
          type string;
          description
              "Result of command.";
          ntos-ext:nc-cli-stdout;
      }
    }
  }

  rpc db-trackio-close {
    ntos-ext:nc-cli-cmd "db-trackio-close";

    description
      "Close database track io timing.";
    output {
      leaf result {
          type string;
          description
              "Result of command.";
          ntos-ext:nc-cli-stdout;
      }
    }
  }

  rpc db-stats-collect {
    ntos-ext:nc-cli-cmd "db-stats-collect";

    description
      "Collect database logs.";
    output {
      leaf result {
          type string;
          description
              "Result of command.";
          ntos-ext:nc-cli-stdout;
      }
    }
  }
}
