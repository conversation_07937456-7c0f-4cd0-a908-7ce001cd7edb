package daccesslog

import (
	"fmt"
	"reflect"
	"time"

	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/user"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const ModelName = "访问日志"

type Response struct {
	ID            uint           `gorm:"primarykey" json:"id"`
	CreatedAt     time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"deleted_at"`
	ServerName    string         `gorm:"type:varchar(100)" json:"server_name" comment:"服务名称"`
	IP            string         `gorm:"type:varchar(100)" json:"ip"  comment:"ip地址"`
	RequestMethod string         `gorm:"type:varchar(100)" json:"request_method"  comment:"请求方式"`
	RequestPath   string         `gorm:"type:varchar(255)" json:"request_path"  comment:"请求路径"`
	RequestAction string         `gorm:"type:varchar(255)" json:"request_action"  comment:"请求方法"`
	RouteName     string         `gorm:"type:varchar(100)" json:"route_name"  comment:"路由名称"`
	UserID        string         `gorm:"type:varchar(100)" json:"user_id"  comment:"用户id"`
}

type ListResponse struct {
	Response
}

type Request struct {
	ServerName    string `json:"server_name" form:"server_name"` //服务名称
	IP            string `json:"ip"  form:"ip"`
	RequestMethod string `json:"request_method" form:"request_method"`
	RequestPath   string `json:"request_path"  form:"request_path"`
	RequestAction string `json:"request_action"  form:"request_action"`
	RouteName     string `json:"route_name"  form:"route_name"`
	userID        string `json:"user_id"  form:"user_id"`
}

func (a *Response) ModelName() string {
	return ModelName
}

func Model() *user.AccessLog {
	return &user.AccessLog{}
}

func (a *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var approvals []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&approvals).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	list := map[string]interface{}{"items": approvals, "total": count, "limit": pageSize}
	return list, nil
}

func (a *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}

	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (a *Response) Update(id uint, object map[string]interface{}) error {
	err := a.Find(id)
	if err != nil {
		return err
	}

	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByUserId(id, userId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and user_id", id, userId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) FindByAuditorId(id, auditorId uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ? and auditor_id", id, auditorId).Find(a).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (a *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func (u *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func (u *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(u).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find approval err ", err)
		return err
	}
	return nil
}

func UpdateOrCreateAccesslogTransaction(accesslogs []*Response) error {
	batchCreateObjects := []map[string]interface{}{}
	for _, accesslog := range accesslogs {
		logObject := map[string]interface{}{
			"CreatedAt":     accesslog.CreatedAt,
			"UpdatedAt":     accesslog.UpdatedAt,
			"ServerName":    accesslog.ServerName,
			"IP":            accesslog.IP,
			"RequestMethod": accesslog.RequestMethod,
			"RequestPath":   accesslog.RequestPath,
			"RequestAction": accesslog.RequestAction,
			"RouteName":     accesslog.RouteName,
			"UserID":        accesslog.UserID,
		}
		batchCreateObjects = append(batchCreateObjects, logObject)
	}
	xt := reflect.TypeOf(Model())
	columns := []string{}
	for i := 0; i < xt.Elem().NumField(); i++ {
		key, ok := xt.Elem().Field(i).Tag.Lookup("update")
		if ok {
			columns = append(columns, key)
		}
	}

	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		if len(batchCreateObjects) > 0 {
			err := tx.Model(&user.AccessLog{}).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns(columns),
			}).Create(&batchCreateObjects).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

type AccessLogByDay struct {
	ServerName      string `json:"server_name"`
	TimePeriod      string `json:"time_period"`
	VisitCount      int    `json:"visit_count"`
	UniqueUserCount int    `json:"unique_user_count"`
}

func GetAccessLogByDay() ([]*AccessLogByDay, error) {
	items := []*AccessLogByDay{}
	sql := `
SELECT
    server_name,
    DATE_FORMAT(created_at, '%Y-%m-%d') AS time_period,
    COUNT(*) AS visit_count,
    COUNT(DISTINCT user_id) AS unique_user_count
FROM access_logs
WHERE created_at > '2024-07-01'
GROUP BY server_name, time_period
ORDER BY server_name;`
	err := easygorm.GetEasyGormDb().Table("access_logs").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetAccessLogByMonth() ([]*AccessLogByDay, error) {
	items := []*AccessLogByDay{}
	sql := `
SELECT
    server_name,
    DATE_FORMAT(created_at, '%Y-%m') AS time_period,
    COUNT(*) AS visit_count,
    COUNT(DISTINCT user_id) AS unique_user_count
FROM access_logs
WHERE created_at > '2024-07-01'
GROUP BY server_name, time_period
ORDER BY server_name;`
	err := easygorm.GetEasyGormDb().Table("access_logs").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

type AccessLogByDayAndAction struct {
	ServerName      string `json:"server_name"`
	TimePeriod      string `json:"time_period"`
	RequestAction   string `json:"request_action"`
	RouteName       string `json:"route_name"`
	VisitCount      int    `json:"visit_count"`
	UniqueUserCount int    `json:"unique_user_count"`
}

func GetAccessLogByDayAndAction() ([]*AccessLogByDayAndAction, error) {
	items := []*AccessLogByDayAndAction{}
	sql := `
SELECT
    server_name,
    DATE_FORMAT(created_at, '%Y-%m-%d') AS time_period,
    request_action,
    route_name,
    COUNT(*) AS visit_count,
    COUNT(DISTINCT user_id) AS unique_user_count
FROM access_logs
WHERE created_at > '2024-07-01'
GROUP BY server_name, time_period,request_action,route_name
ORDER BY server_name;`
	err := easygorm.GetEasyGormDb().Table("access_logs").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}

func GetAccessLogByMonthAndAction() ([]*AccessLogByDayAndAction, error) {
	items := []*AccessLogByDayAndAction{}
	sql := `
SELECT
    server_name,
    DATE_FORMAT(created_at, '%Y-%m') AS time_period,
    request_action,
    route_name,
    COUNT(*) AS visit_count,
    COUNT(DISTINCT user_id) AS unique_user_count
FROM access_logs
WHERE created_at > '2024-07-01'
GROUP BY server_name, time_period,request_action,route_name
ORDER BY server_name;`
	err := easygorm.GetEasyGormDb().Table("access_logs").Raw(sql).Scan(&items).Error
	if err != nil {
		return items, err
	}
	return items, nil
}
