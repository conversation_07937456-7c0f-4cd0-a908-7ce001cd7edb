package middleware

import (
	"irisAdminApi/application/logging"
	"strings"

	"github.com/kataras/iris/v12"
)

// I18nMiddleware 国际化中间件，用于处理请求的语言设置
func I18nMiddleware(ctx iris.Context) {
	// 获取请求头中的Accept-Language
	acceptLanguage := ctx.GetHeader("Accept-Language")

	// 从查询参数中获取lang
	langParam := ctx.URLParam("lang")

	var language string

	// 优先使用查询参数中的lang
	if langParam != "" {
		language = normalizeLang(langParam)
	} else if acceptLanguage != "" {
		// 解析Accept-Language头
		language = parseAcceptLanguage(acceptLanguage)
	} else {
		// 默认使用中文
		language = "zh-CN"
	}

	// 保存语言设置到上下文中
	ctx.Values().Set("language", language)

	// 记录调试日志
	logging.DebugLogger.Debugf("请求语言设置: %s，来源: %s", language, getLanguageSource(langParam, acceptLanguage))

	// 继续处理请求
	ctx.Next()
}

// 解析Accept-Language头，选择支持的语言
func parseAcceptLanguage(acceptLanguage string) string {
	// 分割language-q对
	parts := strings.Split(acceptLanguage, ",")

	// 简单实现：仅检查是否包含zh或en前缀
	for _, part := range parts {
		langQ := strings.Split(part, ";")
		lang := strings.TrimSpace(langQ[0])

		if strings.HasPrefix(lang, "zh") {
			return "zh-CN"
		} else if strings.HasPrefix(lang, "en") {
			return "en-US"
		}
	}

	// 默认返回中文
	return "zh-CN"
}

// 标准化语言代码
func normalizeLang(lang string) string {
	lang = strings.ToLower(strings.TrimSpace(lang))

	switch lang {
	case "zh", "zh-cn", "zh_cn", "cn", "chinese":
		return "zh-CN"
	case "en", "en-us", "en_us", "us", "english":
		return "en-US"
	default:
		return "zh-CN" // 默认使用中文
	}
}

// 获取语言来源描述
func getLanguageSource(langParam, acceptLanguage string) string {
	if langParam != "" {
		return "URL参数"
	} else if acceptLanguage != "" {
		return "Accept-Language头"
	}
	return "默认设置"
}
