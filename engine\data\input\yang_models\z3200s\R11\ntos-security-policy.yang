module ntos-security-policy {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:security-policy";
  prefix ntos-security-policy;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }

  import ntos-user-management {
    prefix ntos-user-management;
  }

  import ntos-region-obj {
    prefix ntos-region-obj;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS security policy module.";

  revision 2025-04-09 {
    description
      "Add status query command.";
    reference "";
  }

  revision 2024-07-24 {
    description
      "Add capacity query command.";
    reference "";
  }

  revision 2024-03-01 {
    description
      "add interface profile";
    reference "";
  }

  revision 2023-10-31 {
    description
      "add region-obj";
    reference "";
  }

  revision 2023-08-25 {
    description
      "add file-filter profile";
    reference "";
  }

  revision 2023-08-24 {
    description
      "add content profile";
    reference "";
  }

  revision 2023-03-21 {
    description
      "add user object";
    reference "";
  }

  revision 2023-02-21 {
    description
      "add url profile";
    reference "";
  }

  revision 2022-06-10 {
    description
      "add av profile";
    reference "";
  }

  revision 2022-04-29 {
    description
      "Create.";
    reference "";
  }

  identity security-policy {
    base ntos-types:SERVICE_LOG_ID;
    description
      "security-policy service.";
  }

  typedef action-type {
    type enumeration {
      enum permit {
        description
        "Session permit.";
      }
      enum deny {
        description
        "Session deny.";
      }
    }
    description
      "Action type.";
  }

  typedef ip-family-types {
    type union {
      type enumeration {
        enum ipv4;
        enum ipv6;
        enum ip46;
      }
    }
    description
      "The type of this security policy's address.";
  }

  typedef config-source-type {
    type enumeration {
      enum auto {
        description
          "Automatic configuration.";
      }
      enum manual {
        description
          "Manual configuration.";
      }
    }
  }

  grouping user-item {
    list user-group {
      key "name";
      description
        "The list of user group.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        description
          "policy related user group.
           An user group name must carry the authentication domain name.
           For example, /default/group1 indicates group1 in the default authentication domain.";
        type ntos-user-management:user-group-path;
        ntos-ext:nc-cli-no-name;
      }
    }

    list user-label {
      key "name";
      description
        "The list of user label.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        description
          "policy related user label.
           An user label name must carry the authentication domain name.
           For example, /default/group1 indicates group1 in the default authentication domain.";
        type ntos-user-management:user-group-path;
        ntos-ext:nc-cli-no-name;
      }
    }

    list user-name {
      key "name";
      description
          "policy related user obj.
           An user obj name must carry the authentication domain name.
           For example: user1@xxx, if xxx is the default domain,
                                      do not fill it in and remove the '@' character.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        description "Configure the name of the user.";
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }
  }

  grouping contain-object {
    description
      "Detail about object contained by policy.";

    list source-zone {
      description
        "The name of source zone.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    list dest-zone {
      description
        "The name of destination zone.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    list source-interface {
      description
        "The name of source interface.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ifname;
        ntos-ext:nc-cli-no-name;
      }
    }

    list dest-interface {
      description
        "The name of destination interface.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ifname;
        ntos-ext:nc-cli-no-name;
      }
    }

    list source-network {
      description
        "The name of source network.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    list dest-network {
      description
        "The name of destination network.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    list source-region {
      description
        "The name of source region.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-region-obj:region-obj-name;
        ntos-ext:nc-cli-no-name;
      }
    }

    list dest-region {
      description
        "The name of destination region.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-region-obj:region-obj-name;
        ntos-ext:nc-cli-no-name;
      }
    }

    list service {
      description
        "The name of service.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    uses user-item;

    list app {
      description
        "The name of application.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    leaf time-range {
      description
        "The name of time range.";
      type ntos-types:ntos-obj-name-type;
      default "any";
    }
  }

  grouping profile-detail {
    description
      "Detail of profile contained by policy.";

    leaf ips {
      description
        "The name of the IPS template";
      type ntos-types:ntos-obj-name-type;
    }
    leaf av {
      description
        "The name of the AV template";
      type ntos-types:ntos-obj-name-type;
    }

    leaf url {
      description
        "The name of the URL template";
      type ntos-types:ntos-obj-name-type;
    }

    leaf websec {
      description
        "The name of the WEBSEC template";
      type ntos-types:ntos-obj-name-type;
    }

    leaf content-filter {
      description
        "The name of the Content template";
      type ntos-types:ntos-obj-name-type;
    }

    leaf file-filter {
      description
        "The name of the FILE-FILTER template";
      type ntos-types:ntos-obj-name-type;
    }
  }

  grouping policy-detail {
    description
      "Configuration detail of policy.";

    list policy {
      key "name";
      description
        "The detail of security policy.";
      ordered-by user;
      leaf policy-id {
        config false;
        type uint64;

        description
          "The id of policy.";
      }

      leaf position-id {
        config false;
        type uint32;

        description
          "The position id of policy.";
      }

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of security policy.";
      }

      leaf rename {
        type ntos-types:ntos-obj-name-type;
        description
          "The new name of security policy.";
      }

      leaf address-type {
        type ip-family-types;
        config false;
        description
          "The type of this security policy's src and dst address.";
      }

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable security policy.";
      }

      leaf time-range-enabled {
        config false;
        type boolean;
        default "true";
        description
          "Enable or disable security policy by time range.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "The description of policy.";
      }

      leaf group-name {
        type ntos-types:ntos-obj-name-type;
        default 'def-group';

        description
          "The group which security policy belongs to.";
      }

      uses contain-object;

      leaf action {
        type action-type;
        default "permit";
        description
          "The action of security policy.";
      }

      leaf config-source {
        type config-source-type;
        default "manual";
        description
          "Source of security policy.";
      }

      leaf session-timeout {
        type uint32;
        default 0;
        description
          "Timeout of the session aging that matches the current security policy.";
      }

      uses profile-detail;
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Top-Level grouping for security policy configuration.";

    container security-policy {
      description
        "Configuration for security policy.";

      leaf basic-protocol-control-enabled {
        type boolean;
        description
          "Enable or disable basic protocol control by policy";
      }

      list group {
        description
          "The group of security policy.";
        key "name";
        ntos-ext:nc-cli-one-liner;
        ordered-by user;
        leaf name {
          description
            "The name of security policy group";
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
        }

        leaf rename {
          type ntos-types:ntos-obj-name-type;
          description
            "The new name of security policy group.";
        }
      }
      uses policy-detail;
    }
  }

  grouping capacity-detail {
    description
      "The detail of capacity.";
    leaf total {
      description
        "The total capacity.";
      type uint32;
    }
    leaf remain {
      description
        "The remaining capacity.";
      type uint32;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "The state of security policy.";
    container security-policy {
      container capacity {
        description
          "The capacity of security policy.";
        container policy {
          description
            "The capacity of policy.";
          uses capacity-detail;
        }
        container group {
          description
            "The capacity of policy group.";
          uses capacity-detail;
        }
      }
      container object-feature {
        description
          "The capacity of the object for one security policy.";
        leaf capacity-of-zone {
          type uint32;
          description
            "The capacity of zone.";
        }
        leaf capacity-of-interface {
          type uint32;
          description
            "The capacity of interface.";
        }
        leaf capacity-of-ip {
          type uint32;
          description
            "The capacity of ip object.";
        }
        leaf capacity-of-service {
          type uint32;
          description
            "The capacity of service object.";
        }
        leaf capacity-of-appid {
          type uint32;
          description
            "The capacity of appid object.";
        }
        leaf capacity-of-user {
          type uint32;
          description
            "The capacity of user object.";
        }
        leaf capacity-of-user-label {
          type uint32;
          description
            "The capacity of user-label object.";
        }
        leaf capacity-of-user-group {
          type uint32;
          description
            "The capacity of user-group object.";
        }
        leaf capacity-of-region {
          type uint32;
          description
            "The capacity of region object.";
        }
      }
      container status {
        list policy {
          key "name";
          description
            "The status of the security policy.";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description
              "The name of security policy.";
          }

          leaf match-count {
              type uint64;
              description
                "The matching times for the policy.";
          }
          leaf create-time {
            description
              "Policy creation time.";
            type string;
          }

          leaf first-match-time {
            description
              "The first match time of policy";
            type string;
          }

          leaf last-match-time {
            description
              "The last match time of policy.";
            type string;
          }
        }
      }
    }
  }

  rpc policy-stat-clear {
    description
      "Clear hit statistics of security policy.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
      container content {
        ntos-ext:nc-cli-group "subcommand";
        leaf type {
          type enumeration {
            enum policy;
            enum sim-policy;
            enum sim-status-run-time;
          }

          description
            "Policy or simulative policy.";
        }

        leaf policy-id-list {
          type string;
          description
            "Clear hit statistics of security policy by policy id list.";
        }
      }
    }
    ntos-ext:nc-cli-cmd "security-policy-stat-clear";
    ntos-api:internal;
  }

  rpc policy-analyze {
    description
      "Policy redundancy analysis.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
      container content {
        ntos-ext:nc-cli-group "subcommand";
        leaf type {
          type enumeration {
            enum policy;
            enum sim-policy;
            enum stat;
            enum stop;
          }

          description
            "Policy or simulative policy.";
        }

        leaf ignore-policy {
          type string;
          description
            "Add policy into ignore list";
        }
        leaf cancel-policy {
          type string;
          description
            "Delete policy from ignore list";
        }
      }
    }
    output {
      leaf info {
        description
          "Information of command result";
        type string;
      }
      list ignore-policy {
        description
          "The group of security policy";
        key "name";

        leaf name {
          description
            "The name of security policy group";
          type ntos-types:ntos-obj-name-type;
        }

        leaf problem-name {
          description
            "The name of the problem.";
            type string;
        }

        leaf description {
          description
            "Description of the problem";
            type string;
        }

        leaf match-count {
          description
            "The matching times for the policy.";
            type uint32;
        }
      }
    }
    ntos-ext:nc-cli-cmd "security-policy-analyze";
    ntos-api:internal;
  }

  rpc security-policy {
    description
      "Show state of security policy.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";
        leaf type {
          type enumeration {
            enum policy;
            enum group;
            enum sim-policy;
            enum sim-group;
            enum sim-status;
            enum sim-switch;
            enum basic-protocol-control-enabled;
          }

          description
            "Show type of security policy.";
        }

        leaf address-type {
          type ip-family-types;
          description
            "The type of this security policy's src and dst address.";
        }

        leaf filter {
          type string;
          description
            "Filter.";
        }

        leaf name {
          type string;
          description
            "Show security policy by name";
        }

        leaf start {
          type uint32;
          description
            "Start offset of result.";
        }

        leaf end {
          type uint32;
          description
            "End offset of result.";
        }

        leaf group-name {
          type ntos-types:ntos-obj-name-type;
          description
            "Filter by group name.";
        }

        leaf policy-id {
          type string;
          description
            "Filter by policy id";
        }

        leaf permit-all-policy {
          type empty;
          description
            "Show all permit policy";
        }

        list exact-params {
          key field;
          leaf field {
            type enumeration {
              enum name;
              enum group-name;
              enum descript;
              enum service-obj;
              enum source-network;
              enum dest-network;
              enum source-zone;
              enum dest-zone;
              enum source-interface;
              enum dest-interface;
              enum app;
              enum source-region;
              enum dest-region;
              enum username;
              enum time-range;
              enum enabled;
              enum keep-alive;
              enum config-source;
              enum content-security;
              enum first-match-time;
              enum create-time;
              enum last-match-time;
              enum action;
            }
          }

          leaf exact-filter {
            type string;
          }
        }
      }
    }

    output {
      leaf sim-status {
        description
          "The status of simulative security policy.";
        type string;
      }
      leaf sim-switch {
        description
          "The status of switch in simulative security policy.";
        type string;
      }
      leaf sim-run-time-set {
        description
          "Maximum running time of simulative security policy.";
        type string;
      }
      leaf sim-running-time {
        description
          "Running time of simulative security policy.";
        type string;
      }
      leaf sim-remain-time {
        description
          "Remaining time of simulative security policy.";
        type string;
      }
      leaf sim-policy-need-update {
        description
          "The flag of simulative security policy whether need to update.";
        type boolean;
      }
      leaf sim-info {
        description
          "Running info of simulative security policy.";
        type string;
      }
      leaf basic-protocol-control-enabled {
        description
          "The flag of basic protocol control by policy.";
        type boolean;
      }
      leaf group-total {
        description
          "Total number of security policy group.";
        type uint32;
      }
      leaf policy-total {
        description
        "Total number of security policy.";
        type uint32;
      }
      list group {
        description
          "The group of security policy.";
        key "name";

        leaf name {
          description
            "The name of security policy group.";
          type ntos-types:ntos-obj-name-type;
        }
        leaf num {
          description
            "Total number of security policy in current group.";
          type uint32;
        }
      }
      list policy {
        key "name";
        description
          "The detail of security policy.";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of security policy.";
        }

        leaf policy-id {
          type uint64;
          description
            "The id of security policy.";
        }

        leaf position-id {
          type uint32;

          description
            "The position id of security policy.";
        }

        leaf enabled {
          type boolean;
          description
            "Enable or disable security policy.";
        }

        leaf address-type {
          type string;
          description
            "The type of this security policy's src and dst address.";
        }

        leaf time-range-enabled {
          type boolean;
          description
            "Enable or disable security policy by time range.";
        }

        leaf description {
          type string;
          description
            "The description of security policy.";
        }

        leaf group-name {
          type ntos-types:ntos-obj-name-type;
          description
            "Name of the group which current policy belongs to.";
        }


        list source-zone {
          description
            "The name of source zone.";
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        list dest-zone {
          description
            "The name of destination zone.";
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        list source-interface {
          description
            "The name of source interface.";
          key "name";
          leaf name {
            type ntos-types:ifname;
          }
        }

        list dest-interface {
          description
            "The name of destination interface.";
          key "name";
          leaf name {
            type ntos-types:ifname;
          }
        }

        list source-network {
          description
            "The name of source network.";
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        list dest-network {
          description
            "The name of destination network.";
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        list source-region {
          description
            "The name of source region.";
          key "name";
          leaf name {
            type ntos-region-obj:region-obj-name;
          }
          leaf desc_name {
            type string;
            description
              "Transformed name according to the current language.";
          }
        }

        list dest-region {
          description
            "The name of destination region.";
          key "name";
          leaf name {
            type ntos-region-obj:region-obj-name;
          }
          leaf desc_name {
            type string;
            description
              "Transformed name according to the current language.";
          }
        }

        list service {
          description
            "The name of service.";
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        uses user-item;
        list app {
          description
            "Tne name of application.";
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
          leaf name-i18n {
            type ntos-types:ntos-obj-name-type;
            description
              "Indicate the name of application for internationalization.";
          }
        }

        leaf time-range {
          description
            "The name of time range.";
            type ntos-types:ntos-obj-name-type;
        }

        leaf action {
            type action-type;
            description
            "The action of controlling session.";
        }

        leaf ips {
          description
            "The name of the IPS template";

          type ntos-types:ntos-obj-name-type;
        }

        leaf av {
          description
            "The name of the AV template";

          type ntos-types:ntos-obj-name-type;
        }

        leaf url {
          description
              "The name of the URL template";
          type ntos-types:ntos-obj-name-type;
        }

        leaf websec {
          description
              "The name of the WEBSEC template";
          type ntos-types:ntos-obj-name-type;
        }

        leaf content-filter {
          description
            "The name of the Content template";
          type ntos-types:ntos-obj-name-type;
        }

        leaf file-filter {
          description
              "The name of the FILE-FILTER template";
          type ntos-types:ntos-obj-name-type;
        }

        leaf match-count {
            type uint64;
            description
              "The matching times for the policy.";
        }

        leaf session-timeout {
            type uint32;
            description
            "Timeout of the session aging that matches the current security policy.";
        }

        leaf config-source {
          type config-source-type;
          description
            "Source of security policy.";
        }

        leaf create-time {
          description
            "Policy creation time.";
          type string;
        }

        leaf first-match-time {
          description
            "The first match time of policy";
          type string;
        }

        leaf last-match-time {
          description
            "The last match time of policy.";
          type string;
        }
      }
    }

    ntos-ext:nc-cli-show "security-policy";
    ntos-api:internal;
  }

  rpc export-configuration {
    description
      "Export configurations related to security policy.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
      leaf selected {
        type string;
        description
          "Security policys need to be exported.";
      }
      leaf selected-group {
        type string;
        description
          "Security policys group need to be exported.";
      }
      leaf path {
        type string {
          pattern '[a-zA-Z0-9._\-/]+';
        }
        description
          "The path of save configurations.";
      }
      leaf file-name {
        type string {
          pattern '[a-zA-Z0-9._\-/]+';
        }
        description
          "The name must be config-conversion-{yyyyMMddHHmmssSSS}.csv.";
      }
      leaf result {
        type empty;
        description
          "Show last result of export configuration.";
      }
      leaf option {
        type enumeration {
          enum relate;
          enum all;
        }
        default relate;
        description
          "Export related object or all object.";
      }
    }
    output {
      leaf info {
        description
          "Info of command result";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "security-policy-export-config";
    ntos-api:internal;
  }

  rpc import-configuration {
    description
      "Import configurations related to security policy.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
      leaf path {
        type string {
          pattern '[a-zA-Z0-9._\-/]+.csv';
        }
        description
          "The path of configurations, '.csv' files are supported.";
      }
      leaf result {
        type empty;
        description
          "Show last result of import configuration.";
      }
      leaf type {
        type enumeration {
          enum warning;
          enum ignore;
          enum cover;
        }
        description
          "Specifies how conflicts are handled.";
      }
    }
    output {
      leaf info {
        description
          "Info of command result";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "security-policy-import-config";
    ntos-api:internal;
  }

  rpc security-policy-exist-check {
    description
      "Check whether the security policy exists.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      leaf type {
        type enumeration {
          enum policy;
          enum sim-policy;
        }
        description
          "Policy type.";
      }

      leaf name {
        type string;
        description
          "The name of policy, Use ', 'to split when more that one policy,
          e.g.'policy1,policy2,policy3'";
      }
    }
    output {
      list security-policy {
        key name;
        leaf name {
          description
            "The name of security pplicy";
          type string;
        }
        leaf exist {
          description
            "The result of whether or not the security policy exists.";
          type boolean;
        }
      }
    }
    ntos-ext:nc-cli-cmd "security-policy-exist-check";
    ntos-api:internal;
  }
}
