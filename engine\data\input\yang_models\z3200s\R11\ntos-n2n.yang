module ntos-n2n {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:n2n";
  prefix ntos-n2n;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
 import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co., Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS peer-to-peer networking module.";

  revision 2024-10-31 {
    description
      "NTOS N2N.";
  }

  identity n2n {
    base ntos-types:SERVICE_LOG_ID;
    description
      "N2N service.";
  }

  typedef address-type {
    type enumeration {
      enum v4;
      enum v6;
    }
  }

  grouping node-detail {
    leaf type {
      description
        "Supernode type.";
      type address-type;
    }

    leaf address {
      type union {
        type ntos-inet:ipv4-filter-invalid-address;
        type ntos-inet:ipv6-address;
        type ntos-inet:domain-name {
          ntos-ext:nc-cli-shortdesc "<fqdn>";
        }
      }
      description
        "Supernode domain address.";
    }

    leaf port {
      type ntos-inet:port-number;
      description
        "Supernode port.";
    }

    leaf ip {
      type union {
        type ntos-inet:ipv4-filter-invalid-address;
        type ntos-inet:ipv6-address;
      }
      description
        "The IPv4 or IPv6 address.";
    }
  }

  grouping topology-detail {
    leaf type {
      description
        "Topology type.";
      type enumeration {
        enum hub-spoke;
        enum full-mesh;
        enum partial-mesh;
      }
    }

    choice interconnection-type {
      case allow-interconnection {
        list whitelist {
          key "mac-address";
          leaf mac-address {
            description
              "white list mac address.";
            type ntos-if:mac-address;
          }
        }
      }

      case deny-interconnection {
        list blacklist {
          key "mac-address";
          leaf mac-address {
            description
              "white list mac address.";
            type ntos-if:mac-address;
          }
        }
      }
    }
  }

  grouping wan-policy-detail {
    leaf policy-mode {
      description
        "WAN policy.";
      type enumeration {
        enum auto;
        enum backup;
        enum load;
      }
    }

    list interface {
      key "name";
      leaf name {
        type ntos-types:ifname;
        description
            "Interface name.";
      }
    }

    list master-interface {
      key "name";
      leaf name {
        type ntos-types:ifname;
        description
            "Interface name.";
      }
    }

    list backup-interface {
      key "name";
      leaf name {
        type ntos-types:ifname;
        description
            "Interface name.";
      }
    }

    list load-interface {
      key "name";
      leaf name {
        type ntos-types:ifname;
        description
            "Interface name.";
      }

      leaf level {
        description
            "Load level.";
        type uint32 {
            range "1..10";
        }
      }
    }
  }

  grouping detect-policy-detail {
    leaf enabled {
      description
        "Enabled detect policy.";
      type boolean;
      default true;
    }

    leaf interval {
      description
        "Detect interval.";
      type uint32 {
        range "1..120000";
        ntos-ext:nc-cli-shortdesc "<1-120000>";
      }
      default 5000;
    }

    leaf timeout {
      description
        "Detection timeout, default 3000ms.";
      type uint32 {
        range "1..60000";
        ntos-ext:nc-cli-shortdesc "<1-60000>";
      }
      default 3000;
    }
  }

  grouping n2n-config-detail {
    leaf enabled {
      type boolean;
      description
        "Enable/Disable N2N service.";
    }

    leaf community {
      type string;
      description
        "The name of the community.";
    }

    leaf share-key {
      type string;
      description
        "N2N share key. requires secondary encryption.";
    }

    leaf trace-level {
      type uint32 {
        range "0..4";
        ntos-ext:nc-cli-shortdesc "<0-4>";
      }
      description
        "N2N trace level.";
    }

    leaf alias {
      type ntos-types:ntos-obj-name-type;
      description
        "The alias of the device.";
    }

    leaf temporary-mac {
      description
            "temporary leaf.";
      type ntos-if:mac-address;
    }

    leaf crypto {
      type enumeration {
        enum AES;
        enum CC20;
        enum SPECK;
        enum TWOFISH;
      }
      default CC20;
      description
        "The type of encryption algorithm to use.";
    }

    leaf username {
      type string;
      description
        "N2N user name.";
    }

    leaf password {
      type string;
      description
        "User password, 6-15 characters.";
    }

    leaf vpn-port-v4 {
      type ntos-inet:port-number;
      description
        "N2N local-endpoint ipv4.";
      default 0;
    }

    leaf vpn-port-v6 {
      type ntos-inet:port-number;
      description
        "N2N local-endpoint ipv6.";
      default 0;
    }

    list supernode {
        key "address";
        uses node-detail;
      }

    list forward-supernode{
      key "address";
      uses node-detail;
    }

    list stun-server {
      key "address";
      uses node-detail;
    }

    list dns-server {
      key "address";
      leaf type {
        description
          "Supernode type.";
        type address-type;
      }

      leaf address {
        type union {
          type ntos-inet:ipv4-filter-invalid-address;
          type ntos-inet:ipv6-address;
        }
        description
          "DNS server ip address.";
      }
    }

    container wan-policy {
      uses wan-policy-detail;
    }

    container topology {
      uses topology-detail;
    }

    leaf hang-side {
      description
        "N2N hand side. default is not hang side";
      type boolean;
      default false;
    }

    container flow-control {
      leaf enabled {
        description
          "Flow control enabled.";
        type boolean;
        default true;
      }

      leaf rate {
        description
          "Flow control rate.";
        type uint32 {
          range "1..4294967295";
          ntos-ext:nc-cli-shortdesc "<1-4294967295>";
        }
        default 100;
      }

      leaf rate-unit {
        description
          "Flow control rate unit.";
        type enumeration {
          enum kbps;
          enum mbps;
        }
        default kbps;
      }
    }

    container detect-policy {
      uses detect-policy-detail;
    }

    container nat-map {
      leaf-list addr-pool {
        type string;
        description
          "Sdwan nat policy address pool.";
      }
      leaf-list nat-list {
        type string;
        description
          "Sdwan nat list, source address and destination address split with line-through.";
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    container n2n-config {
      uses n2n-config-detail;
    }
  }

  rpc get-n2n-configuration {
    description
      "get n2n configuration.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
        default "main";
      }
    }

    output {
      container n2n-config {
        uses n2n-config-detail;
      }
    }
    ntos-ext:nc-cli-show "n2n configuration";
    ntos-api:internal;
  }

  rpc get-sdwan-policy {
    description "Show the name of the sdwan policy.";
    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }

      leaf policy-id {
        type string;
        description "Indicate the ID list of sdwan policy.";
      }
    }

    output {
      list policy-name {
        description "The sdwan policy name list.";

        leaf id {
          type uint32;
          description "The searched id.";
        }

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of the sdwan policy.";
        }
      }
    }
    ntos-ext:nc-cli-show "n2n sdwan-policy";
    ntos-api:internal;
  }
}
