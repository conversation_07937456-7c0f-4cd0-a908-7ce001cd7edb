2025-08-08 14:45:14 - INFO - Log initialization complete
2025-08-08 14:45:14 - WARNING - Message is missing parameters: language
2025-08-08 14:45:14 - INFO - 多厂商配置转换工具已启动，模式: convert, 厂商: fortigate, 语言: zh-CN
2025-08-08 14:45:14 - INFO - 工具启动，模式: convert, 厂商: fortigate, 语言: zh-CN
2025-08-08 14:45:15 - INFO - 编码设置: PYTHONIOENCODING=utf-8
2025-08-08 14:45:15 - INFO - 区域设置: LANG=zh-CN.UTF-8, LC_ALL=zh-CN.UTF-8
2025-08-08 14:45:15 - INFO - 开始转换配置文件
2025-08-08 14:45:15 - INFO - 输入文件: FortiGate-100F_7-6_3510_202505161613.conf
2025-08-08 14:45:15 - INFO - 映射文件: mappings/interface_mapping_FortiGate-100F_7-6_3510_202505161613.json
2025-08-08 14:45:15 - INFO - 目标型号: z3200s
2025-08-08 14:45:15 - INFO - 目标版本: R11
2025-08-08 14:45:15 - INFO - 输出文件: output/test_fix_output.xml
2025-08-08 14:45:15 - INFO - 开始转换配置文件
2025-08-08 14:45:15 - INFO - 输入文件: FortiGate-100F_7-6_3510_202505161613.conf
2025-08-08 14:45:15 - INFO - 映射文件: mappings/interface_mapping_FortiGate-100F_7-6_3510_202505161613.json
2025-08-08 14:45:15 - INFO - 目标型号: z3200s
2025-08-08 14:45:15 - INFO - 目标版本: R11
2025-08-08 14:45:15 - INFO - 输出文件: output/test_fix_output.xml
2025-08-08 14:45:15 - INFO - NTOS内置服务已加载: 82 个
2025-08-08 14:45:15 - INFO - 厂商映射已加载: fortigate，数量: 216
2025-08-08 14:45:15 - INFO - 服务映射已加载: 104 个
2025-08-08 14:45:15 - INFO - 服务别名已加载: 69 个
2025-08-08 14:45:15 - INFO - name_mapping_manager.initialized
2025-08-08 14:45:15 - INFO - name_mapping_manager.initialized
2025-08-08 14:45:15 - INFO - 转换前验证配置
2025-08-08 14:45:15 - INFO - 转换前验证配置
2025-08-08 14:45:15 - INFO - 开始验证fortigate配置文件: FortiGate-100F_7-6_3510_202505161613.conf
2025-08-08 14:45:15 - INFO - 开始验证配置文件
2025-08-08 14:45:15 - INFO - 最低版本要求
2025-08-08 14:45:15 - INFO - 验证飞塔配置文件: FortiGate-100F_7-6_3510_202505161613.conf
2025-08-08 14:45:15 - INFO - 检测到FortiOS版本: 7.6.3
2025-08-08 14:45:15 - INFO - 配置文件验证通过
2025-08-08 14:45:15 - INFO - 验证通过
2025-08-08 14:45:15 - INFO - 验证通过
2025-08-08 14:45:15 - INFO - 使用新架构进行转换
2025-08-08 14:45:15 - INFO - 配置管理器：检测到开发环境
2025-08-08 14:45:15 - INFO - 配置管理器已初始化
2025-08-08 14:45:15 - INFO - 缓存管理器已初始化
2025-08-08 14:45:15 - INFO - 性能监控器已初始化
2025-08-08 14:45:15 - INFO - 缓存管理器：缓存已创建
2025-08-08 14:45:15 - INFO - 模板管理器已初始化
2025-08-08 14:45:15 - INFO - YANG管理器已初始化
2025-08-08 14:45:15 - INFO - 性能监控器已初始化
2025-08-08 14:45:15 - INFO - 内存管理器已初始化
2025-08-08 14:45:15 - INFO - 异常注册表已初始化
2025-08-08 14:45:15 - INFO - 错误处理器已初始化
2025-08-08 14:45:15 - INFO - 转换工作流程已初始化
2025-08-08 14:45:15 - INFO - 转换服务已初始化
2025-08-08 14:45:15 - INFO - 开始转换
2025-08-08 14:45:15 - INFO - 找到模板文件: E:\code\config-converter\engine\data\input\configData\z3200s\R11\Config\running.xml
2025-08-08 14:45:15 - INFO - 模板已找到
2025-08-08 14:45:15 - INFO - YANG模型已找到
2025-08-08 14:45:15 - INFO - 找到模板文件: E:\code\config-converter\engine\data\input\configData\z3200s\R11\Config\running.xml
2025-08-08 14:45:15 - INFO - 找到模板文件: E:\code\config-converter\engine\data\input\configData\z3200s\R11\Config\running.xml
2025-08-08 14:45:15 - INFO - 模板加载成功: 文件=E:\code\config-converter\engine\data\input\configData\z3200s\R11\Config\running.xml
2025-08-08 14:45:15 - WARNING - Message is missing parameters: urn:ruijie:ntos
2025-08-08 14:45:15 - WARNING - Message is missing parameter: urn
2025-08-08 14:45:15 - WARNING - Message is missing parameters: urn:ruijie:ntos
2025-08-08 14:45:15 - WARNING - Message is missing parameter: urn
2025-08-08 14:45:15 - WARNING - Message is missing parameters: urn:ruijie:ntos
2025-08-08 14:45:15 - WARNING - Message is missing parameter: urn
2025-08-08 14:45:15 - INFO - 模板加载成功: 型号=z3200s, 版本=R11, 类型=running
2025-08-08 14:45:15 - INFO - 模板准备完成: E:\code\config-converter\engine\data\input\configData\z3200s\R11\Config\running.xml
2025-08-08 14:45:15 - INFO - 找到YANG文件
2025-08-08 14:45:15 - INFO - 模式已加载
2025-08-08 14:45:16 - INFO - YANG管理器：模式已加载
2025-08-08 14:45:16 - INFO - YANG管理器：命名空间已加载
2025-08-08 14:45:16 - INFO - YANG模型准备完成: E:\code\config-converter\engine\data\input\yang_models\z3200s\R11
2025-08-08 14:45:16 - INFO - name_mapping_manager.initialized
2025-08-08 14:45:16 - INFO - 管道管理器：已初始化
2025-08-08 14:45:16 - WARNING - Interface mapping file not found: data\interface_mapping.json
2025-08-08 14:45:16 - INFO - 转换策略已初始化
2025-08-08 14:45:16 - INFO - name_mapping_manager.initialized
2025-08-08 14:45:16 - INFO - FortiGate服务映射已加载: 216 个映射关系
2025-08-08 14:45:16 - INFO - Fortigate策略阶段已初始化
2025-08-08 14:45:16 - INFO - XML模板集成阶段已初始化
2025-08-08 14:45:16 - INFO - 设置当前输出文件路径: output/test_fix_output.xml
2025-08-08 14:45:16 - INFO - 解析CLI文件: FortiGate-100F_7-6_3510_202505161613.conf
2025-08-08 14:45:16 - INFO - 开始解析配置文件
2025-08-08 14:45:16 - WARNING - Sensitive Command Detected
2025-08-08 14:45:16 - INFO - 成功读取文件，共 12361 行
2025-08-08 14:45:16 - INFO - 配置文件读取成功，共 12361 行
2025-08-08 14:45:16 - INFO - fortigate.detected_transparent_mode_from_header
2025-08-08 14:45:16 - INFO - 开始解析系统全局配置部分
2025-08-08 14:45:16 - INFO - fortigate.set_hostname
2025-08-08 14:45:16 - INFO - fortigate.set_timezone
2025-08-08 14:45:16 - INFO - fortigate.system_settings_section_end
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 开始解析接口配置部分
2025-08-08 14:45:16 - INFO - 解析接口: dmz
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口IP (掩码): **********/24
2025-08-08 14:45:16 - INFO - 设置接口访问控制: ['ping', 'https', 'fabric']
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置接口角色: dmz
2025-08-08 14:45:16 - WARNING - 警告: 接口 'dmz' 使用了不支持的角色 'dmz'，NTOS只支持lan和wan角色
2025-08-08 14:45:16 - INFO - 接口 'dmz' 的角色 'dmz' 将被转换为 'lan'
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 1
2025-08-08 14:45:16 - INFO - 完成接口 dmz 解析
2025-08-08 14:45:16 - INFO - 解析接口: mgmt
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口IP (掩码): **********/24
2025-08-08 14:45:16 - INFO - 设置接口访问控制: ['ping', 'https', 'ssh', 'snmp', 'http', 'fgfm', 'radius-acct', 'fabric', 'ftm', 'speed-test']
2025-08-08 14:45:16 - WARNING - 警告: 接口 'mgmt' 使用了不支持的访问控制方式 'scim'，NTOS只支持ping、https和ssh
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置接口角色: lan
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 2
2025-08-08 14:45:16 - INFO - 完成接口 mgmt 解析
2025-08-08 14:45:16 - INFO - 解析接口: wan1
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口模式: dhcp
2025-08-08 14:45:16 - INFO - 设置接口访问控制: ['ping']
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置接口角色: wan
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 3
2025-08-08 14:45:16 - INFO - 完成接口 wan1 解析
2025-08-08 14:45:16 - INFO - 解析接口: wan2
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口模式: dhcp
2025-08-08 14:45:16 - INFO - 设置接口访问控制: ['ping']
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置接口角色: wan
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 4
2025-08-08 14:45:16 - INFO - 完成接口 wan2 解析
2025-08-08 14:45:16 - INFO - 解析接口: ha1
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 5
2025-08-08 14:45:16 - INFO - 完成接口 ha1 解析
2025-08-08 14:45:16 - INFO - 解析接口: ha2
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 6
2025-08-08 14:45:16 - INFO - 完成接口 ha2 解析
2025-08-08 14:45:16 - INFO - 解析接口: port1
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 7
2025-08-08 14:45:16 - INFO - 完成接口 port1 解析
2025-08-08 14:45:16 - INFO - 解析接口: port2
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 8
2025-08-08 14:45:16 - INFO - 完成接口 port2 解析
2025-08-08 14:45:16 - INFO - 解析接口: port3
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 9
2025-08-08 14:45:16 - INFO - 完成接口 port3 解析
2025-08-08 14:45:16 - INFO - 解析接口: port4
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 10
2025-08-08 14:45:16 - INFO - 完成接口 port4 解析
2025-08-08 14:45:16 - INFO - 解析接口: port5
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 11
2025-08-08 14:45:16 - INFO - 完成接口 port5 解析
2025-08-08 14:45:16 - INFO - 解析接口: port6
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 12
2025-08-08 14:45:16 - INFO - 完成接口 port6 解析
2025-08-08 14:45:16 - INFO - 解析接口: port7
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 13
2025-08-08 14:45:16 - INFO - 完成接口 port7 解析
2025-08-08 14:45:16 - INFO - 解析接口: port8
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 14
2025-08-08 14:45:16 - INFO - 完成接口 port8 解析
2025-08-08 14:45:16 - INFO - 解析接口: port9
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 15
2025-08-08 14:45:16 - INFO - 完成接口 port9 解析
2025-08-08 14:45:16 - INFO - 解析接口: port10
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 16
2025-08-08 14:45:16 - INFO - 完成接口 port10 解析
2025-08-08 14:45:16 - INFO - 解析接口: port11
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置接口角色: wan
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 17
2025-08-08 14:45:16 - INFO - 完成接口 port11 解析
2025-08-08 14:45:16 - INFO - 解析接口: port12
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 18
2025-08-08 14:45:16 - INFO - 完成接口 port12 解析
2025-08-08 14:45:16 - INFO - 解析接口: x1
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 19
2025-08-08 14:45:16 - INFO - 完成接口 x1 解析
2025-08-08 14:45:16 - INFO - 解析接口: x2
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口状态: down
2025-08-08 14:45:16 - INFO - 设置接口启用状态: False
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 20
2025-08-08 14:45:16 - INFO - 完成接口 x2 解析
2025-08-08 14:45:16 - INFO - 解析接口: port13
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 21
2025-08-08 14:45:16 - INFO - 完成接口 port13 解析
2025-08-08 14:45:16 - INFO - 解析接口: port14
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 22
2025-08-08 14:45:16 - INFO - 完成接口 port14 解析
2025-08-08 14:45:16 - INFO - 解析接口: port15
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 23
2025-08-08 14:45:16 - INFO - 完成接口 port15 解析
2025-08-08 14:45:16 - INFO - 解析接口: port16
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 24
2025-08-08 14:45:16 - INFO - 完成接口 port16 解析
2025-08-08 14:45:16 - INFO - 解析接口: port17
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 25
2025-08-08 14:45:16 - INFO - 完成接口 port17 解析
2025-08-08 14:45:16 - INFO - 解析接口: port18
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 26
2025-08-08 14:45:16 - INFO - 完成接口 port18 解析
2025-08-08 14:45:16 - INFO - 解析接口: port19
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 27
2025-08-08 14:45:16 - INFO - 完成接口 port19 解析
2025-08-08 14:45:16 - INFO - 解析接口: port20
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 28
2025-08-08 14:45:16 - INFO - 完成接口 port20 解析
2025-08-08 14:45:16 - INFO - 解析接口: modem
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口模式: pppoe
2025-08-08 14:45:16 - INFO - 设置接口状态: down
2025-08-08 14:45:16 - INFO - 设置接口启用状态: False
2025-08-08 14:45:16 - INFO - 设置接口类型: physical
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 29
2025-08-08 14:45:16 - INFO - 完成接口 modem 解析
2025-08-08 14:45:16 - INFO - 解析接口: naf.root
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: tunnel
2025-08-08 14:45:16 - INFO - 信息: 接口 'naf.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 30
2025-08-08 14:45:16 - INFO - 完成接口 naf.root 解析
2025-08-08 14:45:16 - INFO - 解析接口: l2t.root
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: tunnel
2025-08-08 14:45:16 - INFO - 信息: 接口 'l2t.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 31
2025-08-08 14:45:16 - INFO - 完成接口 l2t.root 解析
2025-08-08 14:45:16 - INFO - 解析接口: ssl.root
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: tunnel
2025-08-08 14:45:16 - INFO - 信息: 接口 'ssl.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 32
2025-08-08 14:45:16 - INFO - 完成接口 ssl.root 解析
2025-08-08 14:45:16 - INFO - 解析接口: t1
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: switch
2025-08-08 14:45:16 - INFO - 信息: 接口 't1' 类型从 'switch' 转换为 'physical' 以兼容NTOS
2025-08-08 14:45:16 - INFO - 设置接口角色: lan
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 35
2025-08-08 14:45:16 - INFO - 完成接口 t1 解析
2025-08-08 14:45:16 - INFO - 解析接口: t2
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口类型: switch
2025-08-08 14:45:16 - INFO - 信息: 接口 't2' 类型从 'switch' 转换为 'physical' 以兼容NTOS
2025-08-08 14:45:16 - INFO - 设置接口角色: lan
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 36
2025-08-08 14:45:16 - INFO - 完成接口 t2 解析
2025-08-08 14:45:16 - INFO - 解析接口: lan
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口IP (掩码): **************/24
2025-08-08 14:45:16 - INFO - 设置接口访问控制: ['ping', 'https', 'ssh', 'fabric']
2025-08-08 14:45:16 - INFO - 设置接口类型: hard-switch
2025-08-08 14:45:16 - WARNING - 警告: 接口 'lan' 类型 'hard-switch' 不受支持，NTOS只支持物理接口和VLAN子接口
2025-08-08 14:45:16 - INFO - 设置接口角色: lan
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 33
2025-08-08 14:45:16 - INFO - 完成接口 lan 解析
2025-08-08 14:45:16 - INFO - 解析接口: fortilink
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口IP (掩码): **********/24
2025-08-08 14:45:16 - INFO - 设置接口访问控制: ['ping', 'fabric']
2025-08-08 14:45:16 - INFO - 设置接口类型: aggregate
2025-08-08 14:45:16 - INFO - 信息: 接口 'fortilink' 类型从 'aggregate' 转换为 'physical' 以兼容NTOS
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 34
2025-08-08 14:45:16 - INFO - 完成接口 fortilink 解析
2025-08-08 14:45:16 - INFO - 解析接口: testppp91
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口模式: pppoe
2025-08-08 14:45:16 - INFO - 设置接口角色: lan
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 37
2025-08-08 14:45:16 - INFO - 设置PPPoE用户名: asdf
2025-08-08 14:45:16 - INFO - 设置PPPoE固定密码 (原密码已替换为固定值)
2025-08-08 14:45:16 - WARNING - 注意：PPPoE密码已设置为固定值'123456'，请在部署后手动修改为实际密码
2025-08-08 14:45:16 - INFO - 设置子接口所属的物理接口: port9
2025-08-08 14:45:16 - INFO - 设置子接口VLAN ID: 91
2025-08-08 14:45:16 - INFO - 完成接口 testppp91 解析
2025-08-08 14:45:16 - INFO - 解析接口: testppp92
2025-08-08 14:45:16 - INFO - 设置接口虚拟域: root
2025-08-08 14:45:16 - INFO - 设置接口模式: pppoe
2025-08-08 14:45:16 - INFO - 设置接口角色: lan
2025-08-08 14:45:16 - INFO - 设置SNMP索引: 38
2025-08-08 14:45:16 - INFO - 设置PPPoE用户名: user92
2025-08-08 14:45:16 - INFO - 设置PPPoE固定密码 (原密码已替换为固定值)
2025-08-08 14:45:16 - WARNING - 注意：PPPoE密码已设置为固定值'123456'，请在部署后手动修改为实际密码
2025-08-08 14:45:16 - INFO - 设置子接口所属的物理接口: port9
2025-08-08 14:45:16 - INFO - 设置子接口VLAN ID: 92
2025-08-08 14:45:16 - INFO - 完成接口 testppp92 解析
2025-08-08 14:45:16 - INFO - 接口配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - WARNING - 警告: 行 521 包含可疑字符，可能存在命令注入风险: set name "Assets & Identities"
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:16 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 开始解析DNS配置部分
2025-08-08 14:45:17 - INFO - 设置主DNS服务器: 96.45.45.45
2025-08-08 14:45:17 - INFO - 设置辅DNS服务器: 96.45.46.46
2025-08-08 14:45:17 - INFO - 设置DNS协议: dot
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:17 - INFO - 未知 配置部分结束
2025-08-08 14:45:18 - WARNING - 警告: 行 3052 包含可疑字符，可能存在命令注入风险: edit "Botnet-C&C.Server"
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 开始解析系统日志配置部分
2025-08-08 14:45:28 - INFO - 设置系统日志 syslogd setting 状态: enable
2025-08-08 14:45:28 - INFO - 设置系统日志服务器:  "10.96.30.4
2025-08-08 14:45:28 - INFO - 系统日志 syslogd setting 解析完成
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:28 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - WARNING - 警告: 行 6503 包含可疑字符，可能存在命令注入风险: edit "AV & IPS DB update"
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:29 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 未知 配置部分结束
2025-08-08 14:45:30 - INFO - 开始解析系统设置配置部分
2025-08-08 14:45:30 - INFO - fortigate.system_settings_section_end
2025-08-08 14:45:30 - INFO - Start Parsing Dhcp Server
2025-08-08 14:45:30 - INFO - Parsing Dhcp Server
2025-08-08 14:45:30 - INFO - Set Dhcp Default Gateway
2025-08-08 14:45:30 - INFO - Set Dhcp Netmask
2025-08-08 14:45:30 - INFO - Set Dhcp Interface
2025-08-08 14:45:30 - INFO - Parsing Dhcp Ip Range
2025-08-08 14:45:30 - INFO - Set Dhcp Start Ip
2025-08-08 14:45:30 - INFO - Set Dhcp End Ip
2025-08-08 14:45:30 - INFO - 开始解析区域配置部分
2025-08-08 14:45:30 - INFO - 解析区域: trust-ha
2025-08-08 14:45:30 - INFO - 设置区域 trust-ha 的接口: ['ha1']
2025-08-08 14:45:30 - WARNING - Message is missing parameter: 'name'
2025-08-08 14:45:30 - INFO - 完成区域 trust-ha 解析，包含 1 个接口
2025-08-08 14:45:30 - INFO - 开始解析地址对象配置部分
2025-08-08 14:45:30 - INFO - 正在解析地址对象: EMS_ALL_UNKNOWN_CLIENTS
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 EMS_ALL_UNKNOWN_CLIENTS 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: EMS_ALL_UNMANAGEABLE_CLIENTS
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 EMS_ALL_UNMANAGEABLE_CLIENTS 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: none
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 none 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: login.microsoftonline.com
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 login.microsoftonline.com 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: login.microsoft.com
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 login.microsoft.com 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: login.windows.net
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 login.windows.net 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: gmail.com
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 gmail.com 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: wildcard.google.com
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - WARNING - 警告: 地址对象 'wildcard.google.com' 的FQDN '*.google.com' 格式可能不正确
2025-08-08 14:45:30 - INFO - 完成地址对象 wildcard.google.com 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: wildcard.dropbox.com
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - WARNING - 警告: 地址对象 'wildcard.dropbox.com' 的FQDN '*.dropbox.com' 格式可能不正确
2025-08-08 14:45:30 - INFO - 完成地址对象 wildcard.dropbox.com 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: all
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 all 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: FIREWALL_AUTH_PORTAL_ADDRESS
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 FIREWALL_AUTH_PORTAL_ADDRESS 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: FABRIC_DEVICE
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 FABRIC_DEVICE 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: SSLVPN_TUNNEL_ADDR1
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 SSLVPN_TUNNEL_ADDR1 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: dmz
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 dmz 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: lan
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 lan 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: FCTEMS_ALL_FORTICLOUD_SERVERS
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 FCTEMS_ALL_FORTICLOUD_SERVERS 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: t1
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 t1 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: t2
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 t2 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: bad11
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 bad11 解析
2025-08-08 14:45:30 - INFO - 正在解析地址对象: a4
2025-08-08 14:45:30 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:30 - INFO - 完成地址对象 a4 解析
2025-08-08 14:45:30 - INFO - address 配置部分结束
2025-08-08 14:45:31 - INFO - 未知 配置部分结束
2025-08-08 14:45:31 - INFO - 开始解析地址对象配置部分
2025-08-08 14:45:31 - INFO - 正在解析地址对象: SSLVPN_TUNNEL_IPv6_ADDR1
2025-08-08 14:45:31 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:31 - INFO - 完成地址对象 SSLVPN_TUNNEL_IPv6_ADDR1 解析
2025-08-08 14:45:31 - INFO - 正在解析地址对象: all
2025-08-08 14:45:31 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:31 - INFO - 完成地址对象 all 解析
2025-08-08 14:45:31 - INFO - 正在解析地址对象: none
2025-08-08 14:45:31 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:31 - INFO - 完成地址对象 none 解析
2025-08-08 14:45:31 - INFO - address 配置部分结束
2025-08-08 14:45:31 - INFO - 未知 配置部分结束
2025-08-08 14:45:31 - INFO - 开始解析地址组配置部分
2025-08-08 14:45:31 - INFO - 解析地址组: G
2025-08-08 14:45:31 - INFO - 设置地址组成员数: 2
2025-08-08 14:45:31 - INFO - 完成地址组 G 解析，包含 2 个成员
2025-08-08 14:45:31 - INFO - 解析地址组: Microsoft
2025-08-08 14:45:31 - INFO - 设置地址组成员数: 3
2025-08-08 14:45:31 - INFO - 完成地址组 Microsoft 解析，包含 3 个成员
2025-08-08 14:45:31 - INFO - 未知 配置部分结束
2025-08-08 14:45:31 - WARNING - 警告: 行 7159 包含可疑字符，可能存在命令注入风险: edit "VoIP, Messaging & Other Applications"
2025-08-08 14:45:31 - INFO - 未知 配置部分结束
2025-08-08 14:45:31 - INFO - 开始解析自定义服务配置部分
2025-08-08 14:45:31 - INFO - 解析自定义服务: ALL
2025-08-08 14:45:31 - INFO - Setting service protocol: IP
2025-08-08 14:45:31 - INFO - 完成自定义服务 ALL 解析
2025-08-08 14:45:31 - INFO - 解析自定义服务: FTP
2025-08-08 14:45:31 - INFO - 设置服务TCP端口范围: 21
2025-08-08 14:45:31 - INFO - 完成自定义服务 FTP 解析
2025-08-08 14:45:31 - INFO - 解析自定义服务: FTP_GET
2025-08-08 14:45:31 - INFO - 设置服务TCP端口范围: 21
2025-08-08 14:45:31 - INFO - 完成自定义服务 FTP_GET 解析
2025-08-08 14:45:31 - INFO - 解析自定义服务: FTP_PUT
2025-08-08 14:45:31 - INFO - 设置服务TCP端口范围: 21
2025-08-08 14:45:31 - INFO - 完成自定义服务 FTP_PUT 解析
2025-08-08 14:45:31 - INFO - 解析自定义服务: ALL_TCP
2025-08-08 14:45:31 - INFO - 设置服务TCP端口范围: 1-65535
2025-08-08 14:45:31 - INFO - 完成自定义服务 ALL_TCP 解析
2025-08-08 14:45:31 - INFO - 解析自定义服务: ALL_UDP
2025-08-08 14:45:31 - INFO - 设置服务UDP端口范围: 1-65535
2025-08-08 14:45:31 - INFO - 完成自定义服务 ALL_UDP 解析
2025-08-08 14:45:31 - INFO - 解析自定义服务: ALL_ICMP
2025-08-08 14:45:31 - INFO - Setting service protocol: ICMP
2025-08-08 14:45:31 - INFO - 完成自定义服务 ALL_ICMP 解析
2025-08-08 14:45:31 - INFO - 解析自定义服务: ALL_ICMP6
2025-08-08 14:45:31 - INFO - Setting service protocol: ICMP6
2025-08-08 14:45:31 - INFO - 完成自定义服务 ALL_ICMP6 解析
2025-08-08 14:45:31 - INFO - 解析自定义服务: GRE
2025-08-08 14:45:31 - INFO - Setting service protocol: IP
2025-08-08 14:45:31 - INFO - fortigate.set_service_protocol_number
2025-08-08 14:45:31 - INFO - 完成自定义服务 GRE 解析
2025-08-08 14:45:31 - INFO - 解析自定义服务: AH
2025-08-08 14:45:31 - INFO - Setting service protocol: IP
2025-08-08 14:45:31 - INFO - fortigate.set_service_protocol_number
2025-08-08 14:45:31 - INFO - 完成自定义服务 AH 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: ESP
2025-08-08 14:45:32 - INFO - Setting service protocol: IP
2025-08-08 14:45:32 - INFO - fortigate.set_service_protocol_number
2025-08-08 14:45:32 - INFO - 完成自定义服务 ESP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: AOL
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 5190-5194
2025-08-08 14:45:32 - INFO - 完成自定义服务 AOL 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: BGP
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 179
2025-08-08 14:45:32 - INFO - 完成自定义服务 BGP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: DHCP
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 67-68
2025-08-08 14:45:32 - INFO - 完成自定义服务 DHCP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: DNS
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 53
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 53
2025-08-08 14:45:32 - INFO - 完成自定义服务 DNS 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: FINGER
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 79
2025-08-08 14:45:32 - INFO - 完成自定义服务 FINGER 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: GOPHER
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 70
2025-08-08 14:45:32 - INFO - 完成自定义服务 GOPHER 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: H323
2025-08-08 14:45:32 - WARNING - 警告: 行 7259 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 1720 1503
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 1719
2025-08-08 14:45:32 - INFO - 完成自定义服务 H323 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: HTTP
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 80
2025-08-08 14:45:32 - INFO - 完成自定义服务 HTTP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: HTTPS
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 443
2025-08-08 14:45:32 - INFO - 完成自定义服务 HTTPS 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: IKE
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 500 4500
2025-08-08 14:45:32 - INFO - 完成自定义服务 IKE 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: IMAP
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 143
2025-08-08 14:45:32 - INFO - 完成自定义服务 IMAP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: IMAPS
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 993
2025-08-08 14:45:32 - INFO - 完成自定义服务 IMAPS 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: Internet-Locator-Service
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 389
2025-08-08 14:45:32 - INFO - 完成自定义服务 Internet-Locator-Service 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: IRC
2025-08-08 14:45:32 - WARNING - 警告: 行 7294 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 6660-6669
2025-08-08 14:45:32 - INFO - 完成自定义服务 IRC 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: L2TP
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 1701
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 1701
2025-08-08 14:45:32 - INFO - 完成自定义服务 L2TP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: LDAP
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 389
2025-08-08 14:45:32 - INFO - 完成自定义服务 LDAP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: NetMeeting
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 1720
2025-08-08 14:45:32 - INFO - 完成自定义服务 NetMeeting 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: NFS
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 111 2049
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 111 2049
2025-08-08 14:45:32 - INFO - 完成自定义服务 NFS 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: NNTP
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 119
2025-08-08 14:45:32 - INFO - 完成自定义服务 NNTP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: NTP
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 123
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 123
2025-08-08 14:45:32 - INFO - 完成自定义服务 NTP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: OSPF
2025-08-08 14:45:32 - INFO - Setting service protocol: IP
2025-08-08 14:45:32 - INFO - fortigate.set_service_protocol_number
2025-08-08 14:45:32 - INFO - 完成自定义服务 OSPF 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: PC-Anywhere
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 5631
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 5632
2025-08-08 14:45:32 - INFO - 完成自定义服务 PC-Anywhere 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: PING
2025-08-08 14:45:32 - INFO - Setting service protocol: ICMP
2025-08-08 14:45:32 - INFO - fortigate.set_service_icmptype
2025-08-08 14:45:32 - INFO - 完成自定义服务 PING 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: TIMESTAMP
2025-08-08 14:45:32 - INFO - Setting service protocol: ICMP
2025-08-08 14:45:32 - INFO - fortigate.set_service_icmptype
2025-08-08 14:45:32 - INFO - 完成自定义服务 TIMESTAMP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: INFO_REQUEST
2025-08-08 14:45:32 - INFO - Setting service protocol: ICMP
2025-08-08 14:45:32 - INFO - fortigate.set_service_icmptype
2025-08-08 14:45:32 - INFO - 完成自定义服务 INFO_REQUEST 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: INFO_ADDRESS
2025-08-08 14:45:32 - INFO - Setting service protocol: ICMP
2025-08-08 14:45:32 - INFO - fortigate.set_service_icmptype
2025-08-08 14:45:32 - INFO - 完成自定义服务 INFO_ADDRESS 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: ONC-RPC
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 111
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 111
2025-08-08 14:45:32 - INFO - 完成自定义服务 ONC-RPC 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: DCE-RPC
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 135
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 135
2025-08-08 14:45:32 - INFO - 完成自定义服务 DCE-RPC 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: POP3
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 110
2025-08-08 14:45:32 - INFO - 完成自定义服务 POP3 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: POP3S
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 995
2025-08-08 14:45:32 - INFO - 完成自定义服务 POP3S 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: PPTP
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 1723
2025-08-08 14:45:32 - INFO - 完成自定义服务 PPTP 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: QUAKE
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 26000 27000 27910 27960
2025-08-08 14:45:32 - INFO - 完成自定义服务 QUAKE 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: RAUDIO
2025-08-08 14:45:32 - INFO - 设置服务UDP端口范围: 7070
2025-08-08 14:45:32 - INFO - 完成自定义服务 RAUDIO 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: REXEC
2025-08-08 14:45:32 - INFO - 设置服务TCP端口范围: 512
2025-08-08 14:45:32 - INFO - 完成自定义服务 REXEC 解析
2025-08-08 14:45:32 - INFO - 解析自定义服务: RIP
2025-08-08 14:45:33 - INFO - 设置服务UDP端口范围: 520
2025-08-08 14:45:33 - INFO - 完成自定义服务 RIP 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: RLOGIN
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 513:512-1023
2025-08-08 14:45:33 - INFO - 完成自定义服务 RLOGIN 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: RSH
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 514:512-1023
2025-08-08 14:45:33 - INFO - 完成自定义服务 RSH 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: SCCP
2025-08-08 14:45:33 - WARNING - 警告: 行 7419 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 2000
2025-08-08 14:45:33 - INFO - 完成自定义服务 SCCP 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: SIP
2025-08-08 14:45:33 - WARNING - 警告: 行 7424 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 5060
2025-08-08 14:45:33 - INFO - 设置服务UDP端口范围: 5060
2025-08-08 14:45:33 - INFO - 完成自定义服务 SIP 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: SIP-MSNmessenger
2025-08-08 14:45:33 - WARNING - 警告: 行 7430 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 1863
2025-08-08 14:45:33 - INFO - 完成自定义服务 SIP-MSNmessenger 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: SAMBA
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 139
2025-08-08 14:45:33 - INFO - 完成自定义服务 SAMBA 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: SMTP
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 25
2025-08-08 14:45:33 - INFO - 完成自定义服务 SMTP 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: SMTPS
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 465
2025-08-08 14:45:33 - INFO - 完成自定义服务 SMTPS 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: SNMP
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 161-162
2025-08-08 14:45:33 - INFO - 设置服务UDP端口范围: 161-162
2025-08-08 14:45:33 - INFO - 完成自定义服务 SNMP 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: SSH
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 22
2025-08-08 14:45:33 - INFO - 完成自定义服务 SSH 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: SYSLOG
2025-08-08 14:45:33 - INFO - 设置服务UDP端口范围: 514
2025-08-08 14:45:33 - INFO - 完成自定义服务 SYSLOG 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: TALK
2025-08-08 14:45:33 - INFO - 设置服务UDP端口范围: ************-08-08 14:45:33 - INFO - 完成自定义服务 TALK 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: TELNET
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 23
2025-08-08 14:45:33 - INFO - 完成自定义服务 TELNET 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: TFTP
2025-08-08 14:45:33 - INFO - 设置服务UDP端口范围: 69
2025-08-08 14:45:33 - INFO - 完成自定义服务 TFTP 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: MGCP
2025-08-08 14:45:33 - WARNING - 警告: 行 7480 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 2428
2025-08-08 14:45:33 - INFO - 设置服务UDP端口范围: 2427 2727
2025-08-08 14:45:33 - INFO - 完成自定义服务 MGCP 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: UUCP
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 540
2025-08-08 14:45:33 - INFO - 完成自定义服务 UUCP 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: VDOLIVE
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 7000-7010
2025-08-08 14:45:33 - INFO - 完成自定义服务 VDOLIVE 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: WAIS
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 210
2025-08-08 14:45:33 - INFO - 完成自定义服务 WAIS 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: WINFRAME
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 1494 2598
2025-08-08 14:45:33 - INFO - 完成自定义服务 WINFRAME 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: X-WINDOWS
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 6000-6063
2025-08-08 14:45:33 - INFO - 完成自定义服务 X-WINDOWS 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: PING6
2025-08-08 14:45:33 - INFO - Setting service protocol: ICMP6
2025-08-08 14:45:33 - INFO - fortigate.set_service_icmptype
2025-08-08 14:45:33 - INFO - 完成自定义服务 PING6 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: MS-SQL
2025-08-08 14:45:33 - WARNING - 警告: 行 7513 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 1433 1434
2025-08-08 14:45:33 - INFO - 完成自定义服务 MS-SQL 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: MYSQL
2025-08-08 14:45:33 - WARNING - 警告: 行 7518 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 3306
2025-08-08 14:45:33 - INFO - 完成自定义服务 MYSQL 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: RDP
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 3389
2025-08-08 14:45:33 - INFO - 完成自定义服务 RDP 解析
2025-08-08 14:45:33 - INFO - 解析自定义服务: VNC
2025-08-08 14:45:33 - INFO - 设置服务TCP端口范围: 5900
2025-08-08 14:45:33 - INFO - 完成自定义服务 VNC 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: DHCP6
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 546 547
2025-08-08 14:45:34 - INFO - 完成自定义服务 DHCP6 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: SQUID
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 3128
2025-08-08 14:45:34 - INFO - 完成自定义服务 SQUID 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: SOCKS
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 1080
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 1080
2025-08-08 14:45:34 - INFO - 完成自定义服务 SOCKS 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: WINS
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 1512
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 1512
2025-08-08 14:45:34 - INFO - 完成自定义服务 WINS 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: RADIUS
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 1812 1813
2025-08-08 14:45:34 - INFO - 完成自定义服务 RADIUS 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: RADIUS-OLD
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 1645 1646
2025-08-08 14:45:34 - INFO - 完成自定义服务 RADIUS-OLD 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: CVSPSERVER
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 2401
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 2401
2025-08-08 14:45:34 - INFO - 完成自定义服务 CVSPSERVER 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: AFS3
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 7000-7009
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 7000-7009
2025-08-08 14:45:34 - INFO - 完成自定义服务 AFS3 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: TRACEROUTE
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 33434-33535
2025-08-08 14:45:34 - INFO - 完成自定义服务 TRACEROUTE 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: RTSP
2025-08-08 14:45:34 - WARNING - 警告: 行 7580 包含可疑字符，可能存在命令注入风险: set category "VoIP, Messaging & Other Applications"
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 554 7070 8554
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 554
2025-08-08 14:45:34 - INFO - 完成自定义服务 RTSP 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: MMS
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 1755
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 1024-5000
2025-08-08 14:45:34 - INFO - 完成自定义服务 MMS 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: KERBEROS
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 88 464
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 88 464
2025-08-08 14:45:34 - INFO - 完成自定义服务 KERBEROS 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: LDAP_UDP
2025-08-08 14:45:34 - INFO - 设置服务UDP端口范围: 389
2025-08-08 14:45:34 - INFO - 完成自定义服务 LDAP_UDP 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: SMB
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 445
2025-08-08 14:45:34 - INFO - 完成自定义服务 SMB 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: NONE
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 0
2025-08-08 14:45:34 - INFO - 完成自定义服务 NONE 解析
2025-08-08 14:45:34 - INFO - 解析自定义服务: webproxy
2025-08-08 14:45:34 - INFO - Setting service protocol: ALL
2025-08-08 14:45:34 - INFO - 设置服务TCP端口范围: 0-65535:0-65535
2025-08-08 14:45:34 - INFO - 完成自定义服务 webproxy 解析
2025-08-08 14:45:34 - INFO - 开始解析服务组配置部分
2025-08-08 14:45:34 - INFO - 解析服务组: Email Access
2025-08-08 14:45:34 - INFO - 设置服务组成员数: 7
2025-08-08 14:45:34 - INFO - 完成服务组 Email Access 解析，包含 7 个成员
2025-08-08 14:45:34 - INFO - 解析服务组: Web Access
2025-08-08 14:45:34 - INFO - 设置服务组成员数: 3
2025-08-08 14:45:34 - INFO - 完成服务组 Web Access 解析，包含 3 个成员
2025-08-08 14:45:34 - INFO - 解析服务组: Windows AD
2025-08-08 14:45:34 - INFO - 设置服务组成员数: 7
2025-08-08 14:45:34 - INFO - 完成服务组 Windows AD 解析，包含 7 个成员
2025-08-08 14:45:34 - INFO - 解析服务组: Exchange Server
2025-08-08 14:45:34 - INFO - 设置服务组成员数: 3
2025-08-08 14:45:34 - INFO - 完成服务组 Exchange Server 解析，包含 3 个成员
2025-08-08 14:45:34 - INFO - 未知 配置部分结束
2025-08-08 14:45:34 - INFO - 未知 配置部分结束
2025-08-08 14:45:34 - INFO - 未知 配置部分结束
2025-08-08 14:45:34 - INFO - 未知 配置部分结束
2025-08-08 14:45:34 - INFO - 未知 配置部分结束
2025-08-08 14:45:34 - INFO - 未知 配置部分结束
2025-08-08 14:45:34 - INFO - 未知 配置部分结束
2025-08-08 14:45:34 - INFO - 未知 配置部分结束
2025-08-08 14:45:34 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:35 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:36 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:37 - INFO - 未知 配置部分结束
2025-08-08 14:45:38 - INFO - 未知 配置部分结束
2025-08-08 14:45:38 - INFO - 未知 配置部分结束
2025-08-08 14:45:38 - INFO - 未知 配置部分结束
2025-08-08 14:45:38 - INFO - 未知 配置部分结束
2025-08-08 14:45:38 - INFO - 未知 配置部分结束
2025-08-08 14:45:38 - INFO - 未知 配置部分结束
2025-08-08 14:45:38 - INFO - 未知 配置部分结束
2025-08-08 14:45:38 - INFO - 未知 配置部分结束
2025-08-08 14:45:38 - INFO - 未知 配置部分结束
2025-08-08 14:45:38 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - WARNING - 警告: 行 9554 包含可疑字符，可能存在命令注入风险: set host-regex "^([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\.([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])){3}$"
2025-08-08 14:45:39 - WARNING - Message is missing parameters: 0,4, 1,7, 1,4
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:39 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - WARNING - Message is missing parameters: 8, 4, 4, 4, 12
2025-08-08 14:45:40 - WARNING - 格式错误: Replacement index 8 out of range for positional args tuple
2025-08-08 14:45:40 - WARNING - Message is missing parameters: 1, 3, 4, 2, 2, 2,4
2025-08-08 14:45:40 - WARNING - 格式错误: Replacement index 1 out of range for positional args tuple
2025-08-08 14:45:40 - WARNING - Message is missing parameters: 3, 2, 4
2025-08-08 14:45:40 - WARNING - 格式错误: Replacement index 3 out of range for positional args tuple
2025-08-08 14:45:40 - WARNING - 警告: 行 9626 包含可疑字符，可能存在命令注入风险: set verify "(?<!-)\\b(?!666|000|9\\d{2})\\d{3}-(?!00)\\d{2}-(?!0{4})\\d{4}\\b(?!-)"
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - Start Parsing Local Users
2025-08-08 14:45:40 - INFO - Parsing Local User
2025-08-08 14:45:40 - INFO - Set User Auth Type
2025-08-08 14:45:40 - INFO - Set User Password
2025-08-08 14:45:40 - INFO - Local User Parsing Complete
2025-08-08 14:45:40 - INFO - Parsing Local User
2025-08-08 14:45:40 - INFO - Set User Auth Type
2025-08-08 14:45:40 - INFO - Set User Password
2025-08-08 14:45:40 - INFO - Set User Password
2025-08-08 14:45:40 - INFO - Local User Parsing Complete
2025-08-08 14:45:40 - INFO - Start Parsing User Settings
2025-08-08 14:45:40 - INFO - Set Auth Cert
2025-08-08 14:45:40 - INFO - Start Parsing User Groups
2025-08-08 14:45:40 - INFO - Parsing User Group
2025-08-08 14:45:40 - INFO - User Group Parsing Complete
2025-08-08 14:45:40 - INFO - Parsing User Group
2025-08-08 14:45:40 - INFO - Set User Group Members
2025-08-08 14:45:40 - INFO - User Group Parsing Complete
2025-08-08 14:45:40 - INFO - Start Parsing Ssl Vpn Portal
2025-08-08 14:45:40 - INFO - Parsing Ssl Vpn Portal
2025-08-08 14:45:40 - INFO - Set Ssl Vpn Web Mode
2025-08-08 14:45:40 - INFO - Ssl Vpn Portal Parsing Complete
2025-08-08 14:45:40 - INFO - Parsing Ssl Vpn Portal
2025-08-08 14:45:40 - INFO - Set Ssl Vpn Web Mode
2025-08-08 14:45:40 - INFO - Ssl Vpn Portal Parsing Complete
2025-08-08 14:45:40 - INFO - Parsing Ssl Vpn Portal
2025-08-08 14:45:40 - INFO - Ssl Vpn Portal Parsing Complete
2025-08-08 14:45:40 - INFO - Start Parsing Ssl Vpn Settings
2025-08-08 14:45:40 - INFO - Set Ssl Vpn Servercert
2025-08-08 14:45:40 - INFO - Set Ssl Vpn Port
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:40 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:41 - INFO - 未知 配置部分结束
2025-08-08 14:45:42 - INFO - 未知 配置部分结束
2025-08-08 14:45:42 - INFO - 未知 配置部分结束
2025-08-08 14:45:43 - INFO - 未知 配置部分结束
2025-08-08 14:45:43 - INFO - 未知 配置部分结束
2025-08-08 14:45:44 - INFO - 未知 配置部分结束
2025-08-08 14:45:44 - INFO - 未知 配置部分结束
2025-08-08 14:45:44 - INFO - 未知 配置部分结束
2025-08-08 14:45:44 - INFO - 未知 配置部分结束
2025-08-08 14:45:44 - INFO - 未知 配置部分结束
2025-08-08 14:45:44 - WARNING - 警告: 行 11028 包含可疑字符，可能存在命令注入风险: set url "^\\/((custom|search|images|videosearch|webhp)\\?)"
2025-08-08 14:45:44 - WARNING - 警告: 行 11031 包含可疑字符，可能存在命令注入风险: set safesearch-str "&safe=active"
2025-08-08 14:45:44 - WARNING - 警告: 行 11035 包含可疑字符，可能存在命令注入风险: set url "^\\/search(\\/video|\\/images)[缺失:0,1](\\?|;)"
2025-08-08 14:45:44 - WARNING - 警告: 行 11038 包含可疑字符，可能存在命令注入风险: set safesearch-str "&vm=r"
2025-08-08 14:45:44 - WARNING - 警告: 行 11042 包含可疑字符，可能存在命令注入风险: set url "^(\\/images|\\/videos)?(\\/search|\\/async|\\/asyncv2)\\?"
2025-08-08 14:45:44 - WARNING - 警告: 行 11048 包含可疑字符，可能存在命令注入风险: set url "^\\/((|yand|images\\/|video\\/)(search)|search\\/)\\?"
2025-08-08 14:45:44 - WARNING - 警告: 行 11051 包含可疑字符，可能存在命令注入风险: set safesearch-str "&family=yes"
2025-08-08 14:45:44 - WARNING - 警告: 行 11064 包含可疑字符，可能存在命令注入风险: set url "^\\/(ns|q|m|i|v)\\?"
2025-08-08 14:45:44 - WARNING - Message is missing parameters: 4,15
2025-08-08 14:45:45 - WARNING - 警告: 行 11114 包含可疑字符，可能存在命令注入风险: set safesearch-str "regex::(?:\\?|&)u=([^&]+)::\\1"
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - Start Parsing Log Settings
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 开始解析周期性时间对象配置部分
2025-08-08 14:45:45 - INFO - 解析时间对象 always
2025-08-08 14:45:45 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:45 - INFO - 时间对象 always 解析完成
2025-08-08 14:45:45 - INFO - 解析时间对象 none
2025-08-08 14:45:45 - WARNING - 警告: 时间对象 'none' 只有UUID标识符，没有其他配置，将跳过默认值填充
2025-08-08 14:45:45 - WARNING - 警告: 时间对象 'none' 没有时间设置和星期几设置
2025-08-08 14:45:45 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:45 - INFO - 时间对象 none 解析完成
2025-08-08 14:45:45 - INFO - 解析时间对象 default-darrp-optimize
2025-08-08 14:45:45 - WARNING - Message is missing parameter: "name"
2025-08-08 14:45:45 - INFO - 时间对象 default-darrp-optimize 解析完成
2025-08-08 14:45:45 - INFO - schedule 配置部分结束
2025-08-08 14:45:45 - INFO - Start Parsing Vip
2025-08-08 14:45:45 - INFO - Parsing Vip
2025-08-08 14:45:45 - INFO - Set Vip Extip
2025-08-08 14:45:45 - INFO - Set Vip Mappedip
2025-08-08 14:45:45 - INFO - Set Vip Extintf
2025-08-08 14:45:45 - INFO - Set Vip Portforward
2025-08-08 14:45:45 - INFO - Set Vip Extport
2025-08-08 14:45:45 - INFO - Set Vip Mappedport
2025-08-08 14:45:45 - INFO - Vip Parsing Complete
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:45 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:46 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - Start Parsing Policy
2025-08-08 14:45:47 - INFO - Parsing Policy
2025-08-08 14:45:47 - INFO - fortigate.set_policy_uuid
2025-08-08 14:45:47 - INFO - Set Policy Srcintf
2025-08-08 14:45:47 - INFO - Set Policy Dstintf
2025-08-08 14:45:47 - INFO - Set Policy Action
2025-08-08 14:45:47 - INFO - Set Policy Srcaddr
2025-08-08 14:45:47 - INFO - Set Policy Dstaddr
2025-08-08 14:45:47 - INFO - Set Policy Schedule
2025-08-08 14:45:47 - INFO - Set Policy Service
2025-08-08 14:45:47 - INFO - Set Policy Nat
2025-08-08 14:45:47 - INFO - Policy Parsing Complete
2025-08-08 14:45:47 - INFO - Parsing Policy
2025-08-08 14:45:47 - INFO - fortigate.set_policy_name
2025-08-08 14:45:47 - INFO - fortigate.set_policy_uuid
2025-08-08 14:45:47 - INFO - Set Policy Srcintf
2025-08-08 14:45:47 - INFO - Set Policy Dstintf
2025-08-08 14:45:47 - INFO - Set Policy Action
2025-08-08 14:45:47 - INFO - Set Policy Srcaddr
2025-08-08 14:45:47 - INFO - Set Policy Dstaddr
2025-08-08 14:45:47 - INFO - Set Policy Schedule
2025-08-08 14:45:47 - INFO - Set Policy Service
2025-08-08 14:45:47 - INFO - Set Policy Nat
2025-08-08 14:45:47 - INFO - Policy Parsing Complete
2025-08-08 14:45:47 - INFO - Parsing Policy
2025-08-08 14:45:47 - INFO - fortigate.set_policy_name
2025-08-08 14:45:47 - INFO - fortigate.set_policy_uuid
2025-08-08 14:45:47 - INFO - Set Policy Srcintf
2025-08-08 14:45:47 - INFO - Set Policy Dstintf
2025-08-08 14:45:47 - INFO - Set Policy Action
2025-08-08 14:45:47 - INFO - Set Policy Srcaddr
2025-08-08 14:45:47 - INFO - Set Policy Dstaddr
2025-08-08 14:45:47 - INFO - Set Policy Schedule
2025-08-08 14:45:47 - INFO - Set Policy Service
2025-08-08 14:45:47 - INFO - 设置策略UTM状态: enable
2025-08-08 14:45:47 - INFO - Set Policy Av Profile
2025-08-08 14:45:47 - INFO - Set Policy Webfilter Profile
2025-08-08 14:45:47 - INFO - Set Policy Nat
2025-08-08 14:45:47 - INFO - 设置策略端口保持: disable
2025-08-08 14:45:47 - INFO - Set Policy Comments
2025-08-08 14:45:47 - INFO - Policy Parsing Complete
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:47 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:48 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 开始解析静态路由配置部分
2025-08-08 14:45:49 - INFO - 静态路由网关: 10.96.30.1
2025-08-08 14:45:49 - INFO - 静态路由管理距离: 1
2025-08-08 14:45:49 - INFO - 静态路由出接口: mgmt
2025-08-08 14:45:49 - INFO - 静态路由 1 缺少目标网络，将使用默认值: 0.0.0.0/0
2025-08-08 14:45:49 - WARNING - Message is missing parameters: 'id': '1', 'gateway': '10.96.30.1', 'distance': '1', 'device': 'mgmt', 'destination': '0.0.0.0/0'
2025-08-08 14:45:49 - WARNING - Message is missing parameter: 'id'
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - 未知 配置部分结束
2025-08-08 14:45:49 - INFO - DNS配置处理完成，服务器数量: 2，DNS over TLS: 未设置
2025-08-08 14:45:49 - INFO - 解析完成，找到 38 个接口, 1 个静态路由, 1 个区域, 23 个地址对象, 2 个地址组, 87 个服务对象, 4 个服务组
2025-08-08 14:45:49 - INFO - 解析完成，找到 38 个接口, 1 个静态路由, 1 个区域, 23 个地址对象, 2 个地址组, 87 个服务对象, 4 个服务组
2025-08-08 14:45:49 - INFO - Interface mapping loaded
2025-08-08 14:45:49 - WARNING - Message is missing parameters: 'mgmt': 'Ge0/0', 'port1': 'Ge0/3', 'port2': 'Ge0/4', 'port3': 'Ge0/5', 'port4': 'Ge0/6', 'port5': 'Ge0/7', 'port6': 'Ge0/8', 'port7': 'Ge0/9', 'port8': 'Ge0/10', 'port9': 'Ge0/11', 'port10': 'Ge0/12', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'dmz': 'Ge0/13', 'fortilink': 'Ge0/14', 'ha1': 'Ge0/15', 'ha2': 'Ge0/16', 'lan': 'Ge0/17', 'modem': 'Ge0/18', 'port11': 'Ge0/19', 'port12': 'Ge0/20', 'port13': 'Ge0/21', 'port14': 'Ge0/22', 'port15': 'Ge0/23', 'wan1': 'Ge0/1', 't2': 'TenGe0/1', 't1': 'TenGe0/0'
2025-08-08 14:45:49 - WARNING - Message is missing parameter: 'mgmt'
2025-08-08 14:45:49 - INFO - 加载的接口映射: {'mgmt': 'Ge0/0', 'port1': 'Ge0/3', 'port2': 'Ge0/4', 'port3': 'Ge0/5', 'port4': 'Ge0/6', 'port5': 'Ge0/7', 'port6': 'Ge0/8', 'port7': 'Ge0/9', 'port8': 'Ge0/10', 'port9': 'Ge0/11', 'port10': 'Ge0/12', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'dmz': 'Ge0/13', 'fortilink': 'Ge0/14', 'ha1': 'Ge0/15', 'ha2': 'Ge0/16', 'lan': 'Ge0/17', 'modem': 'Ge0/18', 'port11': 'Ge0/19', 'port12': 'Ge0/20', 'port13': 'Ge0/21', 'port14': 'Ge0/22', 'port15': 'Ge0/23', 'wan1': 'Ge0/1', 't2': 'TenGe0/1', 't1': 'TenGe0/0'}
2025-08-08 14:45:49 - INFO - 配置管理器：检测到开发环境
2025-08-08 14:45:49 - INFO - 配置管理器已初始化
2025-08-08 14:45:49 - INFO - YANG管理器已初始化
2025-08-08 14:45:49 - INFO - 验证服务已初始化
2025-08-08 14:45:49 - ERROR - 接口映射验证失败：发现 17 个无效映射
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 port7，目标接口 Ge0/9，原因：接口 Ge0/9 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 port8，目标接口 Ge0/10，原因：接口 Ge0/10 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 port9，目标接口 Ge0/11，原因：接口 Ge0/11 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 port10，目标接口 Ge0/12，原因：接口 Ge0/12 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 x2，目标接口 TenGe0/1，原因：接口 TenGe0/1 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 dmz，目标接口 Ge0/13，原因：接口 Ge0/13 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 fortilink，目标接口 Ge0/14，原因：接口 Ge0/14 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 ha1，目标接口 Ge0/15，原因：接口 Ge0/15 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 ha2，目标接口 Ge0/16，原因：接口 Ge0/16 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 lan，目标接口 Ge0/17，原因：接口 Ge0/17 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 modem，目标接口 Ge0/18，原因：接口 Ge0/18 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 port11，目标接口 Ge0/19，原因：接口 Ge0/19 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 port12，目标接口 Ge0/20，原因：接口 Ge0/20 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 port13，目标接口 Ge0/21，原因：接口 Ge0/21 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 port14，目标接口 Ge0/22，原因：接口 Ge0/22 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 port15，目标接口 Ge0/23，原因：接口 Ge0/23 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - 无效的接口映射：源接口 t2，目标接口 TenGe0/1，原因：接口 TenGe0/1 不在设备型号 z3200s 支持的接口列表中
2025-08-08 14:45:49 - ERROR - conversion_workflow.interface_mapping_validation_failed
2025-08-08 14:45:49 - WARNING - Message is missing parameters: initial_data.get('config_file', 'unknown')
2025-08-08 14:45:49 - WARNING - Message is missing parameter: initial_data
2025-08-08 14:45:49 - WARNING - Message is missing parameters: initial_data.get('vendor', 'unknown')
2025-08-08 14:45:49 - WARNING - Message is missing parameter: initial_data
2025-08-08 14:45:49 - WARNING - Message is missing parameters: initial_data.get('model', 'unknown')
2025-08-08 14:45:49 - WARNING - Message is missing parameter: initial_data
2025-08-08 14:45:49 - WARNING - Message is missing parameters: initial_data.get('version', 'unknown')
2025-08-08 14:45:49 - INFO - 阶段开始: fortigate_conversion, stage count: 12
2025-08-08 14:45:49 - INFO - 阶段开始: fortigate_conversion -> operation_mode (1/12)
2025-08-08 14:45:49 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:49 - INFO - 开始检测操作模式
2025-08-08 14:45:49 - INFO - Operation mode analysis: explicit=nat, role_interfaces=True, mgmt_interface=True, manageip=False
2025-08-08 14:45:49 - INFO - Route mode detected from explicit configuration: nat
2025-08-08 14:45:49 - INFO - 处理路由模式配置
2025-08-08 14:45:49 - INFO - 路由模式处理完成，主机名: FortiGate-100F
2025-08-08 14:45:49 - INFO - 操作模式检测完成，模式: route，主机名: FortiGate-100F
2025-08-08 14:45:49 - INFO - 管道阶段：阶段完成
2025-08-08 14:45:49 - INFO - 阶段开始: fortigate_conversion -> interface_processing (2/12)
2025-08-08 14:45:49 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:49 - INFO - 开始处理接口配置
2025-08-08 14:45:49 - INFO - 开始接口处理，共38个项目
2025-08-08 14:45:50 - WARNING - interface_processor.unsupported_access_service
2025-08-08 14:45:50 - WARNING - interface_processor.unsupported_access_service
2025-08-08 14:45:50 - WARNING - interface_processor.unsupported_access_service
2025-08-08 14:45:50 - WARNING - interface_processor.unsupported_access_service
2025-08-08 14:45:50 - WARNING - interface_processor.unsupported_access_service
2025-08-08 14:45:50 - WARNING - interface_processor.unsupported_access_service
2025-08-08 14:45:50 - WARNING - interface_processor.unsupported_access_service
2025-08-08 14:45:50 - WARNING - 接口 'wan2' 警告：缺少接口映射
2025-08-08 14:45:50 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-08 14:45:50 - WARNING - 接口 'port16' 警告：缺少接口映射
2025-08-08 14:45:50 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-08 14:45:50 - WARNING - 接口 'port17' 警告：缺少接口映射
2025-08-08 14:45:50 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-08 14:45:50 - WARNING - 接口 'port18' 警告：缺少接口映射
2025-08-08 14:45:50 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-08 14:45:50 - WARNING - 接口 'port19' 警告：缺少接口映射
2025-08-08 14:45:50 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-08 14:45:50 - WARNING - 接口 'port20' 警告：缺少接口映射
2025-08-08 14:45:50 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-08 14:45:50 - INFO - 过滤modem接口: modem
2025-08-08 14:45:50 - WARNING - 接口 'naf.root' 警告：缺少接口映射
2025-08-08 14:45:50 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-08 14:45:50 - WARNING - 接口 'l2t.root' 警告：缺少接口映射
2025-08-08 14:45:50 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-08 14:45:50 - WARNING - 接口 'ssl.root' 警告：缺少接口映射
2025-08-08 14:45:50 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-08 14:45:50 - INFO - 接口 't1' 使用转换后的类型: switch -> physical
2025-08-08 14:45:50 - INFO - 接口 't2' 使用转换后的类型: switch -> physical
2025-08-08 14:45:51 - WARNING - interface_processor.unsupported_access_service
2025-08-08 14:45:51 - INFO - 接口 'fortilink' 使用转换后的类型: aggregate -> physical
2025-08-08 14:45:51 - WARNING - interface_processor.unsupported_access_service
2025-08-08 14:45:51 - INFO - DEBUG: 开始生成XML片段，converted接口数量: 28
2025-08-08 14:45:51 - INFO - DEBUG: physical_interfaces数量: 26
2025-08-08 14:45:51 - INFO - DEBUG: vlan_interfaces数量: 2
2025-08-08 14:45:51 - INFO - DEBUG: XML片段生成 - converted_interfaces: 28
2025-08-08 14:45:51 - INFO - DEBUG: XML片段生成 - vlan_interfaces: 2
2025-08-08 14:45:51 - INFO - DEBUG: XML片段生成 - physical_interfaces: 26
2025-08-08 14:45:51 - INFO - 创建新VLAN接口: Ge0/11.91
2025-08-08 14:45:51 - INFO - interface handler: mapped parent interface
2025-08-08 14:45:51 - INFO - 为子接口 Ge0/11.91 添加IPv4配置，模式: 
2025-08-08 14:45:51 - INFO - 使用通用方法配置PPPoE
2025-08-08 14:45:51 - INFO - interface handler: assigned pppoe tunnel id
2025-08-08 14:45:51 - INFO - 分配PPPoE隧道ID: Ge0/11.91 -> 0
2025-08-08 14:45:51 - INFO - PPPoE用户名已设置: asdf
2025-08-08 14:45:51 - INFO - interface handler: pppoe password set
2025-08-08 14:45:51 - INFO - interface handler: pppoe configured
2025-08-08 14:45:51 - INFO - 子接口 Ge0/11.91 配置为PPPoE模式，用户名: asdf
2025-08-08 14:45:51 - INFO - VLAN接口已创建: Ge0/11.91
2025-08-08 14:45:51 - INFO - 创建新VLAN接口: Ge0/11.92
2025-08-08 14:45:51 - INFO - interface handler: mapped parent interface
2025-08-08 14:45:51 - INFO - 为子接口 Ge0/11.92 添加IPv4配置，模式: 
2025-08-08 14:45:51 - INFO - 使用通用方法配置PPPoE
2025-08-08 14:45:51 - INFO - interface handler: assigned pppoe tunnel id
2025-08-08 14:45:51 - INFO - 分配PPPoE隧道ID: Ge0/11.92 -> 1
2025-08-08 14:45:51 - INFO - PPPoE用户名已设置: user92
2025-08-08 14:45:51 - INFO - interface handler: pppoe password set
2025-08-08 14:45:51 - INFO - interface handler: pppoe configured
2025-08-08 14:45:51 - INFO - 子接口 Ge0/11.92 配置为PPPoE模式，用户名: user92
2025-08-08 14:45:51 - INFO - VLAN接口已创建: Ge0/11.92
2025-08-08 14:45:51 - INFO - DEBUG: 开始处理物理接口，数量: 26
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 dmz, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 dmz 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - interface handler: configured static ip
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: ['ping', 'https']
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 dmz XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 mgmt, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 mgmt 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - interface handler: configured static ip
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: ['ping', 'ssh', 'https']
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: true
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 mgmt XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 wan1, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 wan1 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - interface handler: configured dhcp mode
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: ['ping']
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: true, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 wan1 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 ha1, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 ha1 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 ha1 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 ha2, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 ha2 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 ha2 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port1, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port1 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port1 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port2, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port2 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port2 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port3, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port3 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port3 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port4, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port4 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port4 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port5, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port5 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port5 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port6, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port6 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port6 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port7, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port7 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port7 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port8, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port8 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port8 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port9, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port9 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port9 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port10, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port10 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port10 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port11, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port11 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port11 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port12, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port12 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port12 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 x1, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 x1 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 x1 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 x2, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 x2 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 x2 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port13, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port13 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port13 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port14, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port14 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port14 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 port15, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 port15 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 port15 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 t1, is_subinterface: False
2025-08-08 14:45:51 - INFO - DEBUG: 为接口 t1 生成XML
2025-08-08 14:45:51 - INFO - interface handler: create new interface
2025-08-08 14:45:51 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:51 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:51 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:51 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:51 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:51 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:51 - INFO - interface handler: configured access control
2025-08-08 14:45:51 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:51 - INFO - DEBUG: 接口 t1 XML生成完成
2025-08-08 14:45:51 - INFO - DEBUG: 处理物理接口 t2, is_subinterface: False
2025-08-08 14:45:52 - INFO - DEBUG: 为接口 t2 生成XML
2025-08-08 14:45:52 - INFO - interface handler: create new interface
2025-08-08 14:45:52 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:52 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:52 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:52 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:52 - INFO - DEBUG: 最终的combined_access: []
2025-08-08 14:45:52 - INFO - DEBUG: 最终XML值 - https: false, ping: false, ssh: false
2025-08-08 14:45:52 - INFO - interface handler: configured access control
2025-08-08 14:45:52 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:52 - INFO - DEBUG: 接口 t2 XML生成完成
2025-08-08 14:45:52 - INFO - DEBUG: 处理物理接口 lan, is_subinterface: False
2025-08-08 14:45:52 - INFO - DEBUG: 为接口 lan 生成XML
2025-08-08 14:45:52 - INFO - interface handler: create new interface
2025-08-08 14:45:52 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:52 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:52 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:52 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:52 - INFO - interface handler: configured static ip
2025-08-08 14:45:52 - INFO - DEBUG: 最终的combined_access: ['ping', 'ssh', 'https']
2025-08-08 14:45:52 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: true
2025-08-08 14:45:52 - INFO - interface handler: configured access control
2025-08-08 14:45:52 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:52 - INFO - DEBUG: 接口 lan XML生成完成
2025-08-08 14:45:52 - INFO - DEBUG: 处理物理接口 fortilink, is_subinterface: False
2025-08-08 14:45:52 - INFO - DEBUG: 为接口 fortilink 生成XML
2025-08-08 14:45:52 - INFO - interface handler: create new interface
2025-08-08 14:45:52 - INFO - interface handler: using fortinet optimizer
2025-08-08 14:45:52 - INFO - interface handler: create fortinet interface
2025-08-08 14:45:52 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-08 14:45:52 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-08 14:45:52 - INFO - interface handler: configured static ip
2025-08-08 14:45:52 - INFO - DEBUG: 最终的combined_access: ['ping']
2025-08-08 14:45:52 - INFO - DEBUG: 最终XML值 - https: false, ping: true, ssh: false
2025-08-08 14:45:52 - INFO - interface handler: configured access control
2025-08-08 14:45:52 - INFO - interface handler: fortinet interface created
2025-08-08 14:45:52 - INFO - DEBUG: 接口 fortilink XML生成完成
2025-08-08 14:45:52 - INFO - DEBUG: XML片段生成完成，长度: 44840
2025-08-08 14:45:52 - INFO - DEBUG: 接口映射 1: dmz -> Ge0/13 (类型: physical)
2025-08-08 14:45:52 - INFO - DEBUG: 接口映射 2: mgmt -> Ge0/0 (类型: physical)
2025-08-08 14:45:52 - INFO - DEBUG: 接口映射 3: wan1 -> Ge0/1 (类型: physical)
2025-08-08 14:45:52 - INFO - DEBUG: 接口映射 4: ha1 -> Ge0/15 (类型: physical)
2025-08-08 14:45:52 - INFO - DEBUG: 接口映射 5: ha2 -> Ge0/16 (类型: physical)
2025-08-08 14:45:52 - INFO - DEBUG: 接口映射 6: port1 -> Ge0/3 (类型: physical)
2025-08-08 14:45:52 - INFO - DEBUG: 接口映射 7: port2 -> Ge0/4 (类型: physical)
2025-08-08 14:45:52 - INFO - DEBUG: 接口映射 8: port3 -> Ge0/5 (类型: physical)
2025-08-08 14:45:52 - INFO - DEBUG: 接口映射 9: port4 -> Ge0/6 (类型: physical)
2025-08-08 14:45:52 - INFO - DEBUG: 接口映射 10: port5 -> Ge0/7 (类型: physical)
2025-08-08 14:45:52 - INFO - 最终接口映射已创建
2025-08-08 14:45:52 - INFO - 接口映射已保存
2025-08-08 14:45:52 - INFO - 最终接口映射已保存
2025-08-08 14:45:52 - INFO - [待翻译] interface_processing_stage.final_mapping_saved
2025-08-08 14:45:52 - INFO - DEBUG: 最终映射文件已保存到: output\interface_mapping.json
2025-08-08 14:45:52 - INFO - DEBUG: 验证成功，文件确实存在: output\interface_mapping.json
2025-08-08 14:45:52 - INFO - DEBUG: 保存的映射内容: {'dmz': 'Ge0/13', 'mgmt': 'Ge0/0', 'wan1': 'Ge0/1', 'ha1': 'Ge0/15', 'ha2': 'Ge0/16', 'port1': 'Ge0/3', 'port2': 'Ge0/4', 'port3': 'Ge0/5', 'port4': 'Ge0/6', 'port5': 'Ge0/7', 'port6': 'Ge0/8', 'port7': 'Ge0/9', 'port8': 'Ge0/10', 'port9': 'Ge0/11', 'port10': 'Ge0/12', 'port11': 'Ge0/19', 'port12': 'Ge0/20', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'port13': 'Ge0/21', 'port14': 'Ge0/22', 'port15': 'Ge0/23', 't1': 'TenGe0/0', 't2': 'TenGe0/1', 'lan': 'Ge0/17', 'fortilink': 'Ge0/14', 'testppp91': 'Ge0/11.91', 'testppp92': 'Ge0/11.92'}
2025-08-08 14:45:52 - INFO - 接口处理完成：成功转换28/38，耗时2.36s
2025-08-08 14:45:52 - INFO - 接口处理：10个项目被跳过
2025-08-08 14:45:52 - INFO - 接口处理完成，转换: 28，安全区域: 3
2025-08-08 14:45:52 - INFO - 管道阶段：阶段完成
2025-08-08 14:45:52 - INFO - 阶段开始: fortigate_conversion -> service_processing (3/12)
2025-08-08 14:45:52 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:52 - INFO - 开始服务对象处理
2025-08-08 14:45:52 - INFO - 为保持与原版一致性，跳过服务对象iprange转换
2025-08-08 14:45:52 - INFO - 开始服务对象处理，共91个项目
2025-08-08 14:45:52 - INFO - service_processing.skip_predefined_services
2025-08-08 14:45:52 - INFO - 服务对象处理完成：转换成功 72/87
2025-08-08 14:45:52 - INFO - 服务对象处理完成：成功转换72/87，耗时510ms
2025-08-08 14:45:52 - INFO - 管道阶段：阶段完成
2025-08-08 14:45:52 - INFO - 阶段开始: fortigate_conversion -> address_processing (4/12)
2025-08-08 14:45:52 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:52 - INFO - 开始处理地址对象
2025-08-08 14:45:52 - INFO - 发现 0 个动态生成的地址对象
2025-08-08 14:45:52 - INFO - 地址对象统计: 原始=21, VIP=1, 动态生成=0, 总计=22
2025-08-08 14:45:52 - INFO - 找到 21 个地址对象
2025-08-08 14:45:52 - INFO - address_processor.found_vip_objects
2025-08-08 14:45:52 - INFO - 开始地址对象处理，共22个项目
2025-08-08 14:45:52 - WARNING - 跳过不支持的地址类型: EMS_ALL_UNKNOWN_CLIENTS (dynamic)
2025-08-08 14:45:52 - WARNING - 跳过不支持的地址类型: EMS_ALL_UNMANAGEABLE_CLIENTS (dynamic)
2025-08-08 14:45:52 - WARNING - 地址对象 none 缺少subnet信息
2025-08-08 14:45:52 - WARNING - 地址对象 'none' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-08 14:45:52 - WARNING - 跳过不支持的地址类型: login.microsoftonline.com (fqdn)
2025-08-08 14:45:52 - WARNING - 跳过不支持的地址类型: login.microsoft.com (fqdn)
2025-08-08 14:45:52 - WARNING - 跳过不支持的地址类型: login.windows.net (fqdn)
2025-08-08 14:45:52 - WARNING - 跳过不支持的地址类型: gmail.com (fqdn)
2025-08-08 14:45:52 - WARNING - 跳过不支持的地址类型: wildcard.google.com (fqdn)
2025-08-08 14:45:52 - WARNING - 跳过不支持的地址类型: wildcard.dropbox.com (fqdn)
2025-08-08 14:45:52 - WARNING - 地址对象 all 缺少subnet信息
2025-08-08 14:45:52 - WARNING - 地址对象 'all' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-08 14:45:53 - WARNING - 地址对象 FIREWALL_AUTH_PORTAL_ADDRESS 缺少subnet信息
2025-08-08 14:45:53 - WARNING - 地址对象 'FIREWALL_AUTH_PORTAL_ADDRESS' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-08 14:45:53 - WARNING - 地址对象 FABRIC_DEVICE 缺少subnet信息
2025-08-08 14:45:53 - WARNING - 地址对象 'FABRIC_DEVICE' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-08 14:45:53 - WARNING - 跳过不支持的地址类型: dmz (interface-subnet)
2025-08-08 14:45:53 - WARNING - 跳过不支持的地址类型: lan (interface-subnet)
2025-08-08 14:45:53 - WARNING - 跳过不支持的地址类型: FCTEMS_ALL_FORTICLOUD_SERVERS (dynamic)
2025-08-08 14:45:53 - WARNING - 跳过不支持的地址类型: t1 (interface-subnet)
2025-08-08 14:45:53 - WARNING - 跳过不支持的地址类型: t2 (interface-subnet)
2025-08-08 14:45:53 - WARNING - 地址对象 SSLVPN_TUNNEL_IPv6_ADDR1 缺少subnet信息
2025-08-08 14:45:53 - WARNING - 地址对象 'SSLVPN_TUNNEL_IPv6_ADDR1' 转换失败：[待翻译] address_processing.conversion_failed
2025-08-08 14:45:53 - INFO - DEBUG: Generated XML fragment length: 594
2025-08-08 14:45:53 - INFO - DEBUG: Address result converted count: 4
2025-08-08 14:45:53 - INFO - DEBUG: Address result converted objects: ['testvip1', 'SSLVPN_TUNNEL_ADDR1', 'bad11', 'a4']
2025-08-08 14:45:53 - INFO - DEBUG: XML fragment preview: <network-obj xmlns="urn:ruijie:ntos">
  <address-set>
    <name>testvip1</name>
    <ip-set>
      <ip-address>*******/32</ip-address>
    </ip-set>
  </address-set>
  <address-set>
    <name>SSLVPN_TUNNEL_ADDR1</name>
    <ip-set>
      <ip-address>**************-**************</ip-address>
    </ip-set>
  </address-set>
  <address-set>
    <name>bad11</name>
    <ip-set>
      <ip-address>***************/32</ip-address>
    </ip-set>
  </address-set>
  <address-set>
    <name>a4</name>
    <ip...
2025-08-08 14:45:53 - INFO - 地址对象处理完成：成功转换4/22，耗时289ms
2025-08-08 14:45:53 - INFO - 地址对象处理：5个项目转换失败
2025-08-08 14:45:53 - INFO - 地址对象处理：13个项目被跳过
2025-08-08 14:45:53 - INFO - 地址对象处理完成: 转换成功 4/22
2025-08-08 14:45:53 - INFO - 管道阶段：阶段完成
2025-08-08 14:45:53 - INFO - 阶段开始: fortigate_conversion -> address_group_processing (5/12)
2025-08-08 14:45:53 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:53 - INFO - 开始处理地址对象组
2025-08-08 14:45:53 - WARNING - 地址组成员不存在于已转换地址对象中: gmail.com
2025-08-08 14:45:53 - WARNING - 地址组成员不存在于已转换地址对象中: wildcard.google.com
2025-08-08 14:45:53 - WARNING - 跳过无有效成员的地址组: G
2025-08-08 14:45:53 - WARNING - 地址组成员不存在于已转换地址对象中: login.microsoftonline.com
2025-08-08 14:45:53 - WARNING - 地址组成员不存在于已转换地址对象中: login.microsoft.com
2025-08-08 14:45:53 - WARNING - 地址组成员不存在于已转换地址对象中: login.windows.net
2025-08-08 14:45:53 - WARNING - 跳过无有效成员的地址组: Microsoft
2025-08-08 14:45:53 - INFO - 没有转换成功的地址对象组，返回空XML片段
2025-08-08 14:45:53 - INFO - 地址对象组处理完成: 转换成功 0/2
2025-08-08 14:45:53 - INFO - 管道阶段：阶段完成
2025-08-08 14:45:53 - INFO - 阶段开始: fortigate_conversion -> service_group_processing (6/12)
2025-08-08 14:45:53 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:53 - INFO - 开始处理服务对象组
2025-08-08 14:45:53 - INFO - 找到 4 个服务对象组
2025-08-08 14:45:53 - INFO - service_group_processor.applying_name_sanitization
2025-08-08 14:45:53 - INFO - ntos_name_validator.name_sanitized
2025-08-08 14:45:53 - INFO - ntos_name_validator.name_sanitized
2025-08-08 14:45:53 - INFO - ntos_name_validator.name_sanitized
2025-08-08 14:45:53 - INFO - ntos_name_validator.name_sanitized
2025-08-08 14:45:53 - INFO - ntos_name_validator.service_groups_sanitized
2025-08-08 14:45:53 - INFO - service_group_processor.name_sanitization_stats
2025-08-08 14:45:53 - INFO - service_group_processor.name_changed
2025-08-08 14:45:53 - INFO - service_group_processor.name_changed
2025-08-08 14:45:53 - INFO - service_group_processor.name_changed
2025-08-08 14:45:53 - INFO - service_group_processor.name_changed
2025-08-08 14:45:53 - INFO - 服务对象组处理完成: 转换成功 4/4
2025-08-08 14:45:53 - INFO - 管道阶段：阶段完成
2025-08-08 14:45:53 - INFO - 阶段开始: fortigate_conversion -> zone_processing (7/12)
2025-08-08 14:45:53 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:53 - INFO - 开始处理安全区域
2025-08-08 14:45:53 - INFO - Using interface mapping from context: 27 mappings
2025-08-08 14:45:53 - INFO - Interface mapping set directly: 27 mappings
2025-08-08 14:45:53 - INFO - 找到 1 个区域配置，操作模式: route
2025-08-08 14:45:53 - INFO - Interface mapping loaded
2025-08-08 14:45:53 - INFO - Interface mapping loaded (flat format): 28 mappings
2025-08-08 14:45:53 - WARNING - Zone skipped: trust - 区域的所有接口都未配置映射关系或区域没有配置接口
2025-08-08 14:45:53 - WARNING - Zone skipped: untrust - 区域的所有接口都未配置映射关系或区域没有配置接口
2025-08-08 14:45:53 - INFO - 区域处理完成: 转换成功 1/1
2025-08-08 14:45:53 - INFO - 管道阶段：阶段完成
2025-08-08 14:45:53 - INFO - 阶段开始: fortigate_conversion -> time_range_processing (8/12)
2025-08-08 14:45:53 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:53 - INFO - 开始处理时间对象
2025-08-08 14:45:53 - INFO - 找到 3 个时间对象
2025-08-08 14:45:53 - INFO - 开始生成时间对象XML片段，转换结果: 3 个
2025-08-08 14:45:53 - INFO - XML片段生成：找到 3 个转换的时间对象
2025-08-08 14:45:53 - INFO - XML片段生成成功，长度: 1308
2025-08-08 14:45:53 - INFO - XML片段生成完成，长度: 1308
2025-08-08 14:45:53 - INFO - 时间对象处理完成: 转换成功 3/3
2025-08-08 14:45:53 - INFO - 管道阶段：阶段完成
2025-08-08 14:45:53 - INFO - 阶段开始: fortigate_conversion -> dns_processing (9/12)
2025-08-08 14:45:53 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:53 - INFO - 开始DNS处理
2025-08-08 14:45:53 - INFO - 使用已配置的DNS服务器: 2 个
2025-08-08 14:45:53 - INFO - DNS配置处理完成: DNS服务器 2 个，静态域名 0 个
2025-08-08 14:45:53 - INFO - DNS处理完成: DNS服务器 2 个，静态域名 0 个
2025-08-08 14:45:53 - INFO - 管道阶段：阶段完成
2025-08-08 14:45:53 - INFO - 阶段开始: fortigate_conversion -> static_route_processing (10/12)
2025-08-08 14:45:53 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:53 - INFO - 开始静态路由处理
2025-08-08 14:45:53 - INFO - 检测到列表格式的静态路由数据，转换为字典格式
2025-08-08 14:45:53 - INFO - 转换后找到 1 个静态路由
2025-08-08 14:45:53 - INFO - 静态路由处理完成: 转换成功 1 个，跳过 0 个，失败 0 个
2025-08-08 14:45:53 - INFO - 静态路由处理完成: 转换成功 1/1, 分组 1 个
2025-08-08 14:45:53 - INFO - 管道阶段：阶段完成
2025-08-08 14:45:53 - INFO - 阶段开始: fortigate_conversion -> fortigate_policy_conversion (11/12)
2025-08-08 14:45:53 - INFO - 管道阶段：阶段开始
2025-08-08 14:45:53 - ERROR - 数据上下文添加错误: fortigate_policy_stage.interface_mapping_validation_failed (阶段: None)
2025-08-08 14:45:53 - ERROR - 数据上下文添加错误: pipeline_stage.input_validation_failed (阶段: fortigate_policy_conversion)
2025-08-08 14:45:53 - ERROR - Pipeline stopped due to error: fortigate_conversion -> fortigate_policy_conversion
2025-08-08 14:45:53 - INFO - Pipeline execution completed: fortigate_conversion, state: failed, duration: 3.94s, errors: 2, warnings: 0
2025-08-08 14:45:53 - INFO - 🔍 DEBUG: 新架构管道执行完成，状态: failed
2025-08-08 14:45:53 - INFO - 转换已完成
2025-08-08 14:45:53 - ERROR - 转换过程中发生错误
2025-08-08 14:45:53 - ERROR - 转换过程中发生错误
