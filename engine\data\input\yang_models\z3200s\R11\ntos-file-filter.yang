module ntos-file-filter {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:file-filter";
  prefix ntos-file-filter;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";

  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";

  description
    "Ruijie NTOS file filtering module.";

  revision 2025-01-17 {
    description "Initial version.";
    reference "";
  }

  identity file-filter {
    base ntos-types:SERVICE_LOG_ID;
    description "The file filtering.";
  }

  typedef unknown-action {
    type enumeration {
      enum allow {
        description "Indicate the type of allow.";
      }
      enum alert {
        description "Indicate the type of alert.";
      }
      enum block {
        description "Indicate the type of block.";
      }
    }
    description "Indicate unknown action of the file filtering.";
  }

  typedef rule-action {
    type enumeration {
      enum alert {
        description "Indicate the type of alert.";
      }
      enum block {
        description "Indicate the type of block.";
      }
    }
    description "Indicate the action of the file filtering rule.";
  }

  typedef direction-type {
    type enumeration {
      enum download {
        description "Indicate download direction.";
      }
      enum upload {
        description "Indicate upload direction.";
      }
      enum both {
        description "Indicate upload and download direction.";
      }
    }
    description "Indicate the direction type of the filtering rule.";
  }

  typedef protocol {
    type bits {
      bit HTTP {
        description "HTTP protocol.";
      }

      bit FTP {
        description "FTP protocol.";
      }

      bit SMTP {
        description "SMTP protocol.";
      }

      bit POP3 {
        description "POP3 protocol.";
      }

      bit IMAP {
        description "IMAP protocol.";
      }
    }
    description "Indicate the type of application protocol.";
  }

  typedef file-group-type {
    type enumeration {
      enum user-defined {
        description "Indicate user-defined type.";
      }
      enum pre-defined {
        description "Indicate pre-defined type.";
      }
    }
    description "Indicate the type of file group.";
  }

  grouping grp-refer {
    description "The grouping of the URL reference.";

    list refer {
      key "refer-type";
      config false;
      description "Indicate the reference list.";

      leaf refer-type {
        type uint8;
        description "Indicate the type of reference.";
      }

      leaf-list id {
        type uint32;
        description "Indicate the id of reference object.";
      }
    }
  }

  grouping grp-file-group {
    description "Indicate the grouping of file type group.";
    list file-group {
      key "name";
      description "Indicate the configuration of file type group.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description "The name of the file type group.";
      }

      leaf name-i18n {
        type ntos-types:ntos-obj-name-type;
        config false;
        description "The international name.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description "The description of the file type group.";
      }

      leaf-list file-extension {
        type string {
          length "1..15";
          pattern "[_0-9a-z]+" {
            error-message "The file extension must be digits, lowercase letters and underscores.";
          }
        }
        description "The string of file extension.";
      }

      uses grp-refer;
    }
  }

  grouping grp-file-filter {
    description "Indicate the grouping of file filtering.";

    list profile {
      key "name";
      description "Indicate the configuration of file filter.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description "The name of the file filtering.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description "The description of the file filtering.";
      }

      leaf unknown-file-action {
        type unknown-action;
        default allow;
        description "Indicate action of unknown file type.";
      }

      list rule {
        key "name";
        description "Indicate the configuration of the filtering rule.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of the filtering rule.";
        }

        leaf action {
          type rule-action;
          default alert;
          description "Indicate the action of the filtering rule.";
        }

        leaf direction {
          type direction-type;
          default both;
          description "Indicate the data transmission direction.";
        }

        choice file-group {
          description "The file type group of the file filtering rule.";

          leaf all-file-group {
            type empty;
            description "All file type groups.";
          }

          case specific-group {
            description "The specified file group of the predefined and user-defined.";
            container file-group {
              description "The list of file group.";

              list pre-file-group {
                key "name";
                description "The predefined file types of the file filtering rule.";

                leaf name {
                  type ntos-types:ntos-obj-name-type;
                  description "The specified predefined file group of the file filtering rule.";
                }

                leaf name-i18n {
                  type ntos-types:ntos-obj-name-type;
                  config false;
                  description "The international name of predefined file group.";
                }
              }

              list user-file-group {
                key "name";
                description "The user-defined file types of the file filtering rule.";

                leaf name {
                  type ntos-types:ntos-obj-name-type;
                  description "The specified predefined file group of the file filtering rule.";
                }
              }
            }
          }
        }

        choice protocols {
          description "The application protocol of the file filtering rule.";

          leaf all-protocol {
            type empty;
            description "All application protocols.";
          }

          leaf specific-protocol {
            type protocol;
            description "The specified application protocol of the file filtering rule.";
          }
        }
      }

      uses grp-refer;
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description "The configuration of file filtering and file type.";

    uses grp-file-group;
    container file-filter {
      description "File Filter configuration.";
      uses grp-file-filter;
      container global-config {
        description
          "The information of file filtering global configuration.";

        leaf feature-identify-enabled {
          type boolean;
          default false;
          description
            "Set status of feature-identify-enabled.";
        }
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description "The state of file filtering and file type.";

    container file-filter-state {
      config false;
      description "The state of file filtering and file type.";
      uses grp-file-group;
      uses grp-file-filter;
    }
  }

  /* rpc show */
  grouping grp-input-leaf-rpc {
    description "The grouping of input leaf node.";

    leaf vrf {
      type ntos:vrf-name;
      description "The specific vrf.";
    }

    leaf start {
      type uint32;
      description "The index of page start.";
    }

    leaf end {
      type uint32;
      must "current() >= ../start" {
          error-message "The end value must be larger than the start value.";
      }
      description "The index of page end.";
    }

    leaf id {
      type uint32;
      description "The exact id.";
      ntos-ext:nc-cli-group "subcommand1";
    }

    leaf name {
      type string;
      description "The exact name.";
      ntos-ext:nc-cli-group "subcommand1";
    }

    leaf filter {
      type string;
      description "The vague name.";
      ntos-ext:nc-cli-group "subcommand1";
    }
  }

  rpc show-file-group {
    description "Show file type groups.";

    input {
      uses grp-input-leaf-rpc;

      leaf group-type {
        type file-group-type;
        default user-defined;
        description "Indicate the type of search.";
      }
    }

    output {
      leaf num {
        type uint32;
        description "The number of file type groups";
      }

      uses grp-file-group;
    }
    ntos-ext:nc-cli-show "file-group";
  }

  rpc show-file-group-name {
    description "Show supported all file group.";
    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }
    }

    output {
      list pre-file-group {
        key "name";
        description "The predefined file group.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The predefined file group name.";
        }

        leaf name-i18n {
          type ntos-types:ntos-obj-name-type;
          description "The international name of predefined file group.";
        }
      }

      list user-file-group {
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The user-defined file group name.";
        }
      }
    }
    ntos-ext:nc-cli-show "file-group name";
  }

  rpc show-file-filter {
    description "Show file filtering.";

    input {
      uses grp-input-leaf-rpc;
    }

    output {
      leaf num {
        type uint32;
        description "The number of file filter";
      }

      uses grp-file-filter;
    }
    ntos-ext:nc-cli-show "file-filter";
  }

  rpc show-file-filter-name {
    description "Show the name of file filter.";
    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }

      leaf id-list {
        type string;
        description "The id list of file filter.";
      }
    }

    output {
      list profile-name {
        description "The file filter name list.";

        leaf id {
          type uint32;
          description "The id of the profile.";
        }

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of the profile.";
        }
      }
    }
    ntos-ext:nc-cli-show "file-filter name";
  }

  rpc show-file-filter-global-config {
    description
      "Show the global config of file filter.";

    output {
      leaf feature-identify-enabled {
        type boolean;
        description
          "Status of feature-identify-enabled.";
      }
    }
    ntos-ext:nc-cli-show "file-filter global-config";
  }

}