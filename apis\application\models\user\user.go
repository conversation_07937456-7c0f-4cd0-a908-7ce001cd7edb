package user

import "irisAdminApi/application/models"

type User struct {
	models.ModelBase

	Name     string `gorm:"index;not null; type:varchar(60)" json:"name" `
	Username string `gorm:"uniqueIndex;not null;type:varchar(60)" json:"username"`
	Password string `gorm:"type:varchar(100)" json:"password"`
	Intro    string `gorm:"null;type:varchar(512)" json:"introduction"`
	Avatar   string `gorm:"type:varchar(1024)" json:"avatar"`
	Enable   bool   `gorm:"not null;default:true"`
	OpenID   string `gorm:"type:varchar(100)" json:"open_id"`

	RoleIds []uint `gorm:"-" json:"role_ids"`
}
