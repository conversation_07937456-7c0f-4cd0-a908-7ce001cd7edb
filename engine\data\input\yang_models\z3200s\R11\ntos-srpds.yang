module ntos-srpds {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:srpds";
  prefix ntos-srpds;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS sysrepo plugins management module.";

  revision 2023-05-01 {
    description
      "Add sysrepo plugins rds RPC";
  }
  
  identity srpds {
    base ntos-types:SERVICE_LOG_ID;
    description
      "SRPDS service.";
  }
  
  augment "/ntos:config/ntos-system:system" {
    container srpds {
      description
        "Sysrepo plugins datastore management";

      leaf-list nosync-instance {
        max-elements 20;
        type string {
          length "1..100";
        }
        description
          "Not sync instance xpath";
      }
    }
  }

  rpc show-nosync-instance {
    ntos-ext:nc-cli-show "nosync-instance";
    ntos-api:internal;
    description
      "Get nosync-instance info.";

    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
  }

  rpc srpds-rds-get-leaf {
    ntos-ext:nc-cli-cmd "srpds-rds-get-leaf";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
    description
      "Get local rds hash key info.";
    input {
      leaf path {
        type string;
        ntos-ext:nc-cli-no-name;
        description
          "Path of rds hash.";
      }
      leaf key {
        type string;
        ntos-ext:nc-cli-no-name;
        description
          "Key of rds hash.";
      }
    }
    
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
  }

  rpc srpds-rds-get-leaf-siblings {
    ntos-ext:nc-cli-cmd "srpds-rds-get-leaf-siblings";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
    description
      "Get local rds hash all key info.";
    input {
      leaf path {
        type string;
        ntos-ext:nc-cli-no-name;
        description
          "Path of rds hash.";
      }
    }
    
	output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
  }
  
  rpc srpds-rds-get-list {
    ntos-ext:nc-cli-cmd "srpds-rds-get-list";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
    description
      "Get local rds list key info.";
    input {
      leaf key {
        type string;
        ntos-ext:nc-cli-no-name;
        description
          "Key of rds list.";
      }
      leaf start {
        type int32;
        description
          "Index of start.";
      }
      leaf end {
        type int32;
        description
          "Index of end.";
      }
    }
    
	output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
  }
  
  rpc show-srpds-rds-server {
    description
      "Show sysrepo plugins datastore rds server infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-show "srpds-rds-server";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
  }
  
  rpc show-srpds-rds-clients {
    description
      "Show sysrepo plugins datastore rds clients infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-show "srpds-rds-clients";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
  }
  
  rpc show-srpds-rds-memory {
    description
      "Show sysrepo plugins datastore rds memory infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-show "srpds-rds-memory";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
  }
  
  rpc show-srpds-rds-cpu {
    description
      "Show sysrepo plugins datastore rds cpu infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-show "srpds-rds-cpu";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
  }
  
  rpc show-srpds-rds-status {
    description
      "Show sysrepo plugins datastore rds status infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-show "srpds-rds-status";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
  }
  
  rpc show-srpds-rds-keyspace {
    description
      "Show sysrepo plugins datastore rds keyspace infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-show "srpds-rds-keyspace";
    ntos-ext:nc-cli-hidden;
    ntos-api:internal;
  }

  rpc show-srpds-rds-replication {
    description
      "Show sysrepo plugins datastore rds replication infomation.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-show "srpds-rds-repli";
    ntos-api:internal;
  }

  rpc show-ha-state-config-sync {
    description
      "Show config sync state.";
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-show "ha state config-sync";
    ntos-api:internal;
  }

  rpc cmd-srpds-dump-file {
    description
      "Set dump dnode to file.";
    input {
      choice name {
        case choice-on {
          leaf on {
            description
              "Debug on.";
            type empty;
          }
        }
        case choice-off {
          leaf off {
            description
              "Debug off.";
            type empty;
          }
        }
        case choice-see {
          leaf see {
            description
              "Debug see.";
            type empty;
          }
        }
      }
    }
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
      }
    }
    ntos-ext:nc-cli-cmd "srpds-dump-file";
    ntos-api:internal;
  }
}
