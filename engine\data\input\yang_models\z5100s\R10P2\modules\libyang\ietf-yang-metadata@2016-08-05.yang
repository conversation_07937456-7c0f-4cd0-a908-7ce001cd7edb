module ietf-yang-metadata {
  namespace "urn:ietf:params:xml:ns:yang:ietf-yang-metadata";
  prefix md;

  organization
    "IETF NETMOD (NETCONF Data Modeling Language) Working Group";
  contact
    "WG Web:   <https://datatracker.ietf.org/wg/netmod/>

     WG List:  <mailto:<EMAIL>>

     WG Chair: <PERSON>
               <mailto:<EMAIL>>

     WG Chair: <PERSON>
               <mailto:<EMAIL>>

     Editor:   <PERSON><PERSON><PERSON>
               <mailto:<EMAIL>>";
  description
    "This YANG module defines an 'extension' statement that allows
     for defining metadata annotations.

     Copyright (c) 2016 IETF Trust and the persons identified as
     authors of the code.  All rights reserved.

     Redistribution and use in source and binary forms, with or
     without modification, is permitted pursuant to, and subject to
     the license terms contained in, the Simplified BSD License set
     forth in Section 4.c of the IETF Trust's Legal Provisions
     Relating to IETF Documents
     (http://trustee.ietf.org/license-info).

     This version of this YANG module is part of RFC 7952
     (http://www.rfc-editor.org/info/rfc7952); see the RFC itself
     for full legal notices.";

  revision 2016-08-05 {
    description
      "Initial revision.";
    reference
      "RFC 7952: Defining and Using Metadata with YANG";
  }

  extension annotation {
    argument name;
    description
      "This extension allows for defining metadata annotations in
       YANG modules.  The 'md:annotation' statement can appear only
       at the top level of a YANG module or submodule, i.e., it
       becomes a new alternative in the ABNF production rule for
       'body-stmts' (Section 14 in RFC 7950).

       The argument of the 'md:annotation' statement defines the name
       of the annotation.  Syntactically, it is a YANG identifier as
       defined in Section 6.2 of RFC 7950.

       An annotation defined with this 'extension' statement inherits
       the namespace and other context from the YANG module in which
       it is defined.

       The data type of the annotation value is specified in the same
       way as for a leaf data node using the 'type' statement.

       The semantics of the annotation and other documentation can be
       specified using the following standard YANG substatements (all
       are optional): 'description', 'if-feature', 'reference',
       'status', and 'units'.

       A server announces support for a particular annotation by
       including the module in which the annotation is defined among
       the advertised YANG modules, e.g., in a NETCONF <hello>
       message or in the YANG library (RFC 7950).  The annotation can
       then be attached to any instance of a data node defined in any
       YANG module that is advertised by the server.

       XML encoding and JSON encoding of annotations are defined in
       RFC 7952.";
  }
}
