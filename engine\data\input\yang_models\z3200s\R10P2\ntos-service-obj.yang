module ntos-service-obj {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:service-obj";
  prefix ntos-service-obj;

  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS service object module.";

  revision 2021-12-21 {
    description
      "create";
    reference "";
  }

  typedef port-range {
    type string{
      ntos-ext:nc-cli-shortdesc "<port-range>";
      pattern '([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])' +
      '(-([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))?' +
      '(,([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])' +
      '(-([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))?)*' {
        error-message "Invalid port range format. Example: '21,22,1024-2048' in the range 0-65535";
      }
      length 0..128;
    }
    description
      "A comma-separated list of ports or ports ranges and the protc-id in the range 0-65535.
      Examples: '21,22,1024-2048'.";
  }

  typedef protc-id-range {
    type string {
      ntos-ext:nc-cli-shortdesc "<protc-id>";
      pattern '(25[0-6]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])(,(25[0-6]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]))*' {
        error-message "Invalid protc-id range format. Example: '21,22,10' in the range 0-255 ";
      }
      length 0..32;
    }
    description
      "Protocol-id, range 0-255, example 15,16. 256 for protocol 2-5, 7-16, 18-57, 59-255.";
  }

  typedef type-code-range {
    type string {
      pattern '25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]' {
        error-message "Invalid type or code range";
      }
      ntos-ext:nc-cli-shortdesc "<type-code>";
    }
    description
      "Input icmp type and code. Separate by space. Example: 1 21";
  }

  grouping service-obj-comm-config {
    leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Service object name.";
    }

    leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "The descrption of the service object.";
    }
  }

  grouping port-items {
    leaf source-port {
      type port-range;
    }
    leaf dest-port{
      type port-range;
    }
  }

  grouping protocl-item{
    container tcp {
      uses port-items;
    }

    container udp {
      uses port-items;
    }

    leaf protocol-id {
      type protc-id-range;
    }

    list icmp {
      key "type code";
      ntos-ext:nc-cli-one-liner;
      leaf type{
        type type-code-range;
      }
      leaf code{
        type type-code-range;
      }
    }

    list icmpv6 {
      key "type code";
      ntos-ext:nc-cli-one-liner;
      leaf type{
        type type-code-range;
      }
      leaf code{
        type type-code-range;
      }
    }
  }

  grouping service-obj-config{
    list service-set {
      key "name";
      ordered-by user;
      description
        "Detail of the service object.";

      uses service-obj-comm-config;
      uses protocl-item;
    }

    list service-group {
      key "name";
      ordered-by user;
      description
        "Detail of the service group.";

      uses service-obj-comm-config;
      list service-set {
        key "name";
        ntos-ext:nc-cli-one-liner;
        leaf name{
          type ntos-types:ntos-obj-name-type;
          description
            "The name of service object contained by service group.";
        }
      }
    }
  }

  rpc show-service-obj {
    description
      "Show service object";

    input {
      leaf vrf {
        type string;
        description
          "VRF.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";
        leaf type {
          type enumeration {
            enum service-pre;
            enum service-user;
            enum service-group;
            enum service-reserver;
          }

          description
            "Service object or service group object.";
        }

        leaf filter {
          type string;
          description
            "Filter.";
        }

        leaf name {
          type string;
          description
            "Show service object by name";
        }

        leaf start {
          type uint32;
          description
            "Show service object,start with the offset.";
        }

        leaf end {
          type uint32;
          description
            "Show service object,end with the offset.";
        }
      }

      container reference {
        description
          "The reference infomation of service object by policy";
        ntos-ext:nc-cli-group "subcommand";
        leaf name {
          type string;
          description
            "Name of service object.";
        }
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
          ntos-ext:nc-cli-stdout;
          ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "service-obj";
    ntos-api:internal;
  }

  rpc show-all-service-obj {
    description
      "Show all service object and service group.";

    input {
      leaf vrf {
        type string;
        description
          "VRF.";
      }

      leaf filter {
        type string;
        description
          "Filter.";
      }
    }

    output {
      list pre-defined-service {
        key name;
        leaf name {
          type string;
        }
      }

      list user-defined-service {
        key name;
        leaf name {
          type string;
        }
      }

      list service-group {
        key name;
        leaf name {
          type string;
        }
      }
    }

    ntos-ext:nc-cli-show "all-service-obj";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf"{
    description
      "configuration of service object .";

    container service-obj {
      presence "Make service obj available";
      description
        "Service object configuration.";
      uses service-obj-config;
    }
  }

  augment "/ntos:state/ntos:vrf"{
    description
      "State of service object.";

    container service-obj {
      presence "Make service obj available";
      description
        "Service object state";
      uses service-obj-config;
    }
  }
}
