module ntos-traffic-analy {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:traffic-analy";
  prefix ntos-traffic-analy;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS traffic analysis module.";

  revision 2021-11-19 {
    description
      "Initial version.";
    reference "";
  }

  identity traffic-analy {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Traffic analysis service.";
  }

  typedef ipv4-address {
    type union {
      type ntos-inet:ipv4-address {
        ntos-ext:nc-cli-shortdesc "<ipv4-address>";
      }
      type ntos-inet:ipv4-prefix {
        ntos-ext:nc-cli-shortdesc "<ipv4-prefix>";
      }
      type ntos-inet:ipv4-range {
        ntos-ext:nc-cli-shortdesc "<ipv4-range>";
      }
      type ntos-inet:ipv4-mask {
        ntos-ext:nc-cli-shortdesc "<ipv4-mask>";
      }
    }
    description
      "An IPv4 address, addresses range or subnet.";
  }

  grouping traffic-analy-config {
    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the traffic analysis.";
    }
    list learning-address {
      key "ipv4-address";
      max-elements 50;
      ordered-by user;
      description
        "Learning address.";
      leaf ipv4-address {
        type ipv4-address;
        description
          "An IPv4 address.";
      }
      ntos-ext:nc-cli-one-liner;
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Traffic analysis configuration.";

    container traffic-analy {
      description
        "Traffic analysis function.";
      uses traffic-analy-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Traffic analysis state.";

    container traffic-analy {
      description
        "Traffic analysis function.";
      uses traffic-analy-config;
    }
  }

  rpc traffic-analy-show {
    description
      "Show traffic analysis information.";
    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "Vrf name.";
      }
    }
    output {
      leaf enabled {
        type boolean;
        description
          "Traffic analysis status.";
      }
      leaf day {
        type int32;
        description
          "The running day of traffic analysis.";
      }
      leaf hour {
        type int8;
        description
          "The running hour of traffic analysis.";
      }
      leaf min {
        type int8;
        description
          "The running minute of traffic analysis.";
      }
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-show "traffic-analy";
    ntos-api:internal;
  }
}