module ntos-ldap {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ldap";
  prefix ntos-ldap;

  import ntos {
    prefix ntos;
  }
  import ntos-inet-types {
    prefix ntos-inet-types;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS LDAP management.";

  revision 2023-07-25 {
    description
      "Initial version.";
    reference "";
  }

  identity ldap {
    base ntos-types:SERVICE_LOG_ID;
    description
      "LDAP service.";
  }

  typedef import-mode-type {
    type enumeration {
      enum user-only {
        description
        "Only synchronize users to user management.";
      }
      enum group-only {
        description
        "Only synchronize groups to user management.";
      }
      enum user-and-group {
        description
        "Synchronize users and groups to user management.";
      }
    }
  }

  typedef conflict-handling-mode-type {
    type enumeration {
      enum ignore {
        description
        "Ignore in conflict.";
      }
      enum override {
        description
        "Override in conflict.";
      }
    }
  }

  grouping sync-user-item {
    leaf auto-sync-enabled {
      description
        "After enabled, users are automatically synchronized to user management";
      type boolean;
    }

    leaf import-mode {
      type import-mode-type;
      description
        "The action of security policy.";
    }

    list start-node {
      description
        "Start node of user synchronization.";

      key node-data;
      max-elements 5;

      leaf node-data {
        type ntos-types:ntos-obj-description-type;
        description
            "The data of node.";
      }
    }

    leaf max-depth {
      type uint32;
      description
        "Maximum depth of synchronized organizational structure.";
    }

    leaf period {
      type uint32;
      description
        "Period of synchronization.";
    }

    leaf conflict-handling-mode {
      type conflict-handling-mode-type;
      description
        "The config of How to deal with conflicts.";
    }

    leaf sync-to-user-group {
      type string {
        pattern "[^`~!#$%^&*+|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+|{};:",\<>?';
        }
      }
      description
        "The config of synchronized to the user group.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "LDAP server configuration.";

    container ldap {
      description
       "LDAP module.";

      list server-group {
        description
          "LDAP server group.";
        key name;

        leaf enabled {
          description
            "LDAP server group switch.";

          type boolean;
          default true;
        }

        leaf name {
          description
            "LDAP group name.";
          type ntos-types:ntos-obj-name-type;
        }

        leaf type {
          description
            "LDAP server type: AD/OpenLDAP.";
          type enumeration {
            enum ad {
              description
                "An AD server.";
            }
            enum openldap {
              description
                "An LDAP server.";
            }
          }
        }

        leaf user-name-attr {
          description
            "which attribute in dn stands for username.";
          type string;
        }

        leaf user-group-attr {
          description
            "which attribute in dn stands for user group.";
          type string;
        }

        leaf admin-dn {
          description
            "admin account's DN.";
          type string;
        }

        leaf password {
          description
            "admin account's password.";
          type string;
        }

        leaf base-dn {
          description
            "DN of searching entrance.";
          type string;
        }

        leaf auth-sub-filter {
          description
            "filter while searching DNs.";
          type string;
        }

        leaf use-tls {
          description
            "server tls switch";
          type boolean;
        }

        list server {
          key "ip port";
          description
            "LDAP server";
          ordered-by user;
          max-elements 2;
          leaf ip {
            description
              "LDAP server ip.";
            type ntos-inet-types:ipv4-address;
          }

          leaf port {
            description
              "LDAP server port.";
            type ntos-inet-types:port-number;
          }

          leaf source-interface {
            description
              "source interface.";
            type ntos-types:ifname;
          }
        }

        uses sync-user-item;
      }
    }
  }

  rpc ldap-test {
    description
      "ldap test bind.";
    input {
      leaf ip {
        type ntos-inet-types:ipv4-address;
      }
      leaf port {
        type ntos-inet-types:port-number;
      }
      leaf username {
        type string;
      }
      leaf password {
        type string;
      }
      leaf source-interface {
        type string;
      }
      leaf use-tls {
        type boolean;
      }
    }
    output {
      leaf result {
        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "ldap-test";
  }

  rpc show-ldap {
    input {
      container group-json {
        leaf start {
          type uint32;
        }
        leaf end {
          type uint32;
        }
        leaf filter {
          type string;
        }
        leaf name {
          type string;
        }
      }

      leaf server {
        type empty;
      }

      leaf group {
        type empty;
      }

      leaf debug-switch {
        type empty;
      }
    }
    output {
      leaf buffer {
        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "ldap";
  }

  rpc get-ldap-dn {
    description
      "get ldap base-dn.";
    input {
      leaf ip {
        type ntos-inet-types:ipv4-address;
      }
      leaf port {
        type ntos-inet-types:port-number;
      }
      leaf username {
        type string;
      }
      leaf password {
        type string;
      }
      leaf base-dn {
        type string;
      }
      leaf use-tls {
        type boolean;
      }
      leaf source-interface {
        type string;
      }
    }
    output {
      leaf result {
        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "get-ldap-dn";
  }

  rpc set-ldap-debug-switch {
    description
      "ldap debug switches";
    input {
      container debug {
        leaf all {
          type enumeration {
            enum "on";
            enum "off";
          }
        }
        leaf openldap {
          type enumeration {
            enum "on";
            enum "off";
          }
        }
        leaf user {
          type enumeration {
            enum "on";
            enum "off";
          }
        }
        leaf event {
          type enumeration {
            enum "on";
            enum "off";
          }
        }
        leaf error {
          type enumeration {
            enum "on";
            enum "off";
          }
        }
        leaf detail {
          type enumeration {
            enum "on";
            enum "off";
          }
        }
        leaf memory {
          type enumeration {
            enum "on";
            enum "off";
          }
        }
      }
      leaf tls {
        type enumeration {
          enum "on";
          enum "off";
        }
      }
    }
    ntos-ext:nc-cli-cmd "ldap";
  }

  rpc ldap-user-sync-policy {
    description
      "show ldap user sync policy.";
    input {
      leaf username {
        type string;
      }
      leaf start {
        type uint32;
        description
        "Start offset of result.";
      }
      leaf end {
        type uint32;
        description
        "End offset of result.";
      }
      leaf format {
        type empty;
        description
          "Formatting display";
      }
    }
    output {
      leaf result {
        type string;
      }
    }

    ntos-ext:nc-cli-show "ldap-user-sync-policy";
    ntos-api:internal;
  }

  rpc ldap-user-sync-manual {
    description
      " import user sync by manual.";

    input {
      leaf server-group {
        type string;
      }
    }

    output {
      leaf result {
        type string;
      }
    }

    ntos-ext:nc-cli-cmd "ldap-user-sync-manual";
    ntos-api:internal;
  }

  rpc ldap-user-sync-history {
    description
      "show ldap user sync policy.";
    input {
      leaf start {
        type uint32;
        description
        "Start offset of result.";
      }
      leaf end {
        type uint32;
        description
        "End offset of result.";
      }
      leaf uuid {
        type string;
        description
        "session detail of result.";
      }
      leaf format {
        type empty;
        description
          "Formatting display";
      }
      leaf filter {
        type ntos-types:ntos-obj-name-type;
        description
        "Show history by filter";
      }
      leaf start-time {
        type string;
        description
        "Show history by filter start time";
      }
      leaf end-time {
        type string;
        description
        "Show history by filter end time";
      }
      leaf sync-type {
        type enumeration {
          enum "auto";
          enum "manual";
        }
        description
        "Show history by filter sync type";
      }
      leaf sync-ret {
        type enumeration {
          enum "fail";
          enum "wait";
          enum "succ";
        }
        description
        "show history by filter sync ret";
      }
    }

    output {
      leaf result {
        type string;
      }
    }

    ntos-ext:nc-cli-show "ldap-user-sync-history";
    ntos-api:internal;
  }

  rpc ldap-user-sync-error-detail {
    description
      "Create ldap user sync error detial.";
    input {
      leaf uuid {
        type string;
        description
        "Session detail of result.";
      }
      leaf format {
        type empty;
        description
          "Formatting display";
      }
    }

    output {
      leaf result {
        type string;
      }
    }

    ntos-ext:nc-cli-cmd "ldap-user-sync-error-detail";
    ntos-api:internal;
  }
}