package middleware

import (
	"bufio"
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao/user/daccesslog"
	"irisAdminApi/service/dao/user/duser"
	"net"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/iris-contrib/middleware/jwt"
	"github.com/kataras/iris/v12"
)

const (
	Layout        = "2006-01-02 15:04:05"
	RedisKey      = "AccessLog"
	BatchSize     = 1000 // 每批次处理的日志行数
	MaxGoroutines = 10   // 最大并发数
)

func AccessLogger(ctx iris.Context) {
	parts := strings.Split(ctx.Path(), "/") // /api/v1/服务名称
	var server string                       // 服务名称
	if len(parts) > 3 {
		server = parts[3]
	}
	var token string
	if ctx.Values().Get("jwt") == nil {
		token = ctx.GetCookie("AccessToken")
	} else {
		token = ctx.Values().Get("jwt").(*jwt.Token).Raw
	}
	userID := "NA"
	if token != "" {
		sess, err := duser.Check(token)
		if err != nil {
			userID = "unknown"
		} else {
			userID = sess.UserId
		}
	}
	//服务名称 ip  userid  请求方式  请求路径  请求方法  路由名称
	currentTime := time.Now().Local()
	formattedTime := currentTime.Format(Layout)
	addr := GetRemoteAddr(ctx) //获取真实IP
	msg := strings.Join([]string{formattedTime, server, addr, ctx.Method(), ctx.Path(), ctx.GetCurrentRoute().MainHandlerName(), ctx.GetCurrentRoute().Name(), userID}, "|")
	rc := cache.GetRedisClusterClient()
	_, err := rc.LPush(RedisKey, msg)
	if err != nil {
		logging.ErrorLogger.Error(err)
	}
	ctx.Next()
}

func AccessLogWirte() {
	rc := cache.GetRedisClusterClient()
	// 循环读取并删除所有元素
	allItems, err := rc.LPopAll(RedisKey, BatchSize)
	if err != nil {
		logging.ErrorLogger.Errorf("Error popping all items from list:", err.Error())
		return
	}
	for _, allAccessLogs := range allItems {
		// 处理每个元素
		if len(allAccessLogs) == 0 {
			return
		}
		var accessLogs []*daccesslog.Response
		for _, accesslogStr := range allAccessLogs {
			rslog := strings.Split(accesslogStr, "|")
			//转换为日志对象
			createdAt, err := time.ParseInLocation(Layout, rslog[0], time.FixedZone("CST", 8*3600))
			if err != nil {
				logging.ErrorLogger.Errorf("Error ParseInLocatio:", err.Error())
				continue
			}
			accessLog := &daccesslog.Response{
				CreatedAt: createdAt,
				UpdatedAt: createdAt,
			}
			// 可选字段，检查是否存在
			if len(rslog) > 1 {
				accessLog.ServerName = rslog[1]
			}
			if len(rslog) > 2 {
				accessLog.IP = rslog[2]
			}
			if len(rslog) > 3 {
				accessLog.RequestMethod = rslog[3]
			}
			if len(rslog) > 4 {
				accessLog.RequestPath = rslog[4]
			}
			if len(rslog) > 5 {
				accessLog.RequestAction = rslog[5]
			}
			if len(rslog) > 6 {
				accessLog.RouteName = rslog[6]
			}
			if len(rslog) > 7 {
				accessLog.UserID = rslog[7]
			}
			//存放到日志对象切片中
			accessLogs = append(accessLogs, accessLog)
		}
		err = daccesslog.UpdateOrCreateAccesslogTransaction(accessLogs)
		if err != nil {
			fmt.Println(err)
			logging.ErrorLogger.Errorf("Error UpdateOrCreateAccesslogTransaction:", err.Error())
			return
		}
	}
}

// archiveLogs 归档日志
func archiveLogs(logDir string, siteName string) error {
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1).Format("2006-01-02")
	logFileName := fmt.Sprintf("%s.access.log", siteName)
	logFilePath := filepath.Join(logDir, logFileName)
	archiveDir := filepath.Join(logDir, "archive", siteName, yesterday)

	fileInfo, err := os.Stat(logFilePath)
	if os.IsNotExist(err) {
		logging.ErrorLogger.Errorf(logFileName + "日志文件不存在")
		if err := createEmptyLog(logDir, logFileName); err != nil {
			return err
		}
		return errors.New(logFileName + "日志文件不存在")
	} else if fileInfo.Size() == 0 {
		return nil // 文件为空，直接返回，不执行后续归档操作
	}

	// 创建归档目录
	if err := os.MkdirAll(archiveDir, 0755); err != nil {
		return err
	}

	// 移动日志文件
	oldPath := filepath.Join(logDir, logFileName)
	newPath := filepath.Join(archiveDir, logFileName)
	if err := os.Rename(oldPath, newPath); err != nil {
		return err
	}
	// 创建新的日志文件
	_, err = os.Create(oldPath)
	if err != nil {
		return err
	}
	// 分析归档日志
	err = analyzeArchiveLog(newPath, siteName)
	return err
}

type LogEntry struct {
	IP        string
	Time      time.Time
	Method    string
	Path      string
	Protocol  string
	Status    string
	Size      string
	Referer   string
	UserAgent string
}

func AnalyzeLogs() {
	// 假设已经知道要分析的日志文件路径
	Sites := strings.Split(libs.Config.AccessLog.Sites, "|")
	for _, siteName := range Sites {
		logFilePath := libs.Config.AccessLog.LogPath
		err := archiveLogs(logFilePath, siteName)
		if err != nil {
			logging.ErrorLogger.Errorf("Error archiving logs:", err)
			continue
		}
	}
}

func analyzeArchiveLog(logFilePath, siteName string) error {
	file, err := os.Open(logFilePath)
	if err != nil {
		fmt.Println("Error opening log file:", err)
		return err
	}
	defer file.Close()

	rc := cache.GetRedisClusterClient()

	scanner := bufio.NewScanner(file)
	var wg sync.WaitGroup
	lines := make([]string, 0, BatchSize)
	lineChan := make(chan []string, MaxGoroutines)

	// 启动一个goroutine来处理批量数据
	go func() {
		for batch := range lineChan {
			wg.Add(1)
			go func(batch []string) {
				defer wg.Done()
				processBatch(batch, rc, siteName)
			}(batch)
		}
	}()

	for scanner.Scan() {
		lines = append(lines, scanner.Text())
		if len(lines) >= BatchSize {
			lineChan <- lines
			lines = make([]string, 0, BatchSize)
		}
	}

	// 处理剩余的行
	if len(lines) > 0 {
		lineChan <- lines
	}

	close(lineChan)
	wg.Wait()

	if err := scanner.Err(); err != nil {
		fmt.Println("Error reading log file:", err)
		return err
	}
	return nil
}

func processBatch(lines []string, rc *cache.RedisCluster, siteName string) {
	for _, line := range lines {
		entry, err := parseLogLine(line)
		if err != nil {
			fmt.Println("Error parsing log line:", err)
			continue
		}
		formattedTime := entry.Time.Format(Layout)
		msg := strings.Join([]string{formattedTime, siteName, entry.IP, entry.Method, entry.Path, entry.Referer, "", "NA"}, "|")
		_, err = rc.LPush(RedisKey, msg)
		if err != nil {
			logging.ErrorLogger.Error(err)
			continue
		}
	}
}

func parseLogLine(line string) (LogEntry, error) {
	// 使用正则表达式解析日志行
	// 注意：这个正则表达式是简化的，可能需要根据实际日志格式进行调整
	re := regexp.MustCompile(`(\d+\.\d+\.\d+\.\d+) - - \[(.*?)\] "(.*?) (.*?) (.*?)" (\d+) (\d+) "(.*?)" "(.*?)"`)
	matches := re.FindStringSubmatch(line)
	logTime, err := parseLogTime(matches[2])
	if err != nil {
		logging.ErrorLogger.Errorf("Error parsing log time:", err)
		return LogEntry{}, errors.New("error parsing log time")
	}
	return LogEntry{
		IP:        matches[1],
		Time:      logTime,
		Method:    matches[3],
		Path:      matches[4],
		Protocol:  matches[5],
		Status:    matches[6],
		Size:      matches[7],
		Referer:   matches[8],
		UserAgent: matches[9],
	}, nil
}

func parseLogTime(logTime string) (time.Time, error) {
	// 定义Nginx日志时间的格式
	const logTimeLayout = "02/Jan/2006:15:04:05 -0700"
	// 解析日志时间
	parsedTime, err := time.Parse(logTimeLayout, logTime)
	if err != nil {
		return time.Time{}, err
	}
	return parsedTime, nil
}

func analyzeArchiveLogBak(logFilePath, siteName string) error {
	// 打开日志文件
	file, err := os.Open(logFilePath)
	if err != nil {
		fmt.Println("Error opening log file:", err)
		return err
	}
	defer file.Close()

	rc := cache.GetRedisClusterClient()

	// 逐行读取日志文件
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		entry, err := parseLogLine(line)
		if err != nil {
			fmt.Println("Error parsing log line:", err)
			continue
		}
		// 这里可以处理日志条目，比如存储到Redis
		formattedTime := entry.Time.Format(Layout)
		msg := strings.Join([]string{formattedTime, siteName, entry.IP, entry.Method, entry.Path, entry.Referer, "", "NA"}, "|")
		_, err = rc.LPush("AccessLog", msg)
		if err != nil {
			logging.ErrorLogger.Error(err)
			continue
		}
	}

	if err := scanner.Err(); err != nil {
		fmt.Println("Error reading log file:", err)
		return err
	}
	return nil
}

// createEmptyLog 创建一个空的日志文件
func createEmptyLog(dir, name string) error {
	_, err := os.Create(filepath.Join(dir, name))
	if err != nil {
		return err
	}
	logging.ErrorLogger.Info("Info: 创建了空的日志文件: %s", name)
	return nil
}

func GetRemoteAddr(ctx iris.Context) string {
	addr := ctx.GetHeader("X-Real-Ip")
	if len(addr) == 0 {
		addr := strings.TrimSpace(ctx.Request().RemoteAddr)
		if addr != "" {
			// if addr has port use the net.SplitHostPort otherwise(error occurs) take as it is
			if ip, _, err := net.SplitHostPort(addr); err == nil {
				return ip
			}
		}
	} else {
		if ip, _, err := net.SplitHostPort(addr); err == nil {
			return ip
		}
	}
	return addr
}
