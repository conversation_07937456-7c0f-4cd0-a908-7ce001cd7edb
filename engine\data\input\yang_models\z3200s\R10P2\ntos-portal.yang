module ntos-portal {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:portal";
  prefix ntos-portal;

  import ntos {
    prefix ntos;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS Portal.";

  revision 2023-03-28 {
    description
      "Initial version.";
    reference
      "";
  }

  identity portal {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Portal service.";
  }

  grouping system-portal-config {
    description
      "Configuration data for system portal configuration.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the portal.";
    }

    leaf port {
      type ntos-inet:port-number;
      default "8081";
      description
        "The local port of the portal.";
    }

    container customized-page {
      description
        "Configure customized content for the Portal authentication page.";
      leaf modify-password-enabled {
        type boolean;
        description
          "Enable or disable the switch of modify password.";
      }
    }

    leaf ssl-enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the portal ssl.";
    }

    leaf redirection-mode {
      default no-redirection;
      description "Configure the redirection mode for the authenticated users.";
      type enumeration {
          enum "no-redirection" {
              description "No redirection, and stay on authentication page.";
          }
          enum "previous-page" {
              description "Redirects to the last page you visited.";
          }
          enum "customized-url" {
              description "Redirects to the customized URL.";
          }
      }
    }

    leaf customized-url {
      when "../redirection-mode = 'customized-url'";
      description "Configure the customized URL.";
      type ntos-types:http-dual-stack-url;
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Portal configuration.";

    container wba-portal {
      description
        "Portal configuration.";
      uses system-portal-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Portal state.";

    container wba-portal {
      description
        "Portal state.";
      uses system-portal-config;
    }
  }

}


