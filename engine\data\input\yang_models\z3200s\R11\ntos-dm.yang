module ntos-dm {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:dm";
  prefix ntos-dm;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS device management module.";

  revision 2021-12-01 {
    description
      "Device management RPCs";
    reference "revision 2021-12-01";
  }

  typedef dm-device-type {
    type enumeration {
      enum "fan" {
        description
          "Device: fan";
      }
      enum "power" {
        description
          "Device: power";
      }
      enum "temperature" {
        description
          "Device: temperature";
      }
      enum "voltage" {
        description
          "Device: voltage";
      }
      enum "bypass" {
        description
          "Device: bypass";
      }
    }
    description
      "Device Management's device type";
  }

  grouping dm-attr-output-info {
    leaf id {
      type uint32;
      description
        "A attribute's id.";
    }

    leaf attribute {
      type string;
      description
        "A attribute's name.";
    }

    leaf data {
      type string;
      description
        "A attribute's data.";
    }

    leaf detail {
      type string;
      description
        "A attribute's detail data.";
    }

    ntos-ext:nc-cli-stdout;
    ntos-ext:nc-cli-hidden;
  }

  grouping dm-dev-output-info {
    leaf name {
      type string;
      description
        "A device's name.";
    }

    list attributes {
      key "id";
      uses dm-attr-output-info;
    }

    ntos-ext:nc-cli-stdout;
    ntos-ext:nc-cli-hidden;
  }

  rpc show-slot {
    description
      "Show slot information.";
    output {
      list slot {
        key id;
        leaf id {
          type string;
          description
            "The slot number.";
        }
        leaf ports {
          type string;
          description
            "The max ports of this module.";
        }
        leaf module {
          type string;
          description
            "The slot module name.";
        }
        leaf serialnum {
          type string;
          description
            "The serial number of this module.";
        }
        leaf version {
          type string;
          description
            "The hardware version of this module.";
        }
        leaf present {
          type string;
          description
            "The online/offline status of this module.";
        }
        leaf status {
          type string;
          description
            "The working status of this module.";
        }
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "slot";
    ntos-api:internal;
  }

  rpc show-fan {
    description
      "Show fan information.";
    output {
      list fan {
        key id;
        leaf id {
          type string;
          description
            "The fan number.";
        }
        leaf level {
          type uint32;
          description
            "The speed level.";
        }
        leaf speed {
          type string;
          description
            "The speed of fan";
        }
        leaf present {
          type string;
          description
           "The online/offline status of fan.";
        }
        leaf status {
          type string;
          description
            "The working status of fan.";
        }
        leaf model {
          type string;
          description
            "The fan model nums.";
        }
        leaf serialnum {
          type string;
          description
            "The serialnum of fan.";
        }
        leaf version {
          type string;
          description
            "The version of fan.";
        }
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "fan";
    ntos-api:internal;
  }

  rpc show-power {
    description
      "Show power information.";
    output {
      list power {
        key id;
        leaf id {
          type string;
          description
            "The power number.";
        }
        leaf present {
          type string;
          description
            "The online/offline status of power.";
        }
        leaf status {
          type string;
          description
            "The working status of power.";
        }
        leaf model {
          type string;
          description
            "The power model nums.";
        }
        leaf serialnum {
          type string;
          description
            "The serialnum of power.";
        }
        leaf version {
          type string;
          description
            "The version of power.";
        }
        leaf in-voltage {
            type string;
            description
               "The input voltage(Unit: V) of the power.";
        }
        leaf in-current {
            type string;
            description
               "The input current(Unit: mA) of the power.";
        }
        leaf in-power {
            type string;
            description
               "The input power(Unit: W) of the power.";
        }
        leaf out-voltage {
            type string;
            description
               "The output voltage(Unit: V) of the power.";
        }
        leaf out-current {
            type string;
            description
               "The output current(Unit: mA) of the power.";
        }
        leaf out-power {
            type string;
            description
               "The output power(Unit: W) of the power.";
        }
        leaf rated-power {
            type string;
            description
               "The rated power(Unit: W) of the power.";
        }
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "power";
    ntos-api:internal;
  }

  rpc show-temperature {
    description
      "Show temperature.";
    output {
      list temperature {
        key name;
        leaf name {
          type string;
          description
            "The temperature name.";
        }
        leaf value {
          type string;
          description
            "The temperature value.";
        }
        leaf status {
          type string;
          description
            "Warning status.";
        }
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "temperature";
    ntos-api:internal;
  }

  rpc show-voltage {
    description
      "Show voltage information.";
    output {
      list voltage {
        key id;
        leaf id {
          type string;
          description
            "The voltage number.";
        }
        leaf name {
          type string;
          description
            "The voltage name.";
        }
        leaf value {
          type int32;
          units "mV";
          description
            "The voltage value.";
        }
        leaf status {
          type string;
          description
            "Warning status.";
        }
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "voltage";
    ntos-api:internal;
  }

  rpc show-sata {
    description
      "Show sata information.";
    output {
	  leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "sata";
    ntos-api:internal;
  }

  rpc show-usb {
    description
      "Show usb information.";
    output {
	  leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "usb";
    ntos-api:internal;
  }

  rpc show-emmc {
    description
      "Show emmc information.";
    output {
	  leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "emmc";
    ntos-api:internal;
  }

  rpc show-reboot {
    description
      "Show reboot cause.";

    input {
      leaf detail {
        type enumeration {
          enum detail {
            description
              "Display more reboot cause.";
          }
        }
        description
          "Details information requested.";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
	  leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "reboot";
    ntos-api:internal;
  }

  rpc show-interface-transceiver {
    description
      "Show interface transceiver information.";
    input {
      leaf name {
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
        ntos-ext:nc-cli-order "1";
        type ntos-types:ifname;
        description
          "Show interface transceiver by this name.";
      }
      leaf level {
        type enumeration {
          enum detail {
            description
              "Show details information.";
          }
          enum diagnostics {
            description
              "Show diagnostics information.";
          }
        }
        description
          "The level of information requested.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-order "2";
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "interface transceiver";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc dm-device {
    ntos-ext:nc-cli-cmd "dm device";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "List all device info.";

    input {
      leaf detail {
        type enumeration {
          enum detail {
            description
              "Device details information.";
          }
        }
        description
          "Details information requested.";
        ntos-ext:nc-cli-no-name;
      }
    }


    output {
      list devices {
        key "name";
        uses dm-dev-output-info;
      }
    }
  }

  rpc dm-device-list {
    ntos-ext:nc-cli-cmd "dm device list";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "List all devices name.";

    output {
      list names {
        key "id";
        leaf id {
          type uint32;
          description
            "A device's id.";
        }
        leaf type {
          type string;
          description
            "A device's type.";
        }
        leaf name {
          type string;
          description
            "A device's name.";
        }
      }
    }
  }

  rpc dm-device-name {
    ntos-ext:nc-cli-cmd "dm device name";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "A information of a device name.";
    input {
      leaf name {
        type string;
        mandatory true;
        description
          "The device's name.";
        ntos-ext:nc-cli-order "1";
        ntos-ext:nc-cli-no-name;
      }
      leaf detail {
        type enumeration {
          enum detail {
            description
              "Device details information.";
          }
        }
        description
          "Details information requested.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-order "2";
      }
    }

    output {
      list attrs {
        key "id";
        uses dm-attr-output-info;
      }
    }
  }

  rpc dm-device-type {
    ntos-ext:nc-cli-cmd "dm device type";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "List one type's device information.";
    input {
      leaf type {
          type union {
            type dm-device-type;
            type string;
          }
          mandatory true;
          description
            "A device type.";
          ntos-ext:nc-cli-order "1";
          ntos-ext:nc-cli-no-name;
      }
      leaf detail {
        type enumeration {
          enum detail {
            description
              "Device details information.";
          }
        }
        description
          "Details information requested.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-order "2";
      }
    }

    output {
      list devices {
        key "name";
        uses dm-dev-output-info;
      }
    }
  }

  rpc dm-info {
    ntos-api:internal;
    description
      "List types's device information.";
    input {
      list type {
        description
          "The dev type.";
        key "name";
        leaf name {
          mandatory true;
          type string;
        }

        leaf attrlist {
          type string;
          description
            "List of attr name, use comma to split.";
        }

        leaf limit_con {
          type string;
          description
            "Limit conditions of attrs.";
        }

        leaf detail {
          type enumeration {
            enum detail {
              description
                "Device details information.";
            }
          }
          description
            "Details information requested.";
          ntos-ext:nc-cli-no-name;
        }
      }

      leaf format {
        type enumeration {
          enum json;
          enum xml;
        }
        description
          "The format that want to return.";
      }
    }

    output {
      list devices {
        key "name";
        uses dm-dev-output-info;
      }

      leaf json_out {
        type string;
      }
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Position config.";

    container position {
      description
        "enable or disable LOGO led";

      leaf status {
        type boolean;
        description
        "Enable or disable LOGO led";
      }
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Position config.";

    container position {
      description
        "Show LOGO led is enable or disable.";

      leaf status {
        type boolean;
        description
        "Show LOGO led is enable or disable.";
      }
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Bypass config.";

    container bypass {
      list couple {
        key "couple-id";
        description
          "Which couple to enable or disable.";

        leaf couple-id {
          type uint32;
          description
            "Which couple to enable or disable.";
        }

        leaf enable {
          type boolean;
          description
          "Enable or disable the couple.";
        }
      }
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "Bypass config.";

    container bypass {
      list couple {
        key "couple-id";
        description
          "Which couple to enable or disable.";

        leaf couple-id {
          type uint32;
          description
            "Which couple to enable or disable.";
        }

        leaf enable {
          type boolean;
          description
          "Enable or disable the couple.";
        }

        leaf interface0 {
          type string;
          description
          "config information of interface0.";
        }

        leaf interface1 {
          type string;
          description
          "config information of interface1.";
        }
      }
    }
  }
}
