# -*- coding: utf-8 -*-
"""
规则注册表 - 管理转换规则
"""

from typing import Dict, Any
from engine.utils.logger import log
from engine.utils.i18n import _


class RuleRegistry:
    """
    规则注册表
    管理各种转换规则
    """
    
    def __init__(self):
        """初始化规则注册表"""
        self._rules = {}
        log(_("rule_registry.initialized"), "info")
    
    def register_rule(self, rule_name: str, rule_config: Dict[str, Any]):
        """
        注册转换规则
        
        Args:
            rule_name: 规则名称
            rule_config: 规则配置
        """
        self._rules[rule_name] = rule_config
        log(_("rule_registry.rule_registered"), "info", name=rule_name)
    
    def get_rule(self, rule_name: str) -> Dict[str, Any]:
        """
        获取转换规则
        
        Args:
            rule_name: 规则名称
            
        Returns: Dict[str, Any]: 规则配置
        """
        return self._rules.get(rule_name, {})
