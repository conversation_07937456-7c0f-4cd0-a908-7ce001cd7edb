"""
地址池容量分析器

提供地址池容量分析和预测功能，包括：
- 容量计算和统计
- 使用预测和趋势分析
- 冲突检测和解决建议
- 性能优化建议
"""

import ipaddress
import math
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from engine.utils.logger import log
from engine.utils.i18n import _


@dataclass
class PoolCapacityInfo:
    """地址池容量信息"""
    pool_name: str
    total_addresses: int
    usable_addresses: int
    network_efficiency: float
    subnet_alignment: bool
    fragmentation_score: float
    utilization_prediction: float


@dataclass
class CapacityAnalysisResult:
    """容量分析结果"""
    total_pools: int
    total_capacity: int
    average_efficiency: float
    capacity_distribution: Dict[str, int]
    optimization_opportunities: List[str]
    conflict_warnings: List[str]
    performance_metrics: Dict[str, float]


class IPPoolCapacityAnalyzer:
    """地址池容量分析器"""
    
    def __init__(self):
        # 分析配置参数
        self.optimal_pool_size_range = (64, 1024)  # 最优池大小范围
        self.fragmentation_threshold = 0.7  # 碎片化阈值
        self.efficiency_threshold = 0.8  # 效率阈值
        self.utilization_prediction_factor = 0.6  # 利用率预测因子
        
        # 网络类型权重
        self.network_type_weights = {
            "private": 1.0,
            "public": 0.8,
            "reserved": 0.0
        }
    
    def analyze_pool_capacity(self, pool_name: str, pool_config: Dict) -> PoolCapacityInfo:
        """
        分析单个地址池容量
        
        Args:
            pool_name: 池名称
            pool_config: 池配置
            
        Returns:
            PoolCapacityInfo: 容量分析信息
        """
        try:
            start_ip = pool_config.get("startip")
            end_ip = pool_config.get("endip")
            
            if not start_ip or not end_ip:
                log(_("capacity_analyzer.missing_ip_range", pool_name=pool_name), "warning")
                return self._create_empty_capacity_info(pool_name)
            
            start_addr = ipaddress.IPv4Address(start_ip)
            end_addr = ipaddress.IPv4Address(end_ip)
            
            # 计算基本容量信息
            total_addresses = int(end_addr) - int(start_addr) + 1
            usable_addresses = self._calculate_usable_addresses(start_addr, end_addr)
            
            # 计算网络效率
            network_efficiency = self._calculate_network_efficiency(start_addr, end_addr)
            
            # 检查子网对齐
            subnet_alignment = self._check_subnet_alignment(start_addr, end_addr)
            
            # 计算碎片化分数
            fragmentation_score = self._calculate_fragmentation_score(start_addr, end_addr)
            
            # 预测利用率
            utilization_prediction = self._predict_utilization(total_addresses, network_efficiency)
            
            return PoolCapacityInfo(
                pool_name=pool_name,
                total_addresses=total_addresses,
                usable_addresses=usable_addresses,
                network_efficiency=network_efficiency,
                subnet_alignment=subnet_alignment,
                fragmentation_score=fragmentation_score,
                utilization_prediction=utilization_prediction
            )
            
        except Exception as e:
            log(_("capacity_analyzer.analysis_failed", pool_name=pool_name, error=str(e)), "error")
            return self._create_empty_capacity_info(pool_name)
    
    def _calculate_usable_addresses(self, start_addr: ipaddress.IPv4Address, 
                                   end_addr: ipaddress.IPv4Address) -> int:
        """计算可用地址数量"""
        total = int(end_addr) - int(start_addr) + 1
        
        # 检查是否跨越多个子网
        start_network = ipaddress.IPv4Network(f"{start_addr}/24", strict=False)
        end_network = ipaddress.IPv4Network(f"{end_addr}/24", strict=False)
        
        if start_network == end_network:
            # 在同一子网内，需要排除网络地址和广播地址（如果包含）
            network_addr = start_network.network_address
            broadcast_addr = start_network.broadcast_address
            
            excluded = 0
            if start_addr <= network_addr <= end_addr:
                excluded += 1
            if start_addr <= broadcast_addr <= end_addr:
                excluded += 1
                
            return max(0, total - excluded)
        else:
            # 跨子网，简化计算
            return total
    
    def _calculate_network_efficiency(self, start_addr: ipaddress.IPv4Address, 
                                    end_addr: ipaddress.IPv4Address) -> float:
        """计算网络效率"""
        try:
            total_addresses = int(end_addr) - int(start_addr) + 1
            usable_addresses = self._calculate_usable_addresses(start_addr, end_addr)
            
            # 基础效率
            base_efficiency = usable_addresses / total_addresses if total_addresses > 0 else 0
            
            # 考虑网络类型权重
            network_type = self._determine_network_type(start_addr, end_addr)
            type_weight = self.network_type_weights.get(network_type, 0.5)
            
            # 考虑大小因子
            size_factor = self._calculate_size_efficiency_factor(total_addresses)
            
            return base_efficiency * type_weight * size_factor
            
        except Exception:
            return 0.0
    
    def _determine_network_type(self, start_addr: ipaddress.IPv4Address, 
                               end_addr: ipaddress.IPv4Address) -> str:
        """确定网络类型"""
        # 私有网络范围
        private_networks = [
            ipaddress.IPv4Network('10.0.0.0/8'),
            ipaddress.IPv4Network('**********/12'),
            ipaddress.IPv4Network('***********/16')
        ]
        
        # 保留网络范围
        reserved_networks = [
            ipaddress.IPv4Network('*********/8'),
            ipaddress.IPv4Network('***********/16'),
            ipaddress.IPv4Network('*********/4'),
            ipaddress.IPv4Network('240.0.0.0/4')
        ]
        
        # 检查是否在私有网络范围内
        for private_net in private_networks:
            if start_addr in private_net and end_addr in private_net:
                return "private"
        
        # 检查是否包含保留地址
        for reserved_net in reserved_networks:
            if start_addr in reserved_net or end_addr in reserved_net:
                return "reserved"
        
        return "public"
    
    def _calculate_size_efficiency_factor(self, total_addresses: int) -> float:
        """计算大小效率因子"""
        optimal_min, optimal_max = self.optimal_pool_size_range
        
        if optimal_min <= total_addresses <= optimal_max:
            return 1.0
        elif total_addresses < optimal_min:
            # 小池效率降低
            return 0.5 + 0.5 * (total_addresses / optimal_min)
        else:
            # 大池效率逐渐降低
            return max(0.3, 1.0 - 0.1 * math.log10(total_addresses / optimal_max))
    
    def _check_subnet_alignment(self, start_addr: ipaddress.IPv4Address, 
                               end_addr: ipaddress.IPv4Address) -> bool:
        """检查子网对齐"""
        try:
            # 检查是否对齐到常见的子网边界
            common_alignments = [256, 128, 64, 32, 16]  # /24, /25, /26, /27, /28
            
            for alignment in common_alignments:
                if (int(start_addr) % alignment == 0 and 
                    (int(end_addr) + 1) % alignment == 0):
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _calculate_fragmentation_score(self, start_addr: ipaddress.IPv4Address, 
                                     end_addr: ipaddress.IPv4Address) -> float:
        """计算碎片化分数（0-1，越低越好）"""
        try:
            total_addresses = int(end_addr) - int(start_addr) + 1
            
            # 检查是否跨越多个子网
            start_network = ipaddress.IPv4Network(f"{start_addr}/24", strict=False)
            end_network = ipaddress.IPv4Network(f"{end_addr}/24", strict=False)
            
            if start_network == end_network:
                # 在同一子网内，碎片化程度较低
                subnet_size = int(start_network.broadcast_address) - int(start_network.network_address) + 1
                fragmentation = 1.0 - (total_addresses / subnet_size)
            else:
                # 跨子网，计算跨越的子网数量
                subnets_crossed = abs(int(end_network.network_address) - int(start_network.network_address)) // 256 + 1
                fragmentation = min(1.0, subnets_crossed / 10.0)  # 标准化到0-1
            
            return fragmentation
            
        except Exception:
            return 1.0  # 最高碎片化
    
    def _predict_utilization(self, total_addresses: int, network_efficiency: float) -> float:
        """预测利用率"""
        try:
            # 基于地址池大小和网络效率预测利用率
            size_factor = min(1.0, total_addresses / self.optimal_pool_size_range[1])
            
            # 利用率预测公式（简化模型）
            predicted_utilization = (
                self.utilization_prediction_factor * network_efficiency * size_factor
            )
            
            return min(1.0, predicted_utilization)
            
        except Exception:
            return 0.0
    
    def _create_empty_capacity_info(self, pool_name: str) -> PoolCapacityInfo:
        """创建空的容量信息"""
        return PoolCapacityInfo(
            pool_name=pool_name,
            total_addresses=0,
            usable_addresses=0,
            network_efficiency=0.0,
            subnet_alignment=False,
            fragmentation_score=1.0,
            utilization_prediction=0.0
        )

    def analyze_multiple_pools(self, ippools: Dict[str, Dict]) -> CapacityAnalysisResult:
        """
        分析多个地址池的整体容量情况

        Args:
            ippools: IP池配置字典

        Returns:
            CapacityAnalysisResult: 整体分析结果
        """
        pool_infos = []

        # 分析每个池
        for pool_name, pool_config in ippools.items():
            pool_info = self.analyze_pool_capacity(pool_name, pool_config)
            pool_infos.append(pool_info)

        # 计算整体统计
        total_pools = len(pool_infos)
        total_capacity = sum(info.total_addresses for info in pool_infos)

        # 计算平均效率
        valid_pools = [info for info in pool_infos if info.total_addresses > 0]
        average_efficiency = (
            sum(info.network_efficiency for info in valid_pools) / len(valid_pools)
            if valid_pools else 0.0
        )

        # 容量分布统计
        capacity_distribution = self._calculate_capacity_distribution(pool_infos)

        # 生成优化机会
        optimization_opportunities = self._identify_optimization_opportunities(pool_infos)

        # 检测冲突警告
        conflict_warnings = self._detect_capacity_conflicts(ippools, pool_infos)

        # 计算性能指标
        performance_metrics = self._calculate_performance_metrics(pool_infos)

        return CapacityAnalysisResult(
            total_pools=total_pools,
            total_capacity=total_capacity,
            average_efficiency=average_efficiency,
            capacity_distribution=capacity_distribution,
            optimization_opportunities=optimization_opportunities,
            conflict_warnings=conflict_warnings,
            performance_metrics=performance_metrics
        )

    def _calculate_capacity_distribution(self, pool_infos: List[PoolCapacityInfo]) -> Dict[str, int]:
        """计算容量分布"""
        distribution = {
            "small": 0,    # < 64
            "medium": 0,   # 64-1024
            "large": 0,    # 1024-4096
            "xlarge": 0    # > 4096
        }

        for info in pool_infos:
            if info.total_addresses < 64:
                distribution["small"] += 1
            elif info.total_addresses <= 1024:
                distribution["medium"] += 1
            elif info.total_addresses <= 4096:
                distribution["large"] += 1
            else:
                distribution["xlarge"] += 1

        return distribution

    def _identify_optimization_opportunities(self, pool_infos: List[PoolCapacityInfo]) -> List[str]:
        """识别优化机会"""
        opportunities = []

        # 低效率池
        low_efficiency_pools = [
            info for info in pool_infos
            if info.network_efficiency < self.efficiency_threshold and info.total_addresses > 0
        ]

        if low_efficiency_pools:
            opportunities.append(
                _("capacity_analyzer.low_efficiency_pools_found",
                  count=len(low_efficiency_pools))
            )

        # 高碎片化池
        fragmented_pools = [
            info for info in pool_infos
            if info.fragmentation_score > self.fragmentation_threshold
        ]

        if fragmented_pools:
            opportunities.append(
                _("capacity_analyzer.fragmented_pools_found",
                  count=len(fragmented_pools))
            )

        # 未对齐的池
        unaligned_pools = [
            info for info in pool_infos
            if not info.subnet_alignment and info.total_addresses > 32
        ]

        if unaligned_pools:
            opportunities.append(
                _("capacity_analyzer.unaligned_pools_found",
                  count=len(unaligned_pools))
            )

        # 小池合并机会
        small_pools = [info for info in pool_infos if info.total_addresses < 32]
        if len(small_pools) >= 2:
            opportunities.append(
                _("capacity_analyzer.small_pools_merge_opportunity",
                  count=len(small_pools))
            )

        # 大池拆分建议
        large_pools = [info for info in pool_infos if info.total_addresses > 4096]
        if large_pools:
            opportunities.append(
                _("capacity_analyzer.large_pools_split_suggestion",
                  count=len(large_pools))
            )

        return opportunities

    def _detect_capacity_conflicts(self, ippools: Dict[str, Dict],
                                  pool_infos: List[PoolCapacityInfo]) -> List[str]:
        """检测容量冲突"""
        conflicts = []

        # 检查地址范围重叠
        pool_ranges = {}
        for pool_name, pool_config in ippools.items():
            try:
                start_ip = pool_config.get("startip")
                end_ip = pool_config.get("endip")
                if start_ip and end_ip:
                    start_addr = ipaddress.IPv4Address(start_ip)
                    end_addr = ipaddress.IPv4Address(end_ip)
                    pool_ranges[pool_name] = (start_addr, end_addr)
            except:
                continue

        # 检查重叠
        pool_names = list(pool_ranges.keys())
        for i, pool1 in enumerate(pool_names):
            for pool2 in pool_names[i+1:]:
                if self._check_range_overlap(pool_ranges[pool1], pool_ranges[pool2]):
                    conflicts.append(
                        _("capacity_analyzer.address_range_overlap",
                          pool1=pool1, pool2=pool2)
                    )

        # 检查容量不平衡
        if pool_infos:
            capacities = [info.total_addresses for info in pool_infos if info.total_addresses > 0]
            if capacities:
                max_capacity = max(capacities)
                min_capacity = min(capacities)
                if max_capacity > min_capacity * 10:  # 10倍差异
                    conflicts.append(
                        _("capacity_analyzer.capacity_imbalance_detected",
                          max_capacity=max_capacity, min_capacity=min_capacity)
                    )

        return conflicts

    def _check_range_overlap(self, range1: Tuple, range2: Tuple) -> bool:
        """检查两个地址范围是否重叠"""
        start1, end1 = range1
        start2, end2 = range2

        return (start2 <= start1 <= end2 or start2 <= end1 <= end2 or
                start1 <= start2 <= end1 or start1 <= end2 <= end1)

    def _calculate_performance_metrics(self, pool_infos: List[PoolCapacityInfo]) -> Dict[str, float]:
        """计算性能指标"""
        if not pool_infos:
            return {}

        valid_pools = [info for info in pool_infos if info.total_addresses > 0]
        if not valid_pools:
            return {}

        # 计算各种指标
        efficiencies = [info.network_efficiency for info in valid_pools]
        fragmentations = [info.fragmentation_score for info in valid_pools]
        utilizations = [info.utilization_prediction for info in valid_pools]

        return {
            "average_efficiency": sum(efficiencies) / len(efficiencies),
            "max_efficiency": max(efficiencies),
            "min_efficiency": min(efficiencies),
            "average_fragmentation": sum(fragmentations) / len(fragmentations),
            "average_predicted_utilization": sum(utilizations) / len(utilizations),
            "alignment_ratio": sum(1 for info in valid_pools if info.subnet_alignment) / len(valid_pools)
        }
