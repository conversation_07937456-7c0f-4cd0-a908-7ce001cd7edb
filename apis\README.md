# ConfigTrans APIs 模块

[![Go Version](https://img.shields.io/badge/go-1.22+-00ADD8.svg)](https://golang.org/)
[![API Version](https://img.shields.io/badge/api-v1-blue.svg)](https://github.com/your-repo/config-converter)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

## 📖 概述

ConfigTrans APIs 模块是企业级配置转换平台的核心后端服务，基于 Go 1.22+ 开发。该模块提供高性能的 RESTful API 接口，支持配置文件验证、转换任务管理、文件处理等核心功能。

### 🎯 设计目标

- **高性能**: 基于Go语言的并发特性，支持高并发请求处理
- **可扩展**: 模块化设计，支持多厂商设备扩展
- **安全性**: 完整的认证授权机制和文件安全处理
- **可靠性**: 完善的错误处理和任务状态管理
- **易用性**: 标准化的RESTful API和详细的文档

## 目录结构

```
apis/
├── application/           # 应用程序核心
│   ├── controllers/       # 控制器，处理HTTP请求
│   │   ├── firewallflextrans/ # 防火墙配置转换控制器
│   │   └── user/          # 用户认证控制器
│   ├── libs/              # 公共库
│   │   └── response/      # 响应格式定义
│   ├── middleware/        # 中间件
│   ├── models/            # 数据模型
│   │   └── firewallflextrans/ # 转换相关模型
│   └── logging/           # 日志系统
├── service/               # 服务层
│   └── dao/               # 数据访问对象
├── bin/                   # 编译输出目录
├── locales/               # 国际化资源文件
├── uploads/               # 上传文件临时存储
├── logs/                  # 日志存储目录
├── main.go                # 程序入口
├── application.yml        # 应用配置文件
└── rbac_model.conf        # RBAC权限控制配置
```

## 🚀 核心功能

### API服务能力

| 功能模块 | 描述 | 技术实现 |
|----------|------|----------|
| **RESTful API** | 标准化HTTP接口服务 | Iris Web框架 + 中间件 |
| **文件处理** | 安全的文件上传下载 | 多格式支持 + 安全验证 |
| **任务管理** | 异步任务调度和监控 | Asynq队列 + Redis |
| **认证授权** | 企业级安全控制 | JWT + RBAC + Casbin |
| **数据存储** | 高性能数据访问 | GORM + MySQL + Redis |
| **健康监控** | 服务状态实时监控 | 内置健康检查端点 |
| **错误处理** | 统一错误响应机制 | 标准化错误码体系 |
| **国际化** | 多语言支持 | i18n本地化框架 |

### 业务功能

#### 🔍 配置验证
- 支持多厂商配置文件格式验证
- 语法检查和结构完整性验证
- 实时验证结果反馈

#### 🔄 配置转换
- 异步转换任务处理
- 实时转换进度跟踪
- 支持批量转换操作

#### 📊 任务管理
- 转换任务生命周期管理
- 任务状态实时查询
- 历史任务记录和统计

#### 📁 文件管理
- 安全的文件上传和存储
- 多格式文件下载支持
- 临时文件自动清理

#### 📈 监控报告
- 详细的转换报告生成
- 性能指标统计
- 错误日志分析

## 🔐 认证与安全

### 认证机制

ConfigTrans APIs 支持多种认证方式：

#### 1. JWT Token 认证（推荐）

```bash
# 1. 获取访问令牌
curl -X POST "http://localhost:9005/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'

# 2. 使用令牌访问API
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/tasks" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 2. API Key 认证

```bash
# 使用API Key访问
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/tasks" \
  -H "X-API-Key: YOUR_API_KEY"
```

### 权限控制

基于RBAC（基于角色的访问控制）模型：

| 角色 | 权限 | 描述 |
|------|------|------|
| **管理员** | 全部权限 | 系统管理、用户管理、所有API访问 |
| **操作员** | 转换权限 | 配置转换、任务查看、文件下载 |
| **查看者** | 只读权限 | 任务查看、报告查看 |

### 安全特性

- **文件安全**: 上传文件类型验证和病毒扫描
- **数据加密**: 敏感数据传输和存储加密
- **访问控制**: IP白名单和访问频率限制
- **审计日志**: 完整的API访问和操作日志
- **会话管理**: 安全的会话超时和令牌刷新

## 📋 API接口文档

### 响应格式

所有API接口返回统一的JSON格式响应：

```json
{
  "code": 20000,        // 状态码，20000表示成功
  "message": "请求成功", // 状态消息
  "data": { ... },      // 响应数据
  "timestamp": "2023-12-01T10:30:00Z", // 响应时间戳
  "request_id": "req_123456789" // 请求追踪ID
}
```

### 状态码说明

| 状态码 | 类型 | 说明 | 处理建议 |
|--------|------|------|----------|
| **20000** | 成功 | 请求处理成功 | 继续处理响应数据 |
| **30000** | 客户端错误 | 请求参数错误 | 检查请求参数格式 |
| **4003** | 权限错误 | 访问权限不足 | 检查认证信息和权限 |
| **5000** | 服务器错误 | 系统内部错误 | 重试或联系技术支持 |
| **5001** | 数据错误 | 数据为空或不存在 | 检查请求的资源ID |
| **5004** | 文件错误 | 文件不存在或损坏 | 检查文件路径和完整性 |
| **50008** | 认证错误 | Token无效或过期 | 重新获取访问令牌 |

### 重要说明

1. **多厂商支持**: 当前主要支持 Fortinet FortiGate 防火墙，通过 `vendor` 参数指定厂商类型
2. **任务状态**: 转换任务有三种状态 - 0:处理中, 1:成功, 2:失败
3. **文件上传**: 所有文件上传接口使用 `multipart/form-data` 格式
4. **国际化**: 支持中文(zh-CN)和英文(en-US)，通过请求头或参数指定语言
5. **任务限制**: 同时处理的转换任务数量有限制（默认10个），避免系统过载
6. **日志类型**: 支持多种日志类型 - summary(摘要), detail(详细), debug(调试), engine(引擎), full(完整), full-debug(所有调试)等
7. **文件安全**: 上传的文件会进行安全检查和格式验证
8. **临时目录**: 每个任务都有独立的工作目录，处理完成后自动清理
9. **API标识符**: 所有API统一使用job_id作为任务的唯一标识符，API路径格式为`/api/v1/firewallflextrans/{操作}/{job_id}`

### API使用注意事项

1. **并发限制**: 建议客户端控制并发请求数量，避免触发速率限制
2. **文件大小**: 上传文件大小限制默认为10MB
3. **任务轮询**: 查询任务状态时建议使用适当的轮询间隔（如5-10秒）
4. **错误重试**: 对于系统错误（5xxx状态码），可以进行重试
5. **日志访问**: 调试日志仅管理员用户可访问

### 通用状态码

| 状态码 | 说明 |
| ------ | ---- |
| 20000 | 请求成功 |
| 30000 | 参数错误 |
| 4003 | 权限错误 |
| 5000 | 系统错误 |
| 5001 | 数据为空 |
| 5002 | 存在重复记录 |
| 5003 | 禁止访问 |
| 5004 | 文件不存在 |
| 50007 | 用户名密码错误 |
| 50008 | Token无效 |
| 50012 | Token缓存错误 |
| 50014 | Token过期 |

### 1. 配置文件验证接口

- **URL**: `/api/v1/firewallflextrans/validate`
- **方法**: `POST`
- **Content-Type**: `multipart/form-data`
- **功能**: 验证配置文件格式是否正确有效，支持容量限制校验

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| file | file | 是 | 要验证的配置文件 |
| vendor | string | 否 | 厂商名称，默认为`fortigate`，目前支持：fortigate |
| device_model | string | 否 | 🆕 目标设备型号，支持：z3200s, z5100s。指定后将进行容量限制校验 |

#### 响应参数

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| valid | boolean | 配置文件是否有效 |
| message | string | 验证结果消息 |
| warnings | array | 警告信息列表（包含容量校验警告） |
| capacity_violations | array | 🆕 容量违规详细信息（当指定device_model时返回） |

#### 请求示例

**基本验证**：
```bash
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/validate" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/config.conf" \
  -F "vendor=fortigate"
```

**带容量校验**：
```bash
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/validate" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/config.conf" \
  -F "vendor=fortigate" \
  -F "device_model=z3200s"
```

#### 响应示例

**基本验证响应**：
```json
{
  "code": 20000,
  "message": "请求成功",
  "data": {
    "valid": true,
    "message": "配置文件格式有效"
  }
}
```

**带容量校验的响应**：
```json
{
  "code": 20000,
  "message": "请求成功",
  "data": {
    "valid": true,
    "message": "配置文件验证通过",
    "warnings": [
      "容量限制警告：DNS服务器数量(5)超出z3200s设备限制(3)。建议配置不超过3个DNS服务器",
      "容量限制警告：安全策略数量(3500)超出z3200s设备限制(3000)。建议优化安全策略配置或升级到Z5100S型号"
    ],
    "capacity_violations": [
      {
        "resource_type": "dns_servers",
        "description": "DNS服务器最大数量",
        "current_count": 5,
        "max_limit": 3,
        "violation_count": 2,
        "usage_rate": "166.7%",
        "severity": "error",
        "risk_level": "critical",
        "category": "network",
        "device_model": "z3200s",
        "suggestion": "建议配置不超过3个DNS服务器",
        "analysis": {
          "usage_percentage": "166.7%",
          "excess_count": 2,
          "recommended_action": "minor_adjustment",
          "impact_assessment": "可能导致设备性能严重下降或功能异常",
          "optimization_tips": [
            "优化网络配置",
            "检查配置的必要性",
            "考虑使用更高效的配置方式"
          ]
        }
      }
    ]
  }
}
```

### 2. 接口信息提取接口

- **URL**: `/api/v1/firewallflextrans/interfaces`
- **方法**: `POST`
- **Content-Type**: `multipart/form-data`
- **功能**: 从配置文件中提取接口信息

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| file | file | 是 | 配置文件 |
| vendor | string | 否 | 厂商名称，默认为`fortigate`，目前支持：fortigate |

#### 响应参数

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| interfaces | array | 接口列表，包含名称、类型、IP地址等信息 |
| count | integer | 接口总数 |

#### 请求示例

```bash
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/interfaces" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/config.conf" \
  -F "vendor=fortigate"
```

#### 响应示例

```json
{
  "code": 20000,
  "message": "请求成功",
  "data": {
    "interfaces": [
      {
        "name": "port1",
        "alias": "WAN1",
        "type": "physical",
        "vlanid": null,
        "ip": "***********/24"
      },
      {
        "name": "port2",
        "alias": "LAN",
        "type": "physical",
        "vlanid": null,
        "ip": "********/24"
      }
    ],
    "count": 2
  }
}
```

### 3. 创建配置转换任务接口

- **URL**: `/api/v1/firewallflextrans/convert`
- **方法**: `POST`
- **Content-Type**: `multipart/form-data`
- **功能**: 创建新的配置转换任务

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| file | file | 是 | 要转换的配置文件 |
| vendor | string | 否 | 厂商名称，默认为`fortigate`，目前支持：fortigate |
| model | string | 是 | 防火墙设备型号，用于生成标准格式的输出文件名 |
| version | string | 是 | 防火墙设备软件版本，用于生成标准格式的输出文件名 |
| mapping_json | string | 是 | 接口映射JSON字符串，包含源接口到目标接口的映射关系 |
| mode | string | 否 | 工作模式，默认为`convert` |

#### 响应参数

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | integer | 任务ID |
| job_id | string | 任务唯一标识 |
| status | integer | 任务状态 (0:处理中, 1:成功, 2:失败) |
| result | string | 任务消息或错误信息 |

#### 请求示例

```bash
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/convert" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/config.conf" \
  -F "vendor=fortigate" \
  -F "model=FortiGate-100D" \
  -F "version=6.0.5" \
  -F "mapping_json={\"port1\":\"WAN1\",\"port2\":\"LAN\"}"
```

#### 响应示例

```json
{
  "code": 20000,
  "message": "请求成功",
  "data": {
    "id": 123,
    "job_id": "job_20230315123456",
    "status": 0,
    "result": "任务已创建，正在处理中"
  }
}
```

### 4. 查看转换任务列表接口

- **URL**: `/api/v1/firewallflextrans/tasks`
- **方法**: `GET`
- **功能**: 获取所有转换任务的列表

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | integer | 否 | 页码，默认为1 |
| pageSize | integer | 否 | 每页数量，默认为10 |
| sort | string | 否 | 排序方向（asc或desc），默认为desc |
| orderBy | string | 否 | 排序字段，默认为id |
| vendor | string | 否 | 厂商名称过滤 |
| status | string | 否 | 任务状态过滤 |
| job_id | string | 否 | 任务ID过滤 |
| start | string | 否 | 开始时间过滤 (格式: YYYY-MM-DD) |
| end | string | 否 | 结束时间过滤 (格式: YYYY-MM-DD) |

#### 响应参数

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| items | array | 任务列表 |
| total | integer | 总任务数 |
| limit | integer | 每页数量 |

任务对象字段：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | integer | 任务ID |
| job_id | string | 任务唯一标识 |
| vendor | string | 厂商名称 |
| model | string | 设备型号 |
| version | string | 设备版本 |
| status | integer | 任务状态 (0:处理中, 1:成功, 2:失败) |
| status_desc | string | 任务状态描述（新增） |
| input_file_name | string | 输入文件名 |
| output_file_name | string | 输出文件名 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| has_report | boolean | 是否有转换报告（新增） |

#### 请求示例

```bash
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/tasks?page=1&pageSize=10"
```

#### 响应示例

```json
{
  "code": 20000,
  "message": "请求成功",
  "data": {
    "items": [
      {
        "id": 123,
        "job_id": "job_20230315123456",
        "vendor": "fortigate",
        "model": "FortiGate-100D",
        "version": "6.0.5",
        "status": 1,
        "status_desc": "成功",
        "input_file_name": "fortigate-config.conf",
        "output_file_name": "standard-config.xml",
        "has_report": true,
        "created_at": "2023-03-15T12:34:56Z",
        "updated_at": "2023-03-15T12:45:22Z"
      }
    ],
    "total": 1,
    "limit": 10
  }
}
```

### 5. 查看转换任务详情接口

- **URL**: `/api/v1/firewallflextrans/task/{job_id}`
- **方法**: `GET`
- **功能**: 获取特定转换任务的详细信息

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| job_id | string | 是 | 任务唯一标识（路径参数） |

#### 响应参数

任务的详细信息，包括状态、输入输出文件信息等。

#### 请求示例

```bash
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/task/job_20230315123456"
```

#### 响应示例

```json
{
  "code": 20000,
  "message": "请求成功",
  "data": {
    "id": 123,
    "job_id": "job_20230315123456",
    "vendor": "fortigate",
    "model": "FortiGate-100D",
    "version": "6.0.5",
    "status": 1,
    "status_desc": "成功",
    "input_file_name": "fortigate-config.conf",
    "output_file_name": "standard-config.xml",
    "mapping_file_name": "mapping.json",
    "created_at": "2023-03-15T12:34:56Z",
    "updated_at": "2023-03-15T12:45:22Z",
    "has_report": true,
    "download_url": "/api/v1/firewallflextrans/download/job_20230315123456",
    "log_url": "/api/v1/firewallflextrans/log/job_20230315123456",
    "view_log_url": "/api/v1/firewallflextrans/view-log/job_20230315123456",
    "report_url": "/api/v1/firewallflextrans/report/job_20230315123456"
  }
}
```

### 6. 查询任务状态接口

- **URL**: `/api/v1/firewallflextrans/status/{job_id}`
- **方法**: `GET`
- **功能**: 查询转换任务的当前状态和进度

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| job_id | string | 是 | 任务唯一标识（路径参数） |

#### 响应参数

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| job_id | string | 任务唯一标识 |
| id | integer | 任务ID |
| status | integer | 任务状态 (0:处理中, 1:成功, 2:失败) |
| status_desc | string | 状态描述 |
| result | string | 处理结果或错误信息 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| file_name | string | 输入文件名 |
| output_file | string | 输出文件名 |
| has_report | boolean | 是否有转换报告 |
| model | string | 防火墙设备型号，用于生成输出文件名 |
| version | string | 防火墙设备软件版本，用于生成输出文件名 |
| vendor | string | 厂商名称 |
| download_url | string | 下载URL（任务完成时），格式为`/api/v1/firewallflextrans/download/{job_id}` |
| log_url | string | 日志URL（任务完成时），格式为`/api/v1/firewallflextrans/log/{job_id}` |
| view_log_url | string | 在线查看日志URL（任务完成时），格式为`/api/v1/firewallflextrans/view-log/{job_id}` |
| report_url | string | 报告URL（如果有），格式为`/api/v1/firewallflextrans/report/{job_id}` |

#### 请求示例

```bash
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/status/job_20230315123456"
```

#### 响应示例

```json
{
  "code": 20000,
  "message": "请求成功",
  "data": {
    "job_id": "job_20230315123456",
    "id": 123,
    "status": 1,
    "status_desc": "成功",
    "result": "转换完成",
    "created_at": "2023-03-15T12:34:56Z",
    "updated_at": "2023-03-15T12:45:22Z",
    "file_name": "fortigate-config.conf",
    "output_file": "standard-config.xml",
    "has_report": true,
    "model": "FortiGate-100D",
    "version": "6.0.5",
    "vendor": "fortigate",
    "download_url": "/api/v1/firewallflextrans/download/job_20230315123456",
    "log_url": "/api/v1/firewallflextrans/log/job_20230315123456",
    "view_log_url": "/api/v1/firewallflextrans/view-log/job_20230315123456",
    "report_url": "/api/v1/firewallflextrans/report/job_20230315123456"
  }
}
```

### 7. 下载转换后的配置文件接口

- **URL**: `/api/v1/firewallflextrans/download/{job_id}`
- **方法**: `GET`
- **功能**: 下载已完成转换的配置文件

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| job_id | string | 是 | 任务唯一标识（路径参数） |
| type | string | 否 | 文件类型：xml(XML文件)或encrypt(加密文件)，默认为encrypt |

#### 响应

返回文件流，Content-Type为application/octet-stream或application/xml。

#### 请求示例

```bash
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/download/job_20230315123456" -o converted_config.xml
```

### 8. 下载转换日志接口

- **URL**: `/api/v1/firewallflextrans/log/{job_id}`
- **方法**: `GET`
- **功能**: 下载转换过程的日志文件

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| job_id | string | 是 | 任务唯一标识（路径参数） |
| type | string | 否 | 日志类型：summary(摘要), detail(详细), debug(调试), engine(引擎), full(完整), full-debug(所有调试), all(所有用户可见) |

#### 响应

返回文件流，Content-Type为text/plain。

#### 请求示例

```bash
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/log/job_20230315123456?type=summary" -o conversion.log
```

### 9. 在线查看转换日志接口

- **URL**: `/api/v1/firewallflextrans/view-log/{job_id}`
- **方法**: `GET`
- **功能**: 在线查看转换日志内容

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| job_id | string | 是 | 任务唯一标识（路径参数） |
| type | string | 否 | 日志类型：summary(摘要), detail(详细), debug(调试) |

#### 响应参数

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| content | string | 日志内容 |
| lines | integer | 日志行数 |
| log_type | string | 日志类型（新增） |
| highlight | boolean | 是否应用语法高亮（新增） |

#### 请求示例

```bash
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/view-log/job_20230315123456"
```

#### 响应示例

```json
{
  "code": 20000,
  "message": "请求成功",
  "data": {
    "content": "[2023-03-15 12:34:56] 开始处理转换任务\n[2023-03-15 12:35:01] 解析配置文件...\n[2023-03-15 12:35:10] 转换接口配置...\n[2023-03-15 12:35:22] 转换安全策略...\n[2023-03-15 12:35:35] 生成标准格式文件...\n[2023-03-15 12:35:45] 转换完成",
    "lines": 6,
    "log_type": "summary",
    "highlight": true
  }
}
```

### 10. 获取转换报告接口

- **URL**: `/api/v1/firewallflextrans/report/{job_id}`
- **方法**: `GET`
- **功能**: 获取转换任务的详细报告

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| job_id | string | 是 | 任务唯一标识（路径参数） |
| format | string | 否 | 报告格式：json、html、pdf或xlsx，默认为html |

#### 响应参数

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| job_id | string | 任务唯一标识 |
| summary | object | 转换摘要信息 |
| interfaces | object | 接口转换统计 |
| policies | object | 策略转换统计 |
| objects | object | 对象转换统计 |
| issues | array | 转换过程中的问题列表 |
| download_url | string | 报告下载URL（新增） |

#### 请求示例

```bash
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/report/job_20230315123456"
```

#### 响应示例

```json
{
  "code": 20000,
  "message": "请求成功",
  "data": {
    "job_id": "job_20230315123456",
    "summary": {
      "total_items": 256,
      "converted_items": 245,
      "success_rate": 95.7
    },
    "interfaces": {
      "total": 12,
      "converted": 12,
      "success_rate": 100.0
    },
    "policies": {
      "total": 56,
      "converted": 53,
      "success_rate": 94.6
    },
    "objects": {
      "total": 188,
      "converted": 180,
      "success_rate": 95.7
    },
    "issues": [
      {
        "type": "警告",
        "message": "策略ID 23 中的服务对象'custom-service-1'无法识别",
        "line": 1256
      },
      {
        "type": "警告",
        "message": "策略ID 45 使用了不支持的功能",
        "line": 2789
      }
    ],
    "download_url": "/api/v1/firewallflextrans/report/job_20230315123456/download"
  }
}
```

### 11. 健康检查接口

- **URL**: `/health` 或 `/healthz`
- **方法**: `GET`
- **功能**: 检查服务的健康状态

#### 响应参数

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| status | string | 服务状态 ("ok") |
| time | string | 当前时间戳 |
| hostname | string | 服务器主机名 |
| system_info | object | 系统信息（Go版本、内存使用等） |
| config | object | 配置信息（主机、端口等） |
| queue_status | object | 队列状态信息（新增） |

#### 请求示例

```bash
curl -X GET "http://localhost:9005/health"
```

#### 响应示例

```json
{
  "code": 20000,
  "message": "健康检查成功",
  "data": {
    "status": "ok",
    "time": "2023-05-20T10:30:45Z",
    "hostname": "configtrans-server",
    "system_info": {
      "go_version": "go1.22.0",
      "goroutines": 12,
      "cpu_num": 4,
      "memory": {
        "alloc_mb": 15.2,
        "total_alloc_mb": 25.8,
        "sys_mb": 32.4
      }
    },
    "config": {
      "host": "0.0.0.0",
      "port": 9005
    },
    "queue_status": {
      "active_tasks": 2,
      "pending_tasks": 1,
      "completed_tasks": 345
    }
  }
}
```

### 12. 下载转换报告接口（新增）

- **URL**: `/api/v1/firewallflextrans/report/{job_id}/download`
- **方法**: `GET`
- **功能**: 下载转换报告文件

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| job_id | string | 是 | 任务唯一标识（路径参数） |
| format | string | 否 | 报告格式：json、html、pdf或xlsx，默认为html |

#### 响应

返回文件流，Content-Type根据format参数变化：

- json: application/json
- html: text/html
- pdf: application/pdf
- xlsx: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

#### 请求示例

```bash
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/report/job_20230315123456/download?format=html" -o report.html
```

## 🛠️ API 最佳实践

### 性能优化

#### 1. 请求优化
```bash
# 使用连接池
curl -X POST "http://localhost:9005/api/v1/firewallflextrans/convert" \
  --keepalive-time 60 \
  --max-time 300 \
  -F "file=@config.conf"

# 并发请求控制
# 建议同时最多5个转换任务
```

#### 2. 缓存策略
```bash
# 利用ETag进行缓存
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/tasks" \
  -H "If-None-Match: \"etag-value\""
```

#### 3. 分页查询
```bash
# 大量数据分页获取
curl -X GET "http://localhost:9005/api/v1/firewallflextrans/tasks?page=1&pageSize=50"
```

### 错误处理

#### 重试策略
```python
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def create_session_with_retries():
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        status_forcelist=[429, 500, 502, 503, 504],
        method_whitelist=["HEAD", "GET", "OPTIONS"],
        backoff_factor=1
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

# 使用示例
session = create_session_with_retries()
response = session.get("http://localhost:9005/api/v1/firewallflextrans/tasks")
```

#### 错误分类处理
```python
def handle_api_response(response):
    data = response.json()
    code = data.get('code')

    if code == 20000:
        return data['data']  # 成功
    elif code == 30000:
        raise ValueError(f"参数错误: {data['message']}")
    elif code == 4003:
        raise PermissionError(f"权限不足: {data['message']}")
    elif code >= 5000:
        raise RuntimeError(f"服务器错误: {data['message']}")
    else:
        raise Exception(f"未知错误: {data['message']}")
```

## 📦 SDK 和集成

### Python SDK 示例

```python
import requests
import json
import time
from typing import Dict, Optional

class ConfigTransAPI:
    def __init__(self, base_url: str = "http://localhost:9005", api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()

        if api_key:
            self.session.headers.update({'X-API-Key': api_key})

    def validate_config(self, file_path: str, vendor: str = "fortigate", device_model: str = None) -> Dict:
        """验证配置文件

        Args:
            file_path: 配置文件路径
            vendor: 厂商类型，默认为fortigate
            device_model: 目标设备型号，支持z3200s/z5100s，指定后将进行容量校验
        """
        data = {'vendor': vendor}
        if device_model:
            data['device_model'] = device_model

        with open(file_path, 'rb') as f:
            response = self.session.post(
                f"{self.base_url}/api/v1/firewallflextrans/validate",
                files={'file': f},
                data=data
            )
        return self._handle_response(response)

    def extract_interfaces(self, file_path: str, vendor: str = "fortigate") -> Dict:
        """提取接口信息"""
        with open(file_path, 'rb') as f:
            response = self.session.post(
                f"{self.base_url}/api/v1/firewallflextrans/interfaces",
                files={'file': f},
                data={'vendor': vendor}
            )
        return self._handle_response(response)

    def create_conversion_task(self, file_path: str, mapping: Dict,
                             model: str = "z5100s", version: str = "R11",
                             vendor: str = "fortigate") -> str:
        """创建转换任务，返回job_id"""
        with open(file_path, 'rb') as f:
            response = self.session.post(
                f"{self.base_url}/api/v1/firewallflextrans/convert",
                files={'file': f},
                data={
                    'vendor': vendor,
                    'model': model,
                    'version': version,
                    'mapping_json': json.dumps(mapping)
                }
            )
        result = self._handle_response(response)
        return result['job_id']

    def get_task_status(self, job_id: str) -> Dict:
        """获取任务状态"""
        response = self.session.get(
            f"{self.base_url}/api/v1/firewallflextrans/status/{job_id}"
        )
        return self._handle_response(response)

    def wait_for_completion(self, job_id: str, timeout: int = 300,
                          poll_interval: int = 5) -> Dict:
        """等待任务完成"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            status = self.get_task_status(job_id)

            if status['status'] == 1:  # 成功
                return status
            elif status['status'] == 2:  # 失败
                raise Exception(f"转换失败: {status.get('result', '未知错误')}")

            time.sleep(poll_interval)

        raise TimeoutError(f"任务 {job_id} 在 {timeout} 秒内未完成")

    def download_result(self, job_id: str, output_path: str,
                       file_type: str = "encrypt") -> None:
        """下载转换结果"""
        response = self.session.get(
            f"{self.base_url}/api/v1/firewallflextrans/download/{job_id}",
            params={'type': file_type}
        )

        if response.status_code == 200:
            with open(output_path, 'wb') as f:
                f.write(response.content)
        else:
            raise Exception(f"下载失败: {response.status_code}")

    def get_conversion_report(self, job_id: str, format: str = "json") -> Dict:
        """获取转换报告"""
        response = self.session.get(
            f"{self.base_url}/api/v1/firewallflextrans/report/{job_id}",
            params={'format': format}
        )
        return self._handle_response(response)

    def _handle_response(self, response: requests.Response) -> Dict:
        """处理API响应"""
        try:
            data = response.json()
            if data.get('code') == 20000:
                return data['data']
            else:
                raise Exception(f"API错误: {data.get('message', '未知错误')}")
        except json.JSONDecodeError:
            raise Exception(f"响应解析失败: {response.text}")

# 使用示例
api = ConfigTransAPI(api_key="your-api-key")

# 完整转换流程
try:
    # 1. 验证配置文件（带容量校验）
    validation = api.validate_config("fortigate-config.conf", device_model="z3200s")
    if not validation['valid']:
        raise Exception("配置文件验证失败")

    # 检查容量违规
    if 'capacity_violations' in validation and validation['capacity_violations']:
        print(f"发现 {len(validation['capacity_violations'])} 个容量违规项:")
        for violation in validation['capacity_violations']:
            print(f"- {violation['description']}: {violation['current_count']}/{violation['max_limit']}")
            print(f"  建议: {violation['suggestion']}")

    # 1a. 或者不进行容量校验的基本验证
    # validation = api.validate_config("fortigate-config.conf")
    # if not validation['valid']:
    #     raise Exception("配置文件验证失败")

    # 2. 提取接口信息
    interfaces = api.extract_interfaces("fortigate-config.conf")
    print(f"发现 {interfaces['count']} 个接口")

    # 3. 创建接口映射
    mapping = {"port1": "Ge0/1", "port2": "Ge0/2"}

    # 4. 创建转换任务
    job_id = api.create_conversion_task("fortigate-config.conf", mapping)
    print(f"转换任务已创建: {job_id}")

    # 5. 等待完成
    result = api.wait_for_completion(job_id)
    print("转换完成")

    # 6. 下载结果
    api.download_result(job_id, "converted-config.xml")

    # 7. 获取报告
    report = api.get_conversion_report(job_id)
    print(f"转换成功率: {report['summary']['success_rate']}%")

except Exception as e:
    print(f"转换失败: {e}")
```

### JavaScript/Node.js SDK 示例

```javascript
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

class ConfigTransAPI {
    constructor(baseUrl = 'http://localhost:9005', apiKey = null) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.client = axios.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: apiKey ? { 'X-API-Key': apiKey } : {}
        });
    }

    async validateConfig(filePath, vendor = 'fortigate', deviceModel = null) {
        const formData = new FormData();
        formData.append('file', fs.createReadStream(filePath));
        formData.append('vendor', vendor);

        if (deviceModel) {
            formData.append('device_model', deviceModel);
        }

        const response = await this.client.post(
            '/api/v1/firewallflextrans/validate',
            formData,
            { headers: formData.getHeaders() }
        );

        return this.handleResponse(response);
    }

    async createConversionTask(filePath, mapping, model = 'z5100s', version = 'R11', vendor = 'fortigate') {
        const formData = new FormData();
        formData.append('file', fs.createReadStream(filePath));
        formData.append('vendor', vendor);
        formData.append('model', model);
        formData.append('version', version);
        formData.append('mapping_json', JSON.stringify(mapping));

        const response = await this.client.post(
            '/api/v1/firewallflextrans/convert',
            formData,
            { headers: formData.getHeaders() }
        );

        const result = this.handleResponse(response);
        return result.job_id;
    }

    async getTaskStatus(jobId) {
        const response = await this.client.get(`/api/v1/firewallflextrans/status/${jobId}`);
        return this.handleResponse(response);
    }

    async waitForCompletion(jobId, timeout = 300000, pollInterval = 5000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            const status = await this.getTaskStatus(jobId);

            if (status.status === 1) { // 成功
                return status;
            } else if (status.status === 2) { // 失败
                throw new Error(`转换失败: ${status.result || '未知错误'}`);
            }

            await new Promise(resolve => setTimeout(resolve, pollInterval));
        }

        throw new Error(`任务 ${jobId} 在 ${timeout}ms 内未完成`);
    }

    async downloadResult(jobId, outputPath, fileType = 'encrypt') {
        const response = await this.client.get(
            `/api/v1/firewallflextrans/download/${jobId}`,
            {
                params: { type: fileType },
                responseType: 'stream'
            }
        );

        const writer = fs.createWriteStream(outputPath);
        response.data.pipe(writer);

        return new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
        });
    }

    handleResponse(response) {
        const data = response.data;
        if (data.code === 20000) {
            return data.data;
        } else {
            throw new Error(`API错误: ${data.message || '未知错误'}`);
        }
    }
}

// 使用示例
async function convertConfig() {
    const api = new ConfigTransAPI('http://localhost:9005', 'your-api-key');

    try {
        // 验证配置文件（带容量校验）
        const validation = await api.validateConfig('fortigate-config.conf', 'fortigate', 'z3200s');
        if (!validation.valid) {
            throw new Error('配置文件验证失败');
        }

        // 检查容量违规
        if (validation.capacity_violations && validation.capacity_violations.length > 0) {
            console.log(`发现 ${validation.capacity_violations.length} 个容量违规项:`);
            validation.capacity_violations.forEach(violation => {
                console.log(`- ${violation.description}: ${violation.current_count}/${violation.max_limit}`);
                console.log(`  建议: ${violation.suggestion}`);
            });
        }

        // 创建转换任务
        const mapping = { "port1": "Ge0/1", "port2": "Ge0/2" };
        const jobId = await api.createConversionTask('fortigate-config.conf', mapping);
        console.log(`转换任务已创建: ${jobId}`);

        // 等待完成
        const result = await api.waitForCompletion(jobId);
        console.log('转换完成');

        // 下载结果
        await api.downloadResult(jobId, 'converted-config.xml');
        console.log('结果已下载');

    } catch (error) {
        console.error('转换失败:', error.message);
    }
}

convertConfig();
```

## 🧪 测试指南

### API 测试

#### 使用 curl 进行测试

```bash
# 创建测试脚本
cat > test_api.sh << 'EOF'
#!/bin/bash

BASE_URL="http://localhost:9005"
TEST_FILE="test-config.conf"

echo "=== API 测试开始 ==="

# 1. 健康检查
echo "1. 健康检查..."
curl -s "$BASE_URL/health" | jq .

# 2. 验证配置文件（带容量校验）
echo "2. 验证配置文件（带容量校验）..."
curl -s -X POST "$BASE_URL/api/v1/firewallflextrans/validate" \
  -F "file=@$TEST_FILE" \
  -F "vendor=fortigate" \
  -F "device_model=z3200s" | jq .

# 2a. 基本验证（不进行容量校验）
echo "2a. 基本验证..."
curl -s -X POST "$BASE_URL/api/v1/firewallflextrans/validate" \
  -F "file=@$TEST_FILE" \
  -F "vendor=fortigate" | jq .

# 3. 提取接口信息
echo "3. 提取接口信息..."
curl -s -X POST "$BASE_URL/api/v1/firewallflextrans/interfaces" \
  -F "file=@$TEST_FILE" \
  -F "vendor=fortigate" | jq .

# 4. 创建转换任务
echo "4. 创建转换任务..."
RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/firewallflextrans/convert" \
  -F "file=@$TEST_FILE" \
  -F "vendor=fortigate" \
  -F "model=z5100s" \
  -F "version=R11" \
  -F "mapping_json={\"port1\":\"Ge0/1\",\"port2\":\"Ge0/2\"}")

JOB_ID=$(echo $RESPONSE | jq -r '.data.job_id')
echo "任务ID: $JOB_ID"

# 5. 查询任务状态
echo "5. 查询任务状态..."
curl -s "$BASE_URL/api/v1/firewallflextrans/status/$JOB_ID" | jq .

echo "=== API 测试完成 ==="
EOF

chmod +x test_api.sh
./test_api.sh
```

#### 使用 Postman 集合

```json
{
  "info": {
    "name": "ConfigTrans API Tests",
    "description": "配置转换服务API测试集合",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:9005"
    },
    {
      "key": "jobId",
      "value": ""
    }
  ],
  "item": [
    {
      "name": "健康检查",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{baseUrl}}/health",
          "host": ["{{baseUrl}}"],
          "path": ["health"]
        }
      }
    },
    {
      "name": "验证配置文件",
      "request": {
        "method": "POST",
        "header": [],
        "body": {
          "mode": "formdata",
          "formdata": [
            {
              "key": "file",
              "type": "file",
              "src": "test-config.conf"
            },
            {
              "key": "vendor",
              "value": "fortigate",
              "type": "text"
            }
          ]
        },
        "url": {
          "raw": "{{baseUrl}}/api/v1/firewallflextrans/validate",
          "host": ["{{baseUrl}}"],
          "path": ["api", "v1", "firewallflextrans", "validate"]
        }
      }
    }
  ]
}
```

### 性能测试

#### 使用 Apache Bench (ab)

```bash
# 并发测试健康检查端点
ab -n 1000 -c 10 http://localhost:9005/health

# 测试任务列表API
ab -n 100 -c 5 http://localhost:9005/api/v1/firewallflextrans/tasks
```

#### 使用 wrk

```bash
# 安装 wrk
# Ubuntu: sudo apt-get install wrk
# macOS: brew install wrk

# 性能测试
wrk -t12 -c400 -d30s http://localhost:9005/health
```

## 🔧 开发指南

### 环境配置

#### 开发环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| **Go** | 1.22+ | 主要开发语言 |
| **MySQL** | 8.0+ | 数据持久化 |
| **Redis** | 7.0+ | 缓存和会话 |
| **Git** | 2.0+ | 版本控制 |

#### 开发工具推荐

- **IDE**: GoLand, VS Code with Go extension
- **API测试**: Postman, Insomnia
- **数据库管理**: MySQL Workbench, DBeaver
- **Redis管理**: RedisInsight, Redis Desktop Manager

### 编译和构建

```bash
# 1. 克隆项目
git clone https://github.com/your-repo/config-converter.git
cd config-converter/apis

# 2. 安装依赖
go mod tidy

# 3. 开发模式编译
go build -o bin/configtrans-service main.go

# 4. 生产模式编译（带版本信息）
go build -o bin/configtrans-service \
  -ldflags "-X 'main.BuildName=ConfigTrans API Service' \
            -X 'main.BuildVersion=2.9.1' \
            -X 'main.BuildTime=$(date)' \
            -X 'main.GitCommit=$(git rev-parse HEAD)'" \
  main.go

# 5. 交叉编译（Linux）
GOOS=linux GOARCH=amd64 go build -o bin/configtrans-service-linux main.go

# 6. 运行测试
go test ./...

# 7. 代码格式化
go fmt ./...

# 8. 代码检查
go vet ./...
```

### 配置管理

#### 开发环境配置

```yaml
# application-dev.yml
debug: true
loglevel: debug
host: 127.0.0.1
port: 9005

db:
  adapter: mysql
  conn: "root:password@tcp(localhost:3306)/configtrans_dev?parseTime=True&loc=Local"
  debug: true
  log_level: debug

redis:
  host: localhost
  port: 6379
  database: 0
```

#### 生产环境配置

```yaml
# application-prod.yml
debug: false
loglevel: info
host: 0.0.0.0
port: 9005

db:
  adapter: mysql
  conn: "${DB_CONN}"
  max_idle_conns: 20
  max_open_conns: 200
  debug: false
  log_level: warn

redis:
  host: "${REDIS_HOST}"
  port: 6379
  password: "${REDIS_PASSWORD}"
  database: 5
```

### 启动和调试

```bash
# 1. 启动开发服务器
./bin/configtrans-service -config=application-dev.yml

# 2. 启动调试模式
dlv debug main.go -- -config=application-dev.yml

# 3. 使用环境变量
export DEBUG=true
export LOGLEVEL=debug
./bin/configtrans-service

# 4. 查看帮助信息
./bin/configtrans-service -help
```

### 代码结构

```
apis/
├── application/              # 应用层
│   ├── controllers/          # 控制器
│   │   ├── firewallflextrans/  # 转换相关控制器
│   │   └── user/             # 用户相关控制器
│   ├── middleware/           # 中间件
│   │   ├── auth.go           # 认证中间件
│   │   ├── cors.go           # 跨域中间件
│   │   └── logging.go        # 日志中间件
│   ├── models/               # 数据模型
│   └── libs/                 # 公共库
├── service/                  # 服务层
│   ├── dao/                  # 数据访问层
│   ├── cache/                # 缓存服务
│   └── transaction/          # 事务管理
├── tools/                    # 工具和脚本
├── locales/                  # 国际化资源
├── main.go                   # 程序入口
├── application.yml           # 配置文件
└── go.mod                    # Go模块定义
```

## 配置参数

服务配置通过`application.yml`文件加载，支持以下主要配置项：

```
