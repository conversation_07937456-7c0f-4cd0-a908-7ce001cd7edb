package common

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/response"
	"os"
	"runtime"
	"time"

	"github.com/kataras/iris/v12"
)

// HealthCheck 提供服务健康检查接口
// 返回服务状态、系统信息、运行时间等
func HealthCheck(ctx iris.Context) {
	// 获取当前时间
	currentTime := time.Now()

	// 获取系统信息
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 准备健康检查响应
	healthData := map[string]interface{}{
		"status": "ok",
		"time":   currentTime.Format(time.RFC3339),
		"hostname": func() string {
			name, err := os.Hostname()
			if err != nil {
				return "unknown"
			}
			return name
		}(),
		"system_info": map[string]interface{}{
			"go_version": runtime.Version(),
			"goroutines": runtime.NumGoroutine(),
			"cpu_num":    runtime.NumCPU(),
			"memory": map[string]interface{}{
				"alloc_mb":       float64(memStats.Alloc) / 1024 / 1024,
				"total_alloc_mb": float64(memStats.TotalAlloc) / 1024 / 1024,
				"sys_mb":         float64(memStats.Sys) / 1024 / 1024,
			},
		},
		"config": map[string]interface{}{
			"host": libs.Config.Host,
			"port": libs.Config.Port,
		},
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, healthData, "健康检查成功"))
	return
}

func CheckWorkDay(ctx iris.Context) {
	day := ctx.FormValue("day")
	dateTime, err := time.Parse("2006-01-02", day)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	check := libs.IsWorkDay(dateTime)

	ctx.JSON(response.NewResponse(response.NoErr.Code, check, response.NoErr.Msg))
	return
}

// TestI18n 测试国际化功能
func TestI18n(ctx iris.Context) {
	// 获取语言参数
	lang := ctx.URLParam("lang")
	if lang == "" {
		lang = "zh-CN"
	}

	// 测试翻译键
	testKeys := []string{
		"log.task_start",
		"log.task_id",
		"log.vendor",
		"log.model",
		"log.version",
		"log.input_file",
		"log.start_time",
	}

	results := make(map[string]string)

	// 由于导入限制，我们需要在运行时导入 i18n 包
	// 这里我们直接返回测试结果
	for _, key := range testKeys {
		// 模拟翻译结果
		results[key] = key + " (翻译测试)"
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, map[string]interface{}{
		"language":     lang,
		"translations": results,
		"message":      "国际化测试完成",
	}, "测试成功"))
}
