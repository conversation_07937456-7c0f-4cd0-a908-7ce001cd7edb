module ntos-local-defend {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:local-defend";
  prefix ntos-local-defend;

  import ntos {
    prefix ntos;
  }

  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-system {
    prefix ntos-system;
  }

  import ntos-if-types {
    prefix ntos-if-types;
  }

  import ntos-inet-types {
    prefix ntos-inet-types;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-network-obj {
    prefix ntos-network-obj;
  }

  import ntos-security-zone {
    prefix ntos-security-zone;
  }

  import ntos-vlan {
    prefix ntos-vlan;
  }

  import ntos-bridge {
    prefix ntos-bridge;
  }

  import ntos-vswitch {
    prefix ntos-vswitch;
  }

  import ntos-lag {
    prefix ntos-lag;
  }

  import ntos-vti {
    prefix ntos-vti;
  }

  import ntos-gre {
    prefix ntos-gre;
  }

  import ntos-tun {
    prefix ntos-tun;
  }

  organization
    "Ruijie Networks Co., Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS local defend module.";

  revision 2022-02-21 {
    description
      "Create initial version.";
  }

  revision 2025-05-27 {
    description
      "Support tun access control.";
  }

  identity local-defend {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Local denfed service.";
  }

  typedef network-obj {
    type ntos-types:ntos-obj-name-type;
  }

  grouping cmd-output-buffer {
    leaf buffer {
      description
        "Command output buffer since last request.";

      type string;
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos:name";
    }
  }

  grouping suspect-host-list {
    list suspect-host {
      description
        "A list about information of suspect host's IP, MAC and the suspect reasons.";

      ordered-by user;
      config false;

      key "mac";

      leaf mac {
        description
          "A MAC address.";

        type ntos-if-types:mac-address;
      }

      leaf ip {
        description
          "An IPv4 address.";

        type ntos-inet-types:ip-address;
      }

      leaf reason {
        description
          "The reason why the host was suspected.";

        type string;
      }
    }
  }

  grouping policy-config {
    leaf name {
      description
        "The name of policy.";

      type ntos-types:ntos-obj-name-type;
    }

    leaf enabled {
      description
        "Enable or disable policy.";

      type boolean;
      default "true";
    }

    list source-network {
      description
        "The source network of policy.";

      key "name";

      leaf name {
        description
          "The name of source network.";

        type network-obj;
        ntos-ext:nc-cli-no-name;
      }

      ntos-ext:nc-cli-one-liner;
    }

    list dest-network {
      description
        "The destination network of policy.";

      key "name";

      leaf name {
        description
          "The name of destination network.";

        type network-obj;
        ntos-ext:nc-cli-no-name;
      }

      ntos-ext:nc-cli-one-liner;
    }

    list source-zone {
      description
        "The source zone of policy.";

      key "name";

      leaf name {
        description
          "The name of source zone.";

        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }

      ntos-ext:nc-cli-one-liner;
    }

    list service {
      description
        "The service of policy.";

      key "name";

      leaf name {
        description
          "The name of service.";

        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }

      ntos-ext:nc-cli-one-liner;
    }

    leaf action {
      description
        "The action when the policy is hit.";

      type enumeration {
        enum permit;
        enum deny;
      }

      default "permit";
    }

    leaf limit {
      description
        "Enable or disable rate-limit.";

      type boolean;
      default "false";
    }

    leaf pps {
      description
        "The packet number each host can send per second.";

      type uint32;
      default "600";
    }

    leaf description {
      description
        "The description of policy.";

      type ntos-types:ntos-obj-description-type;
      default "";
    }
  }

  grouping local-defend {
    container local-defend {
      leaf enabled {
        description
          "Enable or disable local defend module.";

        type boolean;
        default "true";
      }

      container host-hardening {

        leaf enabled {
          description
            "Enable or disable host-hardening function of local management plane.";

          type boolean;
          default "true";
        }

        container injection-prevention {

          leaf enabled {
            description
              "Enable or disable injection prevention function of local management plane.";

            type boolean;
              default "false";
            }

            leaf action {
              description
                "Action for injection prevention function of local management plane.";

            type enumeration {
              enum alert;
              enum block;
            }
              default "block";
            }

            list white-list {
              description
                "IP address white list for injection prevention function of local management plane.";

              key "ip";
              max-elements 10;
              leaf ip {
                description
                "An IPv4 address.";

                type ntos-inet-types:ip-address;
              }

              ntos-ext:nc-cli-one-liner;
            }

        }

      }



      container arp-monitor {
        description
          "The ARP monitor configuration.";

        leaf enabled {
          description
            "Enable or disable ARP monitor.";

          type boolean;
          default "false";
        }

        leaf scan-threshold {
          description
            "The number of packets sent by the host exceeding the threshold
            within 10 seconds will be suspected of ARP scanning.";

            type uint32 {
              range "1..max";
            }

            default "200";
        }
      }

      container rate-limit {
        description
          "The packet rate-limit configuration.";

        container arp {
          description
            "Configure how many ARP packets sent from each host can pass through per second.";

          leaf req-token {
            description
              "How many ARP requests from each host can pass through per second.";

            type union {
              type uint32 {
                range "1..20";
              }
              type enumeration {
                enum auto;
              }
            }

            default "5";
          }

          leaf res-token {
            description
              "How many ARP responses from each host can pass through per second.";

            type union {
              type uint32 {
                range "1..20";
              }
              type enumeration {
                enum auto;
              }
            }

            default "1";
          }

          leaf req-threshold {
            description
              "How many ARP requests can pass through per second.";

            type uint32 {
              range '5..1000';
            }
            default "100";
          }

          leaf res-threshold {
            description
              "How many ARP responses can pass through per second.";

            type uint32 {
              range '5..1000';
            }
            default "100";
          }
        }

        container class {
          description
            "Local defend limit the packet send to self per second.";

          leaf token {
            description
              "How many packets can go through per second.";

            type uint32 {
              range "1..max";
            }
          }
        }
      }
    }
  }

  grouping access {
    container access-control {
      description
        "Access mode configuration.";

      leaf https {
        description
          "HTTPS access configuration.";

        type boolean;
        default "false";
      }

      leaf ping {
        description
          "Ping access configuration.";

        type boolean;
        default "false";
      }

      leaf ssh {
        description
          "SSH access configuration.";

        type boolean;
        default "false";
      }
    }
  }

  rpc ld-show-arp-suspect {
    description
      "Show local defend ARP suspicious host list.";

    input {
      leaf show-cnt {
        description
          "Host number shown.";

        type uint32;
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "local-defend arp-suspect";
  }

  rpc ld-show-pkt-status {
    description
      "Show local defend packet status counters.";

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "local-defend-pkt-status";
  }

  rpc ld-clear-pkt-status {
    description
      "Clear local defend packet status counters.";

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "local-defend-clear-pkt-status";
  }

  rpc get-interface-access-control {
    description
      "Show interface access-control configuration.";

    input {
      leaf vrf {
        description
          "VRF name.";

        type string;
        default "main";

        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf interface {
        description
          "Physical interface name.";

        type string;
        ntos-ext:nc-cli-no-name;
      }

      leaf type {
        description
          "Query type.";

        type enumeration {
          enum single;
          enum multi;
        }
        default "single";
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "interface access-control";
  }

  rpc show-local-defend {
    description
      "Show local defend configuration.";

    output {
      uses local-defend;
    }

    ntos-ext:nc-cli-show "local-defend";
  }

  rpc show-local-defend-policy {
    description
      "Show local defend policy or policy list.";

    input {
      uses vrf;

      leaf filter {
        description
          "The content of search.";

        type ntos-types:ntos-obj-description-type;
        default "";
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
        default "0";
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
        default "1024";
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "local-defend policy";
  }

  rpc show-local-defend-policy-brief {
    input {
      uses vrf;

      leaf start {
        description
          "The index of page start.";

        type uint16;
        default "0";
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
        default "1024";
      }
    }

    output {
      uses cmd-output-buffer;
    }
  }

  rpc show-local-defend-policy-detail {
    description
      "Show the details of the local defend policy.";

    input {
      uses vrf;

      leaf policy-name {
        description
          "The name of policy.";

        type ntos-types:ntos-obj-name-type;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-show "local-defend detail";
  }

  rpc get-local-defend-policy-name {
    description
      "Get policy name by given policy ID.";

    input {
      uses vrf;

      leaf policy-id {
        description
          "The id of policy.";

        type string;
      }
    }

    output {
      uses cmd-output-buffer;
    }
  }

  rpc local-defend-subcommand {
    input {
      leaf command {
        description
          "Subcommand.";

        type enumeration {
          enum running;
          enum stat;
          enum policy;
          enum log;
          enum access;
          enum rate;
          enum arp-suspect;
          enum arp-rate;
        }

        ntos-ext:nc-cli-hidden;
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "local-defend-subcommand";
  }

  rpc show-local-inject-prev-stat-cli {
    description "Show injection prevention stat of local management plane at cli protection";

    input {
      leaf cli {
        description "Options for showing injection prevention function of local management plane feature statistics or detecting statistical information output.";

        type enumeration {
          enum detecting;
          enum module;
        }

      }

    }

    output {
      leaf stat {
        type string;
        description "The content of statistics";
      }
    }

    ntos-ext:nc-cli-show "local-inject-prev cli-stat";
  }

  rpc show-local-inject-prev-stat-web {
    description "Show injection prevention stat of local management plane for web protection.";

    input {
      leaf web {
        description "Options for showing injection prevention function of local management plane feature statistics or detecting statistical information output.";

        type enumeration {
          enum detecting;
          enum module;
        }

      }

    }

    output {
      leaf stat {
        type string;
        description "The content of statistics";
      }
    }

    ntos-ext:nc-cli-show "local-inject-prev web-stat";
  }

  rpc reset-all-local-inject-prev-stat {
    description "Reset all statistical values for injection prevention function of local management plane feature to zero.";

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-cmd "local-inject-prev stat-reset";
  }

  rpc set-local-inject-prev-sec-log-output-opt {
    description "Set the security log output options for injection prevention function of local management plane feature";

    input {
      leaf sec-log-opt-enabled {
        type boolean;
        description
          "Option to enable or disable the output of security logs.";
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-cmd "local-inject-prev";
  }

  rpc reload-inject-prev-engine {
    description "Reload the engine of injection prevention function of local management plane.";

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-cmd "local-inject-prev reload-engine";
  }

  rpc show-local-inject-prev-config {
    description "Show all configeration information for injection prevention function of local management plane feature";

    output {
      leaf local-inject-prev-enabled {
        type boolean;
        description
          "Enable or disable injection prevention function of local management plane.";
      }

      leaf action {
        type enumeration {
          enum alert;
          enum block;
        }
        description
          "Action for injection prevention function of local management plane.";
      }

      leaf-list ipv4-address {
        type ntos-inet-types:ip-address;
        description
          "IP address white list for injection prevention function of local management plane.";
      }
    }

    ntos-ext:nc-cli-show "local-inject-prev config";
  }

  augment "/ntos:config/ntos-system:system" {
    uses local-defend {
      refine "local-defend" {
        description
          "Local defend configuration, include:
          enable/disable local defend module,
          enable/disable ARP monitor,
          set the packet rate-limit parameters.";
      }
    }
  }

  augment "/ntos:state/ntos-system:system" {
    uses local-defend;
  }

  augment "/ntos:config/ntos:vrf" {
    container local-defend {
      list policy {
        description
          "Local defend policy configuration.";

        key "name";
        ordered-by user;

        uses policy-config;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    container local-defend {
      list policy {
        key "name";

        uses policy-config;
      }
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
    uses access;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
    uses access;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:ipv4/ntos-interface:pppoe/ntos-interface:connection" {
    uses access;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:ipv4/ntos-interface:pppoe/ntos-interface:connection" {
    uses access;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-vlan:vlan" {
    uses access;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-vlan:vlan" {
    uses access;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-bridge:bridge" {
    uses access;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-bridge:bridge" {
    uses access;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-vswitch:vswitch" {
    uses access;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-vswitch:vswitch" {
    uses access;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-lag:lag" {
    uses access;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-lag:lag" {
    uses access;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-vti:vti" {
    uses access;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-vti:vti" {
    uses access;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-gre:gre" {
    uses access;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-gre:gre" {
    uses access;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-tun:tun" {
    uses access;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-tun:tun" {
    uses access;
  }
}
