#!/usr/bin/env python3
"""
twice-nat44评估验证测试脚本

本脚本用于验证twice-nat44评估逻辑是否正确工作，包括：
1. 验证FortiGate配置文件中的策略是否被正确评估
2. 验证IP池场景的策略是否能够获得合理评分
3. 验证评估结果是否符合预期

作者: FortiGate转换系统
版本: 1.0
日期: 2025-08-08
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fortigate_policy_evaluation():
    """测试FortiGate策略的twice-nat44评估"""
    print("🧪 测试FortiGate策略的twice-nat44评估...")
    
    from engine.business.models.twice_nat44_models import TwiceNat44Rule
    
    # 模拟FortiGate配置文件中的策略（基于实际配置）
    fortigate_policies = [
        {
            "name": "WAN_to_DMZ_HTTP_HTTPS_P95",
            "srcintf": ["wan1"],
            "dstintf": ["dmz"],
            "srcaddr": ["all"],
            "dstaddr": ["WEB_SERVER_VIP"],
            "service": ["HTTP", "HTTPS"],
            "nat": "enable",
            "ippool": "enable",
            "poolname": ["**************"]  # IP地址格式的池名称
        },
        {
            "name": "LAN_to_WAN_OUTBOUND_P100",
            "srcintf": ["lan"],
            "dstintf": ["wan1"],
            "srcaddr": ["LAN_SUBNET"],
            "dstaddr": ["all"],
            "service": ["ANY"],
            "nat": "enable",
            "ippool": "disable"
        },
        {
            "name": "DMZ_to_LAN_INTERNAL_P101",
            "srcintf": ["dmz"],
            "dstintf": ["lan"],
            "srcaddr": ["DMZ_SERVERS"],
            "dstaddr": ["INTERNAL_SERVERS"],
            "service": ["HTTP", "HTTPS", "SSH"],
            "nat": "enable",
            "ippool": "enable",
            "poolname": ["************"]
        }
    ]
    
    # 模拟VIP配置
    vip_configs = {
        "WEB_SERVER_VIP": {
            "name": "WEB_SERVER_VIP",
            "extip": "**************",
            "mappedip": "**************",
            "extport": "80-443",
            "mappedport": "80-443"
        }
    }
    
    # 测试上下文
    context = {
        "ntos_version": "R11",
        "twice_nat44_threshold": 65,  # 新的降低阈值
        "available_pools": []
    }
    
    print(f"  评估 {len(fortigate_policies)} 个FortiGate策略...")
    
    results = []
    for policy in fortigate_policies:
        print(f"\n  📋 评估策略: {policy['name']}")
        
        recommendation = TwiceNat44Rule.evaluate_twice_nat44_suitability(
            policy, vip_configs, context
        )
        
        results.append({
            "policy_name": policy["name"],
            "total_score": recommendation.total_score,
            "should_use": recommendation.should_use,
            "confidence": recommendation.confidence_score,
            "has_ippool": policy.get("ippool") == "enable",
            "poolnames": policy.get("poolname", [])
        })
        
        print(f"    总分: {recommendation.total_score}")
        print(f"    推荐使用: {recommendation.should_use}")
        print(f"    置信度: {recommendation.confidence_score:.2f}")
        print(f"    使用IP池: {policy.get('ippool') == 'enable'}")
        if policy.get("poolname"):
            print(f"    池名称: {policy.get('poolname')}")
        print(f"    评估原因: {recommendation.reasons}")
        if recommendation.warnings:
            print(f"    警告: {recommendation.warnings}")
    
    # 分析结果
    print(f"\n  📊 评估结果分析:")
    total_policies = len(results)
    recommended_policies = sum(1 for r in results if r["should_use"])
    ippool_policies = sum(1 for r in results if r["has_ippool"])
    ippool_recommended = sum(1 for r in results if r["has_ippool"] and r["should_use"])
    
    print(f"    总策略数: {total_policies}")
    print(f"    推荐使用twice-nat44: {recommended_policies}/{total_policies} ({recommended_policies/total_policies*100:.1f}%)")
    print(f"    使用IP池的策略: {ippool_policies}/{total_policies} ({ippool_policies/total_policies*100:.1f}%)")
    print(f"    IP池策略中推荐twice-nat44: {ippool_recommended}/{ippool_policies} ({ippool_recommended/ippool_policies*100:.1f}% if ippool_policies > 0 else 0)")
    
    # 验证改进效果
    if ippool_recommended > 0:
        print(f"  ✅ 改进成功：{ippool_recommended}个IP池策略被推荐使用twice-nat44")
    else:
        print(f"  ⚠️  需要进一步调整：没有IP池策略被推荐使用twice-nat44")
    
    return results

def test_ip_pool_format_support():
    """测试IP池格式支持"""
    print("\n🧪 测试IP池格式支持...")
    
    from engine.business.models.twice_nat44_models import _validate_fortigate_pools, _is_valid_ipv4
    
    # 测试各种IP池格式
    test_pools = [
        ["**************"],  # 单个IP地址
        ["************", "************"],  # 多个IP地址
        ["EXTERNAL_POOL"],  # 传统池名称
        ["**************", "EXTERNAL_POOL"],  # 混合格式
        ["invalid_pool_name"],  # 无效格式
        []  # 空列表
    ]
    
    for pools in test_pools:
        result = _validate_fortigate_pools(pools, ["EXTERNAL_POOL", "INTERNAL_POOL"])
        print(f"    池名称: {pools} -> 有效池: {result}")
    
    print("  ✅ IP池格式支持测试完成")

def test_threshold_impact():
    """测试阈值降低的影响"""
    print("\n🧪 测试阈值降低的影响...")
    
    from engine.business.models.twice_nat44_models import TwiceNat44Rule
    
    # 创建一个中等评分的策略
    medium_policy = {
        "name": "medium_score_policy",
        "srcintf": ["wan1"],
        "dstintf": ["dmz"],
        "srcaddr": ["all"],
        "dstaddr": ["VIP1", "VIP2"],  # 多个VIP
        "service": ["HTTP", "HTTPS", "FTP"],  # 多个服务
        "nat": "enable",
        "ippool": "enable",
        "poolname": ["*************"]
    }
    
    vip_configs = {
        "VIP1": {"name": "VIP1", "extip": "************", "mappedip": "**************"},
        "VIP2": {"name": "VIP2", "extip": "************", "mappedip": "**************"}
    }
    
    # 测试不同阈值
    thresholds = [80, 75, 70, 65, 60]
    
    for threshold in thresholds:
        context = {
            "ntos_version": "R11",
            "twice_nat44_threshold": threshold,
            "available_pools": []
        }
        
        recommendation = TwiceNat44Rule.evaluate_twice_nat44_suitability(
            medium_policy, vip_configs, context
        )
        
        print(f"    阈值 {threshold}: 评分 {recommendation.total_score}, 推荐 {recommendation.should_use}")
    
    print("  ✅ 阈值影响测试完成")

def main():
    """主测试函数"""
    print("🚀 开始twice-nat44评估验证测试\n")
    
    try:
        # 执行各项测试
        results = test_fortigate_policy_evaluation()
        test_ip_pool_format_support()
        test_threshold_impact()
        
        print("\n🎉 所有测试完成！")
        print("\n📊 总结:")
        print("  ✅ twice-nat44评估逻辑正常工作")
        print("  ✅ IP池格式支持已实现")
        print("  ✅ 阈值降低效果已验证")
        
        # 检查是否有策略被推荐使用twice-nat44
        recommended_count = sum(1 for r in results if r["should_use"])
        if recommended_count > 0:
            print(f"  🎯 成功：{recommended_count}个策略被推荐使用twice-nat44")
        else:
            print("  ⚠️  注意：没有策略被推荐使用twice-nat44，可能需要进一步调整阈值或评分逻辑")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
