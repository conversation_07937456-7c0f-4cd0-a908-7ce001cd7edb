module ntos-rip {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:rip";
  prefix ntos-rip;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-routing {
    prefix ntos-rt;
  }
  import ntos-routing-types {
    prefix ntos-rt-types;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Free Range Routing";
  contact
    "FRR Users List:       <mailto:<EMAIL>>
     FRR Development List: <mailto:<EMAIL>>";
  description
    "Ruijie NTOS RIP module.";

  revision 2019-06-06 {
    description
      "Add logging configuration.";
    reference "";
  }
  revision 2018-10-24 {
    description
      "Integration in NTOS system.";
    reference "";
  }
  revision 2017-12-06 {
    description
      "Initial revision.";
    reference
      "RFC 1058: Routing Information Protocol.
       RFC 2453: RIP Version 2.";
  }

  identity rip {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Routing RIP protocol.";
    ntos-extensions:nc-cli-identity-name "routing rip";
  }

  identity route-rip {
    base ntos-types:ROUTE4_FRR_ID;
    description
      "RIP routes.";
    ntos-extensions:nc-cli-identity-name "rip";
  }

  identity ripng {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Routing RIPng protocol.";
    ntos-extensions:nc-cli-identity-name "routing ripng";
  }

  identity route-ripng {
    base ntos-types:ROUTE6_FRR_ID;
    description
      "RIPng routes.";
    ntos-extensions:nc-cli-identity-name "ripng";
  }

  grouping rip-common-conf {
    description
      "Configuration common to RIP and RIPng.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable router.";
    }

    leaf allow-ecmp {
      type boolean;
      default "false";
      description
        "Allow equal-cost multi-path.";
    }

    leaf default-information-originate {
      type boolean;
      default "false";
      description
        "Control distribution of default route.";
    }

    leaf default-metric {
      type uint8 {
        range "1..16";
      }
      default "1";
      description
        "Default metric of redistributed routes.";
    }

    list distribute-list {
      must './access-list or ./prefix-list' {
        error-message "Access or prefix lists must be set (but not both)!";
      }
      key "interface update-direction";
      description
        "Filter networks in routing updates.";
      ntos-extensions:nc-cli-one-liner;

      leaf interface {
        type union {
          type ntos-types:ifname;
          type enumeration {
            enum all {
              description
                "Match all interfaces.";
            }
          }
        }
        description
          "Interface to match.";
        ntos-extensions:nc-cli-completion-xpath
          "../../ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf update-direction {
        type enumeration {
          enum in {
            description
              "Incoming updates.";
          }
          enum out {
            description
              "Outgoing updates.";
          }
        }
        description
          "Incoming or outgoing updates.";
      }
      // Augmented with access-list and prefix-list leafs.
    }

    leaf-list network {
      type ntos-inet:ip-prefix;
      description
        "Enable RIP on the specified IP network.";
    }

    leaf-list interface {
      type ntos-types:ifname;
      description
        "Enable RIP on the specified interface.";
      ntos-extensions:nc-cli-completion-xpath
        "../../ntos-interface:interface/*/*[local-name()='name']";
    }

    list offset-list {
      key "interface update-direction";
      description
        "Offset-list to modify route metric.";
      ntos-extensions:nc-cli-one-liner;

      leaf interface {
        type union {
          type ntos-types:ifname;
          type enumeration {
            enum all {
              description
                "Match all interfaces.";
            }
          }
        }
        description
          "Interface to match.";
        ntos-extensions:nc-cli-completion-xpath
          "../../ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf update-direction {
        type enumeration {
          enum in {
            description
              "Incoming updates.";
          }
          enum out {
            description
              "Outgoing updates.";
          }
        }
        description
          "Incoming or outgoing updates.";
      }

      leaf metric {
        type uint8 {
          range "0..16";
        }
        mandatory true;
        description
          "Route metric.";
      }
      // Augmented with access-list leaf.
    }

    leaf-list passive-interface {
      type ntos-types:ifname;
      description
        "A list of interfaces where the sending of RIP packets is disabled.";
      ntos-extensions:nc-cli-completion-xpath
        "../../ntos-interface:interface/*/*[local-name()='name']";
    }
  }

  grouping rip-conf {
    description
      "RIP routing instance.";

    container administrative-distance {
      description
        "Administrative distance.";

      leaf default {
        type uint8 {
          range "1..255";
        }
        description
          "Default administrative distance.";
      }

      list source {
        key "ip-prefix";
        description
          "Custom administrative distance per IP prefix.";
        ntos-extensions:nc-cli-one-liner;

        leaf ip-prefix {
          type ntos-inet:ipv4-prefix;
          description
            "IPv4 prefix.";
        }

        leaf distance {
          type uint8 {
            range "1..255";
          }
          mandatory true;
          description
            "Administrative distance.";
        }
        // Augmented with access-list leaf.
      }
    }

    leaf-list neighbor {
      type ntos-inet:ipv4-address;
      description
        "Specifies the RIP neighbors. Useful for a non-broadcast multiple
         access (NBMA) network.";
    }

    list redistribute {
      key "protocol";
      description
        "Redistributes routes learned from other routing protocols.";
      ntos-extensions:nc-cli-one-liner;

      leaf protocol {
        type enumeration {
          enum connected {
            description
              "Connected routes (directly attached subnet or host).";
          }
          enum kernel {
            description
              "Kernel routes (not installed via the zebra RIB).";
          }
          enum ospf {
            description
              "Open Shortest Path First (OSPFv2).";
          }
          enum bgp {
            description
              "Border Gateway Protocol (BGP).";
          }
          enum static {
            description
              "Statically configured routes.";
          }
        }
        description
          "Routing protocol.";
      }

      leaf metric {
        type uint8 {
          range "0..16";
        }
        description
          "Metric used for the redistributed route. If a metric is
           not specified, the metric configured with the
           default-metric attribute in RIP router configuration is
           used. If the default-metric attribute has not been
           configured, the default metric for redistributed routes
           is 0.";
      }
      // Augmented with route-map leaf.
    }

    list route-map {
      key "interface route-direction";
      description
        "Apply route map to this neighbor.";
      ntos-extensions:nc-cli-one-liner;

      leaf interface {
        type ntos-types:ifname;
        description
          "Interface to match.";
        ntos-extensions:nc-cli-completion-xpath
          "../../ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf route-direction {
        type enumeration {
          enum in {
            description
              "Apply map to incoming routes.";
          }
          enum out {
            description
              "Apply map to outbound routes.";
          }
        }
        description
          "Route direction.";
      }
      // Augmented with route-map-name leaf.
    }

    leaf-list static-route {
      type ntos-inet:ipv4-prefix;
      description
        "RIP static routes.";
    }

    container timers {
      description
        "Settings of basic timers.";

      leaf flush-interval {
        type uint32 {
          range "5..**********";
        }
        units "seconds";
        default "120";
        description
          "Interval before a route is flushed from the routing table.";
      }

      leaf holddown-interval {
        type uint32 {
          range "5..**********";
        }
        units "seconds";
        default "180";
        description
          "Interval before better routes are released.";
      }

      leaf update-interval {
        type uint32 {
          range "5..**********";
        }
        units "seconds";
        default "30";
        description
          "Interval at which RIP updates are sent.";
      }
    }

    container version {
      must '(./receive = "1" and ./send = "1") or (./receive = "2" and ./send = "2") or (./receive = "1-2" and ./send = "2")' {
        error-message "Bad receive/send version pair.";
      }
      description
        "Set routing protocol version.";

      leaf receive {
        type enumeration {
          enum 1 {
            description
              "Accept RIPv1 updates only.";
          }
          enum 2 {
            description
              "Accept RIPv2 updates only.";
          }
          enum 1-2 {
            description
              "Accept both RIPv1 and RIPv2 updates.";
          }
        }
        default "1-2";
        description
          "Advertisement reception - Version control.";
      }

      leaf send {
        type enumeration {
          enum 1 {
            description
              "Send RIPv1 updates only.";
          }
          enum 2 {
            description
              "Send RIPv2 updates only.";
          }
        }
        default "2";
        description
          "Advertisement transmission - Version control.";
      }
    }
    uses rip-common-conf;
  }

  grouping ripng-conf {
    description
      "RIPng routing instance.";

    leaf-list aggregate {
      type ntos-inet:ipv6-prefix;
      description
        "Set aggregate RIPng route announcement.";
    }

    list redistribute {
      key "protocol";
      description
        "Redistributes routes learned from other routing protocols.";
      ntos-extensions:nc-cli-one-liner;

      leaf protocol {
        type enumeration {
          enum connected {
            description
              "Connected routes (directly attached subnet or host).";
          }
          enum kernel {
            description
              "Kernel routes (not installed via the zebra RIB).";
          }
          enum ospf6 {
            description
              "Open Shortest Path First (OSPFv3).";
          }
          enum bgp {
            description
              "Border Gateway Protocol (BGP).";
          }
          enum static {
            description
              "Statically configured routes.";
          }
        }
        description
          "Routing protocol.";
      }

      leaf metric {
        type uint8 {
          range "0..16";
        }
        description
          "Metric used for the redistributed route. If a metric is
           not specified, the metric configured with the
           default-metric attribute in RIPng router configuration is
           used. If the default-metric attribute has not been
           configured, the default metric for redistributed routes
           is 0.";
      }
      // Augmented with route-map leaf.
    }

    leaf-list static-route {
      type ntos-inet:ipv6-prefix;
      description
        "RIPng static routes.";
    }

    container timers {
      description
        "Settings of basic timers.";

      leaf flush-interval {
        type uint16;
        units "seconds";
        default "120";
        description
          "Interval before a route is flushed from the routing
           table.";
      }

      leaf holddown-interval {
        type uint16;
        units "seconds";
        default "180";
        description
          "Interval before better routes are released.";
      }

      leaf update-interval {
        type uint16;
        units "seconds";
        default "30";
        description
          "Interval at which RIP updates are sent.";
      }
    }
    uses rip-common-conf;
  }

  /*
   * Operational RIP state data.
   */

  grouping rip-common-state {
    description
      "Common RIP/RIPng operational state data.";

    list neighbor {
      key "address";
      description
        "RIP neighbor state.";

      leaf address {
        type ntos-inet:ip-address;
        description
          "IP address that a RIP neighbor is using as its
           source address.";
      }

      leaf last-update {
        type string;
        description
          "The time when the most recent RIP update was
           received from this neighbor.";
      }

      leaf bad-packets-received {
        type uint32;
        description
          "The number of RIP invalid packets received from
           this neighbor which were subsequently discarded
           for any reason (e.g. a version 0 packet, or an
           unknown command type).";
      }

      leaf bad-routes-received {
        type uint32;
        description
          "The number of routes received from this neighbor,
           in valid RIP packets, which were ignored for any
           reason (e.g. unknown address family, or invalid
           metric).";
      }
    }
  }

  grouping rip-state {
    description
      "Operational RIP state data.";

    container state {
      description
        "Operational RIP state data.";

      list route {
        key "ip-prefix";
        description
          "RIP IPv4 route state.";

        leaf ip-prefix {
          type ntos-inet:ipv4-prefix;
          description
            "Network address.";
        }

        leaf protocol {
          type enumeration {
            enum BGP {
              description
                "BGP protocol.";
            }
            enum connected {
              description
                "Connected protocol.";
            }
            enum OSPF {
              description
                "OSPF protocol.";
            }
            enum RIP {
              description
                "RIP protocol.";
            }
            enum static {
              description
                "Static protocol.";
            }
            enum unknown {
              description
                "Unknown route protocol.";
            }
          }
          description
            "Route protocol.";
        }

        leaf route-type {
          type enumeration {
            enum default {
              description
                "Default route.";
            }
            enum interface {
              description
                "Interface route.";
            }
            enum normal {
              description
                "Normal route.";
            }
            enum redistribute {
              description
                "Redistribute route.";
            }
            enum static {
              description
                "Static route.";
            }
            enum unknown {
              description
                "Unknown route type.";
            }
          }
          description
            "Route type.";
        }

        leaf nexthop {
          type ntos-inet:ipv4-address;
          description
            "Nexthop IPv4 address.";
        }

        leaf interface {
          type ntos-types:ifname;
          description
            "The interface that the route uses.";
        }

        leaf metric {
          type uint8 {
            range "0..16";
          }
          description
            "Route metric.";
        }
      }
      uses rip-common-state;
    }
  }

  grouping ripng-state {
    description
      "Operational RIPng state data.";

    container state {
      description
        "Operational RIPng state data.";

      list route {
        key "ip-prefix";
        description
          "RIPng IPv6 route state.";

        leaf ip-prefix {
          type ntos-inet:ipv6-prefix;
          description
            "Network address.";
        }

        leaf protocol {
          type enumeration {
            enum BGP {
              description
                "BGP protocol.";
            }
            enum connected {
              description
                "Connected protocol.";
            }
            enum OSPF {
              description
                "OSPFv3 protocol.";
            }
            enum RIPng {
              description
                "RIPng protocol.";
            }
            enum static {
              description
                "Static protocol.";
            }
            enum unknown {
              description
                "Unknown route protocol.";
            }
          }
          description
            "Route protocol.";
        }

        leaf-list route-type {
          type enumeration {
            enum aggregated {
              description
                "Aggregated route.";
            }
            enum default {
              description
                "Default route.";
            }
            enum interface {
              description
                "Interface route.";
            }
            enum normal {
              description
                "Normal route.";
            }
            enum redistribute {
              description
                "Redistribute route.";
            }
            enum static {
              description
                "Static route.";
            }
            enum suppressed {
              description
                "Suppressed route.";
            }
            enum unknown {
              description
                "Unknown route type.";
            }
          }
          description
            "Route type.";
        }

        leaf nexthop {
          type ntos-inet:ipv6-address;
          description
            "Nexthop IPv6 address.";
        }

        leaf metric {
          type uint8 {
            range "0..16";
          }
          description
            "Route metric.";
        }
      }
      uses rip-common-state;
    }
  }

  /*
   * Per-interface configuration data
   */

  grouping interface-common-conf {
    description
      "RIP/RIPng interface parameters.";

    leaf split-horizon {
      type enumeration {
        enum disabled {
          description
            "Disables split-horizon processing.";
        }
        enum simple {
          description
            "Enables simple split-horizon processing.";
        }
        enum poisoned-reverse {
          description
            "Enables split-horizon processing with poison
             reverse.";
        }
      }
      default "simple";
      description
        "Controls RIP split-horizon processing on the specified
         interface.";
    }
  }

  grouping interface-rip-conf {
    description
      "RIP interface parameters.";

    leaf v2-broadcast {
      type boolean;
      default "false";
      description
        "Send IP broadcast v2 update.";
    }

    container version {
      description
        "Set advertisement recpetion/transmission version.";

      leaf receive {
        type enumeration {
          enum inherit {
            description
              "Inherit configuration from the routing instance.";
          }
          enum 1 {
            description
              "Accept RIPv1 updates only.";
          }
          enum 2 {
            description
              "Accept RIPv2 updates only.";
          }
          enum both {
            description
              "Accept both RIPv1 and RIPv2 updates.";
          }
          enum none {
            description
              "Do not accept neither RIPv1 nor RIPv2 updates.";
          }
        }
        default "inherit";
        description
          "Advertisement reception - Version control.";
      }

      leaf send {
        type enumeration {
          enum inherit {
            description
              "Inherit configuration from the routing instance.";
          }
          enum 1 {
            description
              "Send RIPv1 updates only.";
          }
          enum 2 {
            description
              "Send RIPv2 updates only.";
          }
          enum both {
            description
              "Send both RIPv1 and RIPv2 updates.";
          }
        }
        default "inherit";
        description
          "Advertisement transmission - Version control.";
      }
    }

    container authentication {
      must 'count(./password) + count(./key-chain) <= 1' {
        error-message "password and key-chain cannot co-exist!";
      }
      description
        "Specify the authentication scheme for the RIP interface.";

      leaf mode {
        type enumeration {
          enum none {
            description
              "No authentication.";
          }
          enum plain-text {
            description
              "Plain-text authentication.";
          }
          enum md5 {
            description
              "MD5 authentication.";
          }
        }
        default "none";
        description
          "Specify the authentication mode.";
      }

      leaf md5-auth-length {
        when "../mode = 'md5'";
        type enumeration {
          enum rfc {
            description
              "RFC compatible.";
          }
          enum old-ripd {
            description
              "Old ripd compatible.";
          }
        }
        default "old-ripd";
        description
          "MD5 authentication data length.";
      }

      leaf password {
        type string {
          length "1..16";
        }
        description
          "Authentication string.";
      }

      leaf key-chain {
        type string;
        description
          "Key-chain name.";
      }
    }
    uses interface-common-conf;
  }

  grouping interface-ripng-conf {
    description
      "RIPng interface parameters.";
    uses interface-common-conf;
  }

  grouping logging-common-conf {
    description
      "RIP/RIPng logging configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable/disable router logging configuration.";
    }

    leaf events {
      type boolean;
      default "false";
      description
        "Log router events.";
    }

    leaf packet {
      type enumeration {
        enum receive {
          description
            "Log only received packet info.";
        }
        enum send {
          description
            "Log only sent packet info.";
        }
        enum both {
          description
            "Log all packet info.";
        }
      }
      default "both";
      description
        "Log router received/send packet info.";
    }

    leaf zebra {
      type boolean;
      default "false";
      description
        "Log communication between the router and zebra.";
    }
  }

  rpc show-rip {
    description
      "Show RIP information.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf status {
        type empty;
        description
          "Show RIP status.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "rip";
    ntos-api:internal;
  }

  rpc show-ripng {
    description
      "Show RIPng information.";
    input {

      leaf status {
        type empty;
        description
          "Show RIPng status.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "ripng";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing" {
    description
      "RIP router configuration.";

    container rip {
      must 'count(/ntos:config/ntos:vrf/ntos-rt:routing/rip[enabled="true"]) <= 1' {
        error-message "Only one RIP instance can be enabled.";
      }
      presence "Makes RIP router available";
      description
        "RIP router configuration.";
      ntos-extensions:feature "product";
      uses rip-conf {

        augment "administrative-distance/source" {
          description
            "Add access-list option to administrative-distance source list.";

          leaf access-list {
            type ntos-rt-types:v4-access-list-name;
            description
              "Access-list name.";
          }
        }

        augment "distribute-list" {
          description
            "Add access-list and prefix-list options to distribute list.";

          leaf access-list {
            type ntos-rt-types:v4-access-list-name;
            description
              "Access-list name.";
          }

          leaf prefix-list {
            type ntos-rt-types:v4-prefix-list-name;
            description
              "Prefix-list name.";
          }
        }

        augment "offset-list" {
          description
            "Add access-list option to offset list.";

          leaf access-list {
            type ntos-rt-types:v4-access-list-name;
            mandatory true;
            description
              "Access-list name.";
          }
        }

        augment "redistribute" {
          description
            "Add route-map option to redistribute list.";

          leaf route-map {
            type ntos-rt-types:route-map-name;
            description
              "Applies the conditions of the specified route-map to
               routes that are redistributed into the RIP routing
               instance.";
          }
        }

        augment "route-map" {
          description
            "Add route-map-name option to route-map list.";

          leaf route-map-name {
            type ntos-rt-types:route-map-name;
            mandatory true;
            description
              "Route-map name.";
          }
        }
      }
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing" {
    description
      "RIP router operational state data.";

    container rip {
      presence "Makes RIP router available";
      description
        "RIP router operational state data.";
      ntos-extensions:feature "product";
      uses rip-conf {

        augment "administrative-distance/source" {
          description
            "Add access-list option to administrative-distance source list.";

          leaf access-list {
            type string;
            description
              "Access-list name.";
          }
        }

        augment "distribute-list" {
          description
            "Add access-list and prefix-list options to distribute list.";

          leaf access-list {
            type string;
            description
              "Access-list name.";
          }

          leaf prefix-list {
            type string;
            description
              "Prefix-list name.";
          }
        }

        augment "offset-list" {
          description
            "Add access-list option to offset list.";

          leaf access-list {
            type string;
            description
              "Access-list name.";
          }
        }

        augment "redistribute" {
          description
            "Add route-map option to redistribute list.";

          leaf route-map {
            type string;
            description
              "Applies the conditions of the specified route-map to
               routes that are redistributed into the RIP routing
               instance.";
          }
        }

        augment "route-map" {
          description
            "Add route-map-name option to route-map list.";

          leaf route-map-name {
            type string;
            description
              "Route-map name.";
          }
        }
      }
      uses rip-state;
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ip" {
    description
      "RIP interface configuration.";

    container rip {
      presence "Makes RIP available";
      description
        "RIP configuration.";
      ntos-extensions:feature "product";
      uses interface-rip-conf;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ip" {
    description
      "RIP interface state.";

    container rip {
      presence "Makes RIP available";
      description
        "RIP operational state data.";
      ntos-extensions:feature "product";
      uses interface-rip-conf;
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing" {
    description
      "RIPng router configuration.";

    container ripng {
      must '../../ntos:name = "main"' {
        error-message "RIPng is available in default vrf (main) only.";
      }
      presence "Makes RIPng router available";
      description
        "RIPng router configuration.";
      ntos-extensions:feature "product";
      uses ripng-conf {

        augment "distribute-list" {
          description
            "Add access-list and prefix-list options to distribute list.";

          leaf access-list {
            type ntos-rt-types:v6-access-list-name;
            description
              "Access-list name.";
          }

          leaf prefix-list {
            type ntos-rt-types:v6-prefix-list-name;
            description
              "Prefix-list name.";
          }
        }

        augment "offset-list" {
          description
            "Add access-list option to offset list.";

          leaf access-list {
            type ntos-rt-types:v6-access-list-name;
            mandatory true;
            description
              "Access-list name.";
          }
        }

        augment "redistribute" {
          description
            "Add route-map option to redistribute list.";

          leaf route-map {
            type ntos-rt-types:route-map-name;
            description
              "Applies the conditions of the specified route-map to
               routes that are redistributed into the RIPng routing
               instance.";
          }
        }
      }
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing" {
    description
      "RIPng router operational state data.";

    container ripng {
      presence "Makes RIPng router available";
      description
        "RIPng router operational state data.";
      ntos-extensions:feature "product";
      uses ripng-conf {

        augment "distribute-list" {
          description
            "Add access-list and prefix-list options to distribute list.";

          leaf access-list {
            type string;
            description
              "Access-list name.";
          }

          leaf prefix-list {
            type string;
            description
              "Prefix-list name.";
          }
        }

        augment "offset-list" {
          description
            "Add access-list option to offset list.";

          leaf access-list {
            type string;
            description
              "Access-list name.";
          }
        }

        augment "redistribute" {
          description
            "Add route-map option to redistribute list.";

          leaf route-map {
            type string;
            description
              "Applies the conditions of the specified route-map to
               routes that are redistributed into the RIPng routing
               instance.";
          }
        }
      }
      uses ripng-state;
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ipv6" {
    description
      "RIPng interface configuration.";

    container ripng {
      presence "Makes RIPng available";
      description
        "RIPng configuration.";
      ntos-extensions:feature "product";
      uses interface-ripng-conf;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ipv6" {
    description
      "RIPng interface state.";

    container ripng {
      presence "Makes RIPng available";
      description
        "RIPng operational state data.";
      ntos-extensions:feature "product";
      uses interface-ripng-conf;
    }
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:logging" {
    description
      "Common RIP/RIPng routers logging configuration.";

    container rip {
      presence "Make RIP common logging configuration available";
      description
        "Common RIP routers logging configuration.";
      ntos-extensions:feature "product";
      uses logging-common-conf;
    }

    container ripng {
      presence "Make RIPng common logging configuration available";
      description
        "Common RIPng routers logging configuration.";
      ntos-extensions:feature "product";
      uses logging-common-conf;
    }
  }

  augment "/ntos:state/ntos-rt:routing/ntos-rt:logging" {
    description
      "Common RIP/RIPng routers operational state.";

    container rip {
      presence "Make RIP common logging state available.";
      description
        "Operational state common to all RIP routers.";
      ntos-extensions:feature "product";
      uses logging-common-conf;
    }

    container ripng {
      presence "Make RIPng common logging state available.";
      description
        "Operational state common to all RIPng routers.";
      ntos-extensions:feature "product";
      uses logging-common-conf;
    }
  }
}
