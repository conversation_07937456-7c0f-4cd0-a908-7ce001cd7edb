[{"name": "root", "version": 12618, "ts": *************}, {"name": "physical", "version": 12618, "ts": *************}, {"name": "sub_interface", "version": 12618, "ts": *************}, {"name": "routing", "version": 12618, "ts": *************}, {"name": "dhcp", "version": 12618, "ts": *************}, {"name": "security_zone", "version": 12618, "ts": *************}, {"name": "auth", "version": 12618, "ts": *************}, {"name": "devicename", "version": 12618, "ts": *************}, {"name": "discovery", "version": 12618, "ts": *************}, {"name": "timezone", "version": 12618, "ts": *************}, {"name": "security-policy", "version": 12618, "ts": *************}, {"name": "network-obj", "version": 12618, "ts": *************}, {"name": "appid", "version": 12618, "ts": *************}, {"name": "service-obj", "version": 12618, "ts": *************}, {"name": "time-range", "version": 12618, "ts": *************}, {"name": "ips-config", "version": 12618, "ts": *************}, {"name": "anti-virus", "version": 12618, "ts": *************}, {"name": "url-filter", "version": 12618, "ts": *************}, {"name": "url-category", "version": 12618, "ts": *************}, {"name": "security-defend", "version": 12618, "ts": *************}, {"name": "nat", "version": 12618, "ts": *************}, {"name": "threat-intelligence", "version": 12618, "ts": *************}, {"name": "mac-block", "version": 12618, "ts": *************}, {"name": "user-experience", "version": 12618, "ts": *************}, {"name": "mllb", "version": 12618, "ts": *************}]