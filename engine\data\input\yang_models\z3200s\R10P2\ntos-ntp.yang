module ntos-ntp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ntp";
  prefix ntos-ntp;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS NTP.";

  revision 2020-09-23 {
    description
      "Chrony update.";
    reference "";
  }

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity ntp {
    base ntos-types:SERVICE_LOG_ID;
    description
      "NTP service.";
  }

  grouping system-ntp-config {
    description
      "Configuration data for system-wide NTP operation.";

    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable the NTP protocol and indicates that the system should
         attempt to synchronize the system clock with an NTP server from the
         servers defined in the 'ntp/server' list.";
    }

    leaf ntp-source-address {
      type ntos-inet:ip-address;
      description
        "Source address to use on outgoing NTP packets.";
    }
  }

  grouping system-ntp-server-config {
    description
      "Configuration data for NTP servers.";

    leaf address {
      type ntos-inet:host;
      description
        "The address or hostname of the NTP server.";
    }

    leaf version {
      type uint8 {
        range "1..4";
      }
      default "4";
      description
        "Version number to put in outgoing NTP packets.";
    }

    leaf association-type {
      type enumeration {
        enum SERVER {
          description
            "Use client association mode.  This device
             will not provide synchronization to the
             configured NTP server.";
        }
        enum PEER {
          description
            "Use symmetric active association mode.
             This device may provide synchronization
             to the configured NTP server.";
        }
        enum POOL {
          description
            "Use client association mode with one or
             more of the NTP servers found by DNS
             resolution of the domain name given by
             the 'address' leaf.  This device will not
             provide synchronization to the servers.";
        }
      }
      default "SERVER";
      description
        "The desired association type for this NTP server.";
    }

    leaf iburst {
      type boolean;
      default "false";
      description
        "Indicates whether this server should enable burst
         synchronization or not.";
    }

    leaf prefer {
      type boolean;
      default "false";
      description
        "Indicates whether this server should be preferred
         or not.";
    }
  }

  grouping system-ntp-server-config-for-state {
    description
      "Configuration data for NTP servers.";

    leaf address {
      type ntos-inet:host;
      description
        "The address or hostname of the NTP server.";
    }

    leaf version {
      type uint8 {
        range "1..4";
      }
      description
        "Version number to put in outgoing NTP packets.";
    }

    leaf association-type {
      type enumeration {
        enum SERVER {
          description
            "Use client association mode.  This device
             will not provide synchronization to the
             configured NTP server.";
        }
        enum PEER {
          description
            "Use symmetric active association mode.
             This device may provide synchronization
             to the configured NTP server.";
        }
        enum POOL {
          description
            "Use client association mode with one or
             more of the NTP servers found by DNS
             resolution of the domain name given by
             the 'address' leaf.  This device will not
             provide synchronization to the servers.";
        }
        enum LOCAL-CLOCK {
          description
            "Use a local reference clock.";
        }
        enum INVALID {
          description
            "Invalid use of the client/symmetric active
             association mode. This device can not be synchronized
             or provide synchronization to the servers.";
        }
      }
      description
        "The desired association type for this NTP server.";
    }

    leaf iburst {
      type boolean;
      description
        "Indicates whether this server should enable burst
         synchronization or not.";
    }

    leaf prefer {
      type boolean;
      description
        "Indicates whether this server should be preferred
         or not.";
    }
  }

  grouping system-ntp-server-state {
    description
      "Operational state data for NTP servers.";

    leaf stratum {
      type uint8;
      description
        "Indicates the level of the server in the NTP hierarchy. As
         stratum number increases, the accuracy is degraded.  Primary
         servers are stratum while a maximum value of 16 indicates
         unsynchronized.  The values have the following specific
         semantics:

         | 0      | unspecified or invalid
         | 1      | primary server (e.g., equipped with a GPS receiver)
         | 2-15   | secondary server (via NTP)
         | 16     | unsynchronized
         | 17-255 | reserved.";
      reference
        "RFC 5905 - Network Time Protocol Version 4: Protocol and
         Algorithms Specification";
    }

    leaf root-delay {
      type uint32;
      // TODO: reconsider units for these values -- the spec defines
      // rootdelay and rootdisperson as 2 16-bit integers for seconds
      // and fractional seconds, respectively.  This gives a
      // precision of ~15 us (2^-16).  Using milliseconds here based
      // on what implementations typically provide and likely lack
      // of utility for less than millisecond precision with NTP
      // time sync.
      units "milliseconds";
      description
        "The round-trip delay to the server, in milliseconds.";
      reference
        "RFC 5905 - Network Time Protocol Version 4: Protocol and
         Algorithms Specification";
    }

    leaf root-dispersion {
      type uint64;
      units "milliseconds";
      description
        "Dispersion (epsilon) represents the maximum error inherent
         in the measurement.";
      reference
        "RFC 5905 - Network Time Protocol Version 4: Protocol and
         Algorithms Specification";
    }

    leaf offset {
      type uint64;
      units "milliseconds";
      description
        "Estimate of the current time offset from the peer.  This is
         the time difference between the local and reference clock.";
    }

    leaf poll-interval {
      type uint32;
      units "seconds";
      description
        "Polling interval of the peer.";
    }
  }

  grouping system-ntp-auth-keys-config {
    description
      "Configuration data .";

    leaf key-id {
      type uint16;
      description
        "Integer identifier used by the client and server to
         designate a secret key.  The client and server must use
         the same key id.";
    }

    leaf key-value {
      type string;
      description
        "NTP authentication key value.";
    }
  }

  rpc show-ntp {
    description
      "Show NTP information.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "VRF to look into.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf details {
        type empty;
        description
          "Show per server details.";
      }
    }
    output {

      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "ntp";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "NTP configuration.";

    container ntp {
      must "count(/ntos:config/ntos:vrf/ntos-ntp:ntp/enabled[text() = 'true']) <= 1" {
        error-message "Only one enabled ntp client is permitted at a time.";
      }
      must "enabled = 'false' or count(server) > 0" {
        error-message "At least one server must be specified.";
      }
      presence "Makes ntp available";
      description
        "Top-level container for NTP configuration.";
      uses system-ntp-config;

      list auth-key {
        key "key-id";
        description
          "List of NTP authentication keys.";
        uses system-ntp-auth-keys-config;
      }

      list server {
        key "address";
        description
          "List of NTP servers to use for system clock
           synchronization.  If '/system/ntp/enabled'
           is 'true', then the system will attempt to
           contact and utilize the specified NTP servers.";
        uses system-ntp-server-config;

        leaf auth-key-id {
          type leafref {
            path
              "/ntos:config/ntos:vrf/ntos-ntp:ntp/ntos-ntp:auth-key/ntos-ntp:key-id";
          }
          description
            "Integer identifier used by the client and server to
             designate a secret key.  The client and server must use
             the same key id.";
        }
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "NTP operational state.";

    container ntp {
      description
        "Top-level container for NTP state.";
      uses system-ntp-config;

      list auth-key {
        key "key-id";
        description
          "List of NTP authentication keys.";
        uses system-ntp-auth-keys-config;
      }

      list server {
        key "address";
        description
          "List of NTP servers to use for system clock
           synchronization.  If '/system/ntp/enabled'
           is 'true', then the system will attempt to
           contact and utilize the specified NTP servers.";
        uses system-ntp-server-config-for-state;
        uses system-ntp-server-state;

        leaf synchronized {
          type boolean;
          description
            "True if we are synchronized with this server.";
        }

        leaf state {
          type enumeration {
            enum rejected {
              description
                "Not synchronized. Indicates sources to which connectivity
                 has been lost or whose packets do not pass all tests.";
            }
            enum falsetick {
              description
                "Not synchronized. Indicates a clock which chronyd thinks
                 is a falseticker (i.e. its time is inconsistent with a
                 majority of other sources).";
            }
            enum excess {
              status deprecated {
                ntos-ext:status-obsoleted-release "21q3";
                ntos-ext:status-deprecated-revision "2020-09-23";
                ntos-ext:status-description "The state excess is not
                  used anymore with chrony daemon.";
              }
              description
                "Not synchronized. The peer is discarded as not among the first
                 ten peers sorted by synchronization distance and so is probably
                 poor candidate for further consideration.";
            }
            enum outlyer {
              status deprecated {
                ntos-ext:status-obsoleted-release "21q3";
                ntos-ext:status-deprecated-revision "2020-09-23";
                ntos-ext:status-description "The state outlyer is not
                  used anymore with chrony daemon.";
              }
              description
                "Not synchronized. The peer is discarded by the clustering algorithm
                 as an outlyer.";
            }
            enum candidate {
              description
                "Not synchronized. Indicates acceptable sources which are
                 combined with the selected source.";
            }
            enum selected {
              status deprecated {
                ntos-ext:status-obsoleted-release "21q3";
                ntos-ext:status-deprecated-revision "2020-09-23";
                ntos-ext:status-description "The state selected is not
                  used anymore with chrony daemon.";
              }
              description
                "Not synchronized. The peer is a survivor, but not among the first
                 six peers sorted by synchronization distance. If the assocation is
                 ephemeral, it may be demobilized to conserve resources.";
            }
            enum system-peer {
              description
                "Synchronized. Indicates the source to which chronyd is
                 currently synchronized.";
            }
            enum pulse-per-second-peer {
              status deprecated {
                ntos-ext:status-obsoleted-release "21q3";
                ntos-ext:status-deprecated-revision "2020-09-23";
                ntos-ext:status-description "The state
                  pulse-per-second-peer is not used anymore with chrony daemon.";
              }
              description
                "Synchronized. The peer has been declared the system peer and lends its
                 variables to the system variables. However, the actual system
                 synchronization is derived from a pulse-per-second (PPS) signal,
                 either indirectly via the PPS reference clock driver or directly
                 via kernel interface.";
            }
            enum excluded {
              description
                "Not synchronized. Indicates acceptable sources which are
                 excluded by the combining algorithm.";
            }
            enum inconsistent {
              description
                "Not synchronized. Indicates a source whose time appears
                 to have too much variability.";
            }
          }
          description
            "The server status in the clock selection process.";
        }

        leaf auth-key-id {
          type uint16;
          description
            "Integer identifier used by the client and server to
             designate a secret key.  The client and server must use
             the same key id.";
        }
      }
    }
  }
}
