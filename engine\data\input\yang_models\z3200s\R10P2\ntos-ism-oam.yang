module ntos-ism-oam {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ism-oam";
  prefix ntos-ism-oam;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS ISM OAM(operations, administration, and maintenance).";

  revision 2023-10-27 {
    description
      "First version of ISM OAM.";
    reference "";
  }
  
  identity ism-oam {
    base ntos-types:SERVICE_LOG_ID;
    description
      "ISM OAM(operations, administration, and maintenance).";
  }

  grouping detail-filter {
    description
      "Detail filter param.";

    leaf detail {
      type empty;
      description
        "Detail information.";
      ntos-ext:nc-cli-group "subcommand";
    }
  }
  
  grouping counter-filter {
    description
      "Counter filter param.";
    
    leaf counter {
      type empty;
      description
        "Counter information.";
      ntos-ext:nc-cli-group "subcommand";
    }
  }
  
  grouping common-filter {
    description
      "common filter param.";

    uses detail-filter;
    uses counter-filter;
  }

  rpc show-ism-client {
    description
      "Show ISM client information.";
    input {
      must '(count(detail) + count(counter) <= 1)' {
        error-message "Choose detail or counter.";
      }
      leaf name {
        type ntos-types:ntos-obj-name-type {
          length "1..32";
        }
        description
          "Filter by ISM client name.";
      }
      uses common-filter;
    }
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    
    ntos-ext:feature "ism";
    ntos-ext:nc-cli-show "ism client";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-ism-lib-client {
    description
      "Show ISM LIB client information.";
    input {
      must '(count(pid) = 1) and (count(detail) + count(counter) = 1)' {
        error-message "Please input LIB client PID and choose detail or counter.";
      }
      
      leaf pid {
        type uint32;
        description
          "Filter by the PID of ISM LIB client.";
      }
      uses common-filter;
    }
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    
    ntos-ext:feature "ism";
    ntos-ext:nc-cli-show "ism lib-client";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-ism-fpm {
    description
      "Show ISM FPM information.";
    input {
      container queue-msg {
        description
          "messages in FPM queue.";
        uses common-filter;
        ntos-ext:nc-cli-group "subcommand";
      }
      uses common-filter;
    }
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    
    ntos-ext:feature "ism";
    ntos-ext:nc-cli-show "ism fpm";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-ism-global {
    description
      "Show ISM global information.";
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    
    ntos-ext:feature "ism";
    ntos-ext:nc-cli-show "ism global";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-ism-memory {
    description
      "Show ISM memory information.";
    input {
      must 'count(detail) + count(counter) = 1' {
        error-message "Please choose one type information.";
      }
      
      uses common-filter;
    }
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    
    ntos-ext:feature "ism";
    ntos-ext:nc-cli-show "ism memory";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  grouping netlink-filter {
    description
      "Netlink filter param.";
    
    container send {
      description
        "Send information.";
      ntos-ext:nc-cli-group "subcommand";
      uses common-filter;
    }
    
    container receive {
      description
        "Receive information.";
      ntos-ext:nc-cli-group "subcommand";
      uses common-filter;
    }
  }
  
  rpc show-ism-netlink {
    description
      "Show ISM netlink information.";
    input {
      uses netlink-filter;
    }
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    
    ntos-ext:feature "ism";
    ntos-ext:nc-cli-show "ism netlink";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-ism-interface {
    description
      "Show ISM interface information.";
    input {
      must 'count(detail) + count(counter) + count( ipv4-address) + count( ipv6-address) <= 1' {
        error-message "Please choose one type information.";
      }
      
      leaf ifname {
        type union {
          type ntos-types:ifname;
        }
        description
          "Filter by inbound interface.";
          ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']|
            /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='ipv4']/
            *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface'] |
            /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='ipv4']/
            *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
        ntos-ext:nc-cli-group "command";
      }

      leaf iftype {
        type enumeration {
          enum vlan {
            description
              "VLAN interfaces.";
          }
          enum physical {
            description
              "Physical interfaces.";
          }
          enum dsa {
            description
              "DSA interfaces.";
          }
          enum gre {
            description
              "GRE interfaces.";
          }
          enum bridge {
            description
              "Bridge interfaces.";
          }
          enum ipip {
            description
              "IPIP interfaces.";
          }
          enum lag {
            description
              "LAG interfaces.";
          }
          enum loopback {
            description
              "LoopBack interfaces.";
          }
          enum vti {
            description
              "VTI interfaces.";
          }
          enum macvlan {
            description
              "MACVLAN interfaces.";
          }
          enum lo {
            description
              "LO interfaces.";
          }
          enum ppp {
            description
              "PPP interfaces.";
          }
        }
        description
          "Filter by interface type.";
        ntos-ext:nc-cli-group "command";
      }
      
      leaf detail {
        type empty;
        description
          "Counter information.";
        ntos-ext:nc-cli-group "subcommand";
      }
      leaf counter {
        type empty;
        description
          "Counter information.";
        ntos-ext:nc-cli-group "subcommand";
      }
      leaf ipv4-address {
        type empty;
        description
          "Counter information.";
        ntos-ext:nc-cli-group "subcommand";
      }
      leaf ipv6-address {
        type empty;
        description
          "Counter information.";
        ntos-ext:nc-cli-group "subcommand";
      }
    }
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    
    ntos-ext:feature "ism";
    ntos-ext:nc-cli-show "ism interface";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  grouping debug-status {
    description
      "debug status.";
    
    leaf error {
      type empty;
      description
        "Error level debug.";
      ntos-ext:nc-cli-group "subcommand";
    }
    leaf info {
      type empty;
      description
        "Info level debug.";
      ntos-ext:nc-cli-group "subcommand";
    }
    leaf debug {
      type empty;
      description
        "Debug level debug.";
      ntos-ext:nc-cli-group "subcommand";
    }
    leaf off {
      type empty;
      description
        "Close debug.";
      ntos-ext:nc-cli-group "subcommand";
    }
  }
  
  rpc debug-ism {
    description
      "Configure ISM debug status.";
    input {
      container all {
        description
          "All debug.";
        ntos-ext:nc-cli-group "subcommand";
        uses debug-status;
      }
      container tnl {
        description
          "To netlink debug.";
        ntos-ext:nc-cli-group "subcommand";
        uses debug-status;
      }
      container fnl {
        description
          "From netlink debug.";
        ntos-ext:nc-cli-group "subcommand";
        uses debug-status;
      }
      container pub {
        description
          "Public debug.";
        ntos-ext:nc-cli-group "subcommand";
        uses debug-status;
      }
      container intf {
        description
          "Interface debug.";
        ntos-ext:nc-cli-group "subcommand";
        uses debug-status;
      }
      container fpm {
        description
          "FPM debug.";
        ntos-ext:nc-cli-group "subcommand";
        uses debug-status;
      }
      container libs {
        description
          "LIBS debug.";
        ntos-ext:nc-cli-group "subcommand";
        uses debug-status;
      }
      container libc {
        description
          "LIBC debug.";
        ntos-ext:nc-cli-group "subcommand";
        leaf pid {
          type uint32;
          description
            "The PID of ISM LIB client.";
        }
        uses debug-status;
      }
    }
    
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    
    ntos-ext:feature "ism";
    ntos-ext:nc-cli-cmd "ism debug";
    ntos-api:internal;
  }
}
