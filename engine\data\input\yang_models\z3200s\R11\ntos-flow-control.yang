module ntos-flow-control {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:flow-control";
  prefix ntos-flow-control;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-lag {
    prefix ntos-lag;
  }
  import ntos-vti {
    prefix ntos-vti;
  }
  import ntos-loopback {
    prefix ntos-loopback;
  }
  import ntos-user-management {
    prefix ntos-user-management;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS flow Control module.";

  revision 2023-02-28 {
    description
      "Initial version.";
    reference "";
  }

  identity flow-control {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Flow Control service.";
  }


  grouping if-fc-config {
    container flow-control {
      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable or disable flow control on this interface.";
      }
      leaf app-priority-template {
        type uint32;
        description
          "Flow control template of app priority on this interface.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-flow-control:flow-control/ntos-flow-control:app-priority-template/ntos-flow-control:id";
      }
    }
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/ntos:vrf/ntos:name";
    }
  }

  typedef config-source-type {
    type enumeration {
      enum template {
        description
          "Templated configuration.";
      }
      enum manual {
        description
          "Manual configuration.";
      }
    }
  }

  typedef config-source-internal-type {
    type enumeration {
      enum template {
        description
          "Templated configuration.";
      }
      enum manual {
        description
          "Manual configuration.";
      }
      enum critical {
        description
          "Critical configuration.";
      }
    }
  }

  typedef tpl-file-type {
    type enumeration {
      enum tpl-ibar;
      enum tpl-SMEs;
    }
  }

  typedef bw-per-type {
    type enumeration {
      enum per-ip;
      enum per-user;
    }
  }

  grouping flow-control-match {
    description
      "Match for flow control policy.";

    list source-interface {
      key "name";
      description
        "Set this interface as Source.";
      ntos-extensions:nc-cli-one-liner;

      leaf name {
        type leafref {
          path
            "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:name";
        }
      }

      status deprecated {
        ntos-extensions:status-obsoleted-release "24q1";
        ntos-extensions:status-deprecated-revision "2024-03-06";
        ntos-extensions:status-description "The source-interface only supports physical interface. It will be replaced by the ingress-interface that supports more types of interface.";
        ntos-extensions:status-replacement "/ntos:config/ntos:vrf/ntos-flow-control:flow-control/ntos-flow-control:policy/ntos-flow-control:ingress-interface";
      }
    }
    list dest-interface {
      key "name";
      description
        "Set this interface as Destination.";
      ntos-extensions:nc-cli-one-liner;

      leaf name {
        type leafref {
          path
            "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:name";
        }
      }

      status deprecated {
        ntos-extensions:status-obsoleted-release "24q1";
        ntos-extensions:status-deprecated-revision "2024-03-06";
        ntos-extensions:status-description "The dest-interface only supports physical interface. It will be replaced by the egress-interface that supports more types of interface.";
        ntos-extensions:status-replacement "/ntos:config/ntos:vrf/ntos-flow-control:flow-control/ntos-flow-control:policy/ntos-flow-control:egress-interface";
      }
    }
    choice ingress {
      case interface {
        list ingress-interface {
          key "name";
          description
            "Set this interface as Source.";
          ntos-extensions:nc-cli-one-liner;

          leaf name {
            type ntos-types:ifname;
            ntos-extensions:nc-cli-completion-xpath
              "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:name |
              /ntos:config/ntos:vrf/ntos-interface:interface/ntos-lag:lag/ntos-lag:name |
              /ntos:config/ntos:vrf/ntos-interface:interface/ntos-vti:vti/ntos-vti:name |
              /ntos:config/ntos:vrf/ntos-interface:interface/ntos-loopback:loopback/ntos-loopback:name";
          }
        }
      }
      case zone {
        list source-zone {
          description
            "Set this zone as Source.";
          ntos-extensions:nc-cli-one-liner;
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            ntos-extensions:nc-cli-no-name;
          }
        }
      }
    }
    choice egress {
      case interface {
        list egress-interface {
          key "name";
          description
            "Set this interface as Destination.";
          ntos-extensions:nc-cli-one-liner;

          leaf name {
            type ntos-types:ifname;
            ntos-extensions:nc-cli-completion-xpath
              "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:name |
              /ntos:config/ntos:vrf/ntos-interface:interface/ntos-lag:lag/ntos-lag:name |
              /ntos:config/ntos:vrf/ntos-interface:interface/ntos-vti:vti/ntos-vti:name |
              /ntos:config/ntos:vrf/ntos-interface:interface/ntos-loopback:loopback/ntos-loopback:name |
              /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='ipv4']/
              *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface'] |
              /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='ipv4']/
              *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
          }
        }
      }
      case zone {
        list dest-zone {
          description
            "Set this zone as Destination.";
          ntos-extensions:nc-cli-one-liner;
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            ntos-extensions:nc-cli-no-name;
          }
        }
      }
    }
    list source-network {
      description
        "Name of a source network.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-extensions:nc-cli-no-name;
      }
    }
    list dest-network {
      description
        "Name of a destination network.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-extensions:nc-cli-no-name;
      }
    }
    leaf time-range {
      description
        "Name of a time range.";
      type ntos-types:ntos-obj-name-type;
      default "any";
    }
    list service {
      description
        "The name of service.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-extensions:nc-cli-no-name;
      }
    }
    list app {
      description
        "The name of application.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-extensions:nc-cli-no-name;
      }
    }
    list user-name {
      description
        "policy related user obj. An user obj name must carry the authentication domain name. For example: user1@xxx, if xxx is the default domain, do not fill it in and remove the '@' character.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-extensions:nc-cli-no-name;
      }
    }
    list user-group {
      description
        "The name of user group.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-user-management:user-group-path;
        ntos-extensions:nc-cli-no-name;
      }
    }
    list url-pre-defined {
      description
        "Indicate the flow control policy pre define category.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of pre define category.";
      }
      leaf name-i18n {
        type string;
        config false;
        description
          "The name-i18n of pre define category.";
      }
    }
    list url-user-defined {
      description
        "Indicate the flow control policy user define category.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of user define category.";
      }
    }
  }

  grouping action-def {
    container action {
      description
        "The action of this flow control policy.";
      ntos-extensions:nc-cli-one-liner;
      choice action {
        leaf permit {
          ntos-extensions:nc-cli-group "action";
          description
            "Permit this flow.";
          type empty;
        }

        leaf deny {
          ntos-extensions:nc-cli-group "action";
          description
            "deny this flow.";
          type empty;
        }

        container limit {
          ntos-extensions:nc-cli-group "action";
          description
            "Limit this flow.";
          leaf channel {
            type leafref {
              path
                "/ntos:config/ntos:vrf/flow-control/channel/name";
            }
            description
              "The channel which this flow control policy used.";
          }
        }
      }
    }
  }

  grouping flow-control-policy {
    description
      "Configuration of flow control policy.";

    list policy {
      key "name";
      description
        "The detail of flow control policy.";
      ordered-by user;

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of this flow control policy.";
      }

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable this flow control policy.";
      }

      leaf config-source {
        type config-source-type;
        default "manual";
        description
          "Configuration source of this flow control policy.";
      }

      leaf parent {
        type ntos-types:ntos-obj-name-type;
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-flow-control:flow-control/ntos-flow-control:policy/ntos-flow-control:name";
        description
          "The parent policy name of this flow control policy.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type {
          length "0..63";
        }
        description
          "The description of this flow control policy.";
      }

      uses flow-control-match;
      uses action-def;
    }
  }

  grouping bandwidth-value {
    description
      "Configuration of bandwidth value.";

    leaf upstream {
      type uint32 {
        range "1..200000000";
      }
      description
        "Bandwidth of upstream.";
      units Kbps;
    }
    leaf downstream {
      type uint32 {
        range "1..200000000";
      }
      description
        "Bandwidth of downstream.";
      units Kbps;
    }
  }

  grouping bandwidth-scope {
    description
      "Configuration of bandwidth scope.";

    container maximum-bandwidth {
      description
        "maximum bandwidth parameters. Unset means unlimited.";
      uses bandwidth-value;
    }
    container guaranteed-bandwidth {
      description
        "guaranteed bandwidth parameters. Unset means unguaranteed";
      uses bandwidth-value;
    }
  }

  grouping flow-control-bandwidth {
    description
      "Configuration bandwidth of flow control channel.";

    container bandwidth {
      description
        "bandwidth parameters.";
      container whole {
        description
          "bandwidth of whole channel.";
        uses bandwidth-scope;
      }
      leaf per-ip-or-user {
        description
          "Bandwidth of per-ip or per-user.";
        type bw-per-type;
        default "per-ip";
      }
      choice per-ip-user {
        case per-ip {
          ntos-extensions:nc-cli-group "per-ip-user";
          container per-ip {
            description
              "bandwidth of per-IP.";
            choice manual-average {
              case manual {
                ntos-extensions:nc-cli-group "manual-average";
                container manual {
                  description
                    "Configure the per-IP manual bandwidth.";
                  uses bandwidth-scope;
                }
              }
              case average {
                ntos-extensions:nc-cli-group "manual-average";
                container average {
                  description
                    "Configure the per-IP average bandwidth.";
                  leaf auto {
                    type empty;
                  }
                }
              }
            }
            leaf limit {
              description
                "Set the maxinum number of IP.";
              type uint16;
            }
          }
        }
      }
      must "count(whole/maximum-bandwidth/upstream) = 0 or count(whole/guaranteed-bandwidth/upstream) = 0 or (whole/maximum-bandwidth/upstream >= whole/guaranteed-bandwidth/upstream)" {
        error-message "whole/maximum-bandwidth/upstream must greater than or equal to whole/guaranteed-bandwidth/upstream.";
      }
      must "count(whole/maximum-bandwidth/upstream) = 0 or count(per-ip/manual/maximum-bandwidth/upstream) = 0  or (whole/maximum-bandwidth/upstream >= per-ip/manual/maximum-bandwidth/upstream)" {
        error-message "whole/maximum-bandwidth/upstream must greater than or equal to per-ip/manual/maximum-bandwidth/upstream.";
      }
      must "count(whole/maximum-bandwidth/downstream) = 0 or count(whole/guaranteed-bandwidth/downstream) = 0 or (whole/maximum-bandwidth/downstream >= whole/guaranteed-bandwidth/downstream)" {
        error-message "whole/maximum-bandwidth/downstream must greater than or equal to whole/guaranteed-bandwidth/downstream.";
      }
      must "count(whole/maximum-bandwidth/downstream) = 0 or count(per-ip/manual/maximum-bandwidth/downstream) = 0 or (whole/maximum-bandwidth/downstream >= per-ip/manual/maximum-bandwidth/downstream)" {
        error-message "whole/maximum-bandwidth/downstream must greater than or equal to per-ip/manual/maximum-bandwidth/downstream.";
      }
      must "count(per-ip/manual/guaranteed-bandwidth/upstream) = 0 or count(whole/guaranteed-bandwidth/upstream) = 0 or (whole/guaranteed-bandwidth/upstream >= per-ip/manual/guaranteed-bandwidth/upstream)" {
        error-message "whole/guaranteed-bandwidth/upstream must greater than or equal to per-ip/manual/guaranteed-bandwidth/upstream.";
      }
      must "count(per-ip/manual/guaranteed-bandwidth/downstream) = 0 or count(whole/guaranteed-bandwidth/downstream) = 0 or (whole/guaranteed-bandwidth/downstream >= per-ip/manual/guaranteed-bandwidth/downstream)" {
        error-message "whole/guaranteed-bandwidth/downstream must greater than or equal to per-ip/manual/guaranteed-bandwidth/downstream.";
      }
      must "count(per-ip/manual/maximum-bandwidth/upstream) = 0 or count(per-ip/manual/guaranteed-bandwidth/upstream) = 0 or (per-ip/manual/maximum-bandwidth/upstream >= per-ip/manual/guaranteed-bandwidth/upstream)" {
        error-message "per-ip/manual/maximum-bandwidth/upstream must greater than or equal to per-ip/manual/guaranteed-bandwidth/upstream.";
      }
      must "count(per-ip/manual/maximum-bandwidth/downstream) = 0 or count(per-ip/manual/guaranteed-bandwidth/downstream) = 0 or (per-ip/manual/maximum-bandwidth/downstream >= per-ip/manual/guaranteed-bandwidth/downstream)" {
        error-message "per-ip/manual/maximum-bandwidth/downstream must greater than or equal to per-ip/manual/guaranteed-bandwidth/downstream.";
      }
    }
  }

  grouping flow-control-channel {
    description
      "Configuration channel of flow control policy.";

    list channel {
      key "name";
      description
        "The detail of flow control channel.";
      ordered-by user;

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of flow control channel.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type {
          length "0..63";
        }
        description
          "The description of this flow control channel.";
      }

      leaf shared {
        type boolean;
        default "false";
        description
          "Set this flow control channel to be shared or not.";
      }

      leaf config-source {
        type config-source-type;
        default "manual";
        description
          "Configuration source of this flow control channel.";
      }

      leaf priority {
        type uint8 {
          range "1..7";
        }
        default 4;
        description
          "The priority of flow control channel.";
      }

	  leaf fine-grained {
		  type boolean;
		  default "false";
		  description
			  "Enabled the fine-grained scheduled in channel";
	  }

      uses flow-control-bandwidth;
    }
  }

  grouping flow-control-template {
    description
      "Configuration of flow control template.";

    list app-priority-template {
      key "id";
      description
        "The detail of flow control template for app priority.";
      ordered-by user;

      leaf id {
        type uint32;
        description
          "The id of flow control template.";
      }

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of flow control template.";
      }

      leaf config-source {
        type config-source-type;
        default "manual";
        description
          "Configuration source of this flow control template.";
      }

      list app-priority {
        key "priority";
        description
          "Set app's priority.";

        leaf priority {
          type uint8 {
            range "1..7";
          }
        }

        list app {
          description
            "The name of application.";
          ntos-extensions:nc-cli-one-liner;
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            ntos-extensions:nc-cli-no-name;
          }
        }
      }
    }
  }

  rpc fc-enabled {
    description
      "Show flag of flow control enabled.";

    ntos-extensions:nc-cli-show "flow-control enabled json";
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-channel {
    description
      "Show data of flow control channel.";

    ntos-extensions:nc-cli-show "flow-control channel json";
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;

      leaf filter {
        description
          "The content of search.";

        type ntos-types:ntos-obj-description-type;
      }

      leaf config-source {
        type config-source-internal-type;
        description
          "Configuration source of this flow control channel.";
      }

      leaf name {
        description
          "The name of flow control channel.";

        type ntos-types:ntos-obj-description-type;
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc show-channel-config {
    description
      "Show data of flow control channel.";

    ntos-extensions:nc-cli-show "flow-control channel";

    input {
      uses vrf;

      leaf filter {
        description
          "The content of search.";

        type ntos-types:ntos-obj-description-type;
      }

      leaf config-source {
        type config-source-internal-type;
        description
          "Configuration source of this flow control channel.";
      }

      leaf name {
        description
          "The name of flow control channel.";

        type ntos-types:ntos-obj-description-type;
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-policy {
    description
      "Show data of flow control policy.";

    ntos-extensions:nc-cli-show "flow-control policy json";
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;

      leaf filter {
        description
          "The content of search.";

        type ntos-types:ntos-obj-description-type;
      }

      leaf config-source {
        type config-source-internal-type;
        description
          "Configuration source of this flow control policy.";
      }

      leaf name {
        description
          "The name of flow control policy.";

        type ntos-types:ntos-obj-description-type;
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc show-policy-config {
    description
      "Show data of flow control policy.";

    ntos-extensions:nc-cli-show "flow-control policy";

    input {
      uses vrf;

      leaf filter {
        description
          "The content of search.";

        type ntos-types:ntos-obj-description-type;
      }

      leaf config-source {
        type config-source-internal-type;
        description
          "Configuration source of this flow control policy.";
      }

      leaf name {
        description
          "The name of flow control policy.";

        type ntos-types:ntos-obj-description-type;
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-policy-stat-clear {
    description
      "Clear flow control policy hit counter.";
    ntos-extensions:nc-cli-cmd "flow-control clear-stat";
    ntos-api:internal;

    input {
      uses vrf;
      list policy {
        description
          "The name of this flow control policy.";
        key "name";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          ntos-extensions:nc-cli-no-name;
          description
            "The name of this flow control policy.";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-channel-brief {
    description
      "Get brief of flow control channel.";
    ntos-extensions:nc-cli-show "flow-control channel-brief json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
      leaf config-source {
        type config-source-internal-type;
        description
          "Assign source of this flow control channel.";
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-policy-brief {
    description
      "Get brief of flow control policy.";
    ntos-extensions:nc-cli-show "flow-control policy-brief json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
      leaf config-source {
        type config-source-internal-type;
        description
          "Assign source of this flow control policy.";
      }
      list egress-interface {
        key "name";
        description
          "Destination interface of this flow control policy.";
        ntos-extensions:nc-cli-one-liner;

        leaf name {
          type ntos-types:ifname;
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-policy-children-brief {
    description
      "Get brief of flow control policy children.";
    ntos-extensions:nc-cli-show "flow-control policy-children-brief json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
      list policy {
        key "name";
        description
          "Flow control policy.";
        ntos-extensions:nc-cli-one-liner;

        leaf name {
          type ntos-types:ifname;
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-get-policy-detail {
    description
      "Get info when add a policy";
    ntos-extensions:nc-cli-show "flow-control fc-get-policy-detail json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-get-enabled-intf {
    description
      "Get interface list with flow control enabled.";
    ntos-extensions:nc-cli-show "flow-control get-enabled-interface json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-get-policy-name-by-id {
    description
      "Get policy name by ID";
    ntos-extensions:nc-cli-show "flow-control get-policy-name-by-id json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
      leaf policy-id {
        type string;
        description
          "Policy ID list";
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc get-interface-fc-enabled {
    description
      "Get flow control enabled status of specified interface.";
    ntos-extensions:nc-cli-show "flow-control get-interface-fc-enabled json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
      leaf interface {
        type ntos-types:ifname;
        ntos-extensions:nc-cli-no-name;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-intf-info-get {
    description
      "Get flow control enabled status of specified interface.";
    ntos-extensions:nc-cli-show "flow-control fc-intf-info-get json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
      leaf interface {
        type ntos-types:ifname;
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='name']";
        ntos-extensions:nc-cli-no-name;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-tpl-name {
    description
      "Show the name of template file which is used.";

    ntos-extensions:nc-cli-show "flow-control template-name json";
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-tpl-get {
    description
      "Show the name of template file which is used.";

    ntos-extensions:nc-cli-show "flow-control template json";
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;

      list exact-params {
        key field;
        leaf field {
          type enumeration {
            enum name;
          }
        }

        leaf exact-filter {
          type string;
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;

        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-tpl-app-get {
    description
      "Get template app list of flow control.";
    ntos-extensions:nc-cli-show "flow-control template app json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
      leaf template {
        type ntos-types:ntos-obj-name-type;
        description
          "The the name of template file.";
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-tpl-cal {
    description
      "Calculate configuration data via template of flow control.";
    ntos-extensions:nc-cli-show "flow-control template config json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;
      leaf template {
        type ntos-types:ntos-obj-name-type;
        description
          "The the name of template file.";
      }
      list interface {
        key "name";
        description
          "The data list of interface.";
        ordered-by user;

        leaf name {
          type ntos-types:ifname;
          description
            "The name of interface.";
        }
        uses bandwidth-value;
      }

      list app-group {
        key "name";
        description
          "The name list of app group.";
        ordered-by user;

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of interface.";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-tpl-channel-get {
    description
      "Show data of flow control channel of template.";
    ntos-extensions:nc-cli-show "flow-control template channel json";
    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;

    input {
      uses vrf;

      leaf interface {
        type ntos-types:ifname;
        description
          "The name of interface.";
      }

      leaf start {
        description
          "The index of page start.";

        type uint16;
      }

      leaf end {
        description
          "The index of page end.";

        type uint16;
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-tpl-config-update {
    description
      "Update flow control template DB.";

    input {
      uses vrf;
      leaf type {
        description
          "Type of config update.";
        type config-source-type;
      }
    }

    output {
      leaf id {
        description
          "Id of upgrade.";
        type uint32;
      }
      leaf progress {
        description
          "Progress of upgrade.";
        type uint32;
      }
      leaf errnum {
        description
          "Error code.";
        type uint32;
      }
    }

    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;
  }

  rpc fc-tpl-update {
    description
      "Update flow control template DB.";

    input {
      leaf file-path {
        type string;
        description
          "Path of upgrade file.";
      }
    }

    output {
      leaf id {
        description
          "Id of upgrade.";
        type uint32;
      }
      leaf progress {
        description
          "Progress of upgrade.";
        type uint32;
      }
      leaf errnum {
        description
          "Error code.";
        type uint32;
      }
    }

    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;
  }

  rpc fc-tpl-version-show {
    description
      "Show flow control template version.";

    output {
      leaf version {
        type string;
        description
          "Version of template.";
      }
      leaf format-version {
        type string;
        description
          "Version of template format.";
      }
      list dev-type {
        description
          "The list of device type.";
        key "name";

        leaf name {
          type string;
          description
            "The type name of device.";
        }
      }
    }

    ntos-extensions:nc-cli-show "flow-control template version";
  }

  rpc fc-tpl-config-update-state {
    description
      "Upgrade state of flow control template config.";

    input {
      leaf id {
        description
          "Id of upgrade.";
        type uint32;
      }
    }

    output {
      leaf errnum {
        description
          "Error code.";
        type uint32;
      }
    }

    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;
  }

  rpc fc-tpl-update-state {
    description
      "Upgrade state of flow control template DB.";

    input {
      leaf id {
        description
          "Id of upgrade.";
        type uint16;
      }
    }

    output {
      leaf errnum {
        description
          "Error code.";
        type uint32;
      }
    }

    ntos-api:internal;
    ntos-extensions:nc-cli-hidden;
  }

  rpc fc-chk-intf-ref-state {
    description
      "Check flow control policy refer status for specified interface.";
    ntos-extensions:nc-cli-cmd "flow-control chk-intf-ref-state json";
    ntos-extensions:nc-cli-hidden;
    ntos-api:internal;

    input {
      uses vrf;
      list interface {
        description
          "The name of interface.";
        key "name";

        leaf name {
          type ntos-types:ifname;
          description
            "The name of interface.";
        }
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc fc-clear-statistics-channel {
    description
      "Clear statistics of flow control channel.";

    ntos-extensions:nc-cli-cmd "clear flow-control statistics channel";

    input {
      uses vrf;

      leaf filter {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The content of search.";
        type string;
      }
      leaf name {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The name of flow control channel.";
        type string;
      }
      leaf index {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The index of flow control channel";
        type uint32;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
  }

  rpc flow-control-statistics-channel {
    description
      "Show statistics of flow control channel.";

    ntos-extensions:nc-cli-show "flow-control statistics channel";

    input {
      uses vrf;

      leaf filter {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The content of search.";
        type string;
      }
      leaf name {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The name of flow control channel.";
        type string;
      }
      leaf index {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The index of flow control channel";
        type uint32;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
  }

  rpc fc-clear-statistics-policy {
    description
      "Clear statistics of flow control policy.";

    ntos-extensions:nc-cli-cmd "clear flow-control statistics policy";

    input {
      uses vrf;

      leaf filter {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The content of search.";
        type string;
      }
      leaf name {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The name of flow control policy.";
        type string;
      }
      leaf index {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The index of flow control policy.";
        type uint32;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
  }

  rpc flow-control-statistics-policy {
    description
      "Show statistics of flow control policy.";

    ntos-extensions:nc-cli-show "flow-control statistics policy";

    input {
      uses vrf;

      leaf filter {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The content of search.";
        type string;
      }
      leaf name {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The name of flow control policy.";
        type string;
      }
      leaf index {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The index of flow control policy.";
        type uint32;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
  }

  rpc fc-clear-statistics-interface {
    description
      "Clear statistics of flow control interface.";

    ntos-extensions:nc-cli-cmd "clear flow-control statistics interface";

    input {
      uses vrf;

      leaf filter {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The content of search.";
        type string;
      }
      leaf name {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The name of flow control interface.";
        type string;
      }
      leaf index {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The index of flow control interface.";
        type uint32;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
  }

  rpc flow-control-statistics-interface {
    description
      "Show statistics of flow control interface.";

    ntos-extensions:nc-cli-show "flow-control statistics interface";

    input {
      uses vrf;

      leaf filter {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The content of search.";
        type string;
      }
      leaf name {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The name of flow control interface.";
        type string;
      }
      leaf index {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The index of flow control interface.";
        type uint32;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
  }

  rpc flow-control-statistics-channel-rate {
    description
      "Show rate statistics of flow control channel.";

    ntos-extensions:nc-cli-show "flow-control statistics channel-rate";

    input {
      uses vrf;

      leaf index {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The index of flow control channel.";
        type uint32;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
  }

  rpc show-template {
    description
      "Show data of flow control policy.";

    ntos-extensions:nc-cli-show "flow-control template";

    input {
      uses vrf;

    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  // rpc flow-control-unittest {
  //   description
  //     "Show rate statistics of flow control channel.";

  //   ntos-extensions:nc-cli-cmd "flow-control unittest";

  //   input {
  //     leaf content {
  //       ntos-extensions:nc-cli-group "subcommand";
  //       ntos-extensions:nc-cli-no-name;
  //       type string;
  //     }
  //   }

  //   output {
  //     leaf buffer {
  //       type string;
  //       description
  //         "Command output buffer.";
  //       ntos-extensions:nc-cli-stdout;
  //       ntos-extensions:nc-cli-hidden;
  //     }
  //   }
  // }

  augment "/ntos:config/ntos:vrf" {
    description
      "flow control config.";
    container flow-control {
      description
        "flow control configuration.";
    
      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable or disable flow control.";
      }
      leaf template {
        type tpl-file-type;
        description
          "The the name of template file.";

        status deprecated {
          ntos-extensions:status-obsoleted-release "24q1";
          ntos-extensions:status-deprecated-revision "2024-03-29";
          ntos-extensions:status-description "The template only supports two templates. It will be replaced by the template-name that supports more templates.";
          ntos-extensions:status-replacement "/ntos:config/ntos:vrf/ntos-flow-control:flow-control/ntos-flow-control:template-name";
        }
      }
      leaf template-name {
        type ntos-types:ntos-obj-name-type;
        description
          "The the name of template file.";
      }
      uses flow-control-channel;
      uses flow-control-policy;
      uses flow-control-template;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "flow control policy state.";
	  container flow-control {
      config false;
      leaf enabled {
        type boolean;
      }

      leaf version {
        type uint32;
      }

	    leaf send-error {
	      type uint32;
	    }
	  }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
    uses if-fc-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical/ntos-interface:ipv4/ntos-interface:pppoe/ntos-interface:connection" {
    uses if-fc-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-lag:lag" {
    uses if-fc-config;
  }
}
