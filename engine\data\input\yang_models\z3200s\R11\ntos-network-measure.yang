module ntos-network-measure {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:network-measure";
  prefix ntos-network-measure;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-inet-types {
    prefix ntos-inet;
  }

  import ntos-user-management {
    prefix ntos-user-management;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co., Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS local defend module.";

  revision 2024-04-25 {
    description
      "Used poor performance config value instead of start and end.";
  }

  revision 2024-04-25 {
    description
      "Support service control switch.";
  }

  revision 2023-08-24 {
    description
      "Support Service.";
  }

  revision 2023-05-12 {
    description
      "Create initial version.";
  }

  identity network-measure {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Network Measure service.";
  }

  typedef user-group-path {
    description
      "This type represents a user group path.";
    type string {
      pattern "[^`~!#$%^&*+|{};:\"',\\\\<>?]*" {
        error-message 'cannot include character: `~!#%^&*+|{};:",\<>?';
      }
    }
  }

  grouping cmd-output-buffer {
    leaf buffer {
      description
        "Command output buffer since last request.";

      type string;
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  grouping network-measure-app {
    leaf name {
      description
        "The name of application.";

      type ntos-types:ntos-obj-name-type;
    }

    leaf enabled {
      description
        "Enable or disable policy.";

      type boolean;
      default "true";
    }

    leaf top {
      description
        "Application placed top flag.";

      type uint32 {
        range "0..2";
        ntos-ext:nc-cli-shortdesc "<0-2>";
      }
    }

    leaf sla-policy {
      description
        "Apply the associated assurance policy.";

      type string;
    }

    leaf auto-type {
      description
        "select ai function level.";

      type enumeration {
        enum strict;
        enum balance;
        enum relax;
      }
      default "balance";
    }

    leaf method {
      description
        "select standard performance func.";

      type enumeration {
        enum munual;
        enum auto;
      }
      default "munual";
    }

    container poor-performance-config {
      container lost {
        description
          "Package-Lost rate in poor performance.";

        leaf start {
          type uint32 {
            range "1..100";
            ntos-ext:nc-cli-shortdesc "(Deprecated)<1-100>";
          }
          default 3;
          units "percent";
        }

        leaf end {
          type uint32 {
            range "1..100";
            ntos-ext:nc-cli-shortdesc "(Deprecated)<1-100>";
          }
          units "percent";
          default 10;
          must "current() >= ../start" {
            error-message "The End value must be larger than the start value.";
          }
        }

        leaf value {
          type uint32 {
            range "1..100";
            ntos-ext:nc-cli-shortdesc "<1-100>";
          }
          default 10;
          units "percent";
        }
      }

      container latency {
        description
          "Latency in poor performance.";

        leaf start {
          type uint32 {
            range "1..1000";
            ntos-ext:nc-cli-shortdesc "(Deprecated)<1-1000>";
          }
          default 200;
          units "millisecond";
        }

        leaf end {
          type uint32 {
            range "1..1000";
            ntos-ext:nc-cli-shortdesc "(Deprecated)<1-1000>";
          }
          default 500;
          units "millisecond";
          must "current() >= ../start" {
            error-message "The End value must be larger than the start value.";
          }
        }

        leaf value {
          type uint32 {
            range "1..1000";
            ntos-ext:nc-cli-shortdesc "<1-1000>";
          }
          units "millisecond";
          default 500;
        }
      }

      container jitter {
        description
          "Jitter in poor performance.";

        leaf value {
          type uint32 {
            range "1..1000";
            ntos-ext:nc-cli-shortdesc "<1-1000>";
          }
          default 100;
          units "millisecond";
        }

      }
    }
  }

  grouping network-measure-user {
    leaf description {
      description
        "The description of user.";

      type ntos-types:ntos-obj-description-type {
          length "1..16";
        }
    }

    leaf enabled {
      description
        "Enable or disable policy.";

      type boolean;
      default "true";
    }

    leaf top {
      description
        "User placed top flag.";

      type uint32 {
        range "0..2";
        ntos-ext:nc-cli-shortdesc "<0-2>";
      }
    }

    leaf source-network {
      description
        "The name of source network.";

        type ntos-types:ntos-obj-name-type;
    }

    container poor-performance-config {
      description
        "latency and jitter in poor performance.";

      leaf latency {
        type uint32 {
          range "1..1000";
          ntos-ext:nc-cli-shortdesc "<1-1000>";
        }
        default 500;
        units "millisecond";
      }

      leaf jitter {
        type uint32 {
          range "1..1000";
          ntos-ext:nc-cli-shortdesc "<1-1000>";
        }
        default 100;
        units "millisecond";
      }
    }

    leaf user-name {
      description
        "Configure the name of the user.
          An user obj name must carry the authentication domain name.
          For example: user1@xxx, if xxx is the default domain,
                                      do not fill it in and remove the '@' character.";

      type user-group-path;
    }

    list detect-target {
      key "target";
      max-elements 3;
      ordered-by user;

      leaf target {
        description
          "IPv4/IPv6 address or domain";

        type union {
          type ntos-inet:ip-address;
          type string {
            length "1..253";
            pattern '(([a-zA-Z0-9]([a-zA-Z0-9\-]){0,61})?[a-zA-Z0-9]\.)+(([a-zA-Z0-9]([a-zA-Z0-9\-]){0,61})?[a-zA-Z0-9]\.?)+';
            ntos-ext:nc-cli-shortdesc "<domain-name>";
          }
        }
        ntos-ext:nc-cli-no-name;
      }
    }
  }

  grouping network-measure-combination {
    leaf name {
      description
        "The name of combination.";

      type ntos-types:ntos-obj-name-type;
    }

    leaf description {
      description
        "The description of combination.";

      type ntos-types:ntos-obj-description-type {
        length "1..16";
      }
    }

    leaf enabled {
      description
        "Enable or disable combination.";

      type boolean;
      default "true";
    }

    list app {
      description
        "The app of combination.";

      key "name";
      ordered-by user;

      leaf name {
        description
          "The name of application.";

        type ntos-types:ntos-obj-name-type;
      }
    }

    list source-network {
      description
        "The source network of combination.";

      key "name";
      ordered-by user;

      leaf name {
        description
          "The name of source network.";

          type ntos-types:ntos-obj-name-type;
      }
    }

    list extra-app {
      description
        "The extra app of combination.";

      key "name";
      ordered-by user;

      leaf name {
        description
          "The name of application.";

        type ntos-types:ntos-obj-name-type;
      }
    }

    list extra-source-network {
      description
        "The extra source network of combination.";

      key "name";
      ordered-by user;

      leaf name {
        description
          "The name of source network.";

          type ntos-types:ntos-obj-name-type;
      }
    }

    leaf url {
      description
        "URL of combination.";

      type string;
      default "";
    }

    leaf ip-address {
      description
        "IP address of combination.";

      type string;
      default "";
    }
  }

  grouping network-measure-service {
    container dhcp {
      description
        "The basic service dhcp.";

      leaf enabled {
        description
          "Enable or disable dhcp.";

        type boolean;
        default "true";
      }
    }

    container dns {
      description
        "The basic service dns.";

      leaf enabled {
        description
          "Enable or disable basic service.";

        type boolean;
        default "true";
      }

      list monitor-server {
        key "server-ip";

        leaf server-ip {
          description
            "The ip of server.";

          type union {
            type ntos-inet:ipv4-filter-invalid-address;
            type ntos-inet:ipv6-address;
          }
        }

        leaf ping-enabled {
          description
            "Enabled or disabled the dns active detection function.";

          type boolean;
          default "true";
        }
      }

      leaf monitor-threshold {
        description
          "Consider the server unavailable when it exceeds the threshold.";

        type uint32 {
          range "1..2000";
          ntos-ext:nc-cli-shortdesc "<1-2000>";
        }
        default 1000;
        units "millisecond";
      }
    }

    container nat {
      description
        "The basic service nat.";

      leaf enabled {
        description
          "Enable or disable nat.";

        type boolean;
        default "true";
      }
    }

    container ipsec {
      description
        "The basic service ipsec.";

      leaf enabled {
        description
          "Enable or disable ipsec.";

        type boolean;
        default "true";
      }
    }

    container sslvpn {
      description
        "The basic service sslvpn.";

      leaf enabled {
        description
          "Enable or disable sslvpn.";

        type boolean;
        default "true";
      }
    }
  }

  grouping network-measure-warning-config {
    container link {
      description
        "The warning config about link.";

      container high-load {
        description
          "The warning config about high-load link.";

        leaf enabled {
          description
            "Enable or disable the warning config about high-load link.";
          type boolean;
          default "true";
        }

        leaf duration {
          description
            "The duration time about high-load link.";
          type uint32 {
            range "30..3600";
          }
          default 120;
        }

        leaf threshold {
          description
            "The threshold time about high-load link.";
          type uint32 {
            range "1..100";
          }
          default 95;
        }
      }

      container suspect-disconnected {
        description
          "The link warning config about suspect-disconnected.";

        leaf enabled {
          description
            "Enable or disable the suspect-disconnected.";
          type boolean;
          default "true";
        }

        leaf duration {
          description
            "The duration time about suspect-disconnected.";
          type uint32 {
            range "30..3600";
          }
          default 300;
        }

        leaf threshold {
          description
            "The threshold time about suspect-disconnected.";
          type uint32 {
            range "1..100000000";
          }
          default 10000;
        }
      }

      container change-to-down {
        description
          "The link warning config about change-to-down.";

        leaf enabled {
          description
            "Enable or disable the change-to-down.";
          type boolean;
          default "true";
        }
      }
    }

    container app {
      description
        "The warning config about the critical app.";

      container change-to-poor {
        description
          "The app warning config about change-to-poor.";

        leaf enabled {
          description
            "Enable or disable the change-to-poor.";
          type boolean;
          default "true";
        }
      }
    }

    container user {
      description
        "The warning config about the critical user.";

      container wan-detect-failed {
        description
          "The user warning config about lan-detect-failed.";

        leaf enabled {
          description
            "Enable or disable the wan-detect-failed.";
          type boolean;
          default "true";
        }
      }

      container lan-detect-failed {
        description
          "The user warning config about lan-detect-failed.";

        leaf enabled {
          description
            "Enable or disable the lan-detect-failed.";
          type boolean;
          default "true";
        }
      }

      container change-to-poor {
        description
          "The app warning config about change-to-poor.";

        leaf enabled {
          description
            "Enable or disable the change-to-poor.";
          type boolean;
          default "true";
        }
      }
    }

    container dhcp-server {
      description
        "The warning config about dhcp.";

      container ip-conflict {
        description
          "The dhcp warning config about ip-conflict.";

        leaf enabled {
          description
            "Enable or disable the ip-conflict.";
          type boolean;
          default "true";
        }
      }

      container high-load {
        description
          "The dhcp warning config about high-load.";

        leaf enabled {
          description
            "Enable or disable high-load.";
          type boolean;
          default "true";
        }

        leaf threshold {
          description
            "The threshold time about high-load link.";
          type uint32 {
            range "1..100";
          }
          default 95;
        }
      }
    }

    container dns {
      description
        "The warning config about dns.";

      container unuseable {
        description
          "The dns warning config about unuseable.";

        leaf enabled {
          description
            "Enable or disable the unuseable.";
          type boolean;
          default "true";
        }
      }
    }

    container nat {
      description
        "The warning config about nat.";

      container hit-fail {
        description
          "The nat warning config about unuseable.";

        leaf enabled {
          description
            "Enable or disable the unuseable.";
          type boolean;
          default "false";
        }
      }

      container hit-miss {
        description
          "The nat warning config about unuseable.";

        leaf enabled {
          description
            "Enable or disable the unuseable.";
          type boolean;
          default "false";
        }
      }
    }

    container ipsec {
      description
        "The warning config about ipsec.";

      container disconnected {
        description
          "The ipsec warning config about disconnected.";

        leaf enabled {
          description
            "Enable or disable the disconnected.";
          type boolean;
          default "true";
        }

        leaf duration {
          description
            "The duration time about ipsec disconnected.";
          type uint32 {
            range "30..3600";
          }
          default 120;
        }
      }
    }

    container sslvpn {
      description
        "The warning config about sslvpn.";

      container lost {
        description
          "The sslvpn warning config about lost.";

        leaf enabled {
          description
            "Enable or disable the lost.";
          type boolean;
          default "true";
        }

        leaf threshold {
          description
            "The threshold time about sslvpn lost.";
          type uint32 {
            range "1..100";
          }
          default 10;
        }
      }

      container license {
        description
          "The sslvpn warning config about license.";

        leaf enabled {
          description
            "Enable or disable about sslvpn license.";
          type boolean;
          default "true";
        }

        leaf threshold {
          description
            "The threshold time about sslvpn license.";
          type uint32 {
            range "1..100";
          }
          default 20;
        }
      }
    }
  }

  grouping network-measure-config {
    container network-measure {
      leaf enabled {
        description
          "Enabled traffic measure.";

        type boolean;
        default "true";
      }

      leaf message-send-enabled {
        description
          "Enabled or disable message send";

        type boolean;
        default "false";
      }

      list app {
        key "name";
        ordered-by user;

        uses network-measure-app;
      }

      list user {
        key "source-network user-name";
        ordered-by user;

        uses network-measure-user;
      }

      container service {
        uses network-measure-service;
      }

      container warning-config {
        uses network-measure-warning-config;
      }

      list class {
        description
          "Combination class.";

        key "name";

        leaf name {
          description
            "The name of combination class.";

          type ntos-types:ntos-obj-name-type;
        }

        leaf description {
          description
            "The description of combination class.";

          type ntos-types:ntos-obj-description-type {
            length "1..255";
          }
        }

        leaf enabled {
          description
            "Enable or disable combination class.";

          type boolean;
          default "true";
        }

        leaf priority {
          description
            "The priority of class";

          type uint16;
        }

        list combination {
          description
            "Combination configure.";

          key "name";
          ordered-by user;
          uses network-measure-combination;
        }
      }

      container flow-limit {
        leaf enabled {
          description
            "Enable or disable flow-limit.";
          type boolean;
          default "true";
        }
        leaf up {
          type uint32 {
            range "1..100";
            ntos-ext:nc-cli-shortdesc "<1-100>";
          }
          default "80";
        }
        leaf down {
          type uint32 {
            range "1..100";
            ntos-ext:nc-cli-shortdesc "<1-100>";
          }
          default "80";
        }
      }
    }
  }

  grouping network-measure-time {
    leaf year {
      description
        "Year.";

      type uint32;
      ntos-ext:nc-cli-no-name;
    }
    leaf mon {
      description
        "Month.";

      type uint32;
      ntos-ext:nc-cli-no-name;
    }
    leaf day {
      description
        "Day.";

      type uint32;
      ntos-ext:nc-cli-no-name;
    }
    leaf hour {
      description
        "Hour.";

      type uint32;
      ntos-ext:nc-cli-no-name;
    }
    leaf min {
      description
        "Minute.";

      type uint32;
      ntos-ext:nc-cli-no-name;
    }
    leaf second {
      description
        "Second.";

      type uint32;
      ntos-ext:nc-cli-no-name;
    }
  }

  grouping filter-time {
    container start-time {
      description
        "The start time of the historical data to be searched.";
      uses network-measure-time;
    }

    container end-time {
      description
        "The end time of the historical data to be searched.";
      uses network-measure-time;
    }
  }

  grouping network-measure-interface-detect-task {
    container detect-task {
      list interface {
        key "name";

        leaf name {
          description
            "The name of interface.";

          type union {
            type ntos-types:ifname;
            type ntos-types:ntos-obj-name-type {
              length "1..127";
            }
          }
        }

        list task {
          description
            "Detect task info.";

          key "name";

          leaf name {
            description
              "Task name.";

            type string {
              length "0..128";
            }
          }

          leaf target {
            description
              "Detect target, suck as URL or IP address.";

            type string {
              length "0..128";
            }
          }

          leaf method {
            description
              "How to detect.";

            type string {
              length "1..16";
            }
            default "ping";
          }

          container status {
            description
              "Task status.";

            leaf lost {
              description
                "Detect lost.";

              type uint16 {
                range "0..100";
              }
            }

            leaf latency {
              description
                "Detect latency.";

              type uint32;
            }

            leaf jitter {
              description
                "Detect jitter.";

              type uint32;
            }
          }
        }
      }
    }
  }

  grouping position-element {
    leaf start {
      description
        "The start index of data.";
      type uint32;
    }

    leaf end {
      description
        "The end index of data.";
      type uint32;
      must "current() > ../start" {
        error-message "The End value must be larger than the start value.";
      }
    }
  }

  rpc show-obj-list {
    description
      "The names of objects supported by network measure.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf obj {
        description
          "The type of the object.";

        type bits {
          bit app;
          bit ip;
          bit subnet;
          bit user-name;
          bit dns;
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure obj-list";
  }

  rpc get-app-standard {
    description
      "Get specific app performance standard value.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf app {
        description
          "The name of specific app.";

        type ntos-types:ntos-obj-name-type;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure app-standard";
  }

  rpc get-standard-list {
    description
      "Get app or user performance standard value list.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf obj {
        description
          "The type of the object.";

        type bits {
          bit app;
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure standard-list";
  }

  rpc show-warn-config {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure warn-config";
  }

  rpc show-interface-list {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf app-name {
        description
          "The app-name of service.";
        type ntos-types:ntos-obj-name-type;
      }
      leaf source-network {
        description
          "The source network of specify user.";

        type ntos-types:ntos-obj-name-type;
      }
      leaf user-name {
        description
          "The user-name of specify user.";
        type user-group-path;
      }

      uses filter-time;
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure interface-list";
  }


  rpc show-dashboard-web {
    description
      "Show network measure dashboard info.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      container warning {
        description
          "the warning message about network measure module.";

        uses position-element;
      }

      container app {
        description
          "the critical app data about network measure module.";

        uses position-element;
      }

      container user {
        description
          "the critical user data about network measure module.";

        uses position-element;
      }

      container service {
        description
          "the basic service data about network measure module.";

        uses position-element;
      }

      leaf json {
        description
          "Output in JSON format.";

        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure dashboard-web";
  }

  rpc show-dashboard {
    description
      "Show network measure dashboard info.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf content {
        description
          "The data type.";

        type bits {
          bit warning {
            description "The number of unprocessed warning";
          }
          bit message {
            description "The number of unread message";
          }
          bit app {
            description "The current status information for all applications.";
          }
          bit user {
            description "The current status information for all users.";
          }
          bit service {
            description "The current status information for all services.";
          }
          bit security-record {
            description "The security record information for all services.";
          }
          bit combination-config {
            description "The configure for all combinations.";
          }
          bit combination-state {
            description "The current state information for all combinations.";
          }
        }
      }

      uses position-element;

      leaf json {
        description
          "Output in JSON format.";

        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure dashboard";
  }

  rpc show-warning {
    description
      "Show warning information for network measure.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      container filter {
        description
          "Search filter.";

        leaf status {
          type enumeration {
            enum unprocessed;
            enum processed;
            enum ignored;
          }
          default "unprocessed";
        }

        list service {
          leaf name {
            description
              "The name of critical service.";

            type user-group-path;
          }
        }

        uses filter-time;
      }

      uses position-element;

      leaf json {
        description
          "Output in JSON format.";

        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure warning";
  }

  rpc show-message {
    description
      "Show network measure message.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      container filter {
        description
          "Search filter.";

        leaf status {
          type enumeration {
            enum read;
            enum unread;
          }
          default "unread";
        }

        list service {
          leaf name {
            description
              "The name of service.";

            type user-group-path;
          }
        }

        uses filter-time;
      }

      uses position-element;

      leaf json {
        description
          "Output in JSON format.";

        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure message";
  }

  rpc show-app {
    description
      "Show network measure app info.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf name {
        description
          "The name of app.";

        type ntos-types:ntos-obj-name-type;
      }

      leaf content {
        description
          "The data type.";

        type bits {
          bit warning {
            description "The number of unprocessed warning for the application";
          }
          bit message {
            description "The number of unread message for the application";
          }
          bit current {
            description "The current status information for the application.";
          }
          bit performance-config {
            description "Show performance configure.";
          }
          bit data-source {
            description "Source of the critical app.";
          }
          bit user-status {
            description "the app data include the critical user.";
          }
          bit history {
            description "Show history data for the application.";
          }
          bit interface-state {
            description "Show link data for the application.";
          }
        }
      }

      container data-source {
        description
          "Show data use specific data source.";

        leaf source-network {
          description
            "The source network of specify user.";

          type ntos-types:ntos-obj-name-type;
        }

        leaf user-name {
          description
            "The user-name of specify user.";

          type user-group-path;
        }
      }

      list interface {
        key "name";
        leaf name {
          description
            "Specify interface.";

          type ntos-types:ifname;
        }
      }

      uses position-element;
      uses filter-time;

      leaf json {
        description
          "Output in JSON format.";

        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure app";
  }

  rpc show-user {
    description
      "Show network measure user info.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf source-network {
        description
          "The source network of specify user.";

        type ntos-types:ntos-obj-name-type;
      }

      leaf user-name {
        description
          "The user-name of specify user.";

        type user-group-path;
      }

      leaf content {
        description
          "The data type.";

        type bits {
          bit warning {
            description "The number of unprocessed warning for the user";
          }
          bit message {
            description "The number of unread message for the user";
          }
          bit current {
            description "The current status information for the user.";
          }
          bit history {
            description "Show history data for the user.";
          }
          bit performance-config {
            description "Show performance configure.";
          }
          bit wan-target {
            description "Show wan detection target.";
          }
          bit app-data {
            description "Show the critical app data associate with user.";
          }
        }
      }

      leaf start {
        description
          "The start index of data. Use for show critical app data.";

        type uint32;
      }

      leaf end {
        description
          "The end index of data. Use for show critical app data.";

        type uint32;
      }

      list interface {
        key "name";
        leaf name {
          description
            "Specify interface.";
          type ntos-types:ifname;
        }
      }

      uses filter-time;

      leaf json {
        description
          "Output in JSON format.";

        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure user";
  }

  rpc show-combination {
    description
      "Show network measure combination info.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf class {
        description
          "The class of combination.";

        type ntos-types:ntos-obj-name-type;
      }

      leaf name {
        description
          "The name of combination.";

        type ntos-types:ntos-obj-name-type;
      }

      leaf content {
        description
          "The data type.";

        type bits {
          bit current {
            description "The current status information for the combination.";
          }
          bit abnormal {
            description "The number of unprocessed warning for the combination";
          }
          bit interface-state {
            description "Show link data for the combination.";
          }
        }
      }

      uses position-element;
      uses filter-time;

      leaf json {
        description
          "Output in JSON format.";

        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure combination";
  }

  rpc show-service {
    description
      "Show network measure service info.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      container sslvpn {
        description
          "SSLVPN info.";

        leaf current {
          description
            "Show current state.";

          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-group "service-data";
        }

        container history {
          description
            "History data.";

          uses filter-time;
          ntos-ext:nc-cli-group "service-data";
        }

        ntos-ext:nc-cli-group "service";
      }

      container ipsec {
        description
          "IPSEC-VPN info.";

        leaf current {
          description
            "Show current state.";

          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-group "service-data";
        }

        container history {
          description
            "History data.";

          leaf tunnel {
            description
              "Specify Tunnel.";

            type ntos-types:ntos-obj-name-type;
          }
          uses filter-time;
          ntos-ext:nc-cli-group "service-data";
        }

        container disconnect {
          leaf tunnel {
            type ntos-types:ntos-obj-name-type;
          }

          uses position-element;
          uses filter-time;
          ntos-ext:nc-cli-group "service-data";
        }

        ntos-ext:nc-cli-group "service";
      }

      container nat {
        description
          "NAT info.";

        leaf current {
          description
            "Show current state.";

          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-group "service-data";
        }

        container history {
          description
            "History data.";

          leaf policy {
            description
              "Specify nat name.";

            type ntos-types:ntos-obj-name-type;
          }
          uses filter-time;
          ntos-ext:nc-cli-group "service-data";
        }

        ntos-ext:nc-cli-group "service";
      }

      container dns {
        description
          "DNS info.";

        container current {
          description
            "Show current state.";

          uses position-element;

          leaf all {
            description
              "Show all dns list.";
            type empty;
          }

          ntos-ext:nc-cli-group "service-data";
        }

        container history {
          description
            "History data.";

          leaf server-address {
            description
              "dns ip-address.";

            type union {
              type ntos-inet:ipv4-filter-invalid-address;
              type ntos-inet:ipv6-address;
            }
          }

          leaf content {
            description
              "The data type.";

            type bits {
              bit request-data;
              bit latency;
            }
          }

          uses filter-time;
          ntos-ext:nc-cli-group "service-data";
        }

        ntos-ext:nc-cli-group "service";
      }

      container dhcp-server {
        description
          "Dhcp server info.";

        leaf current {
          description
            "Show current state.";

          type union {
            type empty;
            type ntos-types:ifname;
          }
          ntos-ext:nc-cli-group "service-data";
        }

        container history {
          description
            "History data.";

          leaf interface {
            description
              "Specify interface.";

            type ntos-types:ifname;
          }
          uses filter-time;
          ntos-ext:nc-cli-group "service-data";
        }

        container conflict {
          description
            "Conflict information.";

          leaf interface {
            description
              "Specify interface.";

            type ntos-types:ifname;
          }

          uses position-element;

          uses filter-time;
          ntos-ext:nc-cli-group "service-data";
        }

        ntos-ext:nc-cli-group "service";
      }

      leaf json {
        description
          "Output in JSON format.";

        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure service";
  }

  rpc operate-warning {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf status {
        type enumeration {
          enum unprocessed;
          enum processed;
          enum ignored;
          enum deleted;
        }
      }

      list list {
        key "id";

        leaf id {
          type ntos-types:ntos-obj-name-type;
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-cmd "network-measure operate-warning";
  }

  rpc operate-message {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf status {
        type enumeration {
          enum unread;
          enum read;
          enum deleted;
        }
      }

      list list {
        key "id";

        leaf id {
          type ntos-types:ntos-obj-name-type;
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-cmd "network-measure operate-message";
  }

  rpc get-policy-name {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf policy-id {
        description
          "The id of policy.";

        type string;
      }
    }

    output {
      uses cmd-output-buffer;
    }
  }

  rpc dump-status {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf content {
        description
          "The data type.";

        type bits {
          bit database;
          bit master;
          bit collector;
          bit analyzer;
          bit policy;
          bit combination;
          bit playbook;
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "network-measure dump";
  }

  rpc write-assurance-log {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf name {
        description
          "The name of app.";

        type user-group-path;
      }

      leaf operate-obj {
        description
          "The name of operate-obj.";

        type ntos-types:ntos-obj-name-type;
      }

      leaf operate-event {
        description
          "The operate event message.";

        type ntos-types:ntos-obj-name-type;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-cmd "network-measure write-assurance-log";
  }

  rpc read-assurance-log {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      container filter {
        description
          "Search filter.";

        leaf name {
          description
            "Filter name.";

          type user-group-path;
        }

        leaf match-accurate {
          description
            "Match accurate.";

          type boolean;
          default "false";

        }

        uses filter-time;
      }

      uses position-element;

      leaf json {
        description
          "Output in JSON format.";

        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "network-measure read-assurance-log";
  }

  rpc export-assurance-log {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      container filter {
        description
          "Search filter.";

        leaf name {
          description
            "Filter name.";

          type user-group-path;
        }

        leaf match-accurate {
          description
            "Match accurate.";

          type boolean;
          default "false";

        }

        uses filter-time;
      }

      leaf token {
        description
          "Unique value.";

        type string;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-cmd "network-measure export-assurance-log";
  }

  rpc inquire-assurance-status {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf token {
        description
          "Unique value.";

        type string;
      }

    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "network-measure inquire-assurance-status";
  }

  rpc app-abnormal-history {
    input {
      leaf vrf {
        description
          "Specify the VRF.";
        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf name {
        description
          "The name of app.";
        type ntos-types:ntos-obj-name-type;
      }

      uses position-element;

      container filter {
        description
          "Search filter.";

        leaf ip-address {
          description
            "abnormal node ip-address.";
          type union {
            type ntos-inet:ipv4-filter-invalid-address;
            type ntos-inet:ipv6-address;
          }
        }

        leaf user-name {
          description
            "abnormal node user-name.";
          type user-group-path;
        }

        leaf in-interface {
          description
            "in interface.";
          type ntos-types:ifname;
        }

        leaf out-interface {
          description
            "out interface.";
          type ntos-types:ifname;
        }

        uses filter-time;
      }

      leaf json {
        description
          "Output in JSON format.";

        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "network-measure app-abnormal-history";
  }

  rpc network-measure-sign-update {
    description
      "Update network measure signature.";

    input {
      leaf file-path {
        description
          "The path of the file.";
        type string;
      }
    }

    output {
      leaf id {
        description
          "Id of upgrade.";
        type uint32;
      }
      leaf progress {
        description
          "Progress of upgrade.";
        type uint32;
      }
      leaf errnum {
        description
          "Error code.";
        type uint32;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "network-measure sig-update";
  }

  rpc network-measure-update-state {
    description
      "The state of update signature.";

    output {
      leaf err-num {
        type uint32;
        description
          "The error num.";
      }
    }
  }

  rpc network-measure-combination-is-active {
    description
      "The state of update signature.";

    input {
      leaf interface {
        type ntos-types:ifname;
      }
    }

    output {
      leaf is-active {
        type boolean;
        description
          "The active status.";
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    uses network-measure-config;
  }

  augment "/ntos:state/ntos:vrf" {
    container network-measure {
      description
        "Network measure state.";

      uses network-measure-interface-detect-task;
    }
  }

}
