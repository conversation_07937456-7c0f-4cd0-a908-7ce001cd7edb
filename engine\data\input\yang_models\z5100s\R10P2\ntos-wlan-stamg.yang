module ntos-wlan-stamg {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:wlan-stamg";
  prefix ntos-wlan-stamg;

  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS WLAN stamg module.";

  revision 2024-07-23 {
    description
      "Initial version.";
    reference "";
  }

  grouping enabled-leaf {
    leaf enabled {
      type boolean;
      default "false";
    }
  }

  grouping radio-balance {
    container inter-radio-balance {
      container flow-balance {
        description
          "STA flow balance.";
        container dual-band {
          leaf enable-load {
            type uint16 {
              range "1..1000";
            }
            default "10";
          }

          leaf threshold {
            type uint16 {
              range "1..1000";
            }
            default "10";
          }
        }

        container same-band {
          leaf enable-load {
            type uint16 {
              range "1..1000";
            }
            default "5";
          }

          leaf threshold {
            type uint16 {
              range "1..1000";
            }
            default "5";
          }
        }

        uses enabled-leaf;
      }
      container num-balance {
        description
          "STA number balance.";
        container dual-band {
          leaf enable-load {
            type uint8 {
              range "1..20";
            }
            default "8";
          }

          leaf threshold {
            type uint8 {
              range "1..20";
            }
            default "8";
          }
        }

        container same-band {
          leaf enable-load {
            type uint8 {
              range "1..20";
            }
            default "2";
          }

          leaf threshold {
            type uint8 {
              range "1..20";
            }
            default "2";
          }
        }

        uses enabled-leaf;
      }
    }
  }

  grouping stamg-ac-config {
    leaf sta-limit {
      description
        "Set the limit STA attached to this AC.";
      type uint32;
    }

    container balance-group {
      container flow-balance-group{
        leaf base {
          description
            "Flow balance group base value.";
          type uint8 {
            range "1..100";
          }
          default "10";
        }
      }
    }

    list flow-balance-group {
      key "name";
      description
        "Flow balance group.";

      leaf name {
        description
          "Flow balance group name.";
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }

      leaf enabled {
        description
          "whether enable flow balance group.";
        type boolean;
        default "true";
      }

      leaf threshold {
        description
          "Flow balance group base value.";
        type uint16 {
          range "0..500";
        }
        default "5";
      }

      leaf flow {
        description
          "Set flow threshold.";
        type uint16 {
          range "0..1000";
        }
        default "5";
      }

      leaf radio-flow {
        description
          "The flow balance group radio flow value keyword.";
        type empty;
      }

      list ap {
        key "ap-name";

        leaf ap-name {
          type ntos-types:ntos-obj-name-type;
        }
      }
    }

    list num-balance-group {
      key "name";
      description
        "Num balance group.";

      leaf name {
        description
          "Num balance group name.";
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }

      leaf enabled {
        description
          "whether enable flow balance group.";
        type boolean;
        default "true";
      }

      leaf threshold {
        description
          "Set number threshold.";
        type uint16 {
          range "0..10";
        }
        default "3";
      }

      leaf num {
        description
          "Set number difference.";
        type uint16 {
          range "0..20";
        }
        default "3";
      }

      leaf mode {
        description
          "Num balance group mode.";
        type enumeration {
          enum radio-mode;
          enum ap-mode;
        }
        default "ap-mode";
      }

      list ap {
        key "ap-name";

        leaf ap-name {
          type ntos-types:ntos-obj-name-type;
        }
      }
    }

    container sta-balance {
      container num-limit {
        uses enabled-leaf;
      }
    }

    container sta-blacklist {
      uses enabled-leaf;

      leaf lifetime {
        description
          "Lifetime config.";
        type uint16 {
          range "60..1200";
        }
      }

      leaf detect-time {
        description
          "Suspect STA detect time config.";
        type uint8 {
          range "5..60";
        }
      }

      leaf fail-limit {
        description
          "Sta fail limit config.";
        type uint8 {
          range "1..100";
        }
      }
    }

    container sta-logging {
      leaf rate-limit {
        type uint16 {
          range "0..10000";
        }
      }
    }

    container black-white-list {
      container blacklist {
        leaf enabled {
          description
            "Whether enable blacklist.";
          type boolean;
          default "true";
        }
        list mac {
          key "address";

          leaf address {
            description
              "Sta mac address.";
            type ntos-if:mac-address;
          }

          leaf mnemonic {
            description
              "Set a mnemonic for this STA";
            type ntos-types:ntos-obj-name-type;
          }
        }

        list vendor {
          key "oui";

          leaf oui {
            description
              "Sta vender.";
            type string {
              pattern '[0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){2}';
            }
          }

          leaf mnemonic {
            description
              "Set a mnemonic for this STA";
            type ntos-types:ntos-obj-name-type;
          }
        }
      }

      container whitelist {
        leaf enabled {
          description
            "Whether enable whitelist.";
          type boolean;
          default "false";
        }
        list mac {
          key "address";

          leaf address {
            description
              "Sta mac address.";
            type ntos-if:mac-address;
          }

          leaf mnemonic {
            description
              "Set a mnemonic for this STA";
            type ntos-types:ntos-obj-name-type;
          }
        }

        list vendor {
          key "oui";

          leaf oui {
            description
              "Sta vender.";
            type string {
              pattern '[0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){2}';
            }
          }

          leaf mnemonic {
            description
              "Set a mnemonic for this STA";
            type ntos-types:ntos-obj-name-type;
          }
        }
      }
    }
  }

  grouping stamg-ap-config {
    uses radio-balance;

    leaf sta-idle-timeout {
      type uint32 {
        range "60..86400";
      }
    }

    leaf sta-limit {
      description
        "Set the limit STA attached to this AP.";
      type uint16 {
        range "1..4096";
      }
    }

    container sta-limit-radio {
      leaf radio-id {
        description
          "Radio id";
        type uint8 {
          range "1..96";
        }
        ntos-ext:nc-cli-no-name;
      }

      leaf limit {
        description
          "Set the limit STA attached to this AP.";
        type uint16 {
          range "1..4096";
        }
        when "../radio-id";
      }
    }
  }

  grouping stamg-bw-config {
    container blacklist {
      leaf enabled {
        description
          "Whether enable blacklist.";
        type boolean;
        default "true";
      }
      list mac {
        key "address";

        leaf address {
          description
            "Sta mac address.";
          type ntos-if:mac-address;
        }

        leaf mnemonic {
          description
            "Set a mnemonic for this STA";
          type ntos-types:ntos-obj-name-type;
        }
      }

      list vendor {
        key "oui";

        leaf oui {
          description
            "Sta vender.";
          type string {
            pattern '[0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){2}';
          }
        }

        leaf mnemonic {
          description
            "Set a mnemonic for this STA";
          type ntos-types:ntos-obj-name-type;
        }
      }
    }

    container whitelist {
      leaf enabled {
        description
          "Whether enable whitelist.";
        type boolean;
        default "false";
      }
      list mac {
        key "address";

        leaf address {
          description
            "Sta mac address.";
          type ntos-if:mac-address;
        }

        leaf mnemonic {
          description
            "Set a mnemonic for this STA";
          type ntos-types:ntos-obj-name-type;
        }
      }

      list vendor {
        key "oui";

        leaf oui {
          description
            "Sta vender.";
          type string {
            pattern '[0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){2}';
          }
        }

        leaf mnemonic {
          description
            "Set a mnemonic for this STA";
          type ntos-types:ntos-obj-name-type;
        }
      }
    }
  }

  grouping stamg-wlan-config {
    container sta-limit {
      description
        "Set the limit STA attached to this WLAN id.";
      leaf number {
        description
          "Set the limit STA attached to this WLAN id";
        type uint16 {
          range "1..8192";
        }
        ntos-ext:nc-cli-no-name;
      }

      leaf per-ap {
        description
          "Set the limit of STA attached to this WLAN id on each AP.";
        type uint16 {
          range "1..4096";
        }
      }
    }
  }

  grouping stamg-apg-config {
    uses radio-balance;

    leaf sta-idle-timeout {
      type uint32 {
        range "60..86400";
      }
    }

    leaf sta-limit {
      description
        "Set the limit STA attached to this AP's.";
      type uint16 {
        range "1..1536";
      }
    }

    container sta-limit-radio {
      leaf radio {
        description
          "Radio id";
        type uint8 {
          range "1..96";
        }
        ntos-ext:nc-cli-no-name;
      }

      leaf limit {
        description
          "Set the limit STA attached to this AP.";
        type uint16 {
          range "1..4096";
        }
        when "../radio";
      }
    }
  }

  grouping show-ac-client {
    leaf sta-num {
      type uint32;
    }
    list client {
      key "mac-address";
      leaf mac-address {
        type string;
      }
      leaf ipv4-address {
        type string;
      }
      leaf ipv6-address {
        type string;
      }
      leaf ap-name {
        type string;
      }
      leaf radio {
        type uint8;
      }
      leaf ssid {
        type string;
      }
      leaf rssi {
        type int8;
      }
      leaf wlan {
        type uint32;
      }
      leaf vlan {
        type uint32;
      }
      leaf status {
        type string;
      }
      leaf asso-auth {
        type string;
      }
      leaf net-auth {
        type string;
      }
      leaf v4-up-flow {
        type string;
      }
      leaf v4-down-flow {
        type string;
      }
      leaf v6-up-flow {
        type string;
      }
      leaf v6-down-flow {
        type string;
      }
      leaf up-time {
        type string;
      }
      leaf client-type {
        type string;
      }
      leaf user-name {
        type string;
      }
      leaf ac-description {
        type string;
      }
    }
    leaf count {
      type uint32;
    }
  }

  grouping show-bw-list {
    list bw-list {
      key "address";

      leaf address {
        type string;
      }
      leaf address-type {
        type string;
      }
      leaf mnemonic {
        type string;
      }
    }
  }

  grouping balance-config {
    leaf group {
      type string;
    }
    leaf state {
      type string;
    }
    leaf validity {
      type string;
    }
    leaf threshold {
      type string;
    }
    leaf mode {
      type string;
    }
    leaf base {
      type uint16;
    }
    list ap {
      key "name";

      leaf name {
        type string;
      }
    }
  }

  grouping show-balance-group {
    leaf total-group-num {
      type uint16;
    }
    list num-balance-group {
      key "group";

      uses balance-config;
    }
    list flow-balance-group {
      key "group";

      uses balance-config;
    }
  }

  grouping stamg-log-type {
    leaf trace {
      type empty;
    }
    leaf error {
      type empty;
    }
    leaf log {
      type empty;
    }
    leaf event {
      type empty;
    }
    leaf all {
      type empty;
    }
    leaf gr {
      type empty;
    }
    leaf metric {
      type empty;
    }
    leaf redis {
      type empty;
    }
    leaf ript {
      type empty;
    }
    leaf vac {
      type empty;
    }
    leaf sta {
      type enumeration {
        enum up;
        enum down;
      }
    }
    leaf pkt {
      type enumeration {
        enum attr;
        enum hb;
        enum msg;
        enum other;
      }
    }
    leaf db {
      type enumeration {
        enum err;
        enum log;
        enum notify;
      }
    }
    leaf msg {
      type enumeration {
        enum err;
        enum log;
      }
    }
    leaf actrl {
      type enumeration {
        enum assoc-ctrl;
        enum balance;
        enum blacklist;
        enum limit;
      }
    }
    container libs {
      leaf gipc {
        type enumeration {
          enum all;
          enum event;
          enum info;
          enum packet;
          enum thread;
          enum err;
          enum debug;
          enum warning;
          enum skb;
        }
      }
      leaf wdb {
        type enumeration {
          enum all;
          enum info;
          enum pkt;
          enum err;
        }
      }
      container apmg {
        container apmg-lib {
          leaf all {
            type empty;
          }
          leaf error {
            type empty;
          }
          leaf info {
            type empty;
          }
          leaf gipc {
            type enumeration {
              enum all;
              enum event;
              enum info;
              enum packet;
              enum thread;
              enum err;
              enum debug;
              enum warning;
              enum skb;
            }
          }
          leaf db {
            type enumeration {
              enum all;
              enum error;
              enum info;
            }
          }
          container apc {
            leaf gipc {
              type enumeration {
                enum all;
                enum event;
                enum info;
                enum packet;
                enum thread;
                enum err;
                enum debug;
                enum warning;
                enum skb;
              }
            }
          }
        }
      }

      container redis {
        leaf lib {
          type enumeration {
            enum all;
            enum detail;
            enum error;
            enum event;
            enum payload;
            enum show;
          }
        }
      }
    }
  }

  grouping cmd-stamg {
    container stamg {
      container on {
        uses stamg-log-type;
      }
      container off {
        uses stamg-log-type;
      }
    }
  }

  grouping gipc-info {
    leaf ctrl {
      type empty;
    }

    leaf mem-stats {
      type empty;
    }

    leaf pkt-stats {
      type string;
    }

    leaf recv-skb {
      type string;
    }

    leaf send-skb {
      type string;
    }
  }

  grouping show-stamg {
    container stamg {
      leaf bw-import-result {
        type empty;
      }

      leaf memory {
        description
          "Show memory statistic.";
        type empty;
      }
      leaf prior-sta {
        description
          "Show prior sta.";
        type empty;
      }
      leaf sta-check {
        description
          "Show sta-check ap list.";
        type empty;
      }
      leaf vac {
        description
          "Show vac info.";
        type empty;
      }
      leaf redis {
        description
          "Show redis info.";
        type empty;
      }
      leaf static-table {
        description
          "Show static table.";
        type empty;
      }
      leaf batch-del {
        description
          "Show batch-del list.";
        type empty;
      }
      leaf add-sta-q {
        description
          "Show add sta queue.";
        type empty;
      }
      leaf cb {
        description
          "Control block.";
        type empty;
      }
      leaf global {
        description
          "Show global info.";
        type empty;
      }
      leaf aid-bitmap {
        description
          "Show aid bitmap.";
        type empty;
      }
      leaf offline-timer {
        description
          "Show sta in offline schedule.";
        type empty;
      }
      leaf debug-filter {
        description
          "Show debug mac filter.";
        type empty;
      }
      leaf syslog-freq {
        description
          "Show syslog rate-limit.";
        type empty;
      }
      leaf num-flow-balance {
        description
          "Show num and flow balance group config.";
        type empty;
      }
      leaf gr-status {
        description
          "Show graceful restart status.";
        type empty;
      }
      leaf refuse-statis {
        description
          "Show sta-limit refuse statistics.";
        type empty;
      }
      leaf capability {
        description
          "STA capability on AC.";
        type empty;
      }
      leaf limit-timeout {
        description
          "Show sta-limit and sta-idle-timeout config.";
        type enumeration {
          enum ac;
          enum wlan;
          enum apg;
          enum ap-cfg;
          enum state;
          enum dev;
        }
      }
      container map {
        description
          "Show map table.";
        choice map-choice {
          case ap {
            container ap {
              leaf name {
                type string;
                ntos-ext:nc-cli-no-name;
              }
              leaf radio {
                type uint8;
              }
            }
          }
          case home-ap {
            container home-ap {
              leaf name {
                type string;
                ntos-ext:nc-cli-no-name;
              }
              leaf radio {
                type uint8;
              }
            }
          }
          case wlan {
            leaf wlan {
              type uint16;
            }
          }
          case home-wlan {
            leaf home-wlan {
              type uint16;
            }
          }
        }
      }
      list sta {
        key "name";

        leaf name {
          type union {
            type ntos-if:mac-address;
            type enumeration {
              enum all;
              enum list;
              enum workmode;
              enum mlolist;
            }
          }
          ntos-ext:nc-cli-no-name;
        }
        leaf ip-source {
          type enumeration {
            enum ipv4;
            enum ipv6;
          }
        }
        container member {
          leaf all {
            type empty;
          }
          leaf private {
            type empty;
          }
          leaf public {
            type empty;
          }
          container spec {
            leaf sta-name {
              type string;
              ntos-ext:nc-cli-no-name;
            }
            leaf sta-length {
              type uint16;
              ntos-ext:nc-cli-no-name;
            }
          }
        }
        leaf ssn-key {
          type empty;
        }
        max-elements 1;
      }
      container gipc {
        description
          "Show the stamg gipc information.";
        uses gipc-info;

        leaf client {
          type empty;
        }

        leaf log-switch {
          type string;
        }
      }
      container number {
        description
          "STA number on device.";
        leaf summary {
          type union {
            type enumeration {
              enum ap;
              enum radio;
              enum wlan;
            }
            type empty;
          }
        }
        leaf wlan {
          type uint16 {
            range "1..4096";
          }
        }
        leaf device {
          type empty;
        }
        container apw {
          leaf mac-addr {
            type string;
            ntos-ext:nc-cli-no-name;
          }

          leaf wlan {
            type uint16 {
              range "1..4096";
            }
            ntos-ext:nc-cli-no-name;
          }
        }
        container ap {
          leaf name {
            type string;
            ntos-ext:nc-cli-no-name;
          }
          leaf radio {
            type uint8;
          }
        }
      }
    }
  }
}
