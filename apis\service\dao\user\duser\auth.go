package duser

import (
	"errors"
	"strconv"
	"time"

	"irisAdminApi/application/logging"
	"irisAdminApi/service/auth"
	"irisAdminApi/service/dao"

	"github.com/iris-contrib/middleware/jwt"
)

// Login 登录
func Login(id uint64) (string, error) {
	authDriver := auth.NewAuthDriver()
	defer authDriver.Close()
	authDriver.CleanUserExpireTokenCache(strconv.FormatUint(id, 10))
	if authDriver.IsUserTokenOver(strconv.FormatUint(id, 10)) {
		return "", errors.New("已达到同时登录设备上限")
	}
	// 使用分布唯一算法
	// node, err := snowflake.NewNode(1)
	// if err != nil {
	// 	return "", err
	// }
	token := jwt.NewTokenWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"id":  id,
		"exp": time.Now().Add(32 * time.Hour).Unix(),
		"iat": time.Now().Unix(),
	})
	var tokenString string
	tokenString, err := token.SignedString([]byte("HS2JDFKhu7Y1av7b"))
	if err != nil {
		logging.ErrorLogger.Errorf("signed string token err", err)
		return "", err
	}
	if err = authDriver.ToCache(tokenString, id); err != nil {
		logging.ErrorLogger.Errorf("to cache user token err", err)
		return "", err
	}
	if err = authDriver.SyncUserTokenCache(tokenString); err != nil {
		logging.ErrorLogger.Errorf("sync user token err", err)
		return "", err
	}
	if err = authDriver.UpdateUserTokenCacheExpire(tokenString); err != nil {
		logging.ErrorLogger.Errorf("update user token expire err", err)
		return "", err
	}
	err = dao.CreateOplog("认证", dao.ActionLogin, "", uint(id))
	if err != nil {
		logging.ErrorLogger.Errorf("login add oplog get err ", err)
		return "", err
	}

	return tokenString, nil
}

// Logout 退出
func Logout(token string) error {
	authDriver := auth.NewAuthDriver()
	defer authDriver.Close()
	id, err := authDriver.GetAuthId(token)
	if err != nil {
		logging.ErrorLogger.Errorf("logout get auth id err", err)
		return err
	}
	err = authDriver.DelUserTokenCache(token)
	if err != nil {
		logging.ErrorLogger.Errorf("logout del user token err", err)
		return err
	}
	err = dao.CreateOplog("认证", dao.ActionLogout, "", id)
	if err != nil {
		logging.ErrorLogger.Errorf("logout add oplog get err ", err)
		return err
	}
	return nil
}

// Expire 更新
func Expire(token string) error {
	authDriver := auth.NewAuthDriver()
	defer authDriver.Close()
	if err := authDriver.UpdateUserTokenCacheExpire(token); err != nil {
		logging.ErrorLogger.Errorf("update user token err", err)
		return err
	}
	return nil
}

// Check
func Check(token string) (*auth.Session, error) {
	authDriver := auth.NewAuthDriver()
	defer authDriver.Close()
	rsv2, err := authDriver.GetSessionV2(token)
	if err != nil {
		return nil, err
	}
	return rsv2, nil
}

// Clear 清除
func Clear(token string) error {
	authDriver := auth.NewAuthDriver()
	defer authDriver.Close()
	err := authDriver.CleanUserTokenCache(token)
	if err != nil {
		logging.ErrorLogger.Errorf("check user token err", err)
		return err
	}
	return nil
}
