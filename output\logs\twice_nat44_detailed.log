{"session_info": {"start_time": 1754662670.0083666, "end_time": 1754662670.0151992, "duration": 0.006832599639892578, "total_entries": 7, "total_metrics": 1}, "log_entries": [{"timestamp": "2025-08-08T22:17:50.011295", "level": "LogLevel.DEBUG", "category": "performance", "message": "性能度量 - policy_evaluation: 0.000s", "details": {"operation": "policy_evaluation", "start_time": 1754662670.011295, "end_time": 1754662670.011295, "duration": 0.0, "memory_usage": null, "cpu_usage": null, "details": {"policy": "TEST_POLICY"}}, "policy_name": null, "evaluation_time": null, "error_category": null}, {"timestamp": "2025-08-08T22:17:50.013246", "level": "LogLevel.DEBUG", "category": "evaluation_start", "message": "开始评估策略: TEST_LOGGING_POLICY", "details": {"type": "test", "complexity": "simple"}, "policy_name": "TEST_LOGGING_POLICY", "evaluation_time": null, "error_category": null}, {"timestamp": "2025-08-08T22:17:50.013246", "level": "LogLevel.DEBUG", "category": "dimension_score", "message": "维度评分 - vip_count: 85.0分", "details": {"dimension": "vip_count", "score": 85.0, "reason": "单个VIP配置", "dimension_details": {"vip_count": 1}}, "policy_name": "TEST_LOGGING_POLICY", "evaluation_time": null, "error_category": null}, {"timestamp": "2025-08-08T22:17:50.013246", "level": "LogLevel.DEBUG", "category": "pool_validation", "message": "IP池验证: ['**************']", "details": {"pool_names": ["**************"], "validation_result": {"valid": true, "format": "ip_address"}}, "policy_name": "TEST_LOGGING_POLICY", "evaluation_time": null, "error_category": null}, {"timestamp": "2025-08-08T22:17:50.013246", "level": "LogLevel.WARNING", "category": "warning", "message": "测试警告消息", "details": {"warning_type": "test"}, "policy_name": "TEST_LOGGING_POLICY", "evaluation_time": null, "error_category": null}, {"timestamp": "2025-08-08T22:17:50.014222", "level": "LogLevel.ERROR", "category": "error", "message": "错误: 测试错误处理", "details": {"error_message": "测试错误处理", "error_category": "evaluation_error", "exception_type": "ValueError", "exception_message": "测试错误", "traceback": "Traceback (most recent call last):\n  File \"E:\\code\\config-converter\\tests\\test_twice_nat44_enhanced.py\", line 222, in test_enhanced_logging\n    raise ValueError(\"测试错误\")\nValueError: 测试错误\n"}, "policy_name": "TEST_LOGGING_POLICY", "evaluation_time": null, "error_category": "ErrorCategory.EVALUATION_ERROR"}, {"timestamp": "2025-08-08T22:17:50.014222", "level": "LogLevel.INFO", "category": "evaluation_result", "message": "策略评估完成: TEST_LOGGING_POLICY", "details": {"total_score": 85.0, "should_use": true, "confidence": 0.85}, "policy_name": "TEST_LOGGING_POLICY", "evaluation_time": 0.15, "error_category": null}], "performance_metrics": [{"operation": "policy_evaluation", "start_time": 1754662670.011295, "end_time": 1754662670.011295, "duration": 0.0, "memory_usage": null, "cpu_usage": null, "details": {"policy": "TEST_POLICY"}}]}