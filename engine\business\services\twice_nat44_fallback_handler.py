"""
FortiGate twice-nat44回退处理服务

本模块实现了twice-nat44失败时的自动回退机制，确保系统稳定性和兼容性。
当twice-nat44转换失败或不适用时，自动回退到传统的SNAT/DNAT分离方案。

主要功能：
- 智能回退决策
- 传统NAT规则生成
- 错误恢复机制
- 兼容性保证
- 详细的回退日志

版本: 1.0
作者: FortiGate转换系统
创建时间: 2025-08-06
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from engine.utils.logger import log
from engine.utils.i18n import _


class FallbackReason(Enum):
    """回退原因枚举"""
    EVALUATION_FAILED = "evaluation_failed"
    GENERATION_FAILED = "generation_failed"
    VALIDATION_FAILED = "validation_failed"
    UNSUPPORTED_SCENARIO = "unsupported_scenario"
    USER_PREFERENCE = "user_preference"
    SYSTEM_ERROR = "system_error"


@dataclass
class FallbackContext:
    """
    回退上下文信息
    
    记录回退的详细信息，用于日志记录和问题诊断。
    """
    policy_name: str
    reason: FallbackReason
    error_message: Optional[str] = None
    original_exception: Optional[Exception] = None
    twice_nat44_attempted: bool = False
    evaluation_score: Optional[int] = None
    fallback_method: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "policy_name": self.policy_name,
            "reason": self.reason.value,
            "error_message": self.error_message,
            "twice_nat44_attempted": self.twice_nat44_attempted,
            "evaluation_score": self.evaluation_score,
            "fallback_method": self.fallback_method
        }


class TwiceNat44FallbackHandler:
    """
    twice-nat44回退处理器
    
    负责处理twice-nat44转换失败的情况，提供可靠的回退机制。
    """
    
    def __init__(self):
        """初始化回退处理器"""
        self.fallback_stats = {
            "total_fallbacks": 0,
            "fallback_reasons": {},
            "success_rate": 0.0
        }
        
        log(_("twice_nat44_fallback_handler.initialized"), "info")
    
    def handle_fallback(
        self, 
        policy: Dict[str, Any], 
        vips: Dict[str, Any], 
        ippools: Dict[str, Any],
        context: FallbackContext,
        processor
    ) -> List[Dict[str, Any]]:
        """
        处理twice-nat44回退
        
        Args:
            policy: FortiGate策略配置
            vips: VIP对象字典
            ippools: IP池字典
            context: 回退上下文
            processor: NAT处理器
            
        Returns:
            List[Dict[str, Any]]: 传统NAT规则列表
        """
        try:
            # 更新统计信息
            self._update_fallback_stats(context)
            
            # 记录回退开始
            log(_("twice_nat44_fallback_handler.fallback_started",
                 policy=context.policy_name,
                 reason=context.reason.value), "info")
            
            # 选择回退策略
            fallback_rules = self._execute_fallback_strategy(
                policy, vips, ippools, context, processor
            )
            
            # 验证回退结果
            if self._validate_fallback_rules(fallback_rules, context):
                log(_("twice_nat44_fallback_handler.fallback_successful",
                     policy=context.policy_name,
                     rule_count=len(fallback_rules)), "info")
                return fallback_rules
            else:
                raise ValueError("Fallback rules validation failed")
                
        except Exception as e:
            log(_("twice_nat44_fallback_handler.fallback_failed",
                 policy=context.policy_name,
                 error=str(e)), "error")
            
            # 最后的回退：返回空规则列表
            return []
    
    def _execute_fallback_strategy(
        self, 
        policy: Dict[str, Any], 
        vips: Dict[str, Any], 
        ippools: Dict[str, Any],
        context: FallbackContext,
        processor
    ) -> List[Dict[str, Any]]:
        """
        执行回退策略
        
        Args:
            policy: FortiGate策略配置
            vips: VIP对象字典
            ippools: IP池字典
            context: 回退上下文
            processor: NAT处理器
            
        Returns:
            List[Dict[str, Any]]: 生成的NAT规则列表
        """
        rules = []
        
        # 策略1: 生成传统的DNAT规则
        dnat_rules = self._generate_traditional_dnat_rules(policy, vips, context)
        rules.extend(dnat_rules)
        
        # 策略2: 生成传统的SNAT规则
        snat_rules = self._generate_traditional_snat_rules(policy, ippools, context, processor)
        rules.extend(snat_rules)
        
        # 记录使用的回退方法
        context.fallback_method = "traditional_separate_rules"
        
        return rules
    
    def _generate_traditional_dnat_rules(
        self, 
        policy: Dict[str, Any], 
        vips: Dict[str, Any], 
        context: FallbackContext
    ) -> List[Dict[str, Any]]:
        """
        生成传统的DNAT规则
        
        Args:
            policy: FortiGate策略配置
            vips: VIP对象字典
            context: 回退上下文
            
        Returns:
            List[Dict[str, Any]]: DNAT规则列表
        """
        dnat_rules = []
        
        try:
            dstaddr_list = policy.get("dstaddr", [])
            if isinstance(dstaddr_list, str):
                dstaddr_list = [dstaddr_list]
            
            for vip_name in dstaddr_list:
                if vip_name in vips:
                    vip_config = vips[vip_name]
                    
                    # 创建DNAT规则
                    dnat_rule = self._create_dnat_rule(policy, vip_config, context)
                    if dnat_rule:
                        dnat_rules.append(dnat_rule)
                        
                        log(_("twice_nat44_fallback_handler.dnat_rule_generated",
                             policy=context.policy_name,
                             vip=vip_name), "debug")
            
        except Exception as e:
            log(_("twice_nat44_fallback_handler.dnat_generation_failed",
                 policy=context.policy_name,
                 error=str(e)), "error")
        
        return dnat_rules
    
    def _generate_traditional_snat_rules(
        self, 
        policy: Dict[str, Any], 
        ippools: Dict[str, Any], 
        context: FallbackContext,
        processor
    ) -> List[Dict[str, Any]]:
        """
        生成传统的SNAT规则
        
        Args:
            policy: FortiGate策略配置
            ippools: IP池字典
            context: 回退上下文
            processor: NAT处理器
            
        Returns:
            List[Dict[str, Any]]: SNAT规则列表
        """
        snat_rules = []
        
        try:
            # 创建SNAT规则
            snat_rule = self._create_snat_rule(policy, ippools, context, processor)
            if snat_rule:
                snat_rules.append(snat_rule)
                
                log(_("twice_nat44_fallback_handler.snat_rule_generated",
                     policy=context.policy_name), "debug")
            
        except Exception as e:
            log(_("twice_nat44_fallback_handler.snat_generation_failed",
                 policy=context.policy_name,
                 error=str(e)), "error")
        
        return snat_rules
    
    def _create_dnat_rule(
        self, 
        policy: Dict[str, Any], 
        vip_config: Dict[str, Any], 
        context: FallbackContext
    ) -> Optional[Dict[str, Any]]:
        """
        创建单个DNAT规则
        
        Args:
            policy: FortiGate策略配置
            vip_config: VIP配置
            context: 回退上下文
            
        Returns:
            Optional[Dict[str, Any]]: DNAT规则配置
        """
        try:
            # 获取必要的配置信息
            extip = vip_config.get("extip")
            mappedip = vip_config.get("mappedip")
            
            if not extip or not mappedip:
                log(_("twice_nat44_fallback_handler.incomplete_vip_config",
                     vip=vip_config.get("name", "unknown")), "warning")
                return None
            
            # 构建DNAT规则
            dnat_rule = {
                "type": "static-dnat44",
                "name": f"{policy.get('intelligent_name') or policy.get('name', 'unknown')}_dnat_{vip_config.get('name', 'unknown')}",
                "description": f"Fallback DNAT rule for policy {policy.get('intelligent_name') or policy.get('name', 'unknown')}",
                "match": {
                    "dest-network": {
                        "name": vip_config.get("name", "unknown")
                    }
                },
                "translate-to": {
                    "ipv4-address": mappedip
                },
                "_metadata": {
                    "fallback_from": "twice_nat44",
                    "fallback_reason": context.reason.value,
                    "source_policy": policy.get("intelligent_name") or policy.get("name", "unknown"),
                    "source_vip": vip_config.get("name")
                }
            }
            
            # 添加端口映射（如果有）
            if "mappedport" in vip_config and vip_config["mappedport"]:
                try:
                    port = int(vip_config["mappedport"])
                    dnat_rule["translate-to"]["port"] = port
                except (ValueError, TypeError):
                    log(_("twice_nat44_fallback_handler.invalid_port",
                         vip=vip_config.get("name"),
                         port=vip_config["mappedport"]), "warning")
            
            return dnat_rule
            
        except Exception as e:
            log(_("twice_nat44_fallback_handler.dnat_rule_creation_failed",
                 vip=vip_config.get("name", "unknown"),
                 error=str(e)), "error")
            return None
    
    def _create_snat_rule(
        self, 
        policy: Dict[str, Any], 
        ippools: Dict[str, Any], 
        context: FallbackContext,
        processor
    ) -> Optional[Dict[str, Any]]:
        """
        创建单个SNAT规则
        
        Args:
            policy: FortiGate策略配置
            ippools: IP池字典
            context: 回退上下文
            processor: NAT处理器
            
        Returns:
            Optional[Dict[str, Any]]: SNAT规则配置
        """
        try:
            # 构建SNAT规则
            snat_rule = {
                "type": "static-snat44",
                "name": f"{policy.get('intelligent_name') or policy.get('name', 'unknown')}_snat",
                "description": f"Fallback SNAT rule for policy {policy.get('intelligent_name') or policy.get('name', 'unknown')}",
                "match": {
                    "source-zone": {
                        "name": policy.get("srcintf", ["any"])[0] if policy.get("srcintf") else "any"
                    }
                },
                "translate-to": {
                    "output-address": {},  # 使用出接口地址
                    "no-pat": policy.get("fixedport", "disable") == "enable",
                    "try-no-pat": policy.get("fixedport", "disable") != "enable"
                },
                "_metadata": {
                    "fallback_from": "twice_nat44",
                    "fallback_reason": context.reason.value,
                    "source_policy": policy.get("intelligent_name") or policy.get("name", "unknown")
                }
            }
            
            # 处理IP池配置（如果有）
            if policy.get("ippool", "disable") == "enable" and policy.get("poolname"):
                pool_name = policy.get("poolname")
                if pool_name in ippools:
                    snat_rule["translate-to"] = {
                        "pool-name": pool_name,
                        "no-pat": policy.get("fixedport", "disable") == "enable",
                        "try-no-pat": policy.get("fixedport", "disable") != "enable"
                    }
            
            return snat_rule
            
        except Exception as e:
            log(_("twice_nat44_fallback_handler.snat_rule_creation_failed",
                 policy=policy.get("intelligent_name") or policy.get("name", "unknown"),
                 error=str(e)), "error")
            return None

    def _validate_fallback_rules(
        self,
        rules: List[Dict[str, Any]],
        context: FallbackContext
    ) -> bool:
        """
        验证回退规则的有效性

        Args:
            rules: 生成的规则列表
            context: 回退上下文

        Returns:
            bool: 验证是否通过
        """
        try:
            if not rules:
                log(_("twice_nat44_fallback_handler.no_fallback_rules",
                     policy=context.policy_name), "warning")
                return False

            # 基本验证：检查规则结构
            for rule in rules:
                if not isinstance(rule, dict):
                    return False

                if "type" not in rule or "name" not in rule:
                    return False

                # 验证规则类型
                if rule["type"] not in ["static-dnat44", "static-snat44"]:
                    return False

            log(_("twice_nat44_fallback_handler.fallback_rules_validated",
                 policy=context.policy_name,
                 rule_count=len(rules)), "debug")

            return True

        except Exception as e:
            log(_("twice_nat44_fallback_handler.validation_failed",
                 policy=context.policy_name,
                 error=str(e)), "error")
            return False

    def _update_fallback_stats(self, context: FallbackContext):
        """
        更新回退统计信息

        Args:
            context: 回退上下文
        """
        try:
            self.fallback_stats["total_fallbacks"] += 1

            reason = context.reason.value
            if reason not in self.fallback_stats["fallback_reasons"]:
                self.fallback_stats["fallback_reasons"][reason] = 0
            self.fallback_stats["fallback_reasons"][reason] += 1

            # 计算成功率（这里假设回退都是成功的，实际可以根据需要调整）
            self.fallback_stats["success_rate"] = 1.0

        except Exception as e:
            log(_("twice_nat44_fallback_handler.stats_update_failed",
                 error=str(e)), "error")

    def get_fallback_statistics(self) -> Dict[str, Any]:
        """
        获取回退统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.fallback_stats.copy()

    def create_fallback_context(
        self,
        policy_name: str,
        reason: FallbackReason,
        error_message: Optional[str] = None,
        original_exception: Optional[Exception] = None,
        evaluation_score: Optional[int] = None
    ) -> FallbackContext:
        """
        创建回退上下文

        Args:
            policy_name: 策略名称
            reason: 回退原因
            error_message: 错误消息
            original_exception: 原始异常
            evaluation_score: 评估分数

        Returns:
            FallbackContext: 回退上下文对象
        """
        return FallbackContext(
            policy_name=policy_name,
            reason=reason,
            error_message=error_message,
            original_exception=original_exception,
            twice_nat44_attempted=True,
            evaluation_score=evaluation_score
        )
