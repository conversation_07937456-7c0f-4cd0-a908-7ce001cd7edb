package firewallflextrans

import (
	"fmt"
	"io"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/firewallflextrans"
	"regexp"
	"strings"
	"time"
)

// 敏感信息过滤规则
var sensitivePatterns = []*regexp.Regexp{
	regexp.MustCompile(`(?i)使用正则表达式\s*['"](.+?)['"]`),                                         // 隐藏正则表达式模式
	regexp.MustCompile(`(?i)匹配规则\s*['"](.+?)['"]`),                                            // 隐藏匹配规则
	regexp.MustCompile(`(?i)(算法|algorithm)\s*[:：]\s*['"]?(.+?)['"]?[\s,]`),                    // 隐藏算法名称
	regexp.MustCompile(`(?i)token\s*[:：]\s*['"](.+?)['"]`),                                    // 隐藏令牌
	regexp.MustCompile(`(?i)密钥|secret|password|key\s*[:：]\s*['"](.+?)['"]`),                   // 隐藏密钥信息
	regexp.MustCompile(`(?i)内部函数\s*['"](.+?)['"]`),                                            // 隐藏内部函数名称
	regexp.MustCompile(`(?i)调用模块\s*['"](.+?)['"]`),                                            // 隐藏内部模块名称
	regexp.MustCompile(`(?i)(使用|调用)(了)?\s*(内部|私有)\s*(函数|方法|模块|API)\s*['"]?(.+?)['"]?[\s,\.]`), // 隐藏内部函数/方法/模块/API名称
	regexp.MustCompile(`(?i)(执行|运行)(了)?\s*(命令|脚本|程序)\s*['"](.+?)['"]`),                        // 隐藏执行的命令/脚本/程序
	regexp.MustCompile(`(?i)(调用|执行)(了)?\s*(系统|外部)\s*(命令|函数)\s*['"](.+?)['"]`),                 // 隐藏系统/外部命令/函数
	regexp.MustCompile(`(?i)(解析|分析)(了)?\s*(规则|模式|表达式)\s*['"](.+?)['"]`),                       // 隐藏解析的规则/模式/表达式
	regexp.MustCompile(`(?i)(转换|变换)(了)?\s*(规则|模式|表达式)\s*['"](.+?)['"]`),                       // 隐藏转换的规则/模式/表达式
	regexp.MustCompile(`(?i)(生成|创建)(了)?\s*(规则|模式|表达式)\s*['"](.+?)['"]`),                       // 隐藏生成的规则/模式/表达式
	regexp.MustCompile(`(?i)(匹配|查找)(了)?\s*(规则|模式|表达式)\s*['"](.+?)['"]`),                       // 隐藏匹配的规则/模式/表达式
	regexp.MustCompile(`(?i)(应用|使用)(了)?\s*(规则|模式|表达式)\s*['"](.+?)['"]`),                       // 隐藏应用的规则/模式/表达式
	regexp.MustCompile(`(?i)(导入|加载)(了)?\s*(模块|库|包)\s*['"](.+?)['"]`),                          // 隐藏导入的模块/库/包
	regexp.MustCompile(`(?i)(实现|使用)(了)?\s*(算法|策略|方法)\s*['"](.+?)['"]`),                        // 隐藏实现的算法/策略/方法
	regexp.MustCompile(`(?i)(配置|设置)(了)?\s*(参数|选项|标志)\s*['"](.+?)['"]`),                        // 隐藏配置的参数/选项/标志
}

// 敏感级别的日志模块名称
var sensitiveModules = map[string]bool{
	"parser":      true,
	"converter":   true,
	"transformer": true,
	"matcher":     true,
	"internal":    true,
}

// WriteDetailedLogs 将详细日志写入用户日志文件，同时过滤敏感信息
func WriteDetailedLogs(writer io.Writer, logs []firewallflextrans.LogEntry) {
	if len(logs) == 0 {
		fmt.Fprintln(writer, "没有可用的详细日志记录")
		return
	}

	// 按时间排序并过滤敏感日志
	filteredLogs := filterSensitiveLogs(logs)

	// 写入过滤后的日志
	for _, log := range filteredLogs {
		// 如果日志没有时间戳，添加当前时间
		timeStr := log.Time
		if timeStr == "" {
			timeStr = time.Now().Format("2006-01-02 15:04:05")
		}

		// 格式化日志级别
		level := strings.ToUpper(log.Level)
		if level == "" {
			level = "INFO"
		}

		// 过滤消息中的敏感信息
		safeMessage := log.Message

		// 写入格式化的日志
		fmt.Fprintf(writer, "%s - %s - %s\n", timeStr, level, safeMessage)
	}
}

// filterSensitiveLogs 过滤掉包含敏感信息的日志条目
func filterSensitiveLogs(logs []firewallflextrans.LogEntry) []firewallflextrans.LogEntry {
	var filteredLogs []firewallflextrans.LogEntry

	for _, log := range logs {
		// 跳过来自敏感模块的DEBUG级别日志
		if strings.ToUpper(log.Level) == "DEBUG" && sensitiveModules[log.Module] {
			logging.DebugLogger.Infof("跳过敏感模块日志: [%s] %s", log.Module, truncateString(log.Message, 30))
			continue
		}

		// 跳过明显包含实现细节的日志
		if containsImplementationDetails(log.Message) {
			logging.DebugLogger.Infof("跳过包含实现细节的日志: %s", truncateString(log.Message, 30))
			continue
		}

		// 保留这条日志
		filteredLogs = append(filteredLogs, log)
	}

	return filteredLogs
}

// filterSensitiveContent 过滤消息中的敏感内容
func filterSensitiveContent(message string) string {
	// 应用所有敏感模式过滤
	for _, pattern := range sensitivePatterns {
		message = pattern.ReplaceAllString(message, "$1: [已隐藏]")
	}

	// 检查是否包含明显的实现细节
	if containsImplementationDetails(message) {
		// 如果包含实现细节，只保留基本信息，隐藏详细内容
		parts := strings.SplitN(message, ":", 2)
		if len(parts) > 1 {
			return parts[0] + ": [实现细节已隐藏]"
		}
		return "[实现细节已隐藏]"
	}

	return message
}

// containsImplementationDetails 检查消息是否包含实现细节
func containsImplementationDetails(message string) bool {
	// 检查是否包含实现细节的关键词
	keywords := []string{
		"算法", "algorithm", "实现", "implementation",
		"函数调用", "function call", "内部", "internal",
		"模块", "module", "类", "class", "方法", "method",
		"解析器", "parser", "转换器", "converter",
		"编译器", "compiler", "解释器", "interpreter",
		"引擎", "engine", "核心", "core", "底层", "underlying",
		"私有", "private", "保护", "protected", "封装", "encapsulation",
		"继承", "inheritance", "多态", "polymorphism", "接口", "interface",
		"抽象", "abstract", "具体", "concrete", "实例", "instance",
		"对象", "object", "属性", "property", "字段", "field",
		"构造", "constructor", "析构", "destructor", "初始化", "initialization",
		"注入", "injection", "依赖", "dependency", "工厂", "factory",
		"单例", "singleton", "原型", "prototype", "代理", "proxy",
		"装饰", "decorator", "适配器", "adapter", "观察者", "observer",
		"策略", "strategy", "命令", "command", "状态", "state",
		"访问者", "visitor", "迭代器", "iterator", "组合", "composite",
		"桥接", "bridge", "外观", "facade", "享元", "flyweight",
		"中介者", "mediator", "备忘录", "memento", "解释器", "interpreter",
		"责任链", "chain of responsibility", "模板方法", "template method",
	}

	lowercaseMsg := strings.ToLower(message)
	for _, keyword := range keywords {
		if strings.Contains(lowercaseMsg, strings.ToLower(keyword)) {
			return true
		}
	}

	// 检查是否包含代码片段或技术术语
	codePatterns := []string{
		"import ", "require ", "include ", "#include",
		"function ", "def ", "class ", "struct ", "interface ",
		"public ", "private ", "protected ", "static ", "final ",
		"const ", "var ", "let ", "return ", "if ", "else ", "for ", "while ",
		"try ", "catch ", "throw ", "new ", "delete ", "typeof ",
		"instanceof ", "extends ", "implements ", "super ", "this.",
		"self.", "prototype.", "__init__", "__call__", "__get", "__set",
		"()", "[]", "{}", "<>", "=>", "->", "::", ";;",
	}

	for _, pattern := range codePatterns {
		if strings.Contains(lowercaseMsg, pattern) {
			return true
		}
	}

	return false
}

// WriteConversionSummary 将转换总结信息写入用户日志文件
func WriteConversionSummary(writer io.Writer, summary *firewallflextrans.ConversionSummary) {
	// 使用默认中文，确保向后兼容
	language := "zh-CN"

	if summary == nil {
		fmt.Fprintln(writer, "\n=== "+getI18nMessage("user.summary.conversion_summary", language)+" ===")
		fmt.Fprintln(writer, "没有可用的转换总结信息")
		return
	}

	fmt.Fprintln(writer, "\n=== "+getI18nMessage("user.summary.conversion_summary", language)+" ===")
	fmt.Fprintf(writer, "总接口数: %d\n", summary.TotalInterfaces)
	fmt.Fprintf(writer, "已映射接口数: %d\n", summary.MappedInterfaces)
	fmt.Fprintf(writer, "未映射接口数: %d\n", summary.UnmappedInterfaces)
	fmt.Fprintf(writer, "区域数: %d\n", summary.Zones)
	fmt.Fprintf(writer, "静态路由数: %d\n", summary.StaticRoutes)
	fmt.Fprintf(writer, "地址对象数: %d\n", summary.AddressObjects)
	fmt.Fprintf(writer, "服务对象数: %d\n", summary.ServiceObjects)
	fmt.Fprintf(writer, "策略规则数: %d\n", summary.PolicyRules)

	// 显示跳过的配置项
	if len(summary.SkippedItems) > 0 {
		fmt.Fprintln(writer, "\n=== "+getI18nMessage("user.summary.skipped_items_detail", language)+" ===")
		for _, item := range summary.SkippedItems {
			fmt.Fprintf(writer, "- %s '%s': %s\n", getItemTypeName(item.Type), item.Name, item.Reason)
			if item.Detail != "" {
				// 过滤详情中的敏感信息
				safeDetail := filterSensitiveContent(item.Detail)
				fmt.Fprintf(writer, "  详情: %s\n", safeDetail)
			}
		}
	}

	// 显示需要手动配置的项目
	if len(summary.ManualConfigRequired) > 0 {
		fmt.Fprintln(writer, "\n=== "+getI18nMessage("user.summary.manual_config_needed", language)+" ===")
		for _, item := range summary.ManualConfigRequired {
			fmt.Fprintf(writer, "- %s '%s': %s\n", getItemTypeName(item.Type), item.Name, item.Reason)
			if item.Detail != "" {
				// 过滤详情中的敏感信息
				safeDetail := filterSensitiveContent(item.Detail)
				fmt.Fprintf(writer, "  详情: %s\n", safeDetail)
			}
		}
	}

	// 如果没有跳过的项目和需要手动配置的项目，显示完全转换成功的消息
	if len(summary.SkippedItems) == 0 && len(summary.ManualConfigRequired) == 0 {
		fmt.Fprintln(writer, "\n=== "+getI18nMessage("user.summary.no_unconverted_items", language)+" ===")
	} else {
		totalSkipped := len(summary.SkippedItems)
		totalManual := len(summary.ManualConfigRequired)
		fmt.Fprintf(writer, "\n总结: 有 %d 个配置项被跳过，%d 个配置项需要手动配置。\n", totalSkipped, totalManual)
		fmt.Fprintln(writer, "请查看上述详细信息进行相应处理。")
	}
}

// getItemTypeName 获取配置项类型的中文名称
func getItemTypeName(itemType string) string {
	switch strings.ToLower(itemType) {
	case "interface":
		return "接口"
	case "zone":
		return "安全区域"
	case "zone_interface":
		return "区域接口"
	case "route":
		return "路由"
	case "address":
		return "地址对象"
	case "service":
		return "服务对象"
	case "policy":
		return "策略规则"
	default:
		return itemType
	}
}

// truncateString 截断字符串，用于日志记录
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
