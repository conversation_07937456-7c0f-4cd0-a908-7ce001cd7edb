"""
FortiGate twice-nat44性能优化服务

本模块提供twice-nat44功能的性能优化，包括缓存机制、
批量处理、内存优化和并发处理优化。

主要功能：
- 评估结果缓存
- 批量规则处理
- 内存使用优化
- 并发处理优化
- 性能监控和调优

版本: 1.0
作者: FortiGate转换系统
创建时间: 2025-08-06
"""

from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import hashlib
import threading
import time
import weakref
from functools import lru_cache, wraps
from engine.utils.logger import log
from engine.utils.i18n import _


@dataclass
class PerformanceMetrics:
    """
    性能指标数据结构
    """
    operation_name: str
    total_calls: int = 0
    total_time_ms: float = 0.0
    min_time_ms: float = float('inf')
    max_time_ms: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    
    @property
    def average_time_ms(self) -> float:
        """平均执行时间"""
        return self.total_time_ms / self.total_calls if self.total_calls > 0 else 0.0
    
    @property
    def cache_hit_rate(self) -> float:
        """缓存命中率"""
        total_cache_requests = self.cache_hits + self.cache_misses
        return (self.cache_hits / total_cache_requests * 100) if total_cache_requests > 0 else 0.0
    
    def update(self, execution_time_ms: float, cache_hit: bool = False):
        """更新性能指标"""
        self.total_calls += 1
        self.total_time_ms += execution_time_ms
        self.min_time_ms = min(self.min_time_ms, execution_time_ms)
        self.max_time_ms = max(self.max_time_ms, execution_time_ms)
        
        if cache_hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "operation_name": self.operation_name,
            "total_calls": self.total_calls,
            "average_time_ms": round(self.average_time_ms, 4),
            "min_time_ms": round(self.min_time_ms, 4),
            "max_time_ms": round(self.max_time_ms, 4),
            "cache_hit_rate": round(self.cache_hit_rate, 2)
        }


class TwiceNat44Cache:
    """
    twice-nat44专用缓存系统
    
    提供评估结果和规则生成结果的缓存功能。
    """
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl_seconds: 缓存生存时间（秒）
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache = {}
        self._access_times = {}
        self._lock = threading.RLock()
        
        log(_("twice_nat44_cache.initialized", max_size=max_size, ttl=ttl_seconds), "info")
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Any]: 缓存值，如果不存在或过期则返回None
        """
        with self._lock:
            if key not in self._cache:
                return None
            
            # 检查是否过期
            if self._is_expired(key):
                self._remove(key)
                return None
            
            # 更新访问时间
            self._access_times[key] = time.time()
            return self._cache[key]
    
    def put(self, key: str, value: Any):
        """
        存储缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
        """
        with self._lock:
            # 如果缓存已满，移除最久未访问的条目
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_lru()
            
            self._cache[key] = value
            self._access_times[key] = time.time()
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                "size": len(self._cache),
                "max_size": self.max_size,
                "usage_percentage": (len(self._cache) / self.max_size * 100) if self.max_size > 0 else 0
            }
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存项是否过期"""
        if key not in self._access_times:
            return True
        
        age = time.time() - self._access_times[key]
        return age > self.ttl_seconds
    
    def _remove(self, key: str):
        """移除缓存项"""
        self._cache.pop(key, None)
        self._access_times.pop(key, None)
    
    def _evict_lru(self):
        """移除最久未访问的缓存项"""
        if not self._access_times:
            return
        
        lru_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        self._remove(lru_key)


class TwiceNat44Optimizer:
    """
    twice-nat44性能优化器
    
    提供全面的性能优化功能。
    """
    
    def __init__(self, enable_caching: bool = True, cache_size: int = 1000):
        """
        初始化优化器
        
        Args:
            enable_caching: 是否启用缓存
            cache_size: 缓存大小
        """
        self.enable_caching = enable_caching
        self.cache = TwiceNat44Cache(max_size=cache_size) if enable_caching else None
        self.metrics = {}
        self._lock = threading.Lock()
        
        log(_("twice_nat44_optimizer.initialized", caching=enable_caching), "info")
    
    def performance_monitor(self, operation_name: str):
        """
        性能监控装饰器
        
        Args:
            operation_name: 操作名称
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
                    
                    # 记录性能指标
                    self._record_performance(operation_name, execution_time, False)
                    
                    return result
                    
                except Exception as e:
                    execution_time = (time.time() - start_time) * 1000
                    self._record_performance(operation_name, execution_time, False)
                    raise
            
            return wrapper
        return decorator
    
    def cached_evaluation(self, evaluator_func: Callable):
        """
        缓存评估结果的装饰器
        
        Args:
            evaluator_func: 评估函数
        """
        @wraps(evaluator_func)
        def wrapper(policy: Dict[str, Any], vips: Dict[str, Any], context: Dict[str, Any] = None):
            if not self.enable_caching:
                return evaluator_func(policy, vips, context)
            
            # 生成缓存键
            cache_key = self._generate_evaluation_cache_key(policy, vips, context)
            
            # 尝试从缓存获取
            start_time = time.time()
            cached_result = self.cache.get(cache_key)
            
            if cached_result is not None:
                execution_time = (time.time() - start_time) * 1000
                self._record_performance("evaluation", execution_time, True)
                return cached_result
            
            # 执行评估
            result = evaluator_func(policy, vips, context)
            execution_time = (time.time() - start_time) * 1000
            
            # 存储到缓存
            self.cache.put(cache_key, result)
            self._record_performance("evaluation", execution_time, False)
            
            return result
        
        return wrapper
    
    def batch_process_policies(self, policies: List[Dict[str, Any]], 
                              vips: Dict[str, Any], 
                              processor_func: Callable,
                              batch_size: int = 10) -> List[Any]:
        """
        批量处理策略
        
        Args:
            policies: 策略列表
            vips: VIP配置
            processor_func: 处理函数
            batch_size: 批次大小
            
        Returns:
            List[Any]: 处理结果列表
        """
        results = []
        total_policies = len(policies)
        
        log(_("twice_nat44_optimizer.batch_processing_started", 
             total=total_policies, batch_size=batch_size), "info")
        
        start_time = time.time()
        
        for i in range(0, total_policies, batch_size):
            batch = policies[i:i + batch_size]
            batch_start_time = time.time()
            
            # 处理当前批次
            batch_results = []
            for policy in batch:
                try:
                    result = processor_func(policy, vips)
                    batch_results.append(result)
                except Exception as e:
                    log(_("twice_nat44_optimizer.policy_processing_failed",
                         policy=policy.get("name", "unknown"), error=str(e)), "error")
                    batch_results.append(None)
            
            results.extend(batch_results)
            
            batch_time = (time.time() - batch_start_time) * 1000
            log(_("twice_nat44_optimizer.batch_completed",
                 batch_num=i//batch_size + 1,
                 batch_size=len(batch),
                 time_ms=batch_time), "debug")
        
        total_time = (time.time() - start_time) * 1000
        self._record_performance("batch_processing", total_time, False)
        
        log(_("twice_nat44_optimizer.batch_processing_completed",
             total=total_policies, time_ms=total_time), "info")
        
        return results
    
    def optimize_memory_usage(self):
        """优化内存使用"""
        try:
            # 清理过期缓存
            if self.cache:
                old_size = len(self.cache._cache)
                self._cleanup_expired_cache()
                new_size = len(self.cache._cache)
                
                if old_size > new_size:
                    log(_("twice_nat44_optimizer.cache_cleaned",
                         old_size=old_size, new_size=new_size), "info")
            
            # 触发垃圾回收
            import gc
            collected = gc.collect()
            
            if collected > 0:
                log(_("twice_nat44_optimizer.gc_collected", objects=collected), "debug")
                
        except Exception as e:
            log(_("twice_nat44_optimizer.memory_optimization_failed", error=str(e)), "error")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        with self._lock:
            report = {
                "timestamp": datetime.now().isoformat(),
                "caching_enabled": self.enable_caching,
                "metrics": {name: metric.to_dict() for name, metric in self.metrics.items()}
            }
            
            if self.cache:
                report["cache_stats"] = self.cache.get_stats()
            
            return report
    
    def reset_metrics(self):
        """重置性能指标"""
        with self._lock:
            self.metrics.clear()
            if self.cache:
                self.cache.clear()
        
        log(_("twice_nat44_optimizer.metrics_reset"), "info")
    
    def _generate_evaluation_cache_key(self, policy: Dict[str, Any], 
                                     vips: Dict[str, Any], 
                                     context: Dict[str, Any] = None) -> str:
        """生成评估缓存键"""
        # 创建一个包含关键信息的字符串
        key_data = {
            "policy_name": policy.get("name", ""),
            "policy_dstaddr": sorted(policy.get("dstaddr", [])),
            "policy_service": sorted(policy.get("service", [])),
            "policy_nat": policy.get("nat", ""),
            "policy_ippool": policy.get("ippool", ""),
            "vip_count": len([addr for addr in policy.get("dstaddr", []) if addr in vips]),
            "context_version": context.get("ntos_version", "") if context else "",
            "context_threshold": context.get("twice_nat44_threshold", 80) if context else 80
        }
        
        # 生成哈希
        key_str = str(sorted(key_data.items()))
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _record_performance(self, operation_name: str, execution_time_ms: float, cache_hit: bool):
        """记录性能指标"""
        with self._lock:
            if operation_name not in self.metrics:
                self.metrics[operation_name] = PerformanceMetrics(operation_name)
            
            self.metrics[operation_name].update(execution_time_ms, cache_hit)
    
    def _cleanup_expired_cache(self):
        """清理过期缓存"""
        if not self.cache:
            return
        
        with self.cache._lock:
            expired_keys = []
            for key in list(self.cache._cache.keys()):
                if self.cache._is_expired(key):
                    expired_keys.append(key)
            
            for key in expired_keys:
                self.cache._remove(key)


# 全局优化器实例
_global_optimizer = None
_optimizer_lock = threading.Lock()


def get_global_optimizer() -> TwiceNat44Optimizer:
    """获取全局优化器实例"""
    global _global_optimizer
    
    if _global_optimizer is None:
        with _optimizer_lock:
            if _global_optimizer is None:
                _global_optimizer = TwiceNat44Optimizer()
    
    return _global_optimizer


def performance_monitor(operation_name: str):
    """全局性能监控装饰器"""
    return get_global_optimizer().performance_monitor(operation_name)


def cached_evaluation(func: Callable):
    """全局缓存评估装饰器"""
    return get_global_optimizer().cached_evaluation(func)
