   module ietf-origin {
     yang-version 1.1;
     namespace "urn:ietf:params:xml:ns:yang:ietf-origin";
     prefix or;

     import ietf-yang-metadata {
       prefix md;
     }

     organization
       "IETF Network Modeling (NETMOD) Working Group";

     contact
       "WG Web:   <https://datatracker.ietf.org/wg/netmod/>

        WG List:  <mailto:<EMAIL>>

        Author:   <PERSON>
                  <mailto:<EMAIL>>

        Author:   <PERSON><PERSON><PERSON>
                  <mailto:j.schoen<PERSON><EMAIL>>

        Author:   <PERSON>
                  <mailto:<EMAIL>>

        Author:   <PERSON>
                  <mailto:<EMAIL>>

        Author:   <PERSON>
                  <<EMAIL>>";

     description
       "This YANG module defines an 'origin' metadata annotation and a
        set of identities for the origin value.

        Copyright (c) 2018 IETF Trust and the persons identified as
        authors of the code.  All rights reserved.

        Redistribution and use in source and binary forms, with or
        without modification, is permitted pursuant to, and subject to
        the license terms contained in, the Simplified BSD License set
        forth in Section 4.c of the IETF Trust's Legal Provisions
        Relating to IETF Documents
        (https://trustee.ietf.org/license-info).

        This version of this YANG module is part of RFC 8342
        (https://www.rfc-editor.org/info/rfc8342); see the RFC itself
        for full legal notices.";

     revision 2018-02-14 {
       description
         "Initial revision.";
       reference
         "RFC 8342: Network Management Datastore Architecture (NMDA)";
     }

     /*
      * Identities
      */

     identity origin {
       description
         "Abstract base identity for the origin annotation.";
     }

     identity intended {
       base origin;
       description
         "Denotes configuration from the intended configuration
          datastore.";
     }

     identity dynamic {
       base origin;
       description
         "Denotes configuration from a dynamic configuration
          datastore.";
     }

     identity system {
       base origin;
       description
         "Denotes configuration originated by the system itself.

          Examples of system configuration include applied configuration
          for an always-existing loopback interface, or interface
          configuration that is auto-created due to the hardware
          currently present in the device.";
     }

     identity learned {
       base origin;
       description
         "Denotes configuration learned from protocol interactions with
          other devices, instead of via either the intended
          configuration datastore or any dynamic configuration
          datastore.

          Examples of protocols that provide learned configuration
          include link-layer negotiations, routing protocols, and
          DHCP.";
     }

     identity default {
       base origin;
       description
         "Denotes configuration that does not have a configured or
          learned value but has a default value in use.  Covers both
          values defined in a 'default' statement and values defined
          via an explanation in a 'description' statement.";
     }

     identity unknown {
       base origin;
       description
         "Denotes configuration for which the system cannot identify the
          origin.";
     }

     /*
      * Type definitions
      */

     typedef origin-ref {
       type identityref {
         base origin;
       }
       description
         "An origin identity reference.";
     }

     /*
      * Metadata annotations
      */

     md:annotation origin {
       type origin-ref;
       description
         "The 'origin' annotation can be present on any configuration
          data node in the operational state datastore.  It specifies
          from where the node originated.  If not specified for a given
          configuration data node, then the origin is the same as the
          origin of its parent node in the data tree.  The origin for
          any top-level configuration data nodes must be specified.";
     }
   }
