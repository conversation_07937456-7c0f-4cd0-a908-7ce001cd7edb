source,target
"config firewall address
edit ""***********-*************""
     set uuid 987da852-89c0-51ee-db79-7f4778c644b7
     set type iprange
     set start-ip ***********
     set end-ip *************
next
edit ""***********""
     set uuid 98896f70-89c0-51ee-8e3c-5be630957f68
     set subnet *********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>***********</name>
        <ip-set>
          <ip-address>***********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***********-*************</name>
        <ip-set>
          <ip-address>***********-*************</ip-address>
        </ip-set>
      </address-set>
    </network-obj>"
"config firewall service group
edit ""test""
        set member ""***********-*************"" ""***********"" ""***********""
next"," <network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>test</name>
        <address-set>
          <name>***********-*************</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service custom
edit ""TCP-1521""
        set tcp-portrange 1521
next
edit ""KERBEROS""
        set category ""Authentication""
        set tcp-portrange 88 464
        set udp-portrange 88 464
next
edit ""UDP67-68""
        set udp-portrange 67 68
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>TCP-1521</name>
        <tcp>
          <dest-port>1521</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>KERBEROS</name>
        <tcp>
          <dest-port>88-464</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>UDP67-68</name>
        <udp>
          <dest-port>67,68</dest-port>
        </udp>
      </service-set>
    </service-obj>"
"config firewall service group
edit ""Exchange Server""
        set member ""DCE-RPC"" ""DNS"" ""HTTPS""
next","    <service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-group>
        <name>Exchange-Server</name>
        <service-set>
          <name>DCE-RPC</name>
        </service-set>
        <service-set>
          <name>DNS</name>
        </service-set>
        <service-set>
          <name>HTTPS</name>
        </service-set>
      </service-group>
    </service-obj>"
"config firewall ippool
    edit ""nat-pool""
        set startip ************
        set endip *************
    next
end","    <nat xmlns=""urn:ruijie:ntos:params:xml:ns:yang:nat"">
      <enabled>true</enabled>
      <pool>
        <name>nat-pool</name>
        <address>
          <value>************-*************</value>
        </address>
      </pool>
    </nat>"
"config ips sensor
    edit ""g-default""
        set comment ""Prevent critical attacks.""
        config entries
            edit 1
                set severity medium high critical 
            next
        end
    next
    edit ""g-sniffer-profile""
        set comment ""Monitor IPS attacks.""
        config entries
            edit 1
                set severity medium high critical 
            next
        end
    next
    edit ""g-wifi-default""
        set comment ""Default configuration for offloading WiFi traffic.""
        config entries
            edit 1
                set severity medium high critical 
            next
        end
    next
end","<ips-config xmlns=""urn:ruijie:ntos:params:xml:ns:yang:intrusion-prevention"">
      <template>
        <name>g-default</name>
        <description>Prevent critical attacks.</description>
        <filter>
          <name>test-filter</name>
          <target>both</target>
          <severity>informational</severity>
          <severity>low</severity>
          <severity>medium</severity>
          <severity>high</severity>
          <protocol>
            <all-protocol>true</all-protocol>
          </protocol>
          <category>
            <all-category>true</all-category>
          </category>
        </filter>
      </template>
    </ips-config>"
"config antivirus profile
    edit ""g-default""
        set comment ""Scan files and block viruses.""
        config http
            set options scan
        end
        config ftp
            set options scan
        end
        config imap
            set options scan
            set executables virus
        end
        config pop3
            set options scan
            set executables virus
        end
        config smtp
            set options scan
            set executables virus
        end
    next
    edit ""g-sniffer-profile""
        set comment ""Scan files and monitor viruses.""
        config http
            set options scan
        end
        config ftp
            set options scan
        end
        config imap
            set options scan
            set executables virus
        end
        config pop3
            set options scan
            set executables virus
        end
        config smtp
            set options scan
            set executables virus
        end
    next
end","<nc:rpc xmlns:nc=""urn:ietf:params:xml:ns:netconf:base:1.0"" message-id=""urn:uuid:b2e651eb-30b9-47a2-945b-fb9e33c5c98d"" lang=""0"" src=""1"" ts=""1733797694743"" sync=""0"">
  <nc:edit-config>
    <nc:target>
      <nc:running/>
    </nc:target>
    <nc:config>
      <config xmlns=""urn:ruijie:ntos"">
        <vrf>
          <name>main</name>
          <anti-virus xmlns=""urn:ruijie:ntos:params:xml:ns:yang:anti-virus"">
            <template nc:operation=""create"">
              <name>test</name>
              <scan-mode>deep</scan-mode>
              <protocols>
                <protocol>
                  <name>FTP</name>
                  <direction>both</direction>
                </protocol>
                <protocol>
                  <name>HTTP</name>
                  <direction>both</direction>
                </protocol>
                <protocol>
                  <name>IMAP</name>
                  <direction>to-client</direction>
                </protocol>
                <protocol>
                  <name>NFS</name>
                  <direction>both</direction>
                </protocol>
                <protocol>
                  <name>POP3</name>
                  <direction>to-client</direction>
                </protocol>
                <protocol>
                  <name>SMB</name>
                  <direction>both</direction>
                </protocol>
                <protocol>
                  <name>SMTP</name>
                  <direction>to-server</direction>
                </protocol>
              </protocols>
              <file-type-sets>
                <file-type>
                  <suffix>swf</suffix>
                </file-type>
                <file-type>
                  <suffix>a</suffix>
                </file-type>
                <file-type>
                  <suffix>class</suffix>
                </file-type>
                <file-type>
                  <suffix>com</suffix>
                </file-type>
                <file-type>
                  <suffix>dex</suffix>
                </file-type>
                <file-type>
                  <suffix>dll</suffix>
                </file-type>
                <file-type>
                  <suffix>elf</suffix>
                </file-type>
                <file-type>
                  <suffix>exe</suffix>
                </file-type>
                <file-type>
                  <suffix>macho</suffix>
                </file-type>
                <file-type>
                  <suffix>msi</suffix>
                </file-type>
                <file-type>
                  <suffix>ocx</suffix>
                </file-type>
                <file-type>
                  <suffix>pascal</suffix>
                </file-type>
                <file-type>
                  <suffix>reg</suffix>
                </file-type>
                <file-type>
                  <suffix>rpm</suffix>
                </file-type>
                <file-type>
                  <suffix>doc</suffix>
                </file-type>
                <file-type>
                  <suffix>docx</suffix>
                </file-type>
                <file-type>
                  <suffix>dot</suffix>
                </file-type>
                <file-type>
                  <suffix>dotx</suffix>
                </file-type>
                <file-type>
                  <suffix>eml</suffix>
                </file-type>
                <file-type>
                  <suffix>fla</suffix>
                </file-type>
                <file-type>
                  <suffix>pdf</suffix>
                </file-type>
                <file-type>
                  <suffix>potx</suffix>
                </file-type>
                <file-type>
                  <suffix>ppt</suffix>
                </file-type>
                <file-type>
                  <suffix>pptx</suffix>
                </file-type>
                <file-type>
                  <suffix>rtf</suffix>
                </file-type>
                <file-type>
                  <suffix>vsd</suffix>
                </file-type>
                <file-type>
                  <suffix>xls</suffix>
                </file-type>
                <file-type>
                  <suffix>xlsx</suffix>
                </file-type>
                <file-type>
                  <suffix>xltx</suffix>
                </file-type>
                <file-type>
                  <suffix>7z</suffix>
                </file-type>
                <file-type>
                  <suffix>bz2</suffix>
                </file-type>
                <file-type>
                  <suffix>cab</suffix>
                </file-type>
                <file-type>
                  <suffix>cpio</suffix>
                </file-type>
                <file-type>
                  <suffix>gz</suffix>
                </file-type>
                <file-type>
                  <suffix>jar</suffix>
                </file-type>
                <file-type>
                  <suffix>rar</suffix>
                </file-type>
                <file-type>
                  <suffix>xz</suffix>
                </file-type>
                <file-type>
                  <suffix>zip</suffix>
                </file-type>
                <file-type>
                  <suffix>bash</suffix>
                </file-type>
                <file-type>
                  <suffix>bat</suffix>
                </file-type>
                <file-type>
                  <suffix>pl</suffix>
                </file-type>
                <file-type>
                  <suffix>py</suffix>
                </file-type>
                <file-type>
                  <suffix>sh</suffix>
                </file-type>
                <file-type>
                  <suffix>apk</suffix>
                </file-type>
              </file-type-sets>
            </template>
          </anti-virus>
        </vrf>
      </config>
    </nc:config>
  </nc:edit-config>
</nc:rpc>"
"config firewall policy
    edit 16
        set name ""nat_rule""
        set uuid 86181c18-ae2e-51ef-de46-bc8d00bf0343              
        set srcintf ""port1""
        set dstintf ""port2""
        set srcaddr ""nat_sip""
        set dstaddr ""all""
        set action accept               
        set status disable                
        set schedule ""always""
        set service ""ALL""
        set fixedport enable
        set fsso disable                
        set comments ""test-123123""
        set nat enable
    next
end"," <nat xmlns=""urn:ruijie:ntos:params:xml:ns:yang:nat"">
      <rule>
        <name>nat_rule</name>
        <rule_en>true</rule_en>
        <desc>test-123123</desc>
        <static-snat44>
          <match>
            <source-network>
              <name>***********-*************</name>
            </source-network>
            <dest-network>
              <name>***********</name>
            </dest-network>
            <time-range>
              <value>any</value>
            </time-range>
            <service>
              <name>any</name>
            </service>
          </match>
          <translate-to>
            <output-address/>
            <no-pat>false</no-pat>
          </translate-to>
        </static-snat44>
      </rule>
    </nat>"
"config firewall address
edit ""********-********00""
     set uuid 123e4567-e89b-12d3-a456-************
     set type iprange
     set start-ip ********
     set end-ip ********00
next
edit ""**********""
     set uuid 987fcdeb-12ab-34cd-5678-9abcdef12345
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>********-********00</name>
        <ip-set>
          <ip-address>********-********00</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************-**************""
     set uuid 5f6e7d8c-9a0b-1c2d-3e4f-5a6b7c8d9e0f
     set type iprange
     set start-ip *************
     set end-ip **************
next
edit ""*********""
     set uuid 1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d
     set subnet ********* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*********</name>
        <ip-set>
          <ip-address>*********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>*************-**************</name>
        <ip-set>
          <ip-address>*************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""***********-***********""
     set uuid abcd1234-5678-90ef-ghij-1234567890ab
     set type iprange
     set start-ip ***********
     set end-ip ***********
next
edit ""*************""
     set uuid efgh5678-9012-34ij-klmn-567890abcdef
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***********-***********</name>
        <ip-set>
          <ip-address>***********-***********</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************-************""
     set uuid 1a2b3c4d-5e6f-7g8h-9i0j-1k2l3m4n5o6p
     set type iprange
     set start-ip ************
     set end-ip ************
next
edit ""********""
     set uuid 7p8q9r0s-1t2u-3v4w-5x6y-7z8a9b0c1d2e
     set subnet ******** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>********</name>
        <ip-set>
          <ip-address>********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************-************</name>
        <ip-set>
          <ip-address>************-************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""***********-*************""
     set uuid 3f4g5h6i-7j8k-9l0m-1n2o-3p4q5r6s7t8u
     set type iprange
     set start-ip ***********
     set end-ip *************
next
edit ""************""
     set uuid 9v0w1x2y-3z4a-5b6c-7d8e-9f0g1h2i3j4k
     set subnet ************ *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***********-*************</name>
        <ip-set>
          <ip-address>***********-*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""***********-***********""
     set uuid 5l6m7n8o-9p0q-1r2s-3t4u-5v6w7x8y9z0a
     set type iprange
     set start-ip ***********
     set end-ip ***********
next
edit ""**********""
     set uuid 1b2c3d4e-5f6g-7h8i-9j0k-1l2m3n4o5p6q
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***********-***********</name>
        <ip-set>
          <ip-address>***********-***********</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************-**************""
     set uuid 7r8s9t0u-1v2w-3x4y-5z6a-7b8c9d0e1f2g
     set type iprange
     set start-ip *************
     set end-ip **************
next
edit ""*********""
     set uuid 3h4i5j6k-7l8m-9n0o-1p2q-3r4s5t6u7v8w
     set subnet ********* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*********</name>
        <ip-set>
          <ip-address>*********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>*************-**************</name>
        <ip-set>
          <ip-address>*************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""172.19.1.100-172.19.1.150""
     set uuid 9x0y1z2a-3b4c-5d6e-7f8g-9h0i1j2k3l4m
     set type iprange
     set start-ip 172.19.1.100
     set end-ip 172.19.1.150
next
edit ""192.168.75.0""
     set uuid 5n6o7p8q-9r0s-1t2u-3v4w-5x6y7z8a9b0c
     set subnet 192.168.75.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.75.0</name>
        <ip-set>
          <ip-address>192.168.75.0/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>172.19.1.100-172.19.1.150</name>
        <ip-set>
          <ip-address>172.19.1.100-172.19.1.150</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""10.100.0.1-***********5""
     set uuid 1d2e3f4g-5h6i-7j8k-9l0m-1n2o3p4q5r6s
     set type iprange
     set start-ip 10.100.0.1
     set end-ip ***********5
next
edit ""172.20.10.0""
     set uuid 7t8u9v0w-1x2y-3z4a-5b6c-7d8e9f0g1h2i
     set subnet 172.20.10.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>172.20.10.0</name>
        <ip-set>
          <ip-address>172.20.10.0/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>10.100.0.1-***********5</name>
        <ip-set>
          <ip-address>10.100.0.1-***********5</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.15.20-192.168.15.100""
     set uuid 3j4k5l6m-7n8o-9p0q-1r2s-3t4u5v6w7x8y
     set type iprange
     set start-ip 192.168.15.20
     set end-ip 192.168.15.100
next
edit ""**********""
     set uuid 9z0a1b2c-3d4e-5f6g-7h8i-9j0k1l2m3n4o
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>192.168.15.20-192.168.15.100</name>
        <ip-set>
          <ip-address>192.168.15.20-192.168.15.100</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**********-***********""
     set uuid 5p6q7r8s-9t0u-1v2w-3x4y-5z6a7b8c9d0e
     set type iprange
     set start-ip **********
     set end-ip ***********
next
edit ""************""
     set uuid 1f2g3h4i-5j6k-7l8m-9n0o-1p2q3r4s5t6u
     set subnet ************ *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**********-***********</name>
        <ip-set>
          <ip-address>**********-***********</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""***********-***********""
     set uuid 7v8w9x0y-1z2a-3b4c-5d6e-7f8g9h0i1j2k
     set type iprange
     set start-ip ***********
     set end-ip ***********
next
edit ""**********""
     set uuid 3l4m5n6o-7p8q-9r0s-1t2u-3v4w5x6y7z8a
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>***********-***********</name>
        <ip-set>
          <ip-address>***********-***********</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************-*************0""
     set uuid 9b0c1d2e-3f4g-5h6i-7j8k-9l0m1n2o3p4q
     set type iprange
     set start-ip *************
     set end-ip *************0
next
edit ""*********""
     set uuid 5r6s7t8u-9v0w-1x2y-3z4a-5b6c7d8e9f0g
     set subnet ********* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*********</name>
        <ip-set>
          <ip-address>*********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>*************-*************0</name>
        <ip-set>
          <ip-address>*************-*************0</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************-************""
     set uuid 1h2i3j4k-5l6m-7n8o-9p0q-1r2s3t4u5v6w
     set type iprange
     set start-ip ************
     set end-ip ************
next
edit ""*************""
     set uuid 7x8y9z0a-1b2c-3d4e-5f6g-7h8i9j0k1l2m
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************-************</name>
        <ip-set>
          <ip-address>************-************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************-************""
     set uuid 3n4o5p6q-7r8s-9t0u-1v2w-3x4y5z6a7b8c
     set type iprange
     set start-ip ************
     set end-ip ************
next
edit ""**********""
     set uuid 9d0e1f2g-3h4i-5j6k-7l8m-9n0o1p2q3r4s
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************-************</name>
        <ip-set>
          <ip-address>************-************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************-**************""
     set uuid 5t6u7v8w-9x0y-1z2a-3b4c-5d6e7f8g9h0i
     set type iprange
     set start-ip *************
     set end-ip **************
next
edit ""*********""
     set uuid 1j2k3l4m-5n6o-7p8q-9r0s-1t2u3v4w5x6y
     set subnet ********* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*********</name>
        <ip-set>
          <ip-address>*********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>*************-**************</name>
        <ip-set>
          <ip-address>*************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**********-***********""
     set uuid 7z8a9b0c-1d2e-3f4g-5h6i-7j8k9l0m1n2o
     set type iprange
     set start-ip **********
     set end-ip ***********
next
edit ""*************""
     set uuid 3p4q5r6s-7t8u-9v0w-1x2y-3z4a5b6c7d8e
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**********-***********</name>
        <ip-set>
          <ip-address>**********-***********</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-**************""
     set uuid 9f0g1h2i-3j4k-5l6m-7n8o-9p0q1r2s3t4u
     set type iprange
     set start-ip **************
     set end-ip **************
next
edit ""**********""
     set uuid 5v6w7x8y-9z0a-1b2c-3d4e-5f6g7h8i9j0k
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************-**************</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.225.10-192.168.225.20""
     set uuid 1l2m3n4o-5p6q-7r8s-9t0u-1v2w3x4y5z6a
     set type iprange
     set start-ip 192.168.225.10
     set end-ip 192.168.225.20
next
edit ""10.120.0.0""
     set uuid 7b8c9d0e-1f2g-3h4i-5j6k-7l8m9n0o1p2q
     set subnet 10.120.0.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>10.120.0.0</name>
        <ip-set>
          <ip-address>10.120.0.0/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>192.168.225.10-192.168.225.20</name>
        <ip-set>
          <ip-address>192.168.225.10-192.168.225.20</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""172.27.5.5-172.27.5.15""
     set uuid 3r4s5t6u-7v8w-9x0y-1z2a-3b4c5d6e7f8g
     set type iprange
     set start-ip 172.27.5.5
     set end-ip 172.27.5.15
next
edit ""192.168.250.0""
     set uuid 9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4w
     set subnet 192.168.250.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.250.0</name>
        <ip-set>
          <ip-address>192.168.250.0/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>172.27.5.5-172.27.5.15</name>
        <ip-set>
          <ip-address>172.27.5.5-172.27.5.15</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""10.130.140.150-10.130.140.160""
     set uuid 5x6y7z8a-9b0c-1d2e-3f4g-5h6i7j8k9l0m
     set type iprange
     set start-ip 10.130.140.150
     set end-ip 10.130.140.160
next
edit ""172.28.0.0""
     set uuid 1n2o3p4q-5r6s-7t8u-9v0w-1x2y3z4a5b6c
     set subnet 172.28.0.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>172.28.0.0</name>
        <ip-set>
          <ip-address>172.28.0.0/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>10.130.140.150-10.130.140.160</name>
        <ip-set>
          <ip-address>10.130.140.150-10.130.140.160</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.10.1-192.168.10.100""
     set uuid 7d8e9f0g-1h2i-3j4k-5l6m-7n8o9p0q1r2s
     set type iprange
     set start-ip 192.168.10.1
     set end-ip 192.168.10.100
next
edit ""10.140.0.0""
     set uuid 3t4u5v6w-7x8y-9z0a-1b2c-3d4e5f6g7h8i
     set subnet 10.140.0.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>10.140.0.0</name>
        <ip-set>
          <ip-address>10.140.0.0/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>192.168.10.1-192.168.10.100</name>
        <ip-set>
          <ip-address>192.168.10.1-192.168.10.100</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""172.29.1.10-172.29.1.20""
     set uuid 9j0k1l2m-3n4o-5p6q-7r8s-9t0u1v2w3x4y
     set type iprange
     set start-ip 172.29.1.10
     set end-ip 172.29.1.20
next
edit ""192.168.20.0""
     set uuid 5z6a7b8c-9d0e-1f2g-3h4i-5j6k7l8m9n0o
     set subnet 192.168.20.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.20.0</name>
        <ip-set>
          <ip-address>192.168.20.0/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>172.29.1.10-172.29.1.20</name>
        <ip-set>
          <ip-address>172.29.1.10-172.29.1.20</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-**************""
     set uuid 1p2q3r4s-5t6u-7v8w-9x0y-1z2a3b4c5d6e
     set type iprange
     set start-ip **************
     set end-ip **************
next
edit ""172.30.0.0""
     set uuid 7f8g9h0i-1j2k-3l4m-5n6o-7p8q9r0s1t2u
     set subnet 172.30.0.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>172.30.0.0</name>
        <ip-set>
          <ip-address>172.30.0.0/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************-**************</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************-*************""
     set uuid 3v4w5x6y-7z8a-9b0c-1d2e-3f4g5h6i7j8k
     set type iprange
     set start-ip ************
     set end-ip *************
next
edit ""**********""
     set uuid 9l0m1n2o-3p4q-5r6s-7t8u-9v0w1x2y3z4a
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************-*************</name>
        <ip-set>
          <ip-address>************-*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**********-**********0""
     set uuid 5b6c7d8e-9f0g-1h2i-3j4k-5l6m7n8o9p0q
     set type iprange
     set start-ip **********
     set end-ip **********0
next
edit ""************""
     set uuid 1r2s3t4u-5v6w-7x8y-9z0a-1b2c3d4e5f6g
     set subnet ************ *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**********-**********0</name>
        <ip-set>
          <ip-address>**********-**********0</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-**************""
     set uuid 7h8i9j0k-1l2m-3n4o-5p6q-7r8s9t0u1v2w
     set type iprange
     set start-ip **************
     set end-ip **************
next
edit ""**********""
     set uuid 3x4y5z6a-7b8c-9d0e-1f2g-3h4i5j6k7l8m
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************-**************</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************-*************""
     set uuid 9n0o1p2q-3r4s-5t6u-7v8w-9x0y1z2a3b4c
     set type iprange
     set start-ip ************
     set end-ip *************
next
edit ""**********""
     set uuid 5d6e7f8g-9h0i-1j2k-3l4m-5n6o7p8q9r0s
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************-*************</name>
        <ip-set>
          <ip-address>************-*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**********-**********""
     set uuid 1t2u3v4w-5x6y-7z8a-9b0c-1d2e3f4g5h6i
     set type iprange
     set start-ip **********
     set end-ip **********
next
edit ""************""
     set uuid 7j8k9l0m-1n2o-3p4q-5r6s-7t8u9v0w1x2y
     set subnet ************ *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**********-**********</name>
        <ip-set>
          <ip-address>**********-**********</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-**************""
     set uuid 3z4a5b6c-7d8e-9f0g-1h2i-3j4k5l6m7n8o
     set type iprange
     set start-ip **************
     set end-ip **************
next
edit ""**********""
     set uuid 9p0q1r2s-3t4u-5v6w-7x8y-9z0a1b2c3d4e
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>**************-**************</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************-*************""
     set uuid 5f6g7h8i-9j0k-1l2m-3n4o-5p6q7r8s9t0u
     set type iprange
     set start-ip ************
     set end-ip *************
next
edit ""**********""
     set uuid 1v2w3x4y-5z6a-7b8c-9d0e-1f2g3h4i5j6k
     set subnet ********** *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**********</name>
        <ip-set>
          <ip-address>**********/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>************-*************</name>
        <ip-set>
          <ip-address>************-*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""172.35.1.1-172.35.1.15""
     set uuid 7l8m9n0o-1p2q-3r4s-5t6u-7v8w9x0y1z2a
     set type iprange
     set start-ip 172.35.1.1
     set end-ip 172.35.1.15
next
edit ""192.168.80.0""
     set uuid 3b4c5d6e-7f8g-9h0i-1j2k-3l4m5n6o7p8q
     set subnet 192.168.80.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.80.0</name>
        <ip-set>
          <ip-address>192.168.80.0/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>172.35.1.1-172.35.1.15</name>
        <ip-set>
          <ip-address>172.35.1.1-172.35.1.15</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""10.210.220.230-10.210.220.240""
     set uuid 9r0s1t2u-3v4w-5x6y-7z8a-9b0c1d2e3f4g
     set type iprange
     set start-ip 10.210.220.230
     set end-ip 10.210.220.240
next
edit ""172.36.0.0""
     set uuid 5h6i7j8k-9l0m-1n2o-3p4q-5r6s7t8u9v0w
     set subnet 172.36.0.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>172.36.0.0</name>
        <ip-set>
          <ip-address>172.36.0.0/*************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>10.210.220.230-10.210.220.240</name>
        <ip-set>
          <ip-address>10.210.220.230-10.210.220.240</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.249.0""
     set uuid 658c8187-51d1-4e48-acec-9c6a5b208e5a
     set subnet 192.168.249.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.249.0</name>
        <ip-set>
          <ip-address>192.168.249.0/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.124.0""
     set uuid 79bdff92-a89c-47c0-bcbc-5270a0f23648
     set subnet 192.168.124.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.124.0</name>
        <ip-set>
          <ip-address>192.168.124.0/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.107.34-192.168.107.71""
     set uuid 48695e7d-5014-432c-989e-6069879e529b
     set type iprange
     set start-ip 192.168.107.34
     set end-ip 192.168.107.71
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.107.34-192.168.107.71</name>
        <ip-set>
          <ip-address>192.168.107.34-192.168.107.71</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.221.0""
     set uuid 67e4c1ac-22a2-4e01-8011-ccdea8820501
     set subnet 192.168.221.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.221.0</name>
        <ip-set>
          <ip-address>192.168.221.0/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.161.0""
     set uuid 6676cc25-42d7-4294-815e-74db3ae58998
     set subnet 192.168.161.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.161.0</name>
        <ip-set>
          <ip-address>192.168.161.0/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.12.0""
     set uuid a70c67a2-1045-4da0-aa73-7a8c8a44c7c5
     set subnet 192.168.12.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.12.0</name>
        <ip-set>
          <ip-address>192.168.12.0/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.170.0""
     set uuid 6229921a-d7b8-4c6a-bd9c-cbaa8afd2244
     set subnet 192.168.170.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.170.0</name>
        <ip-set>
          <ip-address>192.168.170.0/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************""
     set uuid 7a30764c-f295-4776-a20d-b7ad599ffaa9
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************""
     set uuid da5805c9-daac-4335-9662-ac056a61731d
     set subnet ************* *************
next","config firewall address
edit ""*************""
     set uuid da5805c9-daac-4335-9662-ac056a61731d
     set subnet ************* *************
next"
"config firewall address
edit ""************""
     set uuid ff0e6cf2-dc40-41a1-b1a1-12553d31f826
     set subnet ************ *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************""
     set uuid 00e4d5da-4952-4cbb-9aed-03f076c73801
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************""
     set uuid 82b7685b-7ab3-4278-a808-aac7546b05db
     set subnet ************ *************
next
","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-**************""
     set uuid de817b74-5ffa-4b36-92e6-7620bc916314
     set type iprange
     set start-ip **************
     set end-ip **************
next","config firewall address
edit ""**************-**************""
     set uuid de817b74-5ffa-4b36-92e6-7620bc916314
     set type iprange
     set start-ip **************
     set end-ip **************
next"
"config firewall address
edit ""************""
     set uuid 5583910e-dd0c-4727-bdbc-2b3323de25e1
     set subnet ************ *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************""
     set uuid 8d86321c-**************-e11721e8d330
     set subnet ************ *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************""
     set uuid 0851f3e9-01fe-4a84-9c11-285126c57a9b
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************""
     set uuid 2dc9120e-ca97-40d4-adb5-cb7f974ed94b
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-**************""
     set uuid e32384b5-d283-4b72-939a-14d091e930d3
     set type iprange
     set start-ip **************
     set end-ip **************
next
","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**************-**************</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************-**************""
     set uuid 32b8a197-6ec7-44a7-a8f4-dfc6eb334de8
     set type iprange
     set start-ip *************
     set end-ip **************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************-**************</name>
        <ip-set>
          <ip-address>*************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************""
     set uuid 1555dc59-ae0b-4b84-b1a7-b468a01b5023
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************-************""
     set uuid 6ec87cce-ea7f-48e2-8709-80a94e1c357e
     set type iprange
     set start-ip ************
     set end-ip ************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************-************</name>
        <ip-set>
          <ip-address>************-************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-***************""
     set uuid c51c4726-3cef-494d-ac73-f179207af983
     set type iprange
     set start-ip **************
     set end-ip ***************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**************-***************</name>
        <ip-set>
          <ip-address>**************-***************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************-**************""
     set uuid e41e6cff-ace8-4be2-aa98-5c48720f7d2b
     set type iprange
     set start-ip *************
     set end-ip **************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************-**************</name>
        <ip-set>
          <ip-address>*************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.14.0""
     set uuid fe94eed0-6341-4fdb-ac3d-faefa7be1ca5
     set subnet 192.168.14.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.14.0</name>
        <ip-set>
          <ip-address>192.168.14.0/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.143.37-192.168.143.55""
     set uuid 6d3810c4-0c9d-484b-8b68-93ffe5fa0bbe
     set type iprange
     set start-ip 192.168.143.37
     set end-ip 192.168.143.55
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.143.37-192.168.143.55</name>
        <ip-set>
          <ip-address>192.168.143.37-192.168.143.55</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.189.147-192.168.189.170""
     set uuid 4883d963-427c-4ade-997f-dcd2f4069dde
     set type iprange
     set start-ip 192.168.189.147
     set end-ip 192.168.189.170
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.189.147-192.168.189.170</name>
        <ip-set>
          <ip-address>192.168.189.147-192.168.189.170</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.39.0""
     set uuid 2253a9ac-3b1f-48b1-b7ce-56b4c9356293
     set subnet 192.168.39.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.39.0</name>
        <ip-set>
          <ip-address>192.168.39.0/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************""
     set uuid 9fa6a879-a3ab-4604-aa63-cfa3e58b4304
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.12.0""
     set uuid e92a3eaa-fc86-4f09-9235-897ff38a6c3c
     set subnet 192.168.12.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.12.0</name>
        <ip-set>
          <ip-address>192.168.12.0/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.183.135-192.168.183.141""
     set uuid 12cc07f9-e9f1-4819-af1e-31150f26978c
     set type iprange
     set start-ip 192.168.183.135
     set end-ip 192.168.183.141
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.183.135-192.168.183.141</name>
        <ip-set>
          <ip-address>192.168.183.135-192.168.183.141</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.205.126-192.168.205.128""
     set uuid 6495524b-9d30-4d14-8e6b-be7c64f42a65
     set type iprange
     set start-ip 192.168.205.126
     set end-ip 192.168.205.128
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.205.126-192.168.205.128</name>
        <ip-set>
          <ip-address>192.168.205.126-192.168.205.128</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************""
     set uuid baa2dd6f-d429-4c39-91ae-7a38e2a3a23b
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************""
     set uuid 5c83c8fe-29fa-40a4-95ce-a9d1284f9671
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-**************""
     set uuid cc38100b-a960-4f4b-9d53-788fd87b5ced
     set type iprange
     set start-ip **************
     set end-ip **************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**************-**************</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-***************""
     set uuid 0b9ab6f3-c075-42ae-8784-44fb01610575
     set type iprange
     set start-ip **************
     set end-ip ***************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**************-***************</name>
        <ip-set>
          <ip-address>**************-***************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""***************-***************""
     set uuid ef093e1f-72ac-4e4b-a394-b19c65ed4cb0
     set type iprange
     set start-ip ***************
     set end-ip ***************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>***************-***************</name>
        <ip-set>
          <ip-address>***************-***************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-**************""
     set uuid 4f0a57b5-94a0-4a25-94d3-74e3932a2550
     set type iprange
     set start-ip **************
     set end-ip **************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**************-**************</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-**************""
     set uuid 7ff04fa9-e689-42d1-a928-f7d81772fdf5
     set type iprange
     set start-ip **************
     set end-ip **************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**************-**************</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""***************-***************""
     set uuid 37e1420c-76b9-4425-b578-2092ee08d69d
     set type iprange
     set start-ip ***************
     set end-ip ***************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>***************-***************</name>
        <ip-set>
          <ip-address>***************-***************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************""
     set uuid b87d909d-e311-4ba5-b094-b10918d6c68b
     set subnet ************ *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************""
     set uuid 01306bdd-0d62-43bd-901f-20ecc85e6285
     set subnet ************ *************
next
","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************</name>
        <ip-set>
          <ip-address>************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************""
     set uuid 7fcd5799-dfce-47f4-9a8a-97ef7dcfef0d
     set subnet ************* *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************</name>
        <ip-set>
          <ip-address>*************/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""**************-**************""
     set uuid 12a18a04-4822-4421-a76c-1600240e16c3
     set type iprange
     set start-ip **************
     set end-ip **************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>**************-**************</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""************-*************""
     set uuid 599f046c-054f-42c9-b573-6a123050220f
     set type iprange
     set start-ip ************
     set end-ip *************
next
","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>************-*************</name>
        <ip-set>
          <ip-address>************-*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""*************-*************""
     set uuid 02a6f07e-14e9-4a72-b8f3-3b2e917b17f6
     set type iprange
     set start-ip *************
     set end-ip *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>*************-*************</name>
        <ip-set>
          <ip-address>*************-*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.154.105-192.168.154.111""
     set uuid a30c2f55-0fc1-4377-a326-5fd9c552550b
     set type iprange
     set start-ip 192.168.154.105
     set end-ip 192.168.154.111
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.154.105-192.168.154.111</name>
        <ip-set>
          <ip-address>192.168.154.105-192.168.154.111</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.211.0""
     set uuid a6bbd239-28a3-4975-b6fa-c42720df93ec
     set subnet 192.168.211.0 *************
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.211.0</name>
        <ip-set>
          <ip-address>192.168.211.0/*************</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.215.3-192.168.215.5""
     set uuid 9f85e514-28e3-461a-a6c1-33e68bc7375f
     set type iprange
     set start-ip 192.168.215.3
     set end-ip 192.168.215.5
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.215.3-192.168.215.5</name>
        <ip-set>
          <ip-address>192.168.215.3-192.168.215.5</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall address
edit ""192.168.160.55-192.168.160.67""
     set uuid 1d74b4ae-d3be-43c7-9da3-11029371a740
     set type iprange
     set start-ip 192.168.160.55
     set end-ip 192.168.160.67
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-set>
        <name>192.168.160.55-192.168.160.67</name>
        <ip-set>
          <ip-address>192.168.160.55-192.168.160.67</ip-address>
        </ip-set>
      </address-set>
</network-obj>"
"config firewall service group
edit ""group1""
        set member ""********-********00"" ""********"" ""10.2.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>group1</name>
        <address-set>
          <name>********-********00</name>
        </address-set>
        <address-set>
          <name>********</name>
        </address-set>
        <address-set>
          <name>10.2.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""web_servers""
        set member ""172.16.1.10-172.16.1.20"" ""172.16.2.0"" ""172.16.3.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>web_servers</name>
        <address-set>
          <name>172.16.1.10-172.16.1.20</name>
        </address-set>
        <address-set>
          <name>172.16.2.0</name>
        </address-set>
        <address-set>
          <name>172.16.3.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""dmz_zone""
        set member ""192.168.10.5-*************"" ""192.168.20.0"" ""************""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>dmz_zone</name>
        <address-set>
          <name>192.168.10.5-*************</name>
        </address-set>
        <address-set>
          <name>192.168.20.0</name>
        </address-set>
        <address-set>
          <name>************</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""internal_net""
        set member ""10.10.0.1-10.10.0.255"" ""10.20.0.0"" ""*********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>internal_net</name>
        <address-set>
          <name>10.10.0.1-10.10.0.255</name>
        </address-set>
        <address-set>
          <name>10.20.0.0</name>
        </address-set>
        <address-set>
          <name>*********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""test_network""
        set member ""**********-***********"" ""**********"" ""**********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>test_network</name>
        <address-set>
          <name>**********-***********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""office_net""
        set member ""************-************00"" ""************"" ""************""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>office_net</name>
        <address-set>
          <name>************-************00</name>
        </address-set>
        <address-set>
          <name>************</name>
        </address-set>
        <address-set>
          <name>************</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""server_group""
        set member ""**********-**********"" ""*********"" ""*********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>server_group</name>
        <address-set>
          <name>**********-**********</name>
        </address-set>
        <address-set>
          <name>*********</name>
        </address-set>
        <address-set>
          <name>*********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""guest_wifi""
        set member ""***********-*************"" ""***********"" ""***********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>guest_wifi</name>
        <address-set>
          <name>***********-*************</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""prod_env""
        set member ""*************-***************"" ""*************"" ""*************""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>prod_env</name>
        <address-set>
          <name>*************-***************</name>
        </address-set>
        <address-set>
          <name>*************</name>
        </address-set>
        <address-set>
          <name>*************</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""dev_team""
        set member ""**********-***********"" ""**********"" ""**********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>dev_team</name>
        <address-set>
          <name>**********-***********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""branch1""
        set member ""**********-**********00"" ""**********"" ""**********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>branch1</name>
        <address-set>
          <name>**********-**********00</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""branch2""
        set member ""**********-***********"" ""**********"" ""**********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>branch2</name>
        <address-set>
          <name>**********-***********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""vpn_users""
        set member ""*************-*************00"" ""*************"" ""*************""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>vpn_users</name>
        <address-set>
          <name>*************-*************00</name>
        </address-set>
        <address-set>
          <name>*************</name>
        </address-set>
        <address-set>
          <name>*************</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""iot_devices""
        set member ""***********-***********"" ""**********"" ""**********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>iot_devices</name>
        <address-set>
          <name>***********-***********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""security_zone""
        set member ""**********-************"" ""**********"" ""**********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>security_zone</name>
        <address-set>
          <name>**********-************</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""test_env1""
        set member ""*************-**************"" ""*************"" ""*************""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>test_env1</name>
        <address-set>
          <name>*************-**************</name>
        </address-set>
        <address-set>
          <name>*************</name>
        </address-set>
        <address-set>
          <name>*************</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""test_env2""
        set member ""**********-**********00"" ""**********"" ""10.150.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>test_env2</name>
        <address-set>
          <name>**********-**********00</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
        <address-set>
          <name>10.150.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""cloud_servers""
        set member ""172.60.0.10-172.60.0.20"" ""172.60.1.0"" ""172.60.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>cloud_servers</name>
        <address-set>
          <name>172.60.0.10-172.60.0.20</name>
        </address-set>
        <address-set>
          <name>172.60.1.0</name>
        </address-set>
        <address-set>
          <name>172.60.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""backup_net""
        set member ""192.168.250.1-192.168.250.255"" ""*************"" ""192.168.252.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>backup_net</name>
        <address-set>
          <name>192.168.250.1-192.168.250.255</name>
        </address-set>
        <address-set>
          <name>*************</name>
        </address-set>
        <address-set>
          <name>192.168.252.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""mgmt_net""
        set member ""10.250.0.1-10.250.0.50"" ""10.250.1.0"" ""10.250.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>mgmt_net</name>
        <address-set>
          <name>10.250.0.1-10.250.0.50</name>
        </address-set>
        <address-set>
          <name>10.250.1.0</name>
        </address-set>
        <address-set>
          <name>10.250.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""remote_users""
        set member ""172.70.0.1-172.70.0.100"" ""172.70.1.0"" ""172.70.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>remote_users</name>
        <address-set>
          <name>172.70.0.1-172.70.0.100</name>
        </address-set>
        <address-set>
          <name>172.70.1.0</name>
        </address-set>
        <address-set>
          <name>172.70.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""app_servers""
        set member ""192.168.300.1-192.168.300.50"" ""192.168.301.0"" ""192.168.302.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>app_servers</name>
        <address-set>
          <name>192.168.300.1-192.168.300.50</name>
        </address-set>
        <address-set>
          <name>192.168.301.0</name>
        </address-set>
        <address-set>
          <name>192.168.302.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""db_cluster""
        set member ""10.300.0.10-10.300.0.20"" ""10.300.1.0"" ""10.300.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>db_cluster</name>
        <address-set>
          <name>10.300.0.10-10.300.0.20</name>
        </address-set>
        <address-set>
          <name>10.300.1.0</name>
        </address-set>
        <address-set>
          <name>10.300.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""monitor_net""
        set member ""**********-************"" ""**********"" ""**********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>monitor_net</name>
        <address-set>
          <name>**********-************</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""qa_team""
        set member ""192.168.400.1-192.168.400.100"" ""192.168.401.0"" ""192.168.402.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>qa_team</name>
        <address-set>
          <name>192.168.400.1-192.168.400.100</name>
        </address-set>
        <address-set>
          <name>192.168.401.0</name>
        </address-set>
        <address-set>
          <name>192.168.402.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""staging_env""
        set member ""10.400.0.1-10.400.0.50"" ""10.400.1.0"" ""10.400.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>staging_env</name>
        <address-set>
          <name>10.400.0.1-10.400.0.50</name>
        </address-set>
        <address-set>
          <name>10.400.1.0</name>
        </address-set>
        <address-set>
          <name>10.400.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""frontend_net""
        set member ""**********-**********00"" ""**********"" ""**********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>frontend_net</name>
        <address-set>
          <name>**********-**********00</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
        <address-set>
          <name>**********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""backend_net""
        set member ""192.168.500.1-192.168.500.255"" ""192.168.501.0"" ""192.168.502.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>backend_net</name>
        <address-set>
          <name>192.168.500.1-192.168.500.255</name>
        </address-set>
        <address-set>
          <name>192.168.501.0</name>
        </address-set>
        <address-set>
          <name>192.168.502.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""api_servers""
        set member ""10.500.0.10-10.500.0.50"" ""10.500.1.0"" ""10.500.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>api_servers</name>
        <address-set>
          <name>10.500.0.10-10.500.0.50</name>
        </address-set>
        <address-set>
          <name>10.500.1.0</name>
        </address-set>
        <address-set>
          <name>10.500.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""load_balancers""
        set member ""***********-************"" ""***********"" ""***********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>load_balancers</name>
        <address-set>
          <name>***********-************</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""cache_nodes""
        set member ""192.168.600.1-192.168.600.100"" ""192.168.601.0"" ""192.168.602.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>cache_nodes</name>
        <address-set>
          <name>192.168.600.1-192.168.600.100</name>
        </address-set>
        <address-set>
          <name>192.168.601.0</name>
        </address-set>
        <address-set>
          <name>192.168.602.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""log_servers""
        set member ""10.600.0.1-10.600.0.50"" ""10.600.1.0"" ""10.600.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>log_servers</name>
        <address-set>
          <name>10.600.0.1-10.600.0.50</name>
        </address-set>
        <address-set>
          <name>10.600.1.0</name>
        </address-set>
        <address-set>
          <name>10.600.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""payment_net""
        set member ""192.168.700.1-192.168.700.50"" ""192.168.701.0"" ""192.168.702.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>payment_net</name>
        <address-set>
          <name>192.168.700.1-192.168.700.50</name>
        </address-set>
        <address-set>
          <name>192.168.701.0</name>
        </address-set>
        <address-set>
          <name>192.168.702.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""media_servers""
        set member ""10.700.0.10-10.700.0.100"" ""10.700.1.0"" ""10.700.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>media_servers</name>
        <address-set>
          <name>10.700.0.10-10.700.0.100</name>
        </address-set>
        <address-set>
          <name>10.700.1.0</name>
        </address-set>
        <address-set>
          <name>10.700.2.0</name>
        </address-set>
      </address-group>
</network-obj>
"
"config firewall service group
edit ""cdn_nodes""
        set member ""***********-************"" ""***********"" ""***********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>cdn_nodes</name>
        <address-set>
          <name>***********-************</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""storage_net""
        set member ""192.168.800.1-192.168.800.255"" ""192.168.801.0"" ""192.168.802.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>storage_net</name>
        <address-set>
          <name>192.168.800.1-192.168.800.255</name>
        </address-set>
        <address-set>
          <name>192.168.801.0</name>
        </address-set>
        <address-set>
          <name>192.168.802.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""auth_servers""
        set member ""10.800.0.1-10.800.0.20"" ""10.800.1.0"" ""10.800.2.0""
next
","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>auth_servers</name>
        <address-set>
          <name>10.800.0.1-10.800.0.20</name>
        </address-set>
        <address-set>
          <name>10.800.1.0</name>
        </address-set>
        <address-set>
          <name>10.800.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""dmz_servers""
        set member ""***********-***********00"" ""***********"" ""***********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>dmz_servers</name>
        <address-set>
          <name>***********-***********00</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""test_servers""
        set member ""192.168.900.1-192.168.900.50"" ""192.168.901.0"" ""192.168.902.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>test_servers</name>
        <address-set>
          <name>192.168.900.1-192.168.900.50</name>
        </address-set>
        <address-set>
          <name>192.168.901.0</name>
        </address-set>
        <address-set>
          <name>192.168.902.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""proxy_net""
        set member ""10.900.0.10-10.900.0.100"" ""10.900.1.0"" ""10.900.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>proxy_net</name>
        <address-set>
          <name>10.900.0.10-10.900.0.100</name>
        </address-set>
        <address-set>
          <name>10.900.1.0</name>
        </address-set>
        <address-set>
          <name>10.900.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""gateway_net""
        set member ""***********-************"" ""***********"" ""***********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>gateway_net</name>
        <address-set>
          <name>***********-************</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""infra_net""
        set member ""192.168.1000.1-192.168.1000.255"" ""192.168.1001.0"" ""192.168.1002.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>infra_net</name>
        <address-set>
          <name>192.168.1000.1-192.168.1000.255</name>
        </address-set>
        <address-set>
          <name>192.168.1001.0</name>
        </address-set>
        <address-set>
          <name>192.168.1002.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""support_team""
        set member ""10.1000.0.1-10.1000.0.50"" ""10.1000.1.0"" ""10.1000.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>support_team</name>
        <address-set>
          <name>10.1000.0.1-10.1000.0.50</name>
        </address-set>
        <address-set>
          <name>10.1000.1.0</name>
        </address-set>
        <address-set>
          <name>10.1000.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""ops_team""
        set member ""***********-***********00"" ""***********"" ""***********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>ops_team</name>
        <address-set>
          <name>***********-***********00</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""dev_net1""
        set member ""192.168.1100.1-192.168.1100.50"" ""192.168.1101.0"" ""192.168.1102.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>dev_net1</name>
        <address-set>
          <name>192.168.1100.1-192.168.1100.50</name>
        </address-set>
        <address-set>
          <name>192.168.1101.0</name>
        </address-set>
        <address-set>
          <name>192.168.1102.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""dev_net2""
        set member ""10.1100.0.10-10.1100.0.100"" ""10.1100.1.0"" ""10.1100.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>dev_net2</name>
        <address-set>
          <name>10.1100.0.10-10.1100.0.100</name>
        </address-set>
        <address-set>
          <name>10.1100.1.0</name>
        </address-set>
        <address-set>
          <name>10.1100.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""test_net1""
        set member ""***********-************"" ""***********"" ""***********""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>test_net1</name>
        <address-set>
          <name>***********-************</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
        <address-set>
          <name>***********</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""test_net2""
        set member ""192.168.1200.1-192.168.1200.255"" ""192.168.1201.0"" ""192.168.1202.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>test_net2</name>
        <address-set>
          <name>192.168.1200.1-192.168.1200.255</name>
        </address-set>
        <address-set>
          <name>192.168.1201.0</name>
        </address-set>
        <address-set>
          <name>192.168.1202.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service group
edit ""prod_net1""
        set member ""10.1200.0.1-10.1200.0.100"" ""10.1200.1.0"" ""10.1200.2.0""
next","<network-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:network-obj"">
      <address-group>
        <name>prod_net1</name>
        <address-set>
          <name>10.1200.0.1-10.1200.0.100</name>
        </address-set>
        <address-set>
          <name>10.1200.1.0</name>
        </address-set>
        <address-set>
          <name>10.1200.2.0</name>
        </address-set>
      </address-group>
</network-obj>"
"config firewall service custom
edit ""HTTP""
        set tcp-portrange 80
next
edit ""DNS""
        set tcp-portrange 53
        set udp-portrange 53
next
edit ""SSH""
        set tcp-portrange 22
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>HTTP</name>
        <tcp>
          <dest-port>80</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>DNS</name>
        <tcp>
          <dest-port>53</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SSH</name>
        <tcp>
          <dest-port>22</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""HTTPS""
        set tcp-portrange 443
next
edit ""NTP""
        set udp-portrange 123
next
edit ""SMTP""
        set tcp-portrange 25 587
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>HTTPS</name>
        <tcp>
          <dest-port>443</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NTP</name>
        <udp>
          <dest-port>123</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>SMTP</name>
        <tcp>
          <dest-port>25-587</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""RDP""
        set tcp-portrange 3389
next
edit ""SNMP""
        set udp-portrange 161 162
next
edit ""FTP""
        set tcp-portrange 20 21
        set udp-portrange 20 21
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>RDP</name>
        <tcp>
          <dest-port>3389</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SNMP</name>
        <udp>
          <dest-port>161,162</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>FTP</name>
        <tcp>
          <dest-port>20-21</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""LDAP""
        set category ""Authentication""
        set tcp-portrange 389
next
edit ""DHCP""
        set udp-portrange 67 68
next
edit ""IMAP""
        set tcp-portrange 143
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>LDAP</name>
        <tcp>
          <dest-port>389</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>DHCP</name>
        <udp>
          <dest-port>67,68</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>IMAP</name>
        <tcp>
          <dest-port>143</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""MYSQL""
        set tcp-portrange 3306
next
edit ""SYSLOG""
        set tcp-portrange 514
        set udp-portrange 514
next
edit ""POP3""
        set tcp-portrange 110
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>MYSQL</name>
        <tcp>
          <dest-port>3306</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SYSLOG</name>
        <tcp>
          <dest-port>514</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>POP3</name>
        <tcp>
          <dest-port>110</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""TELNET""
        set tcp-portrange 23
next
edit ""TFTP""
        set udp-portrange 69
next
edit ""SMB""
        set tcp-portrange 445
        set udp-portrange 445
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>TELNET</name>
        <tcp>
          <dest-port>23</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TFTP</name>
        <udp>
          <dest-port>69</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>SMB</name>
        <tcp>
          <dest-port>445</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""SIP""
        set tcp-portrange 5060
        set udp-portrange 5060
next
edit ""RADIUS""
        set category ""Authentication""
        set udp-portrange 1812 1813
next
edit ""VNC""
        set tcp-portrange 5900
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>SIP</name>
        <tcp>
          <dest-port>5060</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RADIUS</name>
        <udp>
          <dest-port>1812,1813</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>VNC</name>
        <tcp>
          <dest-port>5900</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""PPTP""
        set tcp-portrange 1723
next
edit ""GRE""
        set protocol-number 47
next
edit ""L2TP""
        set udp-portrange 1701
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>PPTP</name>
        <tcp>
          <dest-port>1723</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>GRE</name>
      </service-set>
      <service-set>
        <name>L2TP</name>
        <udp>
          <dest-port>1701</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""ORACLE""
        set tcp-portrange 1521
next
edit ""NETBIOS""
        set tcp-portrange 137 138
        set udp-portrange 137 138
next
edit ""SMTPS""
        set tcp-portrange 465
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>ORACLE</name>
        <tcp>
          <dest-port>1521</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NETBIOS</name>
        <tcp>
          <dest-port>137-138</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SMTPS</name>
        <tcp>
          <dest-port>465</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""POSTGRES""
        set tcp-portrange 5432
next
edit ""RIP""
        set udp-portrange 520
next
edit ""IMAPS""
        set tcp-portrange 993
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>POSTGRES</name>
        <tcp>
          <dest-port>5432</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RIP</name>
        <udp>
          <dest-port>520</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>IMAPS</name>
        <tcp>
          <dest-port>993</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""MSSQL""
        set tcp-portrange 1433
next
edit ""BGP""
        set tcp-portrange 179
next
edit ""RTSP""
        set tcp-portrange 554
        set udp-portrange 554
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>MSSQL</name>
        <tcp>
          <dest-port>1433</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>BGP</name>
        <tcp>
          <dest-port>179</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RTSP</name>
        <tcp>
          <dest-port>554</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""IKE""
        set udp-portrange 500
next
edit ""ESP""
        set protocol-number 50
next
edit ""AH""
        set protocol-number 51
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>IKE</name>
        <udp>
          <dest-port>500</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>ESP</name>
      </service-set>
      <service-set>
        <name>AH</name>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""H323""
        set tcp-portrange 1720
next
edit ""SNMPTRAP""
        set udp-portrange 162
next
edit ""SCCP""
        set tcp-portrange 2000
        set udp-portrange 2000
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>H323</name>
        <tcp>
          <dest-port>1720</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SNMPTRAP</name>
        <udp>
          <dest-port>162</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>SCCP</name>
        <tcp>
          <dest-port>2000</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"Input:
config firewall service custom
edit ""KERBEROS2""
        set category ""Authentication""
        set tcp-portrange 88
        set udp-portrange 88
next
edit ""RTP""
        set udp-portrange 5004 5005
next
edit ""XMPP""
        set tcp-portrange 5222
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>KERBEROS2</name>
        <tcp>
          <dest-port>88</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RTP</name>
        <udp>
          <dest-port>5004,5005</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>XMPP</name>
        <tcp>
          <dest-port>5222</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""TACACS""
        set tcp-portrange 49
next
edit ""OSPF""
        set protocol-number 89
next
edit ""STUN""
        set udp-portrange 3478
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>TACACS</name>
        <tcp>
          <dest-port>49</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>OSPF</name>
      </service-set>
      <service-set>
        <name>STUN</name>
        <udp>
          <dest-port>3478</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""REDIS""
        set tcp-portrange 6379
next
edit ""MEMCACHED""
        set tcp-portrange 11211
next
edit ""RSYNC""
        set tcp-portrange 873
        set udp-portrange 873
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>REDIS</name>
        <tcp>
          <dest-port>6379</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>MEMCACHED</name>
        <tcp>
          <dest-port>11211</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RSYNC</name>
        <tcp>
          <dest-port>873</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""ELASTIC""
        set tcp-portrange 9200
next
edit ""KAFKA""
        set tcp-portrange 9092
next
edit ""ZOOKEEPER""
        set tcp-portrange 2181
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>ELASTIC</name>
        <tcp>
          <dest-port>9200</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>KAFKA</name>
        <tcp>
          <dest-port>9092</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>ZOOKEEPER</name>
        <tcp>
          <dest-port>2181</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""MONGO""
        set tcp-portrange 27017
next
edit ""CASSANDRA""
        set tcp-portrange 9042
        set udp-portrange 9042
next
edit ""NFS""
        set tcp-portrange 2049
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>MONGO</name>
        <tcp>
          <dest-port>27017</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>CASSANDRA</name>
        <tcp>
          <dest-port>9042</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NFS</name>
        <tcp>
          <dest-port>2049</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""GIT""
        set tcp-portrange 9418
next
edit ""SVN""
        set tcp-portrange 3690
next
edit ""WEBDAV""
        set tcp-portrange 2077
        set udp-portrange 2077
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>GIT</name>
        <tcp>
          <dest-port>9418</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SVN</name>
        <tcp>
          <dest-port>3690</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>WEBDAV</name>
        <tcp>
          <dest-port>2077</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""LDAP-SSL""
        set tcp-portrange 636
next
edit ""RADIUS-ACCT""
        set category ""Authentication""
        set udp-portrange 1813
next
edit ""SIP-TLS""
        set tcp-portrange 5061
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>LDAP-SSL</name>
        <tcp>
          <dest-port>636</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RADIUS-ACCT</name>
        <udp>
          <dest-port>1813</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>SIP-TLS</name>
        <tcp>
          <dest-port>5061</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""SFTP""
        set tcp-portrange 22
next
edit ""SCP""
        set tcp-portrange 22
next
edit ""TFTP2""
        set udp-portrange 690
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>SFTP</name>
        <tcp>
          <dest-port>22</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SCP</name>
        <tcp>
          <dest-port>22</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>TFTP2</name>
        <udp>
          <dest-port>690</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""MQTT""
        set tcp-portrange 1883
next
edit ""AMQP""
        set tcp-portrange 5672
        set udp-portrange 5672
next
edit ""COAP""
        set udp-portrange 5683
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>MQTT</name>
        <tcp>
          <dest-port>1883</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>AMQP</name>
        <tcp>
          <dest-port>5672</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>COAP</name>
        <udp>
          <dest-port>5683</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""HTTP2""
        set tcp-portrange 8080
next
edit ""HTTPS2""
        set tcp-portrange 8443
next
edit ""DNS2""
        set udp-portrange 5353
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>HTTP2</name>
        <tcp>
          <dest-port>8080</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>HTTPS2</name>
        <tcp>
          <dest-port>8443</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>DNS2</name>
        <udp>
          <dest-port>5353</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""VNC2""
        set tcp-portrange 5901
next
edit ""RDP2""
        set tcp-portrange 3388
        set udp-portrange 3388
next
edit ""SNMP2""
        set udp-portrange 161
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>VNC2</name>
        <tcp>
          <dest-port>5901</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RDP2</name>
        <tcp>
          <dest-port>3388</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SNMP2</name>
        <udp>
          <dest-port>161</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""MYSQL2""
        set tcp-portrange 3307
next
edit ""POSTGRES2""
        set tcp-portrange 5433
next
edit ""ORACLE2""
        set tcp-portrange 1522
        set udp-portrange 1522
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>MYSQL2</name>
        <tcp>
          <dest-port>3307</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>POSTGRES2</name>
        <tcp>
          <dest-port>5433</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>ORACLE2</name>
        <tcp>
          <dest-port>1522</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""NTP2""
        set udp-portrange 1234
next
edit ""SYSLOG2""
        set tcp-portrange 5140
        set udp-portrange 5140
next
edit ""SMTP2""
        set tcp-portrange 2525
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>NTP2</name>
        <udp>
          <dest-port>1234</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>SYSLOG2</name>
        <tcp>
          <dest-port>5140</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SMTP2</name>
        <tcp>
          <dest-port>2525</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""FTP2""
        set tcp-portrange 2121
next
edit ""SMB2""
        set tcp-portrange 4450
next
edit ""NETBIOS2""
        set udp-portrange 1370 1380
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>FTP2</name>
        <tcp>
          <dest-port>2121</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SMB2</name>
        <tcp>
          <dest-port>4450</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NETBIOS2</name>
        <udp>
          <dest-port>1370,1380</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""RADIUS2""
        set category ""Authentication""
        set udp-portrange 18120 18130
next
edit ""LDAP2""
        set tcp-portrange 3890
next
edit ""KERBEROS3""
        set tcp-portrange 880
        set udp-portrange 880
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>RADIUS2</name>
        <udp>
          <dest-port>18120,18130</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>LDAP2</name>
        <tcp>
          <dest-port>3890</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>KERBEROS3</name>
        <tcp>
          <dest-port>880</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""SIP2""
        set tcp-portrange 50600
        set udp-portrange 50600
next
edit ""RTSP2""
        set tcp-portrange 5540
next
edit ""H323-2""
        set tcp-portrange 17200
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>SIP2</name>
        <tcp>
          <dest-port>50600</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RTSP2</name>
        <tcp>
          <dest-port>5540</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>H323-2</name>
        <tcp>
          <dest-port>17200</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""PPTP2""
        set tcp-portrange 17230
next
edit ""L2TP2""
        set udp-portrange 17010
next
edit ""IKE2""
        set udp-portrange 5000
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>PPTP2</name>
        <tcp>
          <dest-port>17230</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>L2TP2</name>
        <udp>
          <dest-port>17010</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>IKE2</name>
        <udp>
          <dest-port>5000</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""REDIS2""
        set tcp-portrange 63790
next
edit ""MEMCACHED2""
        set tcp-portrange 112110
        set udp-portrange 112110
next
edit ""RSYNC2""
        set tcp-portrange 8730
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>REDIS2</name>
        <tcp>
          <dest-port>63790</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>MEMCACHED2</name>
        <tcp>
          <dest-port>112110</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RSYNC2</name>
        <tcp>
          <dest-port>8730</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""ELASTIC2""
        set tcp-portrange 92000
next
edit ""KAFKA2""
        set tcp-portrange 90920
next
edit ""ZOOKEEPER2""
        set tcp-portrange 21810
        set udp-portrange 21810
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>ELASTIC2</name>
        <tcp>
          <dest-port>92000</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>KAFKA2</name>
        <tcp>
          <dest-port>90920</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>ZOOKEEPER2</name>
        <tcp>
          <dest-port>21810</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""MONGO2""
        set tcp-portrange 270170
next
edit ""CASSANDRA2""
        set tcp-portrange 90420
next
edit ""NFS2""
        set tcp-portrange 20490
        set udp-portrange 20490
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>MONGO2</name>
        <tcp>
          <dest-port>270170</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>CASSANDRA2</name>
        <tcp>
          <dest-port>90420</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NFS2</name>
        <tcp>
          <dest-port>20490</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""GIT2""
        set tcp-portrange 94180
next
edit ""SVN2""
        set tcp-portrange 36900
        set udp-portrange 36900
next
edit ""WEBDAV2""
        set tcp-portrange 20770
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>GIT2</name>
        <tcp>
          <dest-port>94180</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SVN2</name>
        <tcp>
          <dest-port>36900</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>WEBDAV2</name>
        <tcp>
          <dest-port>20770</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""MQTT2""
        set tcp-portrange 18830
next
edit ""AMQP2""
        set tcp-portrange 56720
next
edit ""COAP2""
        set udp-portrange 56830
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>MQTT2</name>
        <tcp>
          <dest-port>18830</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>AMQP2</name>
        <tcp>
          <dest-port>56720</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>COAP2</name>
        <udp>
          <dest-port>56830</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""HTTP3""
        set tcp-portrange 8081
next
edit ""HTTPS3""
        set tcp-portrange 8444
        set udp-portrange 8444
next
edit ""DNS3""
        set udp-portrange 5354
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>HTTP3</name>
        <tcp>
          <dest-port>8081</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>HTTPS3</name>
        <tcp>
          <dest-port>8444</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>DNS3</name>
        <udp>
          <dest-port>5354</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""VNC3""
        set tcp-portrange 5902
next
edit ""RDP3""
        set tcp-portrange 3389
next
edit ""SNMP3""
        set udp-portrange 1620
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>VNC3</name>
        <tcp>
          <dest-port>5902</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RDP3</name>
        <tcp>
          <dest-port>3389</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SNMP3</name>
        <udp>
          <dest-port>1620</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""MYSQL3""
        set tcp-portrange 3308
        set udp-portrange 3308
next
edit ""POSTGRES3""
        set tcp-portrange 5434
next
edit ""ORACLE3""
        set tcp-portrange 1523
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>MYSQL3</name>
        <tcp>
          <dest-port>3308</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>POSTGRES3</name>
        <tcp>
          <dest-port>5434</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>ORACLE3</name>
        <tcp>
          <dest-port>1523</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""NTP3""
        set udp-portrange 1235
next
edit ""SYSLOG3""
        set tcp-portrange 5141
next
edit ""SMTP3""
        set tcp-portrange 2526
        set udp-portrange 2526
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>NTP3</name>
        <udp>
          <dest-port>1235</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>SYSLOG3</name>
        <tcp>
          <dest-port>5141</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SMTP3</name>
        <tcp>
          <dest-port>2526</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""FTP3""
        set tcp-portrange 2122
next
edit ""SMB3""
        set tcp-portrange 4451
        set udp-portrange 4451
next
edit ""NETBIOS3""
        set udp-portrange 1371 1381
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>FTP3</name>
        <tcp>
          <dest-port>2122</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SMB3</name>
        <tcp>
          <dest-port>4451</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NETBIOS3</name>
        <udp>
          <dest-port>1371,1381</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""RADIUS3""
        set category ""Authentication""
        set udp-portrange 18121 18131
next
edit ""LDAP3""
        set tcp-portrange 3891
next
edit ""KERBEROS4""
        set tcp-portrange 881
        set udp-portrange 881
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>RADIUS3</name>
        <udp>
          <dest-port>18121,18131</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>LDAP3</name>
        <tcp>
          <dest-port>3891</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>KERBEROS4</name>
        <tcp>
          <dest-port>881</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""SIP3""
        set tcp-portrange 50601
next
edit ""RTSP3""
        set tcp-portrange 5541
        set udp-portrange 5541
next
edit ""H323-3""
        set tcp-portrange 17201
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>SIP3</name>
        <tcp>
          <dest-port>50601</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RTSP3</name>
        <tcp>
          <dest-port>5541</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>H323-3</name>
        <tcp>
          <dest-port>17201</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""PPTP3""
        set tcp-portrange 17231
next
edit ""L2TP3""
        set udp-portrange 17011
next
edit ""IKE3""
        set udp-portrange 5001
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>PPTP3</name>
        <tcp>
          <dest-port>17231</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>L2TP3</name>
        <udp>
          <dest-port>17011</dest-port>
        </udp>
      </service-set>
      <service-set>
        <name>IKE3</name>
        <udp>
          <dest-port>5001</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""REDIS3""
        set tcp-portrange 63791
        set udp-portrange 63791
next
edit ""MEMCACHED3""
        set tcp-portrange 112111
next
edit ""RSYNC3""
        set tcp-portrange 8731
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>REDIS3</name>
        <tcp>
          <dest-port>63791</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>MEMCACHED3</name>
        <tcp>
          <dest-port>112111</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RSYNC3</name>
        <tcp>
          <dest-port>8731</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""ELASTIC3""
        set tcp-portrange 92001
next
edit ""KAFKA3""
        set tcp-portrange 90921
next
edit ""ZOOKEEPER3""
        set tcp-portrange 21811
        set udp-portrange 21811
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>ELASTIC3</name>
        <tcp>
          <dest-port>92001</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>KAFKA3</name>
        <tcp>
          <dest-port>90921</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>ZOOKEEPER3</name>
        <tcp>
          <dest-port>21811</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""MONGO3""
        set tcp-portrange 270171
next
edit ""CASSANDRA3""
        set tcp-portrange 90421
        set udp-portrange 90421
next
edit ""NFS3""
        set tcp-portrange 20491
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>MONGO3</name>
        <tcp>
          <dest-port>270171</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>CASSANDRA3</name>
        <tcp>
          <dest-port>90421</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NFS3</name>
        <tcp>
          <dest-port>20491</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""GIT3""
        set tcp-portrange 94181
next
edit ""SVN3""
        set tcp-portrange 36901
next
edit ""WEBDAV3""
        set tcp-portrange 20771
        set udp-portrange 20771
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>GIT3</name>
        <tcp>
          <dest-port>94181</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SVN3</name>
        <tcp>
          <dest-port>36901</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>WEBDAV3</name>
        <tcp>
          <dest-port>20771</dest-port>
        </tcp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""MQTT3""
        set tcp-portrange 18831
next
edit ""AMQP3""
        set tcp-portrange 56721
        set udp-portrange 56721
next
edit ""COAP3""
        set udp-portrange 56831
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>MQTT3</name>
        <tcp>
          <dest-port>18831</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>AMQP3</name>
        <tcp>
          <dest-port>56721</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>COAP3</name>
        <udp>
          <dest-port>56831</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""HTTP4""
        set tcp-portrange 8082
next
edit ""HTTPS4""
        set tcp-portrange 8445
next
edit ""DNS4""
        set udp-portrange 5355
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>HTTP4</name>
        <tcp>
          <dest-port>8082</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>HTTPS4</name>
        <tcp>
          <dest-port>8445</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>DNS4</name>
        <udp>
          <dest-port>5355</dest-port>
        </udp>
      </service-set>
</service-obj>"
"config firewall service custom
edit ""VNC4""
        set tcp-portrange 5903
        set udp-portrange 5903
next
edit ""RDP4""
        set tcp-portrange 3390
next
edit ""SNMP4""
        set udp-portrange 1621
next","<service-obj xmlns=""urn:ruijie:ntos:params:xml:ns:yang:service-obj"">
      <service-set>
        <name>VNC4</name>
        <tcp>
          <dest-port>5903</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RDP4</name>
        <tcp>
          <dest-port>3390</dest-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SNMP4</name>
        <udp>
          <dest-port>1621</dest-port>
        </udp>
      </service-set>
</service-obj>"
