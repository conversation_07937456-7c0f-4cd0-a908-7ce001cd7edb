
import gc
import psutil
import os

class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.initial_memory = self.get_memory_usage()
    
    def get_memory_usage(self):
        """获取当前内存使用量（MB）"""
        return self.process.memory_info().rss / 1024 / 1024
    
    def optimize_memory(self, stage_name="unknown"):
        """优化内存使用"""
        try:
            # 强制垃圾回收
            gc.collect()
            
            current_memory = self.get_memory_usage()
            print(f"🔧 内存优化 [{stage_name}]: {current_memory:.1f}MB")
            
            # 如果内存使用超过500MB，进行深度清理
            if current_memory > 500:
                print(f"⚠️ 内存使用过高 ({current_memory:.1f}MB)，进行深度清理")
                
                # 多次垃圾回收
                for _ in range(3):
                    gc.collect()
                
                after_memory = self.get_memory_usage()
                saved_memory = current_memory - after_memory
                print(f"✅ 内存清理完成，节省 {saved_memory:.1f}MB")
            
            return current_memory
            
        except Exception as e:
            print(f"❌ 内存优化失败: {e}")
            return 0

def with_memory_optimization(stage_name="unknown"):
    """内存优化装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            optimizer = MemoryOptimizer()
            
            try:
                # 执行前优化
                optimizer.optimize_memory(f"{stage_name}_start")
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 执行后优化
                optimizer.optimize_memory(f"{stage_name}_end")
                
                return result
                
            except Exception as e:
                # 异常时也进行内存清理
                optimizer.optimize_memory(f"{stage_name}_error")
                raise e
        
        return wrapper
    return decorator

class ChunkedConfigProcessor:
    """分块配置处理器"""
    
    def __init__(self, chunk_size=1000):
        self.chunk_size = chunk_size
        self.optimizer = MemoryOptimizer()
    
    def process_config_in_chunks(self, config_lines):
        """分块处理配置"""
        print(f"🔄 分块处理配置，总行数: {len(config_lines)}")
        
        chunks = []
        for i in range(0, len(config_lines), self.chunk_size):
            chunk = config_lines[i:i + self.chunk_size]
            chunks.append(chunk)
        
        print(f"📊 分为 {len(chunks)} 个块，每块最多 {self.chunk_size} 行")
        
        processed_results = []
        
        for i, chunk in enumerate(chunks):
            print(f"🔄 处理第 {i+1}/{len(chunks)} 块")
            
            try:
                # 处理当前块
                chunk_result = self.process_single_chunk(chunk, i+1)
                processed_results.append(chunk_result)
                
                # 每处理5个块进行一次内存优化
                if (i + 1) % 5 == 0:
                    self.optimizer.optimize_memory(f"chunk_{i+1}")
                
            except Exception as e:
                print(f"❌ 处理第 {i+1} 块失败: {e}")
                # 继续处理下一块
                processed_results.append({"status": "failed", "error": str(e)})
        
        return processed_results
    
    def process_single_chunk(self, chunk, chunk_number):
        """处理单个块"""
        # 这里实现具体的块处理逻辑
        return {
            "status": "success",
            "chunk_number": chunk_number,
            "lines_processed": len(chunk)
        }

# MEMORY_OPTIMIZATION_APPLIED

def enhanced_interface_mapping_validation(parsed_interfaces, mapping_data):
    """增强的接口映射验证"""
    try:
        # 获取解析出的接口列表
        parsed_interface_names = set()
        if isinstance(parsed_interfaces, list):
            for interface in parsed_interfaces:
                if isinstance(interface, dict):
                    name = interface.get('name') or interface.get('raw_name') or interface.get('interface_name')
                    if name:
                        parsed_interface_names.add(name)
        
        # 获取映射文件中的接口
        mapping_interfaces = set()
        if isinstance(mapping_data, dict):
            interface_mappings = mapping_data.get('interface_mappings', {})
            if isinstance(interface_mappings, dict):
                mapping_interfaces = set(interface_mappings.keys())
        
        # 智能验证：只验证映射文件中存在且配置中也存在的接口
        valid_mappings = {}
        invalid_mappings = []
        
        for mapped_interface in mapping_interfaces:
            if mapped_interface in parsed_interface_names:
                # 接口存在，映射有效
                valid_mappings[mapped_interface] = interface_mappings[mapped_interface]
            else:
                # 接口不存在，但不一定是错误（可能是可选接口）
                print(f"⚠️ 映射的接口 {mapped_interface} 在配置中未找到，将跳过")
        
        # 只有当所有解析的接口都没有映射时才报错
        if parsed_interface_names and not valid_mappings:
            return False, "没有找到有效的接口映射"
        
        print(f"✅ 接口映射验证通过: {len(valid_mappings)} 个有效映射")
        return True, valid_mappings
        
    except Exception as e:
        print(f"❌ 接口映射验证异常: {e}")
        return False, f"验证异常: {str(e)}"

# INTERFACE_MAPPING_OPTIMIZED
# -*- coding: utf-8 -*-
"""
转换服务 - 应用服务层的核心转换协调器
保持与现有convert_config函数的完全兼容性
"""

import os
import datetime
from typing import Dict, Any, Optional
from engine.utils.logger import log, user_log, flush_all_logs
from engine.utils.i18n import _, init_i18n, set_debug_mode
from engine.infrastructure.config.config_manager import ConfigManager
from engine.infrastructure.templates.template_manager import TemplateManager
from engine.infrastructure.yang.yang_manager import YangManager
from engine.business.workflows.conversion_workflow import ConversionWorkflow


class ConversionService:
    """
    转换服务
    应用服务层的核心组件，负责协调整个配置转换流程
    保持与现有convert_config函数的API兼容性
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化转换服务
        
        Args:
            config_manager: 配置管理器实例，如果为None则创建新实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.template_manager = TemplateManager(self.config_manager)
        self.yang_manager = YangManager(self.config_manager)
        self.conversion_workflow = ConversionWorkflow(
            self.config_manager, self.template_manager, self.yang_manager)

        log(_("conversion_service.initialized"), "info")
    
    def convert(self, cli_file: str, mapping_file: str = "data/mappings/interface_mapping.json",
                model: str = "z5100s", version: str = "R10P2",
                output_file: str = "data/output/result.xml",
                vendor: str = "fortigate", encrypt_output: Optional[str] = None,
                engine_debug_log: Optional[str] = None, engine_user_log: Optional[str] = None,
                verbose: bool = True, language: Optional[str] = None,
                interface_mapping_file: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        执行配置转换 - 保持与现有convert_config函数的完全兼容性
        
        Args:
            cli_file: 配置文件路径
            mapping_file: 接口映射文件路径
            model: 设备型号
            version: 设备版本
            output_file: 输出文件路径
            vendor: 厂商标识
            encrypt_output: 加密输出文件路径
            engine_debug_log: 引擎调试日志文件路径
            engine_user_log: 引擎用户日志文件路径
            verbose: 是否打印详细日志
            language: 国际化语言设置
            **kwargs: 其他参数
            
        Returns: Dict[str, Any]: 转换结果
        """
        try:
            # 初始化国际化设置
            if language:
                init_i18n(language)

            # 设置调试模式
            if engine_debug_log:
                set_debug_mode(True)

            # 处理参数兼容性 - 支持interface_mapping_file参数
            if interface_mapping_file and not mapping_file:
                mapping_file = interface_mapping_file
            elif interface_mapping_file and mapping_file != interface_mapping_file:
                # 如果两个参数都提供且不同，优先使用interface_mapping_file
                log(_("conversion_service.using_interface_mapping_file"), "info",
                    file=interface_mapping_file)
                mapping_file = interface_mapping_file

            log(_("conversion_service.start_conversion"), "info",
                vendor=vendor, model=model, version=version)

            # 验证输入参数
            validation_result = self._validate_conversion_parameters(
                cli_file, mapping_file, model, version, vendor)
            if not validation_result['valid']:
                return {
                    "success": False,
                    "error": validation_result['error'],
                    "message": validation_result['message']
                }
            
            # 获取XML模板路径
            try:
                template_path = self.template_manager.get_template_path(model, version)
                log(_("conversion_service.template_found"), "info", path=template_path)
            except FileNotFoundError as e:
                error_msg = _("conversion_service.template_not_found", error=str(e))
                log(error_msg, "error")
                return {
                    "success": False,
                    "error": error_msg,
                    "message": _("error.cannot_find_template")
                }
            
            # 验证YANG模型可用性
            try:
                yang_dir = self.yang_manager.get_yang_model_dir(model, version)
                log(_("conversion_service.yang_models_found"), "info", dir=yang_dir)
            except FileNotFoundError as e:
                error_msg = _("conversion_service.yang_models_not_found", error=str(e))
                log(error_msg, "warning")
                # YANG模型不是必需的，继续转换
            
            # 使用新的工作流系统执行转换
            # 工作流内部会委托给现有的convert_config函数，保持逻辑不变
            conversion_params = {
                'cli_file': cli_file,
                'mapping_file': mapping_file,
                'interface_mapping_file': mapping_file,  # 添加兼容性参数
                'model': model,
                'version': version,
                'output_file': output_file,
                'vendor': vendor,
                'encrypt_output': encrypt_output,
                'engine_debug_log': engine_debug_log,
                'engine_user_log': engine_user_log,
                'verbose': verbose,
                'language': language,
                **kwargs
            }

            log(_("conversion_service.prepared_conversion_params"), "debug",
                cli_file=cli_file, model=model, version=version, vendor=vendor)

            result = self.conversion_workflow.execute_conversion(conversion_params)
            
            # 增强结果信息
            if isinstance(result, dict) and result.get("success"):
                result["conversion_service_version"] = "2.0"
                result["architecture"] = "layered"
                
                # YANG验证现在完全在新架构管道中处理，不再在这里重复验证
                yang_validation_info = result.get("yang_validation", {})

                # 添加调试信息以诊断数据结构
                log(_("conversion_service.yang_validation_debug_info"), "debug",
                    yang_validation_keys=list(yang_validation_info.keys()),
                    yang_validation_data=str(yang_validation_info)[:200])

                # 修复状态检查逻辑 - 适配实际的数据结构
                # 检查多种可能的键名以确保兼容性
                yang_performed = (yang_validation_info.get("performed", False) or
                                yang_validation_info.get("validation_performed", False))
                yang_passed = (yang_validation_info.get("passed", False) or
                             yang_validation_info.get("validation_passed", False))
                yang_skipped = (yang_validation_info.get("skipped", False) or
                              yang_validation_info.get("validation_skipped", False))

                if yang_performed:
                    if yang_passed:
                        log(_("conversion_service.yang_validation_completed_successfully"), "info")
                    else:
                        log(_("conversion_service.yang_validation_completed_with_errors"), "warning")
                elif yang_skipped:
                    skip_reason = yang_validation_info.get("reason", "unknown")
                    log(_("conversion_service.yang_validation_skipped"), "info", reason=skip_reason)
                else:
                    # 只有在真正无法确定状态时才警告
                    if yang_validation_info:
                        log(_("conversion_service.yang_validation_status_unclear"), "debug",
                            available_keys=list(yang_validation_info.keys()))
                    else:
                        log(_("conversion_service.yang_validation_not_configured"), "debug")
            
            log(_("conversion_service.conversion_completed"), "info")
            return result
            
        except Exception as e:
            error_msg = _("conversion_service.conversion_failed", error=str(e))
            log(error_msg, "error")
            return {
                "success": False,
                "error": error_msg,
                "message": _("error.conversion_process_failed")
            }
        finally:
            # 确保所有日志被刷新
            flush_all_logs()
    
    def _validate_conversion_parameters(self, cli_file: str, mapping_file: str,
                                      model: str, version: str, vendor: str) -> Dict[str, Any]:
        """
        验证转换参数的有效性
        
        Args:
            cli_file: 配置文件路径
            mapping_file: 接口映射文件路径
            model: 设备型号
            version: 设备版本
            vendor: 厂商标识
            
        Returns: Dict[str, Any]: 验证结果
        """
        # 检查厂商支持
        if not self.config_manager.is_vendor_supported(vendor):
            return {
                "valid": False,
                "error": _("conversion_service.unsupported_vendor", vendor=vendor),
                "message": _("error.vendor_not_supported")
            }
        
        # 检查设备型号和版本支持
        if not self.config_manager.is_model_version_supported(model, version):
            return {
                "valid": False,
                "error": _("conversion_service.unsupported_model_version", model=model, version=version),
                "message": _("error.model_version_not_supported")
            }
        
        # 检查配置文件是否存在
        if not os.path.exists(cli_file):
            return {
                "valid": False,
                "error": _("conversion_service.cli_file_not_found", file=cli_file),
                "message": _("error.config_file_not_found")
            }
        
        # 检查接口映射文件是否存在（如果指定了的话）
        if mapping_file and not os.path.exists(mapping_file):
            log(_("conversion_service.mapping_file_not_found"), "warning", file=mapping_file)
            # 映射文件不存在不是致命错误，可以继续转换
        
        return {"valid": True}
