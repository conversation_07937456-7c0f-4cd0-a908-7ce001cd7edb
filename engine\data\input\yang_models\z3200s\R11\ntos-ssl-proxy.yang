module ntos-ssl-proxy {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ssl-proxy";
  prefix ntos-ssl-proxy;

  import ntos {
    prefix ntos;
  }

  import ntos-system {
    prefix ntos-system;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";

  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";

  description
    "Ruijie NTOS SSL proxy module.";

  revision 2022-06-16 {
    description
      "Create.";
    reference "";
  }

  identity ssl-proxy {
    base ntos-types:SERVICE_LOG_ID;
    description
      "SSL proxy service.";
  }

  typedef policy-type {
    type enumeration {
      enum decrypt {
        description
          "Indicate the configuration of the decrypt policy.";
      }
      enum no-decrypt {
        description
          "Indicate the configuration of the no-decrypt policy.";
      }
    }
    description
      "Indicate the action of the SSL proxy policy.";
  }

  typedef cert-info-type {
    type ntos-types:ntos-obj-description-type;
    description
      "The certificate infomation type.";
  }

  typedef cert-type {
    type enumeration {
      enum ca-cert {
        description
          "The ca-certificate.";
      }
      enum server-cert {
        description
          "The server certificate.";
      }
    }
    description
      "The certificate type.";
  }

  typedef cert-format-type {
    type enumeration {
      enum crt {
        description
          "The CRT file format.";
      }
      enum pem {
        description
          "The PEM file format.";
      }
      enum p12 {
        description
          "The PKCS#12 file format.";
      }
      enum pfx {
        description
          "The PKCS#12 file format.";
      }
    }
    description
      "The enumeration of certificate file format type.";
  }

  typedef wildcard-domain-name {
    type string {
      length "1..255";
      pattern '(\*?(([a-zA-Z0-9\-_]){0,61}[a-zA-Z0-9])?\.' +
              '(([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61})?[a-zA-Z0-9]\.){0,10}' +
              '([a-zA-Z0-9_]([a-zA-Z0-9\-_]){0,61}[a-zA-Z0-9])?\*?)|' +
              '\.';
      ntos-ext:nc-cli-shortdesc "<host-name>";
    }
    description
      "The wildcard-domain-name type represents DNS domain names that supports wildcards.
       Wildcards can only be at the beginning or the end,and can not appear consecutively.";
  }

  grouping profile {
    description
      "Indicate the profile of the SSL proxy.";

    list profile {
      key "name";
      description
        "Indicate the configuration of the SSL proxy profile.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of the SSL proxy profile.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "The description of the SSL proxy profile.";
      }

      choice decryption-type {
        description
          "Indicate the decrypted type of the SSL proxy profile.";
        case protect-server {
          description
            "Indicate the configuration of the server protection scenario.";

          container inbound {
            description
              "Indicate the protection to server.";
            presence "Enable outbound";

            leaf server-cert {
              type ntos-types:ntos-obj-name-type;
              description
                "Indicate an imported server certificate
                in the server protection scenario.";
            }
          }
        }

        case protect-client {
          description
            "Indicate the configuration of the client protection scenario.";

          container outbound {
            presence "Enable outbound";
            description
              "Indicate the protection to client.";
          }
        }
      }
    }
  }

  grouping contain-obj {
    description
      "The object of the SSL proxy policy.";

    list source-zone {
      key "name";
      description
        "The name of source zone.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
        description
          "The name of source zone.";
      }
    }

    list dest-zone {
      key "name";
      description
        "The name of destination zone.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
        description
          "The name of destination zone.";
      }
    }

    list source-network {
      key "name";
      description
        "The name of source network.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
        description
          "The name of source network.";
      }
    }

    list dest-network {
      key "name";
      description
        "The name of destination network.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
        description
          "The name of destination network.";
      }
    }

    list service {
      key "name";
      description
        "The name of service.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
        description
          "The name of service.";
      }
    }

    list app {
      key "name";
      description
        "The name of application.";
      ntos-ext:nc-cli-one-liner;
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
        description
          "The name of application.";
      }
    }
  }

  grouping policy {
    description
      "Indicate the policy configuration of the SSL proxy.";

    list policy {
      key "name";
      ordered-by user;
      description
        "Indicate the policy configuration of the SSL proxy.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "The name of the SSL proxy policy.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "The description of the SSL proxy policy.";
      }

      leaf policy-id {
        type uint64;
        config false;
        description
          "The ID of policy.";
      }

      leaf position-id {
        type uint32;
        config false;
        description
          "The position ID of policy.";
      }

      leaf enabled {
        type boolean;
        default true;
        description
          "Enable or disable the SSL proxy policy.";
      }

      uses contain-obj;

      leaf action {
        type policy-type;
        must "(current() = 'decrypt' and count(../ssl-proxy-profile) = 1) or " +
             "current() = 'no-decrypt'" {
          error-message "The profile must exist in the decryption type.";
        }
        default no-decrypt;
        description
          "Decrypt or no-decrypt the matched policy.";
      }

      leaf ssl-proxy-profile {
        type ntos-types:ntos-obj-name-type;
        must "../../profile[name = current()]" {
          error-message "The profile referenced by policy should exist.";
        }
        description
          "The name of the SSL proxy profile.";
      }
    }
  }

  grouping whitelist-application {
    list application {
      key "name";
      description
        "Indicate the whitelist of applications.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Indicate the name of application.";
      }
    }
  }

  grouping whitelist-hostname {
    list hostname {
      key "name";
      description
        "Indicate the whitelist of domain names.";

      leaf name {
        type wildcard-domain-name;
        description
          "Indicate the domain name.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "Indicate the description of domain name.";
        }

      leaf enabled {
        type boolean;
        default true;
        description
          "Enable or disable the whitelist.";
      }
    }
  }

  grouping whitelist-detail {
    container white-app {
      description
        "Indicate the application whitelist.";
      uses whitelist-application;
    }

    container white-host {
      description
        "Indicate the hostname whitelist.";
      uses whitelist-hostname;
    }
  }

  grouping whitelist {
    description
      "Indicate the SSL proxy whitelist information.";

    container ssl-proxy-whitelist {
      description
        "Indicate the SSL proxy whitelist information.";
      container predefined-whitelist {
        description
          "Indicate the configuration of the predefined whitelist.";
        uses whitelist-detail;
      }

      container custom-whitelist {
        description
          "Indicate the configuration of the custom whitelist.";
        uses whitelist-detail;
      }
    }
  }

  grouping cert {
    description
      "The grouping of certificate.";
    container ca-cert {
      description
        "Indicate the SSL proxy ca-certificate.";

      leaf trust-cert {
        type cert-info-type;
        default "default_ca";
        description
          "Specifies a trusted certificate to SSL proxy.";
      }
    }
  }

  augment "/ntos:config/ntos-system:system" {
    description
      "Configuration of the SSL proxy whitelist.";
    uses whitelist;
  }

  augment "/ntos:state/ntos-system:system" {
    description
      "The state of the SSL proxy whitelist.";
    uses whitelist;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Configuration of the SSL proxy.";
    container ssl-proxy {
      description
        "Configuration of the SSL proxy.";
      uses policy;
      uses profile;
      uses cert;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "The state of the SSL proxy.";
    container ssl-proxy {
      config false;
      description
        "The state of the SSL proxy.";
      uses policy;
      uses profile;
      uses cert;
    }
  }

  rpc ssl-proxy-policy-show {
    description
      "Show the SSL proxy policy.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Indicate the vrf.";
      }

      leaf fuzzy-match {
        type boolean;
        default true;
        description
          "Indicate the type of search.";
      }

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Indicate searched name.";
      }

      leaf start {
        type uint16;
        default 0;
        description
          "The index of page start.";
      }

      leaf end {
        type uint16;
        default 100;
        description
          "The index of page end.";
      }
    }

    output {
      leaf policy-sum {
          type uint16;
          description
            "The total number of policy.";
      }

      list policy {
        key "name";
        description
          "Indicate the policy configuration of the SSL proxy.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of the SSL proxy policy.";
        }

        leaf description {
          type ntos-types:ntos-obj-description-type;
          description
            "The description of the SSL proxy policy.";
        }

        leaf policy-id {
          type uint16;
          description
            "The policy ID of policy.";
        }

        leaf position-id {
          type uint16;
          description
            "The position of policy.";
        }

        leaf enabled {
          type boolean;
          description
            "Enable or disable the SSL proxy policy.";
        }

        list source-zone {
          key "name";
          description
            "The name of source zone.";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description
              "The name of source zone.";
          }
        }

        list dest-zone {
          key "name";
          description
            "The name of destination zone.";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description
              "The name of destination zone.";
          }
        }

        list source-network {
          key "name";
          description
            "The name of source network.";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description
              "The name of source network.";
          }
        }

        list dest-network {
          key "name";
          description
            "The name of destination network.";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description
              "The name of destination network.";
          }
        }

        list service {
          key "name";
          description
            "The name of service.";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description
              "The name of service.";
          }
        }

        list app {
          key "name";
          description
            "Tne name of application.";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description
              "Tne name of application.";
          }
          leaf name-i18n {
            type ntos-types:ntos-obj-name-type;
            description
              "The name of application for internationalization.";
          }
        }

        leaf action {
          type policy-type;
          description
            "Decrypt or no-decrypt the matched policy.";
        }

        leaf profile {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of the SSL proxy profile.";
        }

        leaf match-count {
          type uint64;
          description
            "The matching times for the policy.";
        }

        leaf tip-msg {
          type string;
          description
            "Indicate the error message of the configuration.";
        }
      }
    }

    ntos-ext:nc-cli-show "ssl-proxy policy";
    ntos-api:internal;
  }

  rpc ssl-proxy-profile-show {
    description
      "Show the SSL proxy profile.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Indicate the vrf.";
      }

      leaf fuzzy-match {
        type boolean;
        default true;
        description
          "Indicate the type of search.";
      }

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Indicate searched name.";
      }

      leaf start {
        type uint16;
        default 0;
        description
          "The index of page start.";
      }

      leaf end {
        type uint16;
        default 60;
        description
          "The index of page end.";
      }
    }

    output {
      leaf profile-sum {
          type uint16;
          description
            "The total number of profile.";
      }

      list profile {
        key "name";
        description
          "Indicate the profile configuration of the SSL proxy.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of the SSL proxy profile.";
        }

        leaf description {
          type ntos-types:ntos-obj-description-type;
          description
            "The description of the SSL proxy profile.";
        }

        leaf protect-type {
          type string {
            length "0..10";
          }
          description
            "The protected type of the SSL proxy profile.";
        }

        leaf server-cert {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of certificate in the server protection scenario.";
        }

        leaf tip-msg {
          type string;
          description
            "Indicate the error message of the configuration.";
        }
      }
    }

    ntos-ext:nc-cli-show "ssl-proxy profile";
  }

  rpc ssl-proxy-policy-stat-clear {
    description
      "Clear hit statistics of the SSL proxy policy.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Indicate the vrf.";
      }

      leaf policy-id-list {
        type string;
        description
          "Indicate the ID list of policy.";
      }
    }
    ntos-ext:nc-cli-cmd "ssl-proxy policy stat-clear";
    ntos-api:internal;
  }

  rpc ssl-proxy-policy-name {
    description
      "Show the name of the SSL proxy policy.";
    input {
      leaf policy-id-list {
        type string;
        description
          "Indicate the ID list of policy.";
      }
    }

    output {
      list policy-name {
        description
          "The policy name list.";

        leaf id {
          type uint16;
          description
            "The ID of the SSL proxy policy.";
        }

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of the SSL proxy policy.";
        }
      }
    }
    ntos-ext:nc-cli-show "ssl-proxy policy get-name";
  }

  rpc ssl-proxy-ca-certificate-generate {
    description
      "The rpc of generate ca-certificate.";
    input {
      leaf name {
        type cert-info-type;
        mandatory true;
        description
          "The ca-certificate name.";
      }

      leaf common-name {
        type cert-info-type;
        mandatory true;
        description
          "The common name of the certificate issuer.";
      }

      leaf domain {
        type cert-info-type;
        description
          "The domain of the certificate issuer.";
      }

      leaf email {
        type cert-info-type;
        description
          "The email of the certificate issuer.";
      }

      leaf country-name {
        type cert-info-type;
        description
          "The country name of the certificate issuer.";
      }

      leaf state-name {
        type cert-info-type;
        description
          "The province or state name of the certificate issuer.";
      }

      leaf locality {
        type cert-info-type;
        description
          "The locality of the certificate issuer.";
      }

      leaf organization {
        type cert-info-type;
        description
          "The organization of the certificate issuer.";
      }

      leaf organizational-unit {
        type cert-info-type;
        description
          "The organizational unit of the certificate issuer.";
      }

      leaf overwrite {
        type boolean;
        default false;
        description
          "Indicate overwrite an existing certicate with the same name.";
      }
    }

    ntos-ext:nc-cli-cmd "ssl-proxy cert generate ca-cert";
  }

  rpc ssl-proxy-certificate-show {
    description
      "The rpc to show all certificates.";

    input {
      leaf type {
        type cert-type;
        mandatory true;
        description
          "The certificate type.";
      }

      leaf name {
        type cert-info-type;
        description
          "The certificate name.";
      }

      leaf start {
        type uint32;
        default 0;
        description
          "The start offset.";
      }

      leaf end {
        type uint32;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
        description
          "The end offset.";
      }

      leaf fuzzy-match {
        type boolean;
        default false;
        description
          "Whether to perform fuzzy match.";
      }
    }

    output {
      leaf cert-sum {
          type uint32;
          description
            "The total number of certs.";
      }
      list cert {
        description
          "The certificate list.";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The certificate name.";
        }

        leaf issuer {
          type string;
          description
            "The issuer of the certificate.";
        }

        leaf subject {
          type string;
          description
            "The subject of the certificate.";
        }

        leaf not-before {
          type string;
          description
            "The not-before day of the certificate.";
        }

        leaf not-after {
          type string;
          description
            "The not-after day of the certificate.";
        }

        leaf version {
          type string;
          description
            "The version of the certificate.";
        }

        leaf serial-number {
          type string;
          description
            "The serial number of the certificate.";
        }

        leaf extensions {
          type string;
          description
            "The extensions of the certificate.";
          ntos-ext:nc-cli-stdout;
        }
      }
    }

    ntos-ext:nc-cli-show "ssl-proxy cert";
  }

  rpc ssl-proxy-certificate-del {
    description
      "The rpc to delete a certificate.";

    input {
      leaf type {
        type cert-type;
        mandatory true;
        description
          "The certificate type.";
      }

      leaf name {
        type cert-info-type;
        mandatory true;
        description
          "The certificate name.";
      }
    }

    ntos-ext:nc-cli-cmd "ssl-proxy cert delete";
  }

  rpc ssl-proxy-import-certificate {
    description
      "The rpc to import a certificate to system.";

    input {
      leaf name {
        type cert-info-type;
        mandatory true;
        description
          "The name of the certificate.";
      }

      leaf type {
        type cert-type;
        mandatory true;
        description
          "The certificate type.";
      }

      leaf format {
        type cert-format-type;
        mandatory true;
        description
          "The format of the certificate file format.";
      }

      leaf password {
        type string;
        mandatory true;
        description
          "The password to decrypt the certificate.";
      }

      leaf overwrite {
        type boolean;
        default false;
        description
          "Indicate overwrite an existing certicate with the same name.";
      }
    }
    ntos-ext:nc-cli-cmd "ssl-proxy cert import";
  }

  rpc ssl-proxy-export-certificate {
    description
      "The rpc to export a certificate from system.";

    input {
      leaf name {
        type cert-info-type;
        mandatory true;
        description
          "The name of the certificate.";
      }

      leaf type {
        type cert-type;
        mandatory true;
        description
          "The certificate type.";
      }

      leaf format {
        type cert-format-type;
        mandatory true;
        description
          "The format of the certificate file format.";
      }

      leaf password {
        type string;
        description
          "The password to decrypt the certificate.";
      }
    }

    output {
      leaf filename {
        type string;
        description
          "The filename of the certificate.";
      }
    }
    ntos-ext:nc-cli-cmd "ssl-proxy cert export";
  }

  rpc ssl-proxy-whitelist-app-show {
    description
      "The rpc to show all whitelist application in system.";

    output {
      container predefined {
        uses whitelist-application;
      }
      container custom {
        uses whitelist-application;
      }
    }
    ntos-ext:nc-cli-show "ssl-proxy whitelist app";
    ntos-api:internal;
  }

  rpc ssl-proxy-whitelist-app-show-info {
    description
      "The rpc to show all whitelist application in system.";

    output {
      container predefined {
        list application {
          description
            "Indicate the whitelist of applications.";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description
              "Indicate the name of application.";
          }
          leaf name-i18n {
            type ntos-types:ntos-obj-name-type;
            description
              "Indicate the name of application for internationalization.";
          }
        }
      }
      container custom {
        list application {
          description
            "Indicate the whitelist of applications.";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            description
              "Indicate the name of application.";
          }
          leaf name-i18n {
            type ntos-types:ntos-obj-name-type;
            description
              "Indicate the name of application for internationalization.";
          }
        }
      }
    }
    ntos-ext:nc-cli-show "ssl-proxy whitelist app-info";
    ntos-api:internal;
  }

  rpc ssl-proxy-whitelist-domain-show {
    description
      "The rpc to show all whitelist domain in system.";
    input {
      leaf name {
        type string;
          description
            "Specific domain name or fuzzy matching keywords.";
      }
      leaf fuzzy-match {
        type boolean;
        default false;
        description
          "Whether to perform fuzzy match.";
      }
      leaf start {
        type uint32;
        default 0;
        description
          "The start offset.";
      }
      leaf end {
        type uint32;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
        description
          "The end offset.";
      }
    }

    output {
      leaf hostname-sum {
        type uint32;
        description
          "The total number of hostname.";
      }

      container predefined {
        uses whitelist-hostname;
      }

      container custom {
        uses whitelist-hostname;
      }
    }
    ntos-ext:nc-cli-show "ssl-proxy whitelist domain";
    ntos-api:internal;
  }
}
