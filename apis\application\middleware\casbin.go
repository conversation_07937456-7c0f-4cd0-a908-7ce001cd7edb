package middleware

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/auth"
	"irisAdminApi/service/dao/user/duser"
	"net/http"

	"github.com/casbin/casbin/v2"
	"github.com/iris-contrib/middleware/jwt"
	"github.com/kataras/iris/v12"
)

// Casbin is the auth services which contains the casbin enforcer.
type Casbin struct {
	enforcer *casbin.SyncedEnforcer
}

func New() *Casbin {
	return &Casbin{enforcer: easygorm.GetEasyGormEnforcer()}
}

func (c *Casbin) ServeHTTP(ctx iris.Context) {
	var token string
	if ctx.Values().Get("jwt") == nil {
		token = ctx.GetCookie("AccessToken")
	} else {
		token = ctx.Values().Get("jwt").(*jwt.Token).Raw
	}

	if token == "" {
		if check, _ := c.Check(ctx.Request(), "0"); !check {
			ctx.JSON(response.NewResponse(response.AuthActionErr.Code, nil, fmt.Sprintf("你未拥有当前操作权限，请联系管理员")))
			ctx.StopExecution()
			return
		}
	} else {
		sess, err := duser.Check(token)
		if err != nil {
			if !errors.Is(err, auth.ErrTokenInvalid) {
				logging.ErrorLogger.Error(err)
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
				ctx.StopExecution()
				return
			}
			ctx.JSON(response.NewResponse(response.AuthErr.Code, nil, response.AuthErr.Msg))
			ctx.StopExecution()
			return
		}

		if check, _ := c.Check(ctx.Request(), sess.UserId); !check {
			ctx.JSON(response.NewResponse(response.AuthActionErr.Code, nil, fmt.Sprintf("你未拥有当前操作权限，请联系管理员")))
			ctx.StopExecution()
			return
		}

	}

	ctx.Next()
}

// Check checks the username, request's method and path and
// returns true if permission grandted otherwise false.
func (c *Casbin) Check(r *http.Request, userId string) (bool, error) {
	method := r.Method
	path := r.URL.Path
	ok, err := c.enforcer.Enforce(userId, path, method)
	if err != nil {
		logging.ErrorLogger.Error("验证权限报错：%v;%s-%s-%s", err.Error(), userId, path, method)
		return false, err
	}

	logging.DebugLogger.Debugf("权限：%s-%s-%s-%v", userId, path, method, ok)

	if !ok {
		return ok, errors.New(fmt.Sprintf("你未拥有当前操作权限，请联系管理员"))
	}
	return ok, nil
}
