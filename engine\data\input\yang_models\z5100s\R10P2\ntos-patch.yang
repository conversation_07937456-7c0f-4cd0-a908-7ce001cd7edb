module ntos-patch {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:patch";
  prefix ntos-patch;

  import ntos-api {
    prefix ntos-api;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS patch module.";

  revision 2022-03-16 {
    description
      "Initial version.";
    reference "";
  }

  identity patch-mng {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Patch ui service.";
  }

  grouping package-grouping {
    leaf package {
      type string;
      description
        "The package name.";
    }
  }

  grouping patch-info {
    description "Patch information.";

    uses package-grouping;

    leaf patch-form {
      type uint8;
      description
        "The form of patch.";
    }

    leaf release-time {
      type string;
      description
        "The release time of package.";
    }

    leaf description {
      type string;
      description
        "The description of package.";
    }

    leaf source-type {
      type string;
        description
        "Package from cloud or local.";
    }

    leaf status {
      type string;
        description
        "Package been installed, or unstalled.";
    }

    leaf install-time {
      type string;
      description
        "When is the package installed.";
    }
  }

  rpc patch-control {
    description
      "Patch control.";
    input {
      ntos-extensions:nc-cli-exclusive;

      container online-install {
        description
          "Install the package.";
        uses package-grouping;
      }

      container local-install {
        description
          "Install the package.";
        uses package-grouping;
      }

      container uninstall {
        description
          "Uninstall the package.";
        uses package-grouping;
      }

      container dump-hotpatch {
        leaf dump-angle {
            type uint8 {
              range "0..1";
            }
            description
              "0 means process angle, 1 means patch angle.";
        }
      }

      leaf auto-install {
        type boolean;
        description
          "Enable or disable auto install.";
      }
    }
    
    ntos-extensions:nc-cli-cmd "patch control";
    ntos-extensions:nc-cli-command-no-pager;
    ntos-api:internal;
  }
  
  rpc patch-sync-time {
    description
      "patch control.";
    input {
      leaf hour {
        type uint8{
          range "0..23";
        }
        description
          "Patch sync-time hour.";
      }

      leaf minute {
        type uint8{
          range "0..59";
        }
        description
          "Patch sync-time minute.";
      }
      ntos-extensions:nc-cli-no-name;
    }
    
    ntos-extensions:nc-cli-cmd "patch sync-time";
    ntos-extensions:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-patch-uninstall-count {
    description
      "Display uninstall patch num.";

    output {
      leaf count {
        type uint16;
        description
          "Command output count.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-show "patch uninstall count";
    ntos-api:internal;
  }

  rpc show-patch-install-state {
      description
      "Query install result.";

    output {
      leaf errnum {
        type uint8;
        description
          "Command output errnum.";
      }
      leaf errstring {
         type string;
         description
           "Command output errstring.";
      }
    }
    ntos-extensions:nc-cli-show "patch install state";
    ntos-api:internal;

  }

  rpc show-patch-manager-stat {
    description
      "Display patch manager stat.";

    output {
      leaf auto-install {
        type boolean;
        description
          "Show auto-install configure.";
      }
      leaf busying {
        type boolean;
        description
          "If system is busying.";
      }

      leaf hour {
        type uint8{
          range "0..23";
        }
        description
          "Patch sync-time hour.";
      }

      leaf minute {
        type uint8{
          range "0..59";
        }
        description
          "Patch sync-time minute.";
      }
    }
    ntos-extensions:nc-cli-show "patch manager stat";
    ntos-api:internal;
  }

  rpc show-patch-state {
    description
      "Display patch state.";
    input {
       container all {
        description
          "All patch state";

            leaf start {
              type uint16 {
                range "1..65535";
              }
              description
                "The index of patch start";
            }

            leaf end {
              type uint16;
              must "current() >= ../start" {
                error-message "The end value must be larger than the start value.";
              }

              description
                  "The index of patch end.";
            }
      }

      uses package-grouping;
    }
    output {
      leaf patch-total {
        description
        "Total number of patch suitable the system .";
        type uint16;
      }
      list patch-list {
        key "package";
        uses patch-info;
        description
          "Patch state.";
      }
    }
    ntos-extensions:nc-cli-show "patch";
    ntos-api:internal;
  }
 }
