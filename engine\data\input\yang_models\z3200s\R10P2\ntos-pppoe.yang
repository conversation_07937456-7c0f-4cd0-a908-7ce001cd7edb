module ntos-pppoe {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:pppoe";
  prefix ntos-pppoe;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS PPPoE module.";

  revision 2024-06-17 {
    description
      "Add ppp-mtu config.";
    reference "";
  }

  revision 2021-11-30 {
    description
      "Initial version.";
    reference "";
  }

  identity ppp {
    base ntos-types:INTERFACE_TYPE;
    description
      "PPP interface.";
  }

  identity pppoe-client {
    base ntos-types:SERVICE_LOG_ID;
    description
      "PPPoE client service.";
  }

  typedef ppp-authentication-method {
    type enumeration {
      enum pap {
        value 1;
        description "PAP authentication.";
      }
      enum chap {
        value 2;
        description "CHAP authentication.";
      }
      enum ms-chap {
        value 3;
        description "MS-CHAP authentication.";
      }
      enum ms-chap-v2 {
        value 4;
        description "MS-CHAP-V2 authentication.";
      }
    }
    description "PPP authentication method.";
  }

  grouping cmd-output-buffer {
    leaf buffer {
      description
        "Command output buffer since last request.";

      type string;
      ntos-extensions:nc-cli-stdout;
      ntos-extensions:nc-cli-hidden;
    }
  }

  grouping ppp-authentication-config {
    description
      "PPP authentication configuration.";

    leaf negotiation-timeout {
      type uint32 {
        range "1..32767";
      }
      default "3";
      units second;
      description "PPP negotiation timeout period.";
    }

    leaf-list authentication {
      type ppp-authentication-method;
      max-elements "4";
      description "PPP authentication method.";
    }

    leaf lcp-echo-interval {
      type uint32 {
        range "0..32767";
      }
      default "10";
      units second;
      description "LCP echo interval.";
    }

    leaf lcp-echo-retries {
      type uint32;
      default "10";
      description "LCP echo retry times.";
    }
  }

  grouping pppoe-client-config {
    description
      "Configuration data for PPPoE client interfaces.";

    leaf enabled {
      type boolean;
      must "current() = 'false' or (count(../user) = 1 and count(../password) = 1)" {
        error-message "User name and password must be set when PPPoE is enabled.";
      }
      default "true";
      description
        "Enable or disable PPPoE.";
    }

    leaf tunnel {
      type uint8 {
        range "0..127";
      }
      must "count(../../../../../*[local-name()='physical']/ipv4/
            pppoe/*[local-name()='connection']/*[local-name()='tunnel'][text()=current()]) +
            count(../../../../../*[local-name()='vlan']/ipv4/
            pppoe/*[local-name()='connection']/*[local-name()='tunnel'][text()=current()]) = 1" {
        error-message "The tunnel id has been used.";
      }
      description
        "PPPoE tunnel id (0-127), which is associated with the PPP interface. (ppp0-ppp127).";
    }

    leaf user {
      type string {
          length "3..31";
      }
      description
        "User name to dial in, 3-31 characters.";
    }


    leaf password {
      type string;
      description
        "User password, 1-52 characters.";
    }

    leaf gateway {
      type boolean;
      default "true";
      description
        "Enable or disable gateway.";
    }

    leaf distance {
      type uint8 {
        range "1..255";
      }
      description
        "Distance value for the gateway.";
    }

    leaf timeout {
      type uint32;
      default "5";
      units second;
      description
        "Maximum time to wait for a response from PPPoE
         server.";
    }

    leaf retries {
      type uint32;
      default "3";
      description
        "How many retries to connect peer before considering
         peer is lost.";
    }

    container ppp {
      description
        "PPP parameters configuration.";

      uses ppp-authentication-config;
    }
    leaf ppp-mtu {
      type uint16;
      default 1492;
      description
        "Set the max transmission unit size in octets.";
    }

    leaf description {
      description
        "An textual description of the pppoe.";
      type ntos-types:ntos-obj-description-type;
    }
  }

  grouping pppoe-client-state {
    description "PPPoE client state information.";

    leaf tunnel {
      type uint8;
      description
        "PPPoe tunnel id.";
    }

    leaf tunnel-interface {
      type ntos-types:ifname;
      description
        "PPPoE tunnel interface.";
    }

    leaf link-interface {
      type ntos-types:ifname;
      description
        "Interface of the PPPoE tunnel link.";
    }

    leaf state {
      type enumeration {
        enum disconnected {
          description
            "PPPoE connection is disconnected.";
        }
        enum connected {
          description
            "PPPoE connection is connected.";
        }
        enum negotiated {
          description
            "PPPoE connection is being negotiated.";
        }
        enum disabled {
          description
            "PPPoE connection is disabled.";
        }
      }
    }

    leaf err-code {
      type uint8;
      description
        "PPPoE error code.";
    }

    leaf uptime {
      type string;
      description
        "The period since the session is established.";
    }

    leaf local-address {
      type ntos-inet:ipv4-address;
      description "Local IPv4 address.";
    }

    leaf peer-address {
      type ntos-inet:ipv4-address;
      description "Peer IPv4 address.";
    }

    leaf peer-netmask {
      type ntos-inet:ipv4-address;
      description "Peer IPv4 netmask.";
    }

    leaf dns-primary {
      type ntos-inet:ipv4-address;
      description "Peer DNS Primary.";
    }

    leaf dns-secondary {
      type ntos-inet:ipv4-address;
      description "Peer DNS Secondary.";
    }

    leaf wins-primary {
      type ntos-inet:ipv4-address;
      description "Peer WINS Primary.";
    }

    leaf wins-secondary {
      type ntos-inet:ipv4-address;
      description "Peer WINS Secondary.";
    }

    leaf ppp-mtu {
      type uint16;
      description
        "PPPoE MTU.";
    }

    leaf ppp-mru {
      type uint16;
      description
        "PPPoE negotiated MRU.";
    }

    leaf ppp-state {
      type enumeration {
        enum UP {
          description
            "Ready to pass packets.";
        }
        enum DOWN {
          description
            "Not ready to pass packets and not in some test mode.";
        }
        enum TESTING {
          description
            "In some test mode.";
        }
      }
      description
        "The current state of the pppoe interface.";
    }
  }

  rpc pppoe-control {
    description
      "PPPoE client control.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf connection {
        type uint8 {
          range "0..127";
        }
        mandatory true;
        description
          "PPPoE tunnel id (0-127).";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/
           *[local-name()='ipv4']/pppoe/connection/tunnel |
           /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/
           *[local-name()='ipv4']/pppoe/connection/tunnel";
      }

      leaf action {
        type enumeration {
          enum up {
            description
              "Bring up PPPoE connection.";
          }
          enum down {
            description
              "Shut down PPPoE connection.";
          }
        }
        mandatory true;
        ntos-extensions:nc-cli-no-name;
      }
    }
    ntos-extensions:nc-cli-cmd "pppoe control";
    ntos-extensions:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-pppoe-connection {
    description
      "PPPoE client connection.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf link-interface {
        type ntos-types:ifname;
        description
          "Interface of the PPPoE tunnel link.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='name'] |
           /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='name']";
        ntos-extensions:nc-cli-group "subcommand";
      }

      leaf tunnel {
        type uint8 {
          range "0..127";
        }
        description
          "PPPoE tunnel id (0-127).";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/
           *[local-name()='ipv4']/pppoe/connection/tunnel |
           /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/
           *[local-name()='ipv4']/pppoe/connection/tunnel";
        ntos-extensions:nc-cli-group "subcommand";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-show "pppoe connection";
    ntos-api:internal;
  }

  rpc show-pppoe-connection-state {
    description
      "PPPoE client connection state.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf link-interface {
        type ntos-types:ifname;
        description
          "Interface of the PPPoE tunnel link.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='name'] |
           /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='name']";
        ntos-extensions:nc-cli-group "subcommand";
      }

      leaf tunnel {
        type uint8 {
          range "0..127";
        }
        description
          "PPPoE tunnel id (0-127).";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/
           *[local-name()='ipv4']/pppoe/connection/tunnel |
           /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/
           *[local-name()='ipv4']/pppoe/connection/tunnel";
        ntos-extensions:nc-cli-group "subcommand";
      }
    }
    output {
      list connection {
        key "tunnel";
        uses pppoe-client-state;
        description
          "PPPoE connection state.";
      }
    }
    ntos-extensions:nc-cli-show "pppoe connection state";
    ntos-api:internal;
  }

  rpc show-pppoe-multi-list {
    description
      "Show pppoe multi-dial list.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf physical {
        description
          "The name of physical interface.";
        type string;
      }

      leaf type {
        description
          "The type of pppoe multi-dial list.";
        type enumeration {
          enum detail;
        }
      }

      leaf filter {
        description
          "Filter of pppoe multi-dial list.";
        type string;
      }

      leaf start {
        description
          "Start offset of result.";
        type uint32;
      }

      leaf end {
        description
          "End offset of result.";
        type uint32;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-show "pppoe multi list";
  }

  rpc show-pppoe-multi-ref {
    description
      "Show pppoe multi-dial reference.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      leaf interface {
        description
          "The name of interface.";
        type string;
      }

      leaf start {
        description
          "Start offset of result.";
        type uint32;
      }

      leaf end {
        description
          "End offset of result.";
        type uint32;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-show "pppoe multi reference";
  }

  rpc show-pppoe-available-tunnel {
    description
      "Show pppoe available tunnel.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf number {
        description
          "The number of available tunnel to show.";
        type uint32;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-show "pppoe available tunnel";
  }

  rpc show-pppoe-multi-dial {
    description
      "Show pppoe multi dial.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-show "pppoe multi dial";
  }

  rpc show-pppoe-connection-debug {
    description
      "PPPoE client connection debug.";
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf debug-type {
        type enumeration {
            enum route;
            enum connection;
          }
      }

      leaf link-interface {
        type ntos-types:ifname;
        description
          "Interface of the PPPoE tunnel link.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='name'] |
           /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='name']";
        ntos-extensions:nc-cli-group "subcommand";
      }

      leaf tunnel {
        type uint8 {
          range "0..127";
        }
        description
          "PPPoE tunnel id (0-127).";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/
           *[local-name()='ipv4']/pppoe/connection/tunnel |
           /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/
           *[local-name()='ipv4']/pppoe/connection/tunnel";
        ntos-extensions:nc-cli-group "subcommand";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
      }
    }
    ntos-extensions:nc-cli-show "pppoe connection debug";
    ntos-extensions:nc-cli-hidden;
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    container pppoe {
      container multi-dial {
        leaf enabled {
          description
            "Enable or disable multi-dial.";
          type boolean;
            default "false";
        }
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    container pppoe {
      container multi-dial {
        leaf enabled {
          description
            "Enable or disable multi-dial.";
          type boolean;
        }
      }
    }
  }
}
