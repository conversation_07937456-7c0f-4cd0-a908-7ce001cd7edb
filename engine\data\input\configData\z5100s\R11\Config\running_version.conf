[{"name": "root", "version": 50762, "ts": *************}, {"name": "physical", "version": 50760, "ts": *************}, {"name": "sub_interface", "version": 50760, "ts": *************}, {"name": "routing", "version": 50759, "ts": *************}, {"name": "dhcp", "version": 50760, "ts": *************}, {"name": "security_zone", "version": 50760, "ts": *************}, {"name": "auth", "version": 50760, "ts": *************}, {"name": "devicename", "version": 50759, "ts": *************}, {"name": "discovery", "version": 50759, "ts": *************}, {"name": "timezone", "version": 50760, "ts": *************}, {"name": "security-policy", "version": 50759, "ts": *************}, {"name": "network-obj", "version": 50759, "ts": *************}, {"name": "appid", "version": 50760, "ts": *************}, {"name": "service-obj", "version": 50760, "ts": *************}, {"name": "time-range", "version": 50760, "ts": *************}, {"name": "ips-config", "version": 50759, "ts": *************}, {"name": "anti-virus", "version": 50759, "ts": *************}, {"name": "url-filter", "version": 50759, "ts": *************}, {"name": "url-category", "version": 50759, "ts": *************}, {"name": "security-defend", "version": 50760, "ts": *************}, {"name": "nat", "version": 50760, "ts": 1750147960984}, {"name": "threat-intelligence", "version": 50760, "ts": 1750147960990}, {"name": "mac-block", "version": 50760, "ts": 1750147960992}, {"name": "user-experience", "version": 50760, "ts": *************}, {"name": "mllb", "version": 50760, "ts": 1750147960984}]