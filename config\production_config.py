# -*- coding: utf-8 -*-
"""
FortiGate转换器重构集成阶段 - 生产环境配置

这个配置文件包含了生产环境下的所有配置参数，包括性能优化、
监控设置、日志配置和安全参数。

配置分类：
1. 系统配置 - 基础系统参数
2. 性能配置 - 性能优化参数
3. 日志配置 - 日志记录和轮转
4. 监控配置 - 健康检查和告警
5. 安全配置 - 安全相关设置
"""

import os
import logging
from typing import Dict, Any, List


class ProductionConfig:
    """生产环境配置类"""
    
    # ==================== 系统配置 ====================
    
    # 版本信息
    VERSION = "2.0.0"
    BUILD_DATE = "2025-07-16"
    ENVIRONMENT = "production"
    
    # 基础路径配置
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    DATA_DIR = os.path.join(BASE_DIR, "data")
    LOG_DIR = os.path.join(BASE_DIR, "logs")
    CONFIG_DIR = os.path.join(BASE_DIR, "config")
    TEMPLATE_DIR = os.path.join(BASE_DIR, "templates")
    
    # 确保目录存在
    for directory in [DATA_DIR, LOG_DIR, CONFIG_DIR]:
        os.makedirs(directory, exist_ok=True)
    
    # ==================== 性能配置 ====================
    
    # 并发处理配置
    MAX_CONCURRENT_INTEGRATORS = int(os.getenv("MAX_CONCURRENT_INTEGRATORS", "4"))
    MAX_WORKER_THREADS = int(os.getenv("MAX_WORKER_THREADS", "8"))
    THREAD_POOL_SIZE = min(MAX_WORKER_THREADS, os.cpu_count() or 4)
    
    # 内存管理配置
    MAX_MEMORY_USAGE_MB = int(os.getenv("MAX_MEMORY_USAGE_MB", "2048"))  # 2GB
    MEMORY_CHECK_INTERVAL = 30  # 秒
    GARBAGE_COLLECTION_THRESHOLD = (700, 10, 10)
    
    # 处理超时配置
    INTEGRATION_TIMEOUT = int(os.getenv("INTEGRATION_TIMEOUT", "300"))  # 5分钟
    SINGLE_INTEGRATOR_TIMEOUT = int(os.getenv("SINGLE_INTEGRATOR_TIMEOUT", "60"))  # 1分钟
    XML_PROCESSING_TIMEOUT = int(os.getenv("XML_PROCESSING_TIMEOUT", "30"))  # 30秒
    
    # 批处理配置
    BATCH_SIZE = int(os.getenv("BATCH_SIZE", "100"))
    MAX_BATCH_MEMORY_MB = int(os.getenv("MAX_BATCH_MEMORY_MB", "512"))
    
    # ==================== 日志配置 ====================
    
    # 日志级别配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()
    DEBUG_MODE = os.getenv("DEBUG_MODE", "false").lower() == "true"
    
    # 日志文件配置
    LOG_FILE = os.path.join(LOG_DIR, "fortigate_converter.log")
    ERROR_LOG_FILE = os.path.join(LOG_DIR, "fortigate_converter_error.log")
    PERFORMANCE_LOG_FILE = os.path.join(LOG_DIR, "performance.log")
    
    # 日志轮转配置
    LOG_MAX_SIZE_MB = int(os.getenv("LOG_MAX_SIZE_MB", "100"))
    LOG_BACKUP_COUNT = int(os.getenv("LOG_BACKUP_COUNT", "10"))
    LOG_ROTATION_INTERVAL = "midnight"  # 每天轮转
    
    # 日志格式配置
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # ==================== 监控配置 ====================
    
    # 健康检查配置
    HEALTH_CHECK_ENABLED = os.getenv("HEALTH_CHECK_ENABLED", "true").lower() == "true"
    HEALTH_CHECK_INTERVAL = int(os.getenv("HEALTH_CHECK_INTERVAL", "60"))  # 秒
    HEALTH_CHECK_TIMEOUT = int(os.getenv("HEALTH_CHECK_TIMEOUT", "10"))  # 秒
    
    # 性能监控配置
    PERFORMANCE_MONITORING_ENABLED = os.getenv("PERFORMANCE_MONITORING_ENABLED", "true").lower() == "true"
    PERFORMANCE_METRICS_INTERVAL = int(os.getenv("PERFORMANCE_METRICS_INTERVAL", "30"))  # 秒
    
    # 告警配置
    ALERT_ENABLED = os.getenv("ALERT_ENABLED", "true").lower() == "true"
    ALERT_EMAIL = os.getenv("ALERT_EMAIL", "")
    ALERT_WEBHOOK_URL = os.getenv("ALERT_WEBHOOK_URL", "")
    
    # 告警阈值
    CPU_ALERT_THRESHOLD = float(os.getenv("CPU_ALERT_THRESHOLD", "80.0"))  # %
    MEMORY_ALERT_THRESHOLD = float(os.getenv("MEMORY_ALERT_THRESHOLD", "85.0"))  # %
    ERROR_RATE_ALERT_THRESHOLD = float(os.getenv("ERROR_RATE_ALERT_THRESHOLD", "5.0"))  # %
    
    # ==================== 集成器配置 ====================
    
    # DNS集成器配置
    DNS_CONFIG = {
        "max_servers": int(os.getenv("DNS_MAX_SERVERS", "10")),
        "max_static_hosts": int(os.getenv("DNS_MAX_STATIC_HOSTS", "1000")),
        "timeout": int(os.getenv("DNS_TIMEOUT", "30")),
        "validation_enabled": os.getenv("DNS_VALIDATION_ENABLED", "true").lower() == "true"
    }
    
    # 静态路由集成器配置
    STATIC_ROUTE_CONFIG = {
        "max_routes": int(os.getenv("STATIC_ROUTE_MAX_ROUTES", "5000")),
        "default_distance": int(os.getenv("STATIC_ROUTE_DEFAULT_DISTANCE", "1")),
        "timeout": int(os.getenv("STATIC_ROUTE_TIMEOUT", "60")),
        "validation_enabled": os.getenv("STATIC_ROUTE_VALIDATION_ENABLED", "true").lower() == "true"
    }
    
    # 防火墙策略集成器配置
    FIREWALL_POLICY_CONFIG = {
        "max_policies": int(os.getenv("FIREWALL_POLICY_MAX_POLICIES", "10000")),
        "enable_optimization": os.getenv("FIREWALL_POLICY_ENABLE_OPTIMIZATION", "true").lower() == "true",
        "enable_deduplication": os.getenv("FIREWALL_POLICY_ENABLE_DEDUPLICATION", "true").lower() == "true",
        "timeout": int(os.getenv("FIREWALL_POLICY_TIMEOUT", "120")),
        "validation_enabled": os.getenv("FIREWALL_POLICY_VALIDATION_ENABLED", "true").lower() == "true"
    }
    
    # NAT规则集成器配置
    NAT_RULE_CONFIG = {
        "max_rules": int(os.getenv("NAT_RULE_MAX_RULES", "2000")),
        "enable_pools": os.getenv("NAT_RULE_ENABLE_POOLS", "true").lower() == "true",
        "timeout": int(os.getenv("NAT_RULE_TIMEOUT", "60")),
        "validation_enabled": os.getenv("NAT_RULE_VALIDATION_ENABLED", "true").lower() == "true"
    }
    
    # ==================== XML处理配置 ====================
    
    # XML优化配置
    XML_OPTIMIZATION_CONFIG = {
        "enabled": os.getenv("XML_OPTIMIZATION_ENABLED", "true").lower() == "true",
        "remove_empty_elements": os.getenv("XML_REMOVE_EMPTY_ELEMENTS", "true").lower() == "true",
        "remove_duplicates": os.getenv("XML_REMOVE_DUPLICATES", "true").lower() == "true",
        "sort_elements": os.getenv("XML_SORT_ELEMENTS", "false").lower() == "true",
        "compress_whitespace": os.getenv("XML_COMPRESS_WHITESPACE", "true").lower() == "true",
        "pretty_print": os.getenv("XML_PRETTY_PRINT", "true").lower() == "true"
    }
    
    # XML验证配置
    XML_VALIDATION_CONFIG = {
        "enabled": os.getenv("XML_VALIDATION_ENABLED", "true").lower() == "true",
        "yang_compliance_check": os.getenv("XML_YANG_COMPLIANCE_CHECK", "true").lower() == "true",
        "reference_integrity_check": os.getenv("XML_REFERENCE_INTEGRITY_CHECK", "true").lower() == "true",
        "conflict_detection": os.getenv("XML_CONFLICT_DETECTION", "true").lower() == "true",
        "performance_check": os.getenv("XML_PERFORMANCE_CHECK", "true").lower() == "true",
        "security_check": os.getenv("XML_SECURITY_CHECK", "true").lower() == "true"
    }
    
    # ==================== 安全配置 ====================
    
    # 输入验证配置
    INPUT_VALIDATION = {
        "max_file_size_mb": int(os.getenv("MAX_FILE_SIZE_MB", "100")),
        "allowed_file_extensions": [".conf", ".cfg", ".txt"],
        "max_config_lines": int(os.getenv("MAX_CONFIG_LINES", "100000")),
        "sanitize_input": os.getenv("SANITIZE_INPUT", "true").lower() == "true"
    }
    
    # 输出安全配置
    OUTPUT_SECURITY = {
        "sanitize_output": os.getenv("SANITIZE_OUTPUT", "true").lower() == "true",
        "max_output_size_mb": int(os.getenv("MAX_OUTPUT_SIZE_MB", "50")),
        "validate_xml_structure": os.getenv("VALIDATE_XML_STRUCTURE", "true").lower() == "true"
    }
    
    # ==================== 错误处理配置 ====================
    
    # 重试配置
    RETRY_CONFIG = {
        "max_retries": int(os.getenv("MAX_RETRIES", "3")),
        "retry_delay": float(os.getenv("RETRY_DELAY", "1.0")),  # 秒
        "exponential_backoff": os.getenv("EXPONENTIAL_BACKOFF", "true").lower() == "true",
        "max_retry_delay": float(os.getenv("MAX_RETRY_DELAY", "30.0"))  # 秒
    }
    
    # 错误恢复配置
    ERROR_RECOVERY = {
        "auto_recovery_enabled": os.getenv("AUTO_RECOVERY_ENABLED", "true").lower() == "true",
        "recovery_timeout": int(os.getenv("RECOVERY_TIMEOUT", "60")),  # 秒
        "max_recovery_attempts": int(os.getenv("MAX_RECOVERY_ATTEMPTS", "3")),
        "graceful_shutdown_timeout": int(os.getenv("GRACEFUL_SHUTDOWN_TIMEOUT", "30"))  # 秒
    }
    
    # ==================== 开发和调试配置 ====================
    
    # 调试配置（生产环境通常关闭）
    DEBUG_CONFIG = {
        "enabled": DEBUG_MODE,
        "verbose_logging": DEBUG_MODE,
        "save_intermediate_results": DEBUG_MODE,
        "performance_profiling": DEBUG_MODE,
        "memory_profiling": DEBUG_MODE
    }
    
    # 测试配置
    TEST_CONFIG = {
        "run_integration_tests": os.getenv("RUN_INTEGRATION_TESTS", "false").lower() == "true",
        "test_data_dir": os.path.join(DATA_DIR, "test"),
        "test_timeout": int(os.getenv("TEST_TIMEOUT", "300"))  # 秒
    }
    
    @classmethod
    def get_logging_config(cls) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'standard': {
                    'format': cls.LOG_FORMAT,
                    'datefmt': cls.LOG_DATE_FORMAT
                },
                'detailed': {
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
                    'datefmt': cls.LOG_DATE_FORMAT
                }
            },
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler',
                    'level': cls.LOG_LEVEL,
                    'formatter': 'standard',
                    'stream': 'ext://sys.stdout'
                },
                'file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': cls.LOG_LEVEL,
                    'formatter': 'detailed',
                    'filename': cls.LOG_FILE,
                    'maxBytes': cls.LOG_MAX_SIZE_MB * 1024 * 1024,
                    'backupCount': cls.LOG_BACKUP_COUNT,
                    'encoding': 'utf-8'
                },
                'error_file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'ERROR',
                    'formatter': 'detailed',
                    'filename': cls.ERROR_LOG_FILE,
                    'maxBytes': cls.LOG_MAX_SIZE_MB * 1024 * 1024,
                    'backupCount': cls.LOG_BACKUP_COUNT,
                    'encoding': 'utf-8'
                }
            },
            'loggers': {
                '': {  # root logger
                    'handlers': ['console', 'file', 'error_file'],
                    'level': cls.LOG_LEVEL,
                    'propagate': False
                },
                'engine': {
                    'handlers': ['console', 'file', 'error_file'],
                    'level': cls.LOG_LEVEL,
                    'propagate': False
                }
            }
        }
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """验证配置的有效性"""
        errors = []
        
        # 验证内存配置
        if cls.MAX_MEMORY_USAGE_MB < 512:
            errors.append("MAX_MEMORY_USAGE_MB 不能小于 512MB")
        
        # 验证并发配置
        if cls.MAX_CONCURRENT_INTEGRATORS < 1:
            errors.append("MAX_CONCURRENT_INTEGRATORS 必须大于 0")
        
        # 验证超时配置
        if cls.INTEGRATION_TIMEOUT < 60:
            errors.append("INTEGRATION_TIMEOUT 不能小于 60 秒")
        
        # 验证日志配置
        if cls.LOG_MAX_SIZE_MB < 10:
            errors.append("LOG_MAX_SIZE_MB 不能小于 10MB")
        
        # 验证目录权限
        for directory in [cls.DATA_DIR, cls.LOG_DIR]:
            if not os.access(directory, os.W_OK):
                errors.append(f"目录 {directory} 没有写权限")
        
        return errors
    
    @classmethod
    def get_integrator_config(cls, integrator_name: str) -> Dict[str, Any]:
        """获取特定集成器的配置"""
        config_map = {
            "dns": cls.DNS_CONFIG,
            "static_route": cls.STATIC_ROUTE_CONFIG,
            "firewall_policy": cls.FIREWALL_POLICY_CONFIG,
            "nat_rule": cls.NAT_RULE_CONFIG
        }
        return config_map.get(integrator_name, {})
    
    @classmethod
    def is_production_ready(cls) -> bool:
        """检查是否已准备好生产环境"""
        errors = cls.validate_config()
        return len(errors) == 0


# 创建全局配置实例
config = ProductionConfig()

# 验证配置
config_errors = config.validate_config()
if config_errors:
    print("配置验证失败:")
    for error in config_errors:
        print(f"  - {error}")
    raise ValueError("生产环境配置无效")

print(f"生产环境配置加载成功 - 版本 {config.VERSION}")
print(f"环境: {config.ENVIRONMENT}")
print(f"日志级别: {config.LOG_LEVEL}")
print(f"并发处理器数: {config.MAX_CONCURRENT_INTEGRATORS}")
print(f"最大内存使用: {config.MAX_MEMORY_USAGE_MB}MB")
