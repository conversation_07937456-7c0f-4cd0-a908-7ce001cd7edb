module iana-crypt-hash {
  namespace "urn:ietf:params:xml:ns:yang:iana-crypt-hash";
  prefix ianach;

  organization "IANA";
  contact
    "        Internet Assigned Numbers Authority

     Postal: ICANN
             12025 Waterfront Drive, Suite 300
             Los Angeles, CA  90094-2536
             United States

     Tel:    ****** 301 5800
     E-Mail: <EMAIL>>";
  description
    "This YANG module defines a type for storing passwords
     using a hash function and features to indicate which hash
     functions are supported by an implementation.

     The latest revision of this YANG module can be obtained from
     the IANA web site.

     Requests for new values should be made to IANA via
     email (<EMAIL>).

     Copyright (c) 2014 IETF Trust and the persons identified as
     authors of the code.  All rights reserved.

     Redistribution and use in source and binary forms, with or
     without modification, is permitted pursuant to, and subject
     to the license terms contained in, the Simplified BSD License
     set forth in Section 4.c of the IETF Trust's Legal Provisions
     Relating to IETF Documents
     (http://trustee.ietf.org/license-info).

     The initial version of this YANG module is part of RFC 7317;
     see the RFC itself for full legal notices.";

  revision 2014-08-06 {
    description
      "Initial revision.";
    reference
      "RFC 7317: A YANG Data Model for System Management";
  }

  typedef crypt-hash {
    type string {
      pattern
        '$0$.*'
      + '|$1$[a-zA-Z0-9./]{1,8}$[a-zA-Z0-9./]{22}'
      + '|$5$(rounds=\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{43}'
      + '|$6$(rounds=\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{86}';
    }
    description
      "The crypt-hash type is used to store passwords using
       a hash function.  The algorithms for applying the hash
       function and encoding the result are implemented in
       various UNIX systems as the function crypt(3).

       A value of this type matches one of the forms:

         $0$<clear text password>
         $<id>$<salt>$<password hash>
         $<id>$<parameter>$<salt>$<password hash>

       The '$0$' prefix signals that the value is clear text.  When
       such a value is received by the server, a hash value is
       calculated, and the string '$<id>$<salt>$' or
       $<id>$<parameter>$<salt>$ is prepended to the result.  This
       value is stored in the configuration data store.
       If a value starting with '$<id>$', where <id> is not '0', is
       received, the server knows that the value already represents a
       hashed value and stores it 'as is' in the data store.

       When a server needs to verify a password given by a user, it
       finds the stored password hash string for that user, extracts
       the salt, and calculates the hash with the salt and given
       password as input.  If the calculated hash value is the same
       as the stored value, the password given by the client is
       accepted.

       This type defines the following hash functions:

         id | hash function | feature
         ---+---------------+-------------------
          1 | MD5           | crypt-hash-md5
          5 | SHA-256       | crypt-hash-sha-256
          6 | SHA-512       | crypt-hash-sha-512

       The server indicates support for the different hash functions
       by advertising the corresponding feature.";
    reference
      "IEEE Std 1003.1-2008 - crypt() function
       RFC 1321: The MD5 Message-Digest Algorithm
       FIPS.180-4.2012: Secure Hash Standard (SHS)";
  }

  feature crypt-hash-md5 {
    description
      "Indicates that the device supports the MD5
       hash function in 'crypt-hash' values.";
    reference "RFC 1321: The MD5 Message-Digest Algorithm";
  }

  feature crypt-hash-sha-256 {
    description
      "Indicates that the device supports the SHA-256
       hash function in 'crypt-hash' values.";
    reference "FIPS.180-4.2012: Secure Hash Standard (SHS)";
  }

  feature crypt-hash-sha-512 {
    description
      "Indicates that the device supports the SHA-512
       hash function in 'crypt-hash' values.";
    reference "FIPS.180-4.2012: Secure Hash Standard (SHS)";
  }

}
