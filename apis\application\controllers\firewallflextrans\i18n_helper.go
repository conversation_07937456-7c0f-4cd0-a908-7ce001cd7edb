package firewallflextrans

import (
	"irisAdminApi/application/libs/response"

	"github.com/kataras/iris/v12"
)

// I18nError 辅助函数：创建国际化错误响应
func I18nError(ctx iris.Context, key string, params ...interface{}) {
	response.I18nError(ctx, key, params...)
}

// I18nSuccess 辅助函数：创建国际化成功响应
func I18nSuccess(ctx iris.Context, data interface{}, key string, params ...interface{}) {
	response.I18nResponse(ctx, response.NoErr.Code, data, key, params...)
}

// I18nErrorWithCode 辅助函数：创建带自定义错误码的国际化错误响应
func I18nErrorWithCode(ctx iris.Context, code int, key string, params ...interface{}) {
	response.I18nErrorWithCode(ctx, int64(code), key, params...)
}

// GetLanguage 辅助函数：获取当前请求的语言设置
func GetLanguage(ctx iris.Context) string {
	return response.GetLanguage(ctx)
}
