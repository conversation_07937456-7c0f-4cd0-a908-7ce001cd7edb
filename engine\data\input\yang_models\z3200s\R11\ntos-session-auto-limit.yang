module ntos-session-auto-limit {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:session-auto-limit";
  prefix ntos-session-auto-limit;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-system;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS session auto limit module.";
  
  revision 2024-09-07 {
    description
      "create session auto limit file.";
    reference "revision 2024-09-07.";
  }

  identity session-auto-limit {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Session-auto-limit service.";
    ntos-ext:nc-cli-identity-name "session-auto-limit";
  }

  grouping system-session-auto-limit-config {
    description
      "Configuration data for session auto limit.";

    leaf enabled {
      type boolean;
      description
        "Enable or disable session auto-limit.";
    }

    list not-limit-app {
      description "List of applications that are not subject to session suppression.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }

    leaf sync-time {
      type uint64;
      description
        "Last sync config to fp time.";
    }
  }

  rpc show-enable-status {
    description
      "Show enable status of session auto limit.";
    output {
      container session-auto-limit {
        leaf enabled {
          type boolean;
        }
      }
      container real-time-session {
        leaf enabled {
          type boolean;
        }
      }
    }
    ntos-ext:nc-cli-show "session-auto-limit enable status";
  }

  rpc show-not-limit-app {
    description "Show session auto limit for non-limited applications.";

    output {
      list app {
        description "Details of all non-limited applications.";
        key "name";
        leaf name {
          type string;
        }
        leaf name-i18n {
          type ntos-types:ntos-obj-name-type;
          description "Indicates the name of the application for internationalization.";
        }
        leaf key-bussiness {
          type boolean;
          description "Application is key bussiness.";
        }
      }
    }

    ntos-ext:nc-cli-show "session-auto-limit not-limit-app";
  }

  rpc show-auto-limit-app {
    description "Show session auto limit for auto-limited applications.";

    output {
      container session-auto-limit {
        leaf enabled {
          type boolean;
        }
      }
      container real-time-session {
        leaf enabled {
          type boolean;
        }
      }
      list app {
        description "Details of all non-limited applications.";
        key "name";
        leaf name {
          type string;
        }
        leaf name-i18n {
          type ntos-types:ntos-obj-name-type;
          description "Indicates the name of the application for internationalization.";
        }
        leaf enabled {
          type boolean;
          description "Enable the session limit rule.";
        }
        leaf session-number {
          type uint64;
        }
        leaf per-ip-number {
          type uint64;
        }
        leaf match-cnt {
          type uint64;
        }
      }
    }
    ntos-ext:nc-cli-show "session-auto-limit limit-app";
  }

  augment "/ntos:config/ntos-system:system" {
    description 
      "Configuration data for the session auto limit.";
    container session-auto-limit {
      description 
        "Session auto limit parameter configuration.";
      ntos-ext:feature "product";
      uses system-session-auto-limit-config;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description 
      "The state of session auto limit.";
    container session-auto-limit {
      description 
        "Session auto limit parameter configuration.";
      ntos-ext:feature "product";
      uses system-session-auto-limit-config;
    }
  }
}