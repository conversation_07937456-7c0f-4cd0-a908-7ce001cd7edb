module ntos-aaad {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:aaad";
  prefix ntos-aaad;

  import ntos {
    prefix ntos;
  }
  import ntos-if-types {
    prefix ntos-if-types;
  }
  import ntos-inet-types {
    prefix ntos-inet-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-user-management {
    prefix ntos-user-management;
  }


  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS Authentication, Authorization and Accounting management.";

  revision 2023-09-13 {
    description
      "support Ldap server and sso scene.";
  }

  revision 2023-05-05 {
    description
      "Add web-auth scene.";
  }

  revision 2022-09-19 {
    description
      "Initial version.";
    reference "";
  }

  identity aaad {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Aaad service.";
  }

  typedef order-type
  {
    type enumeration {
      enum only-group {
        description "only use group";
      }
      enum only-subs {
        description "only use subs";
      }
      enum first-group {
        description "first use group";
      }
      enum first-subs {
        description "first use subs";
      }
    }
  }

  typedef debug-type
  {
    type enumeration {
      enum error {
        description "error switch";
      }
      enum info {
        description "info switch";
      }
      enum detail {
        description "detail switch";
      }
      enum user {
        description "user debug switch";
      }
      enum event {
        description "event debug switch";
      }
      enum extend {
        description "ext debug switch";
      }
      enum mib {
        description "mib debug switch";
      }
      enum domain {
        description "domain debug switch";
      }
      enum lib {
        description "lib debug switch";
      }
      enum show {
        description "show debug switch";
      }
      enum dbus {
        description "dbus debug switch";
      }
      enum syslog {
        description "dbus debug switch";
      }
      enum vac {
        description "vac debug switch";
      }
      enum nc-err {
        description "nc_err debug switch";
      }
      enum nc-info {
        description "nc_info debug switch";
      }
      enum all {
        description "all debug switch";
      }
    }
  }

  grouping domain-config {
    container authentication {
      description
        "Domain authentication config.";

      container sslvpn {
        leaf method {
          type string;
          description
            "Sslvpn method.";
        }
        leaf enabled {
          type boolean;
          description
            "Enables or disables the sslvpn method.";
        }
      }

      container webauth {
        leaf method {
          type string;
          description
            "Webauth method.";
        }
        leaf enabled {
          type boolean;
          description
            "Enables or disables the webauth method.";
        }
      }

      container single-sign-on {
        leaf method {
          type string;
          description
            "Single sign-on method.";
        }
        leaf enabled {
          type boolean;
          description
            "Enables or disables the single sign-on method.";
        }
      }

      container vpn {
        leaf method {
          type string;
          description
            "Vpn method.";
        }
        leaf enabled {
          type boolean;
          description
            "Enables or disables the vpn method.";
        }
      }

      container pppoe {
        leaf method {
          type string;
          description
            "pppoe method.";
        }
        leaf enabled {
          type boolean;
          description
            "Enables or disables the pppoe method.";
        }
      }
    }

    container accounting {
      description
        "Domain accounting config.";

      leaf network {
        type string;
        description
          "For network services.";
      }
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "The description of domain.
        String length is limited to 1..128 characters.";
    }

    leaf enabled {
      type boolean;
      default "true";

      description
        "Set the domain whether valid?";
    }

    container username-format {
      description
        "Config username format.";

      choice name {
        case choice-with-domain {
          container with-domain {
            presence "with-domain.";
            description
              "For username with domain.";
          }
        }
        case choice-without-domain {
          container without-domain {
            presence "without-domain.";
            description
              "For username without domain.";
          }
        }
        case raw-input-format {
          container raw-format {
            presence "raw-format.";
            description
              "For username raw format.";
          }
        }
      }
      ntos-ext:nc-cli-one-liner;
    }

    leaf access-limit {
      description
        "Max number of access";
      type uint32 {
        range '1..1024';
      }
    }

    leaf specify-user-path {
      type ntos-user-management:user-group-path;
      description
        "Storage path when user does not exist.";
    }

    leaf auto-create-group {
      type boolean;
      default "true";

      description
        "Set the domain whether auto create group?";
    }
  }

  grouping server {
    leaf group {
      type string {
        pattern "[^`~!#$%^&*+/|{};:\"'\\\\<>?]*" {
          error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
        }
      }
      description
        "Use server-group method";
    }

    leaf group-type {
      type string {
        pattern "[^`~!#$%^&*+/|{};:\"'\\\\<>?]*" {
          error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
        }
      }
      description
        "Use server-group type, support input radius,ldap";
    }
  }

  grouping accounting-method {
    leaf name {
      type ntos-types:ntos-obj-name-type;
      description
        "Named accounting list.";
    }

    uses server;
  }

  grouping accounting-config {
    container update {
      description
        "Enable accounting update records.";

      leaf periodic {
        type uint32 {
          range '1..525600';
        }
        default "5";
        description
          "Accounting update interval(minutes).";
      }

      leaf enabled {
        type boolean;
        default "false";
        description
          "enable update";
      }
      ntos-ext:nc-cli-one-liner;
    }

    container start-fail {
      description
        "Config accounting start-fail policy.";

      choice name {
        case choice-offline {
          leaf offline {
            description
              "Accounting start-fail offline.";
            type empty;
          }
        }
        case choice-online {
          leaf online {
            description
              "Accounting start-fail online.";
            type empty;
          }
        }
      }
      ntos-ext:nc-cli-one-liner;
    }

    list network {
      key "name";
      description
        "For network service(PPP, SLIP, Ethernet).";

      uses accounting-method;
    }
  }

  grouping authen-method {
    uses server;

    leaf none {
      type empty;
      description
        "Use none method";
    }

    leaf subs {
      type empty;
      description
        "Use subscriber method";
    }

    leaf auth-order {
      type order-type;
      description
        "Auth mode order";
    }
  }

  grouping authen-config {
    list sslvpn {
      key "name";
      description
        "Sslvpn user.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Named authentication list.";
      }
      uses authen-method;
    }

    list webauth {
      key "name";
      description
        "Webauth user.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Named authentication list.";
      }
      uses authen-method;
    }

    list vpn {
      key "name";
      description
        "Vpn user.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Named authentication list.";
      }
      uses authen-method;
    }

    list pppoe {
      key "name";
      description
        "pppoe user.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Named authentication list.";
      }
      uses authen-method;
    }

    list single-sign-on {
      key "name";
      description
        "single-sign-on user.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Named authentication list.";
      }

      uses server;
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Network aaa configuration.";

    container aaa {
      leaf enabled {
        type boolean;
        default "true";
        description
          "Set status of aaa-enabled.";
      }
      leaf domain-enabled {
        type boolean;
        default "true";
        description
          "Set status of domain-enabled.";
      }
      list domain {
        key "name";

        description
          "aaa domain configuration.";

        leaf name {
          type string {
            pattern "[a-z0-9._-]+" {
              error-message 'Use unsuport character.';
            }
          }
          description
            "The name of domain.";
        }
        uses domain-config;
      }

      container authentication {
        description
          "Configure authentication.";

        uses authen-config;
      }

      container accounting {
        description
          "Configure accounting.";

        uses accounting-config;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Network aaa configuration.";

    container aaa {
      leaf enabled {
        type boolean;
        default "true";
        description
          "Set status of aaa-enabled.";
      }

      leaf domain-enabled {
        type boolean;
        default "true";
        description
          "Set status of domain-enabled.";
      }

      list domain {
        key "name";
        description
          "aaa domain configuration.";

        leaf name {
          type string {
            pattern "[a-z0-9._-]+" {
              error-message 'Use unsuport character.';
            }
          }
          description
            "The name of domain.";
        }
        uses domain-config;
      }

      container authentication {
        description
          "Configure authentication.";

        uses authen-config;
      }

      container accounting {
        description
          "Configure accounting.";

        uses accounting-config;
      }
    }
  }

  rpc aaa-show {
    description
      "Show aaa information.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
      leaf statistics {
        type empty;
        description
          "show aaa scalar statistics.";
      }
      leaf mlist-cnt {
        type empty;
        description
          "show aaa mlist status.";
      }
      leaf group-server {
        type empty;
        description
          "show aaa group server.";
      }
      leaf group-list {
        type empty;
      }
      leaf user-interface {
        type empty;
        description
          "show aaa user interface.";
      }
      leaf user-all {
        type empty;
        description
          "show aaa user all.";
      }
      leaf method-lists {
        type empty;
        description
          "show aaa method lists.";
      }
      leaf user-sid {
        type uint32;
        description
          "show aaa user by session id.";
      }
      leaf user-name {
        type string;
        description
          "show aaa user by session id.";
      }
      leaf debug {
        type empty;
        description
          "show aaa debug information.";
      }
      leaf log-switch {
        type empty;
        description
          "show log switch information.";
      }

      leaf domain-ref-info {
        type empty;
        description
          "show domain ref info.";
      }

      leaf domain {
        type empty;
        description
          "show domain information.";
      }

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Show single domain info.";
      }

      leaf start {
        type uint32;
        description
          "Start offset.";
      }

      leaf end {
        type uint32;
        description
          "End offset.";
      }

      leaf filter {
        type string;
        description
          "Filter.";
      }

      leaf memory {
        type empty;
        description
          "Show aaa and client memory info.";
      }

      leaf client-info {
        type empty;
        description
          "Show aaa and client memory info.";
      }

      leaf domain-name {
        type empty;
        description
          "User manager get domain name to show.";
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "aaa";
    ntos-api:internal;
  }

  rpc aaa-debug-set {
    input {
      list debug {
        key "dtype";
        ntos-ext:nc-cli-one-liner;
        description
          "Set debug.";

        leaf dtype {
          type debug-type;
          description
            "Set debug type.";

          ntos-ext:nc-cli-no-name;
        }

        leaf client-id {
          when "../dtype = 'lib'";
          type string {
            pattern "^[0-9][0-9,]*" {
              error-message 'The start must be a number and be plited by comma(,).';
            }
          }
          description
            "Debug one client, multi client id must be splited by ','.
            Defalut 0, debug all client.";
        }

        leaf on-or-off {
          type enumeration {
            enum "on";
            enum "off";
          }
          description
            "Set debug switch.";

          ntos-ext:nc-cli-no-name;
        }
        ntos-ext:nc-cli-no-name;
      }

      container debug-filter {
        description
            "Config the debug filter condition.";
        leaf on-or-off {
          type enumeration {
            enum "on";
            enum "off";
          }
          description
            "Set filter condition.";

          ntos-ext:nc-cli-no-name;
        }

        leaf index {
          type uint32 {
            range "1..5" {
              error-message
                "The index must >= 1 and <= 5.";
            }
          }
          description
            "Set the conditon index, every conditon can support 5.";
        }

        leaf condition {
          type enumeration {
            enum "ip";
            enum "username";
            enum "mac";
          }
          description
            "Set filter condition.";

          ntos-ext:nc-cli-no-name;
        }

        leaf ip {
          type ntos-inet-types:ip-address;
          description
            "Set ip.";
          ntos-ext:nc-cli-no-name;
        }

        leaf username {
          type ntos-types:ntos-obj-name-type;
          description
            "Set user name.";
          ntos-ext:nc-cli-no-name;
        }

        leaf mac {
          type ntos-if-types:unicast-mac-address;
          description
            "Set mac.";
          ntos-ext:nc-cli-no-name;
        }
      }

      container logfile {
        leaf enabled {
          type boolean;
          description
            "Enable logfile.";
        }
      }

      container log-switch {
        leaf enabled {
          type boolean;
          description
            "Authen log switch.";
        }

        leaf rate-limit {
          when "../enabled = 'true'";
          type uint32 {
            range "0..65535" {
              error-message
                "The index must >= 0 and <= 65535.";
            }
          }
          description
            "Config rate of print log.";
        }
      }
    }
    ntos-ext:nc-cli-cmd "aaa";
    ntos-api:internal;
  }
}
