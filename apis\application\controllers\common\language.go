package common

import (
	"irisAdminApi/application/libs/i18n"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"

	"github.com/kataras/iris/v12"
)

// SwitchLanguage 切换语言
// @Summary 切换语言
// @Description 切换系统语言设置
// @Tags 通用
// @Accept json
// @Produce json
// @Param lang query string true "语言代码，如zh-CN、en-US"
// @Success 200 {object} response.Response "语言切换成功"
// @Failure 400 {object} response.Response "参数错误"
// @Router /api/v1/common/language [get]
func SwitchLanguage(ctx iris.Context) {
	// 获取语言参数
	lang := ctx.URLParam("lang")
	if lang == "" {
		response.I18nError(ctx, "param.error")
		return
	}

	// 标准化语言代码
	normalizedLang := response.NormalizeLanguageCode(lang)

	// 检查语言是否支持
	if !i18n.IsSupportedLang(normalizedLang) {
		response.I18nError(ctx, "param.invalid_value", "param", "lang")
		return
	}

	// 设置语言到上下文
	response.SetLanguage(ctx, normalizedLang)

	// 记录日志
	logging.DebugLogger.Debugf("切换语言: %s", normalizedLang)

	// 返回成功响应
	response.I18nResponse(ctx, response.NoErr.Code, map[string]interface{}{
		"language": normalizedLang,
		"name":     i18n.Translate(normalizedLang, "language."+normalizedLang, nil),
	}, "language.switch_success")
}

// GetCurrentLanguage 获取当前语言
// @Summary 获取当前语言
// @Description 获取当前系统语言设置
// @Tags 通用
// @Accept json
// @Produce json
// @Success 200 {object} response.Response "当前语言信息"
// @Router /api/v1/common/language/current [get]
func GetCurrentLanguage(ctx iris.Context) {
	// 获取当前语言
	lang := response.GetLanguage(ctx)

	// 返回语言信息
	response.I18nResponse(ctx, response.NoErr.Code, map[string]interface{}{
		"language": lang,
		"name":     i18n.Translate(lang, "language."+lang, nil),
		"supported": []map[string]interface{}{
			{
				"code": "zh-CN",
				"name": i18n.Translate(lang, "language.zh-CN", nil),
			},
			{
				"code": "en-US",
				"name": i18n.Translate(lang, "language.en-US", nil),
			},
		},
	}, "language.current")
}

// I18nDemo 国际化演示页面
// @Summary 国际化演示页面
// @Description 提供一个简单的页面演示国际化功能
// @Tags 通用
// @Accept html
// @Produce html
// @Success 200 {string} string "HTML页面"
// @Router /api/v1/common/i18n-demo [get]
func I18nDemo(ctx iris.Context) {
	// 直接返回HTML页面
	ctx.ServeFile("./application/controllers/common/i18n_demo.html")
}
