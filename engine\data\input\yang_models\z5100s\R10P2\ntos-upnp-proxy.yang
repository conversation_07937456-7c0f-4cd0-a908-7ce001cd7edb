module ntos-upnp-proxy {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:upnp-proxy";
  prefix ntos-upnp-proxy;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS UPnP service module.";

  revision 2023-06-16 {
    description
      "Initial version.";
    reference "";
  }

  identity upnp-proxy {
    base ntos-types:SERVICE_LOG_ID;
    description
      "upnp-proxy service.";
  }

  typedef ip-range-type {
    type union {
      type ntos-inet:ipv4-range;
      type ntos-inet:ipv6-range;
    }
  }

  typedef ip-address-type {
    type union {
      type ntos-inet:ipv4-address;
      type ntos-inet:ipv6-address;
    }
  }

  typedef time-point {
    type string {
      pattern '([01][0-9]|2[0-3]):[0-5][0-9]' {
        error-message "Incorrect time format, expecting: hh:mm.";
      }
      length 5;
    }
  }

  grouping comm-config {
    leaf name {
      mandatory true;
      type string {
        pattern "[^`~!$%^&*+/|{};:\"',\\\\?]*" {
          error-message "cannot include character: `~!$%^&*+|{};:\"',\\/?";
        }
      }
      description
        "Isolate region name.";
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "The description of the isolate region.";
    }

    leaf ip-set {
      type ip-range-type;
    }
  }

  grouping station {
    list station-region {
      key "station";

      leaf station {
        type string {
          pattern "station-[1-9]{1}" {
            error-message "only input station-**, example: station-1.
              can use 'cmd upnp-proxy unused-station' get unused name
              to create new station region.";
          }
        }
      }

      uses comm-config;
    }
  }

  grouping television {
    list television-region {
      key "television";

      leaf television {
        type string {
          pattern "television-[1-9]{1}" {
            error-message "only input television-**, example: television-1.
              can use 'cmd upnp-proxy unused-television' get unused name
              to create new television region.";
          }
        }
      }

      uses comm-config;
    }
  }

  grouping projection-config {
    leaf enabled {
      type boolean;
      default "false";
      description
        "Enable or disable UPnP service.";
    }

    leaf bind-rule {
      description
        "Set room-tv map mode.";
      type enumeration {
        enum ip {
          description "Use ip match rule";
        }
        enum mac {
          description "Use mac match rule";
        }
      }
      default "ip";
    }

    container room {
      list room-bind-ip {
        key "name";
        description
          "Bind ip with room id.";

        leaf name {
          type string {
            pattern "map-[0-9]{1,4}" {
              error-message "only input map-**, example: map-100.
                can use 'cmd upnp-proxy unused-map' get unused name to create new map.";
            }
          }
        }

        leaf room-id {
          mandatory true;
          type string {
            pattern "[^`~!$%^&*+/|{};:\"',\\\\?]*" {
              error-message "cannot include character: `~!$%^&*+|{};:\"',\\/?";
            }
          }
          description
            "Name the room.";
        }

        leaf television-id {
          mandatory true;
          type string;
          description
            "TV number, input 001 - 008, and do not more than server capacity.";
        }

        leaf ip-address {
          type ip-address-type;
        }

        leaf description {
          type ntos-types:ntos-obj-description-type;
          description
            "The description of the tv.";
        }
      }

      list room-bind-mac {
        key "name";
        description
          "Bind mac with room id.";

        leaf name {
          type string {
            pattern "map-[0-9]{1,4}" {
              error-message "only input map-**, example: map-100.
                can use 'cmd upnp-proxy unused-map' get unused name to create new map.";
            }
          }
        }

        leaf room-id {
          mandatory true;
          type string {
            pattern "[^`~!$%^&*+/|{};:\"',\\\\?]*" {
              error-message "cannot include character: `~!$%^&*+|{};:\"',\\/?";
            }
          }
          description
            "Room id";
        }

        leaf television-id {
          mandatory true;
          type string;
          description
            "Must be input 001 - 008, and do not more than server capacity.";
        }

        leaf mac-address {
          type ntos-if:mac-address;
        }

        leaf description {
          type ntos-types:ntos-obj-description-type;
          description
            "The description of the tv.";
        }
      }
    }
  }

  grouping advance-config {
    description
      "Set projection screen advance configurations.";

    container automatic-enrollment {
      description
        "Whether to enable television automatic enrollment.";
      leaf enabled {
        type boolean;
        default "false";
      }

      leaf registration-time {
        type uint16 {
          range "1..43200";
        }
        default "1440";
        description
          "Set tv registration time(minute).";
      }

      leaf logout-check-period {
        type uint16 {
          range "1..43200";
        }
        default "3";
        description
          "Set tv logout check period time(minute).";
      }
    }

    container terminal-authorization {
      description
        "Whether to enable terminal authorization.";
      leaf enabled {
        type boolean;
        default "false";
      }
    }

    container linkage-service {
      description
        "Whether to enable the relevant service.";
      leaf enabled {
        type boolean;
        default "false";
      }
    }

    container offline-detect {
      description
        "Whether to set flow-detection.";
      leaf time-range {
        type uint16 {
          range "1..65535";
        }
        default "150";
        description
          "Set the time(minutes) when there is no flow.";
      }

      leaf flow-rate {
        type uint8 {
          range "0..10";
        }
        default "0";
        description
          "Unbind below this flow rate for a period of time.";
      }
    }

    container scheduled-offline {
      description
        "Whether to enabled scheduled offline service.";
      leaf enabled {
        type boolean;
        default "false";
      }

      leaf time {
        type time-point;
        default "00:00";
        description
          "Set scheduled offline time.";
      }
    }

    container quick-response-code-valid-time {
      description
        "Set QR code validity time(minute).";
      leaf time {
        type uint16 {
          range "1..43200";
        }
        default "480";
      }
    }

    leaf url-cipher-string {
      type string;
      description
        "Used for TV request URLs.";
    }

    leaf md5-cipher-string {
      type string;
      description
        "The cipher used when generating the TV projection URL/QR code.";
    }
  }

  grouping reserve-config {
    description
      "Set projection screen reserve configurations.";

    container single-ip-process {
      leaf interval-time {
        type uint8 {
          range "1..max";
        }
        default "5";
      }

      leaf max-package {
        type uint8 {
          range "1..255";
        }
        default "40";
      }
    }

    container unicast {
      leaf enabled {
        type boolean;
        default "false";
      }
    }

    container web-url-compatible {
      leaf enabled {
        type boolean;
        default "false";
      }
    }

    container map-cover-mode {
      leaf enabled {
        type boolean;
        default "false";
      }
    }

    leaf bonjour-ip {
      type uint8 {
        range "1..255";
      }
      description
        "Bonjour send ip set.";
    }

    leaf server-capacity {
      type uint8 {
        range "1..8";
      }
      default "1";
      description
        "Set tv capacity for one room.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "UPnP configuration.";

    container upnp-proxy {
      description
        "Configuration of UPnP proxy.";
      uses projection-config;

      container isolate-region {
        description
          "Set station and television net region.";
        uses station;
        uses television;
      }

      container advance {
        uses advance-config;
        description
          "Set projection screen advance configurations.";
      }

      container reserve {
        uses reserve-config;
        description
          "Set projection screen reserve configurations.";
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "UPnP state.";
    container upnp-proxy {
      description
        "State of UPnP proxy.";
      uses projection-config;

      container isolate-region {
        uses station;
        uses television;
      }

      container advance {
        uses advance-config;
        description
          "Set projection screen advance configurations.";
      }

      container reserve {
        uses reserve-config;
        description
          "Set projection screen reserve configurations.";
      }
    }
  }

  rpc upnp-proxy-show {
    description
      "Show state of UPnP.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        leaf type {
          type enumeration {
            enum television-map;
            enum bind-status;
            enum isolate-region;
            enum advance-config;
            enum import-result;
            enum export-result;
          }

          description
            "Show type of upnp-proxy.";
        }

        leaf start {
          type uint32;
          description
            "Start offset of result.";
        }

        leaf end {
          type uint32;
          description
            "End offset of result.";
        }

        leaf filter {
          type string;
          description
            "Show info by filter.";
        }

        leaf name {
          type string;
          description
            "Show info by name.";
        }

        leaf television-id {
          type string;
          description
            "Show info by name and television-id.";
        }

        leaf region-type {
          type enumeration {
            enum station-region;
            enum television-region;
          }
          description
            "Show info by name and region-type.";
        }

        leaf sorted {
          type empty;
          description
            "Formatting display.";
        }

        leaf test {
          type string;
          description
            "Show libyams info.you can input config、region、room or memory.";
        }
      }
    }

    output {
      leaf enabled {
        type boolean;
      }

      leaf bind-rule {
        type string;
      }

      leaf number {
        type uint32;
      }

      list room-bind-ip {
        key "room-id television-id";

        leaf key {
          type string;
        }

        leaf room-id {
          type string;
        }

        leaf television-id {
          type string;
        }

        leaf ip-address {
          type string;
        }

        leaf source {
          type string;
        }

        leaf description {
          type string;
        }

        leaf register-time {
          type string;
        }
      }

      list room-bind-mac {
        key "room-id television-id";

        leaf key {
          type string;
        }

        leaf room-id {
          type string;
        }

        leaf television-id {
          type string;
        }

        leaf mac-address {
          type string;
        }

        leaf source {
          type string;
        }

        leaf description {
          type string;
        }

        leaf register-time {
          type string;
        }
      }

      leaf station-region-number {
        type uint8;
      }

      list station-region {
        key "station";

        leaf station {
          type string;
        }

        leaf name {
          type string;
        }

        leaf description {
          type string;
        }

        leaf ip-set {
          type string;
        }
      }

      leaf television-region-number {
        type uint8;
      }

      list television-region {
        key "television";

        leaf television {
          type string;
        }

        leaf name {
          type string;
        }

        leaf description {
          type string;
        }

        leaf ip-set {
          type string;
        }
      }

      list bind-status {
        key "room-id";

        leaf room-id {
          type string;
        }

        leaf tv-id {
          type string;
        }

        leaf television {
          type string;
        }

        leaf station {
          type string;
        }

        leaf bind-mode {
          type string;
        }
      }

      container automatic-enrollment {
        leaf enabled {
          type boolean;
        }

        leaf registration-time {
          type uint16;
        }

        leaf logout-check-period {
          type uint16;
        }
      }

      container terminal-authorization {
        leaf enabled {
          type boolean;
        }
      }

      container linkage-service {
        leaf enabled {
          type boolean;
        }
      }

      container offline-detect {
        leaf time-range {
          type uint16;
        }

        leaf flow-rate {
          type uint8;
        }
      }

      container scheduled-offline {
        leaf enabled {
          type boolean;
        }

        leaf time {
          type string;
        }
      }

      leaf url-cipher-string {
        type string;
      }

      leaf md5-cipher-string {
        type string;
      }

      container quick-response-code-valid-time {
        leaf time {
          type uint16;
        }
      }

      container single-ip-process {
        leaf interval-time {
          type uint8;
        }

        leaf max-package {
          type uint8;
        }
      }

      container unicast {
        leaf enabled {
          type boolean;
        }
      }

      container web-url-compatible {
        leaf enabled {
          type boolean;
        }
      }

      container map-cover-mode {
        leaf enabled {
          type boolean;
        }
      }

      leaf bonjour-ip {
        type uint8;
      }

      leaf server-capacity {
        type uint8;
      }

      leaf result {
        description
          "Info of import or export result.";
        type string;
      }

      leaf success-cnt {
        description
          "Count of success import.";
        type string;
      }

      leaf cover-cnt {
        description
          "Count of cover import.";
        type string;
      }

      leaf fail-cnt {
        description
          "Count of fail import.";
        type string;
      }

      list detail {
        key "msg";

        leaf msg {
          description
            "Error prompt of import or export.";
          type string;
        }

        leaf data {
          description
            "Error detail of import or export.";
          type string;
        }

        leaf error-type {
          description
            "Error type of import.";
          type string;
        }
      }
    }
    ntos-ext:nc-cli-show "upnp-proxy";
    ntos-api:internal;
  }

  grouping room_info {
    leaf room-id {
      type string {
        pattern "[^`~!$%^&*+/|{};:\"'\\\\?]*" {
          error-message "cannot include character: `~!$%^&*+|{};:\"'\\/?";
        }
      }
      description
        "Room id";
    }

    leaf user {
      type string {
        pattern "[^`~!#$%^&*+/|{};:\"'\\\\<>?]*" {
          error-message "cannot include character: `~!#$%^&*+|{};:\"'\\/<>?";
        }
      }
      description
        "user name or ip.";
    }

    leaf tv-id {
      type string;
      description
        "television id.";
    }
  }

  rpc upnp-proxy-cmd {
    description
      "UPnP cmd.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      leaf unbind-all {
        description
          "Unbind all room.";
        type empty;
      }

      container unbind {
        uses room_info;
      }

      container bind {
        uses room_info;
      }

      leaf delete-all-map {
        description
          "Delete all auto register room.";
        type empty;
      }

      container delete-map {
        description
          "Delete auto register room.";
        uses room_info;
      }

      leaf unused-map {
        type empty;
      }

      leaf unused-station {
        type empty;
      }

      leaf unused-television {
        type empty;
      }

      leaf get-station-key {
        type string;
      }

      leaf get-television-key {
        type string;
      }

      container get-map-key {
        leaf room-id {
          type string;
        }

        leaf television-id {
          type string;
        }
      }
    }

    output {
      leaf result {
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "upnp-proxy";
    ntos-api:internal;
  }

  rpc upnp-proxy-import {
    description
      "Television config import.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      leaf file-name {
        type string;
        description
          "The path of configurations, '.csv' files are supported.";
      }

      leaf type {
        type enumeration {
          enum warning;
          enum cover;
        }
        description
          "Specifies how conflicts are handled.";
      }
    }

    output {
      leaf result {
        description
          "Info of import result.";
        type string;
      }

      list detail {
        key msg;

        leaf data {
          description
            "Error detail of import.";
          type string;
        }

        leaf msg {
          description
            "Error prompt of import.";
          type string;
        }

        leaf error-type {
          description
            "Error type of import.";
          type string;
        }
      }

      leaf success-cnt {
        description
          "Count of success import.";
        type string;
      }

      leaf cover-cnt {
        description
          "Count of cover import.";
        type string;
      }

      leaf fail-cnt {
        description
          "Count of fail import.";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "upnp-proxy-import";
    ntos-api:internal;
  }

  rpc upnp-proxy-export {
    description
      "Television config export.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      leaf type {
        type enumeration {
          enum television-map;
          enum quick-response-code;
        }

        description
          "Export type.";
      }

      leaf device-local-ip {
        type ntos-inet:ipv4-address;
      }

      leaf code-type {
        type enumeration {
          enum room-id;
          enum ip;
          enum mac;
        }

        description
          "QR code type.";
      }

      leaf selected {
        type string;
        description
          "Configurations need to be exported.";
      }

      leaf path {
        type string {
          pattern '[a-zA-Z0-9._\-/]+';
        }
        description
          "The path of configurations, '.csv' files are supported.";
      }

      leaf file-name {
        type string;
      }
    }

    output {
      leaf result {
        description
          "Info of export result.";
        type string;
      }

      list detail {
        key "msg";

        leaf msg {
          description
            "Error prompt of export.";
          type string;
        }

        leaf data {
          description
            "Error detail of export.";
          type string;
        }
      }
    }
    ntos-ext:nc-cli-cmd "upnp-proxy-export";
    ntos-api:internal;
  }
}