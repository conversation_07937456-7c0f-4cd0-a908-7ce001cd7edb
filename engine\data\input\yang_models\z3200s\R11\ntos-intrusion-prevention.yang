module ntos-intrusion-prevention {
  yang-version 1.1;

  namespace "urn:ruijie:ntos:params:xml:ns:yang:intrusion-prevention";
  prefix ntos-intrusion-prevention;

  import ntos {
    prefix ntos;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-commands {
    prefix ntos-cmd;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS Intrusion Prevention module.";

  revision 2024-07-11 {
    description
      "Add custom signature and whitelist.";
    reference
      "";
  }

  revision 2023-08-24 {
    description
      "Add rpc for getting license status.";
    reference
      "";
  }

  revision 2022-05-20 {
    description
      "Update Predefined Signature.";
    reference
      "";
  }

  revision 2022-04-11 {
    description
      "Support Exception Signature.";
    reference
      "";
  }

  revision 2022-02-10 {
    description
      "Initial revision.";
    reference
      "";
  }

  typedef target-type {
    type enumeration {
      enum client {
        description
          "Protect client.";
      }
      enum server {
        description
          "Protect server.";
      }
      enum both {
        description
          "Protect client and server.";
      }
    }
    description
      "Protection target.";
  }

  typedef severity-type {
    type enumeration {
      enum informational {
        description
          "Informational severity.";
      }
      enum low {
        description
          "Low severity.";
      }
      enum medium {
        description
          "Medium severity.";
      }
      enum high {
        description
          "High severity.";
      }
    }
    description
      "Level of the severity.";
  }

  typedef action-type {
    type enumeration {
      enum bypass {
        description
          "Flow bypass action.";
      }
      enum use-signature-action {
        description
          "Use signature action.";
      }
      enum alert {
        description
          "Alert action.";
      }
      enum block {
        description
          "Flow block action.";
      }
    }
    description
      "Action when an intrusion is detected.";
  }

  typedef signature-direction {
    type enumeration {
      enum to-server {
        description
          "Direction to the server.";
      }
      enum to-client {
        description
          "Direction to the client.";
      }
    }
  }

  typedef signature-action {
    type enumeration {
      enum bypass {
        description
          "Flow bypass action.";
      }
      enum alert {
        description
          "Alert action.";
      }
      enum block {
        description
          "Flow block action.";
      }
    }
    description
      "Action of the signature.";
  }

  typedef pattern-type {
    type enumeration {
      enum regex {
        description
          "Regex match pattern.";
      }
      enum text {
        description
          "Text match pattern.";
      }
      enum hex {
        description
          "Hex match pattern.";
      }
    }
    description
      "Match mode of signature features.";
  }

  grouping ips-template {
    description
      "The grouping of the ips-template.";
    list template {
      key "name";
      description
        "The list of the ips-template.";
      leaf name {
        type string;
        description
          "The name of the ips-template.";
      }
      leaf description {
        type string;
        description
          "The description of the ips-template.";
      }
      list exception-signatures {
        key "id";
        description
          "The list of the ips exception signatures.";
        leaf id {
          type uint32;
          description
            "Id of signature.";
        }
        leaf action {
          type action-type;
          default bypass;
          description
            "Action when hit the exception signature.";
        }
      }
      list filter {
        key "name";
        description
          "The list of the ips-signature-filter.";
        leaf name {
          type string;
          description
            "The name of the ips-signature-filter.";
        }
        uses ips-signature-filter;
      }
      container reference-list {
        container security-policy {
          description
            "Reference list of security policy.";
          leaf-list id {
            type uint32;
            description
              "Id of security policy.";
          }
        }
        container sim-security-policy {
          description
            "Reference list of simulative security policy.";
          leaf-list id {
            type uint32;
            description
              "Id of simulative security policy.";
          }
        }
        config false;
        description
          "The policy references of the template.";
      }
      leaf predefined {
        type boolean;
        config false;
        description
          "Indicate whether this is a predefined template.";
      }
    }
  }

  grouping ips-signature-filter {
    description
      "The grouping of ips-signature-filter.";
    leaf description {
      type string;
      description
        "The description of the ips-signature-filter.";
    }

    leaf target {
      type target-type;
      default server;
      description
        "Protection target filtering conditions of the ips-signature-filter.";
    }

    leaf-list severity {
      type severity-type;
      description
        "Level of the severity filtering conditions of the ips-signature-filter.";
    }

    container protocol {
      description
        "The protocol filtering conditions of the ips-signature-filter.";

      leaf all-protocol {
        type boolean;
        mandatory true;
        description
          "Indicate whether to select all of protocols.";
      }

      leaf-list specified-protocol {
        when "../all-protocol = 'false'";
        type string;
        description
          "The specified protocol filtering conditions of the ips-signature-filter.";
      }
    }

    container category {
      description
        "The category filtering conditions of the ips-signature-filter.";

      leaf all-category {
        type boolean;
        mandatory true;
        description
          "Indicate whether to select all of categories.";
      }

      list specified-category {
        when "../all-category = 'false'";
        key "name";
        description
          "The specified category filtering conditions of the specified-category.";

        leaf name {
          type string;
          description
            "The specified category filtering conditions of the specified-category.";
        }

        leaf all-sub-category {
          type boolean;
          mandatory true;
          description
            "Indicate whether to select all sub-category.";
        }

        leaf-list specified-sub-category {
          when "../all-sub-category = 'false'";
          type string;
          description
            "The specified sub-category filtering conditions of the specified-sub-category.";
        }
      }
    }
  }

  grouping ips-custom-signatures {
    description
      "The grouping of the ips custom signature.";
    list custom-signatures {
      key "id";
      description
        "The list of the ips custom signatures.";
      leaf id {
        type uint32;
        description
          "ID of custom signature, ID must be in the range of 58785793 to 58851327.";
      }
      leaf name {
        type string;
        description
          "The name of the ips custom signature.";
      }
      leaf description {
        type string;
        description
          "The description of the ips custom signature.";
      }
      leaf target {
        type target-type;
        default server;
        description
          "Protection target of the ips custom signature.";
      }
      leaf severity {
        type severity-type;
        default high;
        description
          "Level of the severity of the ips custom signature.";
      }
      leaf protocol {
        type string;
        description
          "Detection protocol of the ips custom signature.";
      }
      leaf direction {
        type signature-direction;
        default to-server;
        description
          "Ips custom signature detection orientation.";
      }
      leaf action {
        type signature-action;
        default alert;
        description
          "Action of the ips custom signature.";
      }
      list signature-features {
        key "id";
        description
          "Feature of the ips custom signature.";
        leaf id {
          type uint32;
          description
            "ID of the signature feature.";
        }
        leaf keyword {
          type string;
          description
            "Keyword of the signature feature.";
        }
        leaf match-pattern {
          type pattern-type;
          default text;
          description
            "Match pattern of the signature feature.";
        }
        leaf match-content {
          type string;
          description
            "Match content of the signature feature.";
        }
        leaf match-case {
          when "../match-pattern = 'text'";
          type boolean;
          default false;
          description
            "Whether the matched content is case sensitive.";
        }
      }
    }
  }

  grouping ips-whitelist {
    description
      "The grouping of the ips whitelist.";
    list whitelist {
      key "id source-ip dest-ip";
      description
        "The list of the ips whitelist.";
      leaf id {
        type uint32;
        description
          "Id of the signature.";
      }
      leaf source-ip {
        type string;
        description "Source ip of the whitelist.";
      }
      leaf dest-ip {
        type string;
        description "Destination ip of the whitelist.";
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "IPS configuration.";

    container ips-config {
      description
        "IPS configuration.";

      list predefined-signatures {
        key "id";
        description
          "List of predefined signature.";
        leaf id {
          type uint32;
          description
            "Id of predefined signature.";
        }
        leaf state {
          type boolean;
          description
            "State of predefined signature.";
        }
        leaf interval {
          type uint32;
          description
            "The interval time of predefined signature, in seconds.";
        }
        leaf threshold {
          type uint32;
          description
            "The threshold value of predefined signature.";
        }
        leaf block-time {
          type uint32;
          description
            "The blocking time of predefined signature, in minutes.";
        }
      }
      uses ips-template;

      list profile {
        key name;
        description
          "List of profile.";
        leaf name {
          type string;
          description
            "Profile name.";
        }
        leaf template-name {
          type string;
          mandatory true;
          description
            "Template name.";
        }
        leaf action {
          type action-type;
          mandatory true;
          description
            "The action of the profile.";
        }
      }

      container global-config {
        description
          "The information of IPS global configuration.";

        leaf blacklist-enabled {
          type boolean;
          description
            "Set status of blacklist-enabled.";
        }
      }

      uses ips-custom-signatures;

      uses ips-whitelist;

    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "State of IPS.";

    container ips-state {
      config false;
      description
        "IPS state.";

      uses ips-template;
    }
  }

  rpc ips-protocol-set-show {
    description
      "Show protocols supported.";

    output {
      list protocol {
        leaf name {
          type string;
          description
            "The name of protocol.";
        }
        description
          "The list of protocol.";
      }
    }
    ntos-ext:nc-cli-show "ips protocol-set";
  }

  rpc ips-category-set-show {
    description
      "Show category supported.";

    output {
      list category {
        description
          "The list of category.";

        leaf name {
          type string;
          description
            "The name of category.";
        }
        leaf id {
          type uint32;
          description
            "The id of category.";
        }
        list sub-category {
          description
            "The list of sub-category.";

          leaf name {
            type string;
            description
              "The name of sub-category.";
          }

          leaf id {
            type uint32;
            description
              "The id of sub-category.";
          }
        }
      }
    }
    ntos-ext:nc-cli-show "ips category-set";
  }

  rpc ips-template-show {
    description
      "Show defined templates.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The name of VRF.";
      }
      leaf start {
        type uint32;
        description
          "The start offset.";
      }
      leaf end {
        type uint32;
        description
          "The end offset.";
      }
      leaf name {
        type string;
        description
          "The name of template.";
      }
      leaf predefined {
        type boolean;
        default false;
        description
          "Show predefined templates or custom templates.";
      }
      leaf fuzzy-match {
        type boolean;
        default false;
        description
          "Whether to perform fuzzy match.";
      }
    }

    output {
      leaf template-sum {
        type uint32;
        description
          "The total number of templates.";
      }
      uses ips-template;
    }
    ntos-ext:nc-cli-show "ips template";
  }

  rpc ips-signature-filter-show {
    description
      "Show defined filters.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The name of VRF.";
      }
      leaf start {
        type uint32;
        description
          "The start offset.";
      }
      leaf end {
        type uint32;
        description
          "The end offset.";
      }
      leaf template {
        type string;
        mandatory true;
        description
          "The name of template.";
      }
      leaf name {
        type string;
        description
          "The name of signature-filter.";
      }
    }

    output {
      list filter {
        key "name";
        description
          "The list of the ips-signature-filter.";

        leaf name {
          type string;
          description
            "The name of the ips-signature-filter.";
        }
        uses ips-signature-filter;
      }
      leaf filter-sum {
        type uint32;
        description
          "The total number of filters.";
      }
    }
    ntos-ext:nc-cli-show "ips filter";
  }

  rpc ips-signature-update {
    description
      "Update IPS signature.";

    input {
      leaf id {
        type uint32;
        description
          "Id of upgrade process.";
      }
      leaf package-name {
        type string;
        description
          "Name of upgrade package.";
      }
    }

    output {
      leaf id {
        type uint32;
        description
          "Id of tracing upgrade.";
      }
      leaf progress {
        type uint32;
        description
          "Progress of upgrade.";
      }
      leaf errnum {
        type uint32;
        description
          "Error code.";
      }
    }
    ntos-ext:nc-cli-cmd "ips signature-update";
  }

  rpc ips-signature-version-show {
    description
      "Show IPS signature version.";

    output {
      leaf version {
        type string;
        description
          "Version of the signature.";
      }
    }
    ntos-ext:nc-cli-show "ips signature-version";
  }

  rpc ips-fast-path-cmd {
    description
      "Run IPS fast path command.";

    input {
      leaf run {
        type string;
        description
          "Command to run.";
      }
    }
    output {
      leaf result {
        type string;
        description
          "Result of command execution.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "ips";
  }


  rpc ips-parser-fast-path-cmd {
    description
      "Run app-parser fast path command.";

    input {
      leaf run {
        type string;
        description
          "Command to run.";
      }
    }
    output {
      leaf result {
        type string;
        description
          "Result of command execution.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "app-parser";
  }

  rpc ips-signature-rollback {
    description
      "Rollback IPS signature.";

    input {
      leaf mod {
        type string;
        description
          "Mod of rollback process.";
      }
      leaf id {
        type uint32;
        description
          "Id of rollback process.";
      }
      leaf rollback-type {
        type string;
        description
          "Type of rollback process.";
      }
    }

    output {
      leaf mod {
        type string;
        description
          "Mod of tracing rollback.";
      }
      leaf res {
        type uint32;
        description
          "Result of tracing rollback.";
      }
      leaf progress {
        type uint32;
        description
          "Progress of rollback.";
      }
      leaf errnum {
        type uint32;
        description
          "Error code.";
      }
    }
    ntos-ext:nc-cli-cmd "ips signature-rollback";
  }

  rpc ips-rollback-version {
    output {
      leaf buffer {
        type string;
        description
          "Signature version of rollback.";
      }
    }
  }

  rpc ips-get-enable-status {
    description
      "Get IPS enable status.";

    output {
      leaf enabled {
        type boolean;
        description
          "IPS enable status.";
      }

      uses ntos-cmd:long-cmd-output;
    }

    ntos-ext:nc-cli-show "ips enable-status";
  }

  rpc ips-blacklist-enabled-show {
    description
      "Status of blacklist-enabled.";

    output {
      leaf blacklist-enabled {
        type boolean;
        description
          "Status of blacklist-enabled.";
      }
    }
    ntos-ext:nc-cli-show "ips blacklist-enabled";
  }

  rpc show-ips-custom-signature {
    description
      "Custom signature information.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The name of VRF.";
      }
      leaf id {
        type uint32;
        description
          "Id of the custom signature.";
      }
    }

    output {
      leaf available-signature-id {
        type uint32;
        description
          "This number is the available signature ID.";
      }
      leaf signature-sum {
        type uint32;
        description
          "The total number of custom signatures.";
      }
      uses ips-custom-signatures;
    }
    ntos-ext:nc-cli-show "ips custom-signature";
  }

  rpc show-ips-protocol-keyword {
    description
      "Show the supported protocols and keywords.";

    output {
      list protocol-keyword {
        key "protocol";
        description
          "The list of supported protocols and keywords.";

        leaf protocol {
          type string;
          description
            "The name of protocol.";
        }
        leaf direction {
          type string;
          description
            "The name of direction.";
        }
        list keyword {
          key "name";
          description
            "The list of keyword.";
          leaf name {
            type string;
            description
              "The name of keyword.";
          }
        }
      }
    }
    ntos-ext:nc-cli-show "ips custom-signature protocol-keyword";
  }

  rpc show-ips-whitelist {
    description
      "Show ips whitelist.";

    input {
      leaf vrf {
        type string;
        default "main";
        description
          "The name of VRF.";
      }
      leaf start {
        type uint32;
        description
          "The start offset.";
      }
      leaf end {
        type uint32;
        description
          "The end offset.";
      }
      leaf id {
        type uint32;
        description
          "Id of the signature.";
      }
      leaf source-ip {
        type string;
        description "Source ip of the whitelist.";
      }
      leaf dest-ip {
        type string;
        description "Destination ip of the whitelist.";
      }
      leaf fuzzy-match {
        type string;
        description "Fuzzy matching string.";
      }
    }

    output {
      leaf whitelist-sum {
        type uint32;
        description
          "The total number of whitelist.";
      }
      uses ips-whitelist;
    }
    ntos-ext:nc-cli-show "ips whitelist";
  }

}
