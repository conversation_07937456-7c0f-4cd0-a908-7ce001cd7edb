package libs

import (
	"fmt"
	"io/ioutil"
	"os"
	"time"

	"github.com/pkg/sftp"
	"golang.org/x/crypto/ssh"
)

type SshClient struct {
	SSH  *ssh.Client
	SFTP *sftp.Client
}

func (sc *SshClient) Connect(user, host string, port int, password string) error {
	var (
		auth         []ssh.AuthMethod
		addr         string
		clientConfig *ssh.ClientConfig
		client       *ssh.Client
		err          error
	)

	auth = make([]ssh.AuthMethod, 0)

	auth = append(auth, ssh.Password(password))

	clientConfig = &ssh.ClientConfig{
		User: user,
		Auth: auth,
		// Timeout:             30 * time.Second,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// connet to ssh
	addr = fmt.Sprintf("%s:%d", host, port)

	if client, err = ssh.Dial("tcp", addr, clientConfig); err != nil {
		return err
	}

	sftp, err := sftp.NewClient(client)
	if err != nil {
		return err
	}
	sc.SSH = client
	sc.SFTP = sftp

	return nil
}

func (sc *SshClient) UploadFile(src, dst string) error {
	now := time.Now()
	f, err := sc.SFTP.Open(dst)
	if err == nil {
		defer f.Close()
		// 备份已有文件
		fileByte, err := ioutil.ReadAll(f)
		if nil != err {
			return err
		}

		dstFile, err := sc.SFTP.Create(fmt.Sprintf("%s.bak_%s%v", dst, now.Format("20060102150405"), now.Nanosecond()/1e3))
		if nil != err {
			return err
		}
		defer dstFile.Close()
		_, err = dstFile.Write(fileByte)
		if err != nil {
			return err
		}
	}

	srcFile, err := os.Open(src)
	if nil != err {
		return err
	}
	defer srcFile.Close()

	dstFile, err := sc.SFTP.Create(dst)
	if nil != err {
		return err
	}
	defer dstFile.Close()

	fileByte, err := ioutil.ReadAll(srcFile)
	if nil != err {
		return err
	}

	_, err = dstFile.Write(fileByte)
	if err != nil {
		return err
	}
	return nil
}
