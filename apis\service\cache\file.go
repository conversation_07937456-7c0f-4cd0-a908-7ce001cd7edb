package cache

import (
	"sync"
)

type Response struct {
	Id           uint   `json:"id"`
	Name         string `json:"name"`
	Objdump      bool   `json:"objdump"`
	FileCheck    string `json:"file_check"`
	FileMime     string `json:"file_mime"`
	Ext          string `json:"ext"`
	CustomHeader string `json:"custom_header"`
	Size         int    `json:"size"`
	Users        string `json:"users"`
}

var AutoAuditFiles []*Response

type AutoAduitMd5Response struct {
	Md5 string `json:"md5"`
}

var AutoAuditMd5Cache sync.Map
var AutoAuditMd5Change int64
