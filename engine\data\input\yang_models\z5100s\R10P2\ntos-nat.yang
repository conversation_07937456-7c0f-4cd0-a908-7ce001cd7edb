module ntos-nat {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:nat";
  prefix ntos-nat;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-firewall-types {
    prefix ntos-fwt;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ietf-yang-types {
    prefix ietf-yang;
  }
  //import ntos-system {
  //  prefix ntos-sys;
  //}
  //import ntos-fast-path {
  //  prefix ntos-fp;
  //}
  //import ntos-network-obj {
  //  prefix ntos-network-obj;
  //}
  //import ntos-time-range {
  //  prefix ntos-time-range;
  //}
  //import ntos-security-zone {
  //  prefix ntos-security-zone;
  //}

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS NAT module.";

  revision 2025-05-26 {
    description
      "Add specify the IP prefix to be translated.";
    reference "";
  }

  revision 2024-05-30 {
    description
      "Add sip-port-check.";
    reference "";
  }

  revision 2023-04-19 {
    description
      "Modify nat66 prefix check.";
    reference "";
  }

  revision 2022-12-02 {
    description
      "Add policy hit counter.";
    reference "";
  }

  revision 2022-04-08 {
    description
      "Modify twice-nat44 node name.";
    reference "";
  }

  revision 2022-02-08 {
    description
      "Initial version.";
    reference "";
  }

  revision 2021-12-23 {
    description
      "Initial version.";
    reference "";
  }

  revision 2019-05-24 {
    description
      "Initial version.";
    reference "";
  }

  identity nat {
    base ntos-types:SERVICE_LOG_ID;
    description
      "NAT service.";
    ntos-ext:nc-cli-identity-name "nat";
  }

  typedef nat66-ipv6-prefix {
    type string {
      pattern '((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}'
            + '((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|'
            + '(((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\.){3}'
            + '(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))'
            + '(/(([0-9])|([0-9]{2})|(1[0-1][0-9])|(12[0-8])))' {
        error-message "Invalid IPv6 prefix.";
      }
      pattern '(([^:]+:){6}(([^:]+:[^:]+)|(.*\..*)))|'
            + '((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?)'
            + '(/.+)' {
        error-message "Invalid IPv6 prefix.";
      }
      ntos-ext:nc-cli-shortdesc "<X:X::X:X/M>";
    }
    description
      "An IPv6 prefix: address and CIDR mask.";
    ntos-api:pattern-stable;
  }

  typedef pool-ipv4-address {
    type union {
      type ntos-inet:ipv4-address {
        ntos-ext:nc-cli-shortdesc "<ipv4-address>";
      }
      type ntos-inet:ipv4-range {
        ntos-ext:nc-cli-shortdesc "<ipv4-range>";
      }
    }
    description
      "An IPv4 address or addresses range.";
  }

  typedef protocol-type {
    type enumeration {
      enum "tcp" {
        description
          "TCP";
      }
      enum "udp" {
        description
          "UDP";
      }
      enum "tcp+udp" {
        description
          "TCP & UDP";
      }
    }
    description
      "Protocol type.";
  }

  grouping pool-parameters {
    description
      "Parameters of the NAT pool addresses.";

    list address {
      key "value";
      min-elements 1;
      leaf value{
        type pool-ipv4-address;
        ntos-ext:nc-cli-no-name;
      }
      ntos-ext:nc-cli-one-liner;
      description
        "IPv4 addresses in the pool.";
    }
  }

  grouping port-parameters {
    description
      "Parameters of the NAT port.";
    leaf port {
      type ntos-fwt:port-range;
      ntos-ext:nc-cli-one-liner;
    }
  }

  grouping dest-zone-match {
    description
      "Destination zone configuration.";

    list dest-zone {
      key "name";

      description
        "Destination zone.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
      ntos-ext:nc-cli-one-liner;
    }
  }

  grouping source-zone-match {
    description
      "Source zone configuration.";

    list source-zone {
      key "name";

      description
        "Source zone.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
      ntos-ext:nc-cli-one-liner;
    }
  }

  grouping dest-interface-match {
    description
      "Destination interface configuration.";

    list dest-interface {
      description
        "The name of dest interface.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ifname;
        ntos-ext:nc-cli-no-name;
      }
    }
  }

  grouping source-interface-match {
    description
      "Source interface configuration.";

    list source-interface {
      description
        "The name of source interface.";
      ntos-ext:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ifname;
        ntos-ext:nc-cli-no-name;
      }
    }
  }

  grouping time-range-match {
    description
      "Time range configuration.";

    container time-range {
      description
        "Name of a time range.";

      leaf value {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
    }
  }

  grouping service-match {
    description
      "Service configuration.";

    list service {
      key "name";
      description
        "Name of a service object.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }
      ntos-ext:nc-cli-one-liner;
    }
  }

  grouping source-network-match {
    description
      "Source network configuration.";

    list source-network {
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
       ntos-ext:nc-cli-no-name;
      }
      ntos-ext:nc-cli-one-liner;
      description
        "Name of a source network group.";
    }
  }

  grouping dest-network-match {
    description
      "Destination network configuration.";

    choice match-type {
      case dest-network {
        list dest-network {
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
            ntos-ext:nc-cli-no-name;
          }
          ntos-ext:nc-cli-one-liner;
          description
            "Name of a destination network group.";
        }
      }
      case dest-if {
        leaf dest-if {
          type ntos-types:ifname;
          must '../../../static-dnat44 or ../../../twice-nat44' {
            error-message "Destination interface only support on static-dnat44 or twice-nat44.";
          }
          description
            "Interface to match on destination IP address.";
          ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']|
            /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='ipv4']/
             *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface'] |
             /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='ipv4']/
             *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
        }
      }
    }
  }

  grouping dest-address-match {
    description
      "Destination ip configuration.";

    choice match-type {
      default dest-address;
      case dest-address {
        leaf dest-address {
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<ipv4-address>";
          }
          default "0.0.0.0";
        }
      }
      case dest-if {
        leaf dest-if {
          type ntos-types:ifname;
          description
            "Interface to match on destination IP address.";
          ntos-ext:nc-cli-completion-xpath
            "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']|
            /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='ipv4']/
             *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface'] |
             /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='ipv4']/
             *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
        }
      }
    }
    uses port-parameters;
  }

  grouping protocol-match {
    description
      "Protocol configuration.";
    leaf protocol {
      type protocol-type;
    }
  }

  grouping rule-dest-nat-match {
    description
      "Match for destination NAT rule.";

    container match {
      description
        "Match parameters.";

      uses source-network-match;
      uses dest-network-match;
      uses source-zone-match;
      uses source-interface-match;
      uses time-range-match;
      uses service-match;
    }
  }

  grouping rule-source-nat-match {
    description
      "Match for source NAT rule.";

    container match {
      description
        "Match parameters.";
      uses source-network-match;
      uses dest-network-match;
      uses time-range-match;
      uses dest-zone-match;
      uses dest-interface-match;
      uses source-zone-match;
      uses source-interface-match;
      uses service-match;
    }
  }

  grouping rule-port-mapping-match {
    description
      "Match for port mapping rule.";

    container match {
      description
        "Match parameters.";
      uses time-range-match;
      uses source-zone-match;
      uses dest-address-match;
      uses protocol-match;
    }
  }

  grouping alg {
    description
      "Application-Level Gateway.";

    leaf alg {
      type bits {
        bit ftp {
          description
            "ALG for File Transfer Protocol.";
        }
        bit h323-q931 {
          description
            "ALG for H.225.0 Call Signaling Protocol.";
        }
        bit h323-ras {
          description
            "ALG for H.225.0 Registration, Admission and Status Protocol.";
        }
        bit pptp {
          description
            "ALG for Point-to-Point Tunneling Protocol.";
        }
        bit rtsp {
          description
            "ALG for Real Time Streaming Protocol.";
        }
        bit sip-tcp {
          description
            "ALG for Session Initiation Protocol over TCP.";
        }
        bit sip-udp {
          description
            "ALG for Session Initiation Protocol over UDP.";
        }
        bit tftp {
          description
            "ALG for Trivial File Transfer Protocol.";
        }
        bit dns-udp {
          description
            "ALG for Domain Name System over UDP.";
        }
      }
      description
        "Application-Level Gateway.";
    }

    container sip-port-check {
      description
        "To check the parity of RTP/RTCP ports.";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable the parity check for RTP/RTCP ports.";
      }
    }
  }

  grouping dynamic-translate-parameters {
    description
      "Dynamic Translation Parameters.";

    leaf pool-name {
      type ntos-types:ntos-obj-name-type;
      mandatory true;
      description
        "Name of IP address pool used for translation.";
    }
  }

  grouping static-translate-parameters {
    description
      "Static Translation Parameters.";

    leaf ipv4-address {
      type ntos-inet:ipv4-address {
        ntos-ext:nc-cli-shortdesc "<ipv4-address>";
      }
      mandatory true;
      description
        "Translated Address.";
    }
  }

  grouping port-mapping-translate-parameters {
    description
      "Static Translation Parameters.";

    leaf ipv4-address {
      type ntos-inet:ipv4-address {
        ntos-ext:nc-cli-shortdesc "<ipv4-address>";
      }
      description
        "Translated Address.";
    }
    uses port-parameters;
  }

  typedef nat-category {
    type union {
      type enumeration {
        enum nat44 {
          description
            "IPv4 protocol to IPv4 protocol.";
        }
        enum nat46 {
          description
            "IPv4 protocol to IPv6 protocol.";
        }
        enum nat64 {
          description
            "IPv6 protocol to IPv4 protocol.";
        }
        enum nat66 {
          description
            "IPv6 protocol to IPv6 protocol.";
        }
        enum port-mapping {
          description
            "Port mapping.";
        }
      }
    }
  }

  grouping static-natpt-config {
    container static-natpt {
      description
        "Static NAT-PT translate category, from ipv4 to ipv6.";

      uses rule-dest-nat-match;
      container translate-to {
        leaf nat64-prefix {
          type string;
        }
        leaf source-ip {
          type ntos-inet:ipv6-address;
        }
        leaf dest-ip {
          type ntos-inet:ipv6-address;
        }
      }
    }
  }

  grouping dynamic-natpt-config {
    container dynamic-natpt {
      description
        "Dynamic NAT-PT translate category, with pat or no-pat, from ipv6 to ipv4.";

      uses rule-dest-nat-match;
      container translate-to {
        leaf nat64-prefix {
          type string;
        }
        leaf source-ip-pool {
          type string;
        }

        leaf source-port-block {
          type ntos-fwt:port-range {
            pattern '[0-9]+-[0-9]+' {
              error-message "Invalid port range format. Example: '1024-2048'";
            }
          }
        }

        leaf dest-ip {
          type ntos-inet:ipv4-filter-invalid-address;
        }
      }
    }
  }

  grouping stateless-nat64-config {
    container stl-nat64 {
      description
        "Stateless NAT64 translate category, from ipv4 to ipv6.";

      uses rule-dest-nat-match;
      container translate-to {
        leaf nat64-prefix {
          mandatory true;
          type string;
        }
      }
    }
  }

  grouping static-stateful-nat64-config {
    container static-stf-nat64 {
      description
        "Static Stateful NAT64 translate category, from ipv4 to ipv6.";

      uses rule-dest-nat-match;
      container translate-to {
        leaf nat64-prefix {
          type string;
        }

        leaf dest-ip {
          type ntos-inet:ipv6-address;
        }

        leaf dest-port {
          type uint16;
        }
      }
    }
  }

  grouping dynamic-stateful-nat64-config {
    container dynamic-stf-nat64 {
      description
        "Static Stateful NAT64 translate category, from ipv6 to ipv4.";

      uses rule-dest-nat-match;
      container translate-to {
        leaf nat64-prefix {
          type string;
        }

        leaf source-ip-pool {
          type string;
        }

        leaf source-port-block {
          type ntos-fwt:port-range {
            pattern '[0-9]+-[0-9]+' {
              error-message "Invalid port range format. Example: '1024-2048'";
            }
          }
        }

        leaf enabled-eif {
          description
            "Default value is false and default behavior is Address Dependent Filter.";
          type boolean;
          default false;
        }
      }
    }
  }

  grouping source-npt-config {
    container source-npt {
      description
        "Stateless Source Network Prefix translate for IPv6, from IPv6 to IPv6.";
      uses rule-dest-nat-match;
      container translate-to {
        leaf nat66-prefix {
          type nat66-ipv6-prefix;
        }
      }
    }
  }

  grouping dest-npt-config {
    container dest-npt {
      description
        "Stateless Dest Network Prefix translate for IPv6, from IPv6 to IPv6.";
      uses rule-dest-nat-match;
      container translate-to {
        leaf nat66-prefix {
          type nat66-ipv6-prefix;
        }
      }
    }
  }

  grouping dynamic-snat44-config {
    container dynamic-snat44 {
      presence "dynamic-snat44.";
      description
        "Dynamic source NAT44 translation.";
      uses rule-source-nat-match;

      container translate-to {
        description
          "Translate to.";
        uses dynamic-translate-parameters;
      }
    }
  }

  grouping static-dnat44-config {
    container static-dnat44 {
      presence "static-dnat44.";
      description
        "Static destination NAT44 translation.";
      uses rule-dest-nat-match;

      container translate-to {
        description
          "Translate to.";

        choice address-type {
          mandatory true;
          case ip {
            uses static-translate-parameters;
          }
          case prefix {
            leaf ipv4-prefix {
              type ntos-inet:ipv4-prefix {
                ntos-ext:nc-cli-shortdesc "<ipv4-prefix>";
              }
              description
                "Specify the IP prefix to be translated.";
            }
          }
        }

        choice port-type {
          case port {
            leaf port {
              type ntos-inet:port-number;
              description
                "Translate to a port.";
            }
          }
        }
      }
    }
  }

  grouping static-snat44-config {
    container static-snat44 {
      presence "static-snat44.";
      description
        "Static source NAT44 translation.";
      uses rule-source-nat-match;

      container translate-to {
        description
          "Translate to.";

        choice address-type {
          mandatory true;
          case ip {
            uses static-translate-parameters;
          }
          case interface {
            leaf output-address {
              type empty;
              description
                "Translate to the address found on the outgoing interface.";
            }
          }
          case prefix {
            leaf ipv4-prefix {
              type ntos-inet:ipv4-prefix {
                ntos-ext:nc-cli-shortdesc "<ipv4-prefix>";
              }
              description
                "Specify the IP prefix to be translated.";
            }
          }
        }
        leaf no-pat {
          type boolean;
          default "false";
          description
            "Do not convert source port when value is true.";
          ntos-ext:nc-cli-one-liner;
        }
        leaf try-no-pat {
          type boolean;
          default "true";
          description
            "Try not to convert source port when value is true,
             this value is invalid when no-pat is true.";
          ntos-ext:nc-cli-one-liner;
        }
      }
    }
  }

  grouping twice-nat44-config {
    container twice-nat44 {
      presence "twice-nat44.";
      description
        "Twice NAT44 translation.";

      uses rule-dest-nat-match;

      container snat {
        choice address-type {
          mandatory true;
          case pool {
            leaf pool-name {
              type ntos-types:ntos-obj-name-type;
              mandatory true;
              description
                "Name of IP address pool used for translation.";
            }
          }
          case ip {
            uses static-translate-parameters;
          }
          case interface {
            leaf output-address {
              type empty;
              description
                "Translate to the address found on the outgoing interface.";
            }
          }
        }
        leaf no-pat {
          type boolean;
          default "false";
          description
            "Do not convert source port when value is true.";
          ntos-ext:nc-cli-one-liner;
        }
        leaf try-no-pat {
          type boolean;
          default "true";
          description
            "Try not to convert source port when value is true,
             this value is invalid when no-pat is true.";
          ntos-ext:nc-cli-one-liner;
        }
      }

      container dnat {
        description
          "dnat translation.";

        uses static-translate-parameters;
        choice port-type {
          case port {
            leaf port {
              type ntos-inet:port-number;
              description
                "Translate to a port.";
            }
          }
        }
      }
    }
  }

  grouping port-mapping44-config {
    container port-mapping44 {
      presence "port-mapping.";
      description
        "Port mapping.";
      uses rule-port-mapping-match;

      container translate-to {
        description
          "Translate to.";
        uses port-mapping-translate-parameters;
      }
      container permit-inside {
        description
          "Port mapping permit inside configuration.";
        leaf enabled {
          type boolean;
          default "false";
          description
            "Enable or disable permit inside.";
        }
      }
    }
  }

  grouping nat-config {
    description
      "NAT configuration.";

    container nat {
      description
        "NAT configuration.";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable NAT in this VRF.";
      }

      list prefix {
        key "name";
        max-elements 1000;
        must 'count(./address) > 0 and count(./length) > 0' {
          error-message "must be config prefix's length.";
        }

        leaf name {
          type string {
            length "1..128" {
              error-message
                "The length of this string must be in 1..64.";
            }
          }
          description
            "config this prefix special name.";
        }

        leaf address {
          type ntos-inet:ipv6-address {
            pattern '(([fF]{2}[0-9a-fA-F]{2}):).*' {
              modifier invert-match;
              error-message "Invalid IPv6 multicast address.";
            }
            pattern '[fF][eE]80:.*' {
              modifier invert-match;
              error-message "Invalid IPv6 link-local address.";
            }
          }
          description
            "config IPv6 prefix.";
        }

        leaf length {
          type uint8;
          description
            "config IPv6 prefix length.";
        }
      }

      list pool {
        key "name";
        description
          "Pools of IP addresses for the NAT rules.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "Pool name.";
        }
        leaf desc {
          type ntos-types:ntos-obj-description-type;
          ntos-ext:nc-cli-one-liner;
          description
            "Pool description.";
        }
        uses pool-parameters;
      }

      list rule {
        key "name";
        description
          "List of NAT rules.";

        ordered-by user;

        leaf rule_en {
          type boolean;
          default "true";
          description
            "Enable or disable this rule.";
        }

        leaf name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-one-liner;
          description
            "Rule name.";
        }
        leaf desc {
          type ntos-types:ntos-obj-description-type {
            length "0..255";
          }
          ntos-ext:nc-cli-one-liner;
          description
            "Rule description.";
        }

        choice nat-type {
          mandatory true;

          case choice-static-natpt {
            uses static-natpt-config;
          }
          case choice-dynamic-natpt {
            uses dynamic-natpt-config;
          }
          case choice-stateless-nat64 {
            uses stateless-nat64-config;
          }
          case choice-static-stateful-nat64 {
            uses static-stateful-nat64-config;
          }
          case choice-dynamic-stateful-nat64 {
            uses dynamic-stateful-nat64-config;
          }
          case choice-source-npt {
            uses source-npt-config;
          }
          case choice-dest-npt {
            uses dest-npt-config;
          }
          case choice-dynamic-snat44 {
            uses dynamic-snat44-config;
          }
          case choice-static-dnat44 {
            uses static-dnat44-config;
          }
          case choice-static-snat44 {
            uses static-snat44-config;
          }
          case choice-twice-nat44 {
            uses twice-nat44-config;
          }
        }
      }

      uses alg;
    }
  }

  grouping port-mapping-config {
    description
      "Port-mapping configuration.";

    container port-mapping {
      description
        "Port-mapping configuration.";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable port-mapping in this VRF.";
      }

      list rule {
        key "name";
        description
          "List of port-mapping rules.";

        ordered-by user;

        leaf rule_en {
          type boolean;
          default "true";
          description
            "Enable or disable this rule.";
        }

        leaf name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-one-liner;
          description
            "Rule name.";
        }
        leaf desc {
          type ntos-types:ntos-obj-description-type {
            length "0..255";
          }
          ntos-ext:nc-cli-one-liner;
          description
            "Rule description.";
        }

        choice port-mapping-type {
          mandatory true;

          case choice-port-mapping44 {
            uses port-mapping44-config;
          }
        }
      }
    }
  }

  grouping pool-name {
    description
      "IP address pool name.";

    leaf pool-name {
      type string;
      mandatory true;
      description
        "IP address pool name.";
    }
  }

  grouping cmd-output-buffer {
    description
      "Command output buffer.";

    leaf buffer {
      type string;
      description
        "Command output buffer since last request.";
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  rpc show-alg-type {
    description
      "Show ALG type.";
    output {
      uses cmd-output-buffer;
    }
    ntos-ext:feature "nat";
    ntos-api:internal;
  }

  grouping match-if-info {
    leaf ip-address {
      type ntos-inet:ipv4-address;
      description
        "The IP address of interface.";
    }

    leaf ifname {
      type ntos-types:ifname;
      description
        "Interface name.";
      ntos-ext:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*/*[local-name()='name']";
    }

    leaf master-ifname {
      type ntos-types:ifname;
      description
        "The name of master interface.";
      ntos-ext:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*/*[local-name()='name']";
    }
  }
  
  grouping show-nat-policy-match {
    description
      "Show NAT policy match.";

    container match {
      description
        "Match parameters.";

      list source-network {
        key "name";
        leaf name {
          type string;
          ntos-ext:nc-cli-no-name;
        }
        ntos-ext:nc-cli-one-liner;
        description
          "Match on source network group.";
      }

      choice match-type {
        case dest-network {
          list dest-network {
            key "name";
            leaf name {
              type ntos-types:ntos-obj-name-type;
              ntos-ext:nc-cli-no-name;
            }
            ntos-ext:nc-cli-one-liner;
            description
              "Name of a destination network group.";
          }
        }
        case dest-if {
          container dest-if {
            uses match-if-info;
          }
        }
      }

      container time-range {
        description
          "Match a time range.";

        leaf value {
          type string;
          description
            "The time range to match.";
          ntos-ext:nc-cli-no-name;
        }
      }

      list dest-zone {
        key "name";
        description
          "Destination zone.";

        leaf name {
          type string;
          description
            "The zone to match.";
          ntos-ext:nc-cli-no-name;
        }
        ntos-ext:nc-cli-one-liner;
      }

      list dest-interface {
        key "name";
        description
          "Destination interface.";

        leaf name {
          type string;
          description
            "The interface to match.";
          ntos-ext:nc-cli-no-name;
        }
        ntos-ext:nc-cli-one-liner;
      }

      list source-zone {
        key "name";
        description
          "Source zone.";

        leaf name {
          type string;
          description
            "The zone to match.";
          ntos-ext:nc-cli-no-name;
        }
        ntos-ext:nc-cli-one-liner;
      }

      list source-interface {
        key "name";
        description
          "Source interface.";

        leaf name {
          type string;
          description
            "The interface to match.";
          ntos-ext:nc-cli-no-name;
        }
        ntos-ext:nc-cli-one-liner;
      }

      list service {
        key "name";
        description
          "Match a service object.";

        leaf name {
          type string;
          description
            "The service object to match.";
          ntos-ext:nc-cli-no-name;
        }
        ntos-ext:nc-cli-one-liner;
      }
    }
  }

  grouping show-port-mapping-match {
    description
      "Show port mapping match.";

    container match {
      description
        "Match parameters.";

      container time-range {
        description
          "Match a time range.";

        leaf value {
          type string;
          description
            "The time range to match.";
          ntos-ext:nc-cli-no-name;
        }
      }

      list source-zone {
        key "name";
        description
          "Source zone.";

        leaf name {
          type string;
          description
            "The zone to match.";
          ntos-ext:nc-cli-no-name;
        }
        ntos-ext:nc-cli-one-liner;
      }

      choice match-type {
        case dest-address {
          leaf dest-address {
            type ntos-inet:ipv4-address;
            description
              "Destination address.";
          }
        }
        case dest-if {
          container dest-if {
            uses match-if-info;
          }
        }
      }

      leaf port {
        type ntos-fwt:port-range;
        description
          "Destination port.";
      }

      leaf protocol {
        type protocol-type;
        description
          "Protocol type.";
      }
    }
  }

  grouping show-dynamic-snat44-translate-to {
    container translate-to {
      leaf pool-name {
        type string;
      }
    }
  }

  grouping show-static-dnat44-translate-to {
    container translate-to {
      choice address-type {
        case ip {
          leaf ipv4-address {
            type ntos-inet:ipv4-address {
              ntos-ext:nc-cli-shortdesc "<ipv4-address>";
            }
          }
        }
        case prefix {
          leaf ipv4-prefix {
            type ntos-inet:ipv4-prefix {
              ntos-ext:nc-cli-shortdesc "<ipv4-prefix>";
            }
          }
        }
      }

      choice port-type {
        case port {
          leaf port {
            type ntos-inet:port-number;
          }
        }
      }
    }
  }

  grouping show-static-snat44-translate-to {
    container translate-to {
      choice address-type {
        case ip {
          leaf ipv4-address {
            type ntos-inet:ipv4-address {
              ntos-ext:nc-cli-shortdesc "<ipv4-address>";
            }
          }
        }
        case interface {
          leaf output-address {
            type empty;
          }
        }
        case prefix {
          leaf ipv4-prefix {
            type ntos-inet:ipv4-prefix {
              ntos-ext:nc-cli-shortdesc "<ipv4-prefix>";
            }
          }
        }
      }
      leaf no-pat {
        type boolean;
      }
      leaf try-no-pat {
        type boolean;
      }
    }
  }

  grouping show-twice-nat44-translate-to {
    container snat {
      choice snat_type {
        case dynamic-snat44 {
          container dynamic-snat44 {
             container translate-to {
              leaf pool-name {
                type string;
              }
            }
          }
        }

        case static-snat44 {
          container static-snat44 {
            container translate-to {
              choice address-type {
                case ip {
                  leaf ipv4-address {
                    type ntos-inet:ipv4-address {
                      ntos-ext:nc-cli-shortdesc "<ipv4-address>";
                    }
                  }
                }
                case interface {
                  leaf output-address {
                    type empty;
                  }
                }
              }
              leaf no-pat {
                type boolean;
              }
              leaf try-no-pat {
                type boolean;
              }
            }
          }
        }
      }
    }

    container dnat {
      container static-dnat44 {
        container translate-to {
          leaf ipv4-address {
            type ntos-inet:ipv4-address {
              ntos-ext:nc-cli-shortdesc "<ipv4-address>";
            }
          }

          choice port-type {
            case port {
              leaf port {
                type ntos-inet:port-number;
              }
            }
          }
        }
      }
    }
  }

  grouping show-static-natpt-translate-to {
    container translate-to {
      leaf nat64-prefix {
        type string;
      }
      leaf source-ip {
        type ntos-inet:ipv6-address;
      }
      leaf dest-ip {
        type ntos-inet:ipv6-address;
      }
    }
  }

  grouping show-dynamic-natpt-translate-to {
    container translate-to {
      leaf nat64-prefix {
        type string;
      }
      leaf source-ip-pool {
        type string;
      }

      leaf source-port-block {
        type ntos-fwt:port-range;
      }

      leaf dest-ip {
        type ntos-inet:ipv4-address;
      }
    }
  }

  grouping show-stateless-nat64-translate-to {
    container translate-to {
      leaf nat64-prefix {
        type string;
      }
    }
  }

  grouping show-static-stateful-nat64-translate-to {
    container translate-to {
      leaf nat64-prefix {
        type string;
      }

      leaf dest-ip {
        type ntos-inet:ipv6-address;
      }

      leaf dest-port {
        type uint16;
      }
    }
  }

  grouping show-dynamic-stateful-nat64-translate-to {
    container translate-to {
      leaf nat64-prefix {
        type string;
      }

      leaf source-ip-pool {
        type string;
      }

      leaf source-port-block {
        type ntos-fwt:port-range;
      }

      leaf enabled-eif {
        type boolean;
      }
    }
  }

  grouping show-source-npt-translate-to {
    container translate-to {
      leaf nat66-prefix {
        type string;
      }
    }
  }

  grouping show-dest-npt-translate-to {
    container translate-to {
      leaf nat66-prefix {
        type string;
      }
    }
  }

  grouping show-port-mapping-translate-to {
    container translate-to {
      leaf ipv4-address {
        type ntos-inet:ipv4-address;
        description
          "Translated Address.";
      }
      leaf port {
        type ntos-fwt:port-range;
        description
          "Destination port.";
      }
    }
  }

  rpc show-nat-policy {
    description
      "Show NAT policy detailed list.";

    input {
      leaf vrf {
        type string;
        description
          "VRF name.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";

        choice search_type {
          case search_by_name {
            leaf name {
              type string;
            }
          }

          case search-by-rule-type {
            choice search-range {
              case search-by-range {
                leaf category {
                  type nat-category;
                }

                leaf filter {
                  type string;
                }

                leaf start {
                  type uint32;
                  description
                    "Start offset.";
                }

                leaf end {
                  type uint32;
                  description
                    "End offset.";
                }
              }
              case search-by-rule-id {
                leaf rule-id {
                  type string;
                }
              }
            }
          }
        }
      }
    }

    output {
      leaf total {
        type uint32;
      }

      list rule {
        key "name";
        description
          "NAT policy.";

        leaf name {
          type string;
        }

        leaf rule_en {
          type boolean;
          description
            "Enable or disable this rule.";
        }

        leaf hit-cnt {
          type uint64;
          description
            "NAT policy hit counter.";
        }

        leaf rule_type {
          type uint32;
          description
            "Rule type.";
        }

        leaf time_range_en {
          type boolean;
          description
            "Enable or disable this rule.";
        }

        leaf desc {
          type string;
          ntos-ext:nc-cli-one-liner;
          description
            "Rule description.";
        }

        list alarm_info {
          key "alarm";
          leaf alarm {
            type string;
          }
          ntos-ext:nc-cli-one-liner;
          description
            "Rule configuration alarm.";
        }

        container dynamic-snat44 {
          uses show-nat-policy-match;
          uses show-dynamic-snat44-translate-to;
        }

        container static-dnat44 {
          uses show-nat-policy-match;
          uses show-static-dnat44-translate-to;
        }

        container static-snat44 {
          uses show-nat-policy-match;
          uses show-static-snat44-translate-to;
        }

        container twice-nat44 {
          uses show-nat-policy-match;
          uses show-twice-nat44-translate-to;
        }

        container static-natpt {
          uses show-nat-policy-match;
          uses show-static-natpt-translate-to;
        }
        container dynamic-natpt {
          uses show-nat-policy-match;
          uses show-dynamic-natpt-translate-to;
        }
        container stl-nat64 {
          uses show-nat-policy-match;
          uses show-stateless-nat64-translate-to;
        }
        container static-stf-nat64 {
          uses show-nat-policy-match;
          uses show-static-stateful-nat64-translate-to;
        }
        container dynamic-stf-nat64 {
          uses show-nat-policy-match;
          uses show-dynamic-stateful-nat64-translate-to;
        }
        container source-npt {
          uses show-nat-policy-match;
          uses show-source-npt-translate-to;
        }
        container dest-npt {
          uses show-nat-policy-match;
          uses show-dest-npt-translate-to;
        }
        container port-mapping {
          uses show-port-mapping-match;
          uses show-port-mapping-translate-to;
          container permit-inside {
            description
              "Port mapping permit inside configuration.";
            leaf enabled {
              type boolean;
              description
                "Enable or disable permit inside.";
            }
          }
        }
      }
    }
    ntos-ext:nc-cli-show "nat-policy";
    ntos-api:internal;
  }

rpc show-nat-policy-only-name {
    description
      "Show NAT policy name list.";

    input {
      leaf vrf {
        type string;
        description
          "VRF name.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";

        leaf category {
          type nat-category;
        }

        leaf start {
          type uint32;
          description
            "Start offset.";
        }

        leaf end {
          type uint32;
          description
            "End offset.";
        }
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
          ntos-ext:nc-cli-stdout;
      }
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "nat-policy-only-name";
    ntos-api:internal;
  }

  rpc show-nat-pool {
    description
      "Show NAT pool detailed list.";

    input {
      leaf vrf {
        type string;
        description
          "VRF name.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";
        leaf name {
          type string;
          description
            "Policy id.";
        }

        leaf start {
          type uint32;
          description
            "Start offset.";
        }

        leaf end {
          type uint32;
          description
            "End offset.";
        }
      }
    }

    output {
      leaf total {
        type uint32;
      }

      list pool {
        key "name";
        description
          "Pools of IP addresses for the NAT rules.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
        }

        leaf desc {
          type ntos-types:ntos-obj-description-type;
        }
        list address {
          key "value";
          leaf value {
            type pool-ipv4-address;
          }
        }
      }
    }
    ntos-ext:nc-cli-show "nat-pool";
    ntos-api:internal;
  }

  rpc show-nat-prefix {
    description
      "Show NAT64 IPv6 prefix detailed list.";

    input {
      leaf vrf {
        type string;
        description
          "VRF name.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";
        leaf name {
          type string;
          description
            "Nat64 prefix name.";
        }
        leaf filter {
          type string;
          description
            "Filter name or address.";
        }

        leaf start {
          type uint32;
          description
            "Start offset.";
        }

        leaf end {
          type uint32;
          description
            "End offset.";
        }
      }
    }

    output {
      leaf total {
        type uint32;
      }

      list prefix {
        key "name";
        description
          "Prefix of IPv6 addresses for the NAT rules.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
        }

        leaf address {
          type string;
        }

        leaf length {
          type uint8;
        }

        leaf policy-name {
          type string;
        }
      }
    }
    ntos-ext:nc-cli-show "nat-prefix";
    ntos-api:internal;
  }

rpc show-nat-prefix-no-ref {
    description
      "Show NAT64 IPv6 prefix detailed list, and not be referenced.";

    input {
      leaf vrf {
        type string;
        description
          "VRF name.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";

        leaf start {
          type uint32;
          description
            "Start offset.";
        }

        leaf end {
          type uint32;
          description
            "End offset.";
        }
        leaf contain-nat64 {
          type boolean;
        }
      }
    }

    output {
      leaf total {
        type uint32;
      }

      list prefix {
        key "name";
        description
          "Prefix of IPv6 addresses for the NAT rules.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
        }

        leaf address {
          type string;
        }

        leaf length {
          type uint8;
        }

        leaf policy-name {
          type string;
        }
      }
    }
    ntos-ext:nc-cli-show "nat-prefix-no-ref";
    ntos-api:internal;
  }

  rpc refresh-policy-hit-counter {
    description
      "Refresh NAT policy hit counter.";

    ntos-ext:nc-cli-cmd "nat refresh-counter";
    ntos-api:internal;
  }

  rpc clear-policy-hit-counter {
    description
      "Clear NAT policy hit counter.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "VRF name.";
      }
      list rule {
        key name;
        leaf name {
          type ntos-types:ntos-obj-name-type;
        }
      }
    }
    ntos-ext:nc-cli-cmd "nat clear-counter";
    ntos-api:internal;
  }

  rpc import-configuration {
    description
      "Import configurations related to nat rule.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      leaf path {
        type string {
          pattern '[a-zA-Z0-9.\-/_]+.csv';
        }
        description
          "The path of configurations, '.csv' files are supported.";
      }

      leaf result {
        type empty;
        description
          "Show last result of import configuration.";
      }

      leaf type {
        type enumeration {
          enum warning;
          enum ignore;
          enum cover;
        }
        description
          "Specifies how conflicts are handled.";
      }
    }
    output {
      leaf info {
        description
          "Info of command result";
        type string;
      }
    }

    ntos-ext:nc-cli-cmd "nat-import-config";
    ntos-api:internal;
  }

  grouping page-filter {
    description
      "page filter param.";

    leaf page-index {
      type uint32{
          range "1..10000000";
        }
      description
        "Page index.";
    }

    leaf page-size {
      type uint32{
          range "1..100";
        }
      description
        "Number entries of one page.";
    }
  }

  grouping base-port-proto-info {
    leaf ports {
      type string {
        length "1..128";
      }
      description
        "Separated by commas and horizontal bars, multiple ports can be included, but a maximum of 10 ports can be included one time, such as 100, 50-55.";
    }

    leaf protocol {
      type enumeration {
        enum tcp;
        enum udp;
        enum any;
      }
      description
        "The protocol of port.";
    }
  }

  grouping cmd-base-probe-info {
    choice probe-type {
      mandatory true;
      case ip {
        leaf ip-address {
          type ntos-inet:ipv4-address;
          description
            "The IP address that probed.";
        }
      }
      case nat {
        leaf rule-name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-one-liner;
          description
            "The DNAT rule name.";
        }
      }
    }
  }

  grouping show-base-probe-info {
    leaf ip-address {
      type ntos-inet:ipv4-address;
      description
        "The IP address that probed.";
    }
    leaf rule-name {
      type ntos-types:ntos-obj-name-type;
      ntos-ext:nc-cli-one-liner;
      description
        "The DNAT rule name.";
    }
  }
  
  grouping cmd-probe-service-filter {
    leaf vrf {
      type ntos-types:ntos-obj-name-type;
      description
        "Specify the VRF.";
    }

    uses cmd-base-probe-info;
    uses base-port-proto-info;
  }

  grouping show-probe-service-filter {
    leaf vrf {
      type ntos-types:ntos-obj-name-type;
      description
        "Specify the VRF.";
    }

    uses show-base-probe-info;
    uses base-port-proto-info;
  }
  
  grouping nat-service-probe-result {
    leaf status {
      type enumeration {
        enum running;
        enum finished;
        enum unknown;
      }
      description
        "The status of this probe task.";
    }

    leaf vrf {
      type ntos-types:ntos-obj-name-type;
      description
        "Specify the VRF.";
    }

    leaf rule-name {
      type ntos-types:ntos-obj-name-type;
      ntos-ext:nc-cli-one-liner;
      description
        "The DNAT rule name.";
    }

    leaf ip-address {
      type ntos-inet:ipv4-address;
      description
        "The IP address that probed.";
    }

    leaf address-probe-time {
        type ietf-yang:timestamp;
        description
          "The time of IP address last probe.";
    }

    leaf ip-address-status {
      type enumeration {
        enum success;
        enum failed;
        enum probing;
        enum waiting;
        enum unknown;
      }
      description
        "The result type of IP probe.";
    }

    leaf total {
      type uint32;
    }

    list port-list {
      description
        "NAT service port probe result list.";

      leaf ip-address {
        type ntos-inet:ipv4-address;
        description
          "The IP address that probed.";
      }

      leaf port {
        type uint32 {
          range "1..65535";
        }
        description
          "The port number.";
      }

      leaf protocol {
        type enumeration {
          enum tcp;
          enum udp;
        }
        description
          "The protocol of port.";
      }

      leaf last-probe-time {
        type ietf-yang:timestamp;
        description
          "The time of last probe.";
      }

      leaf port-probe-result {
        type enumeration {
          enum success;
          enum failed;
          enum probing;
          enum waiting;
          enum noneed;
          enum unknown;
        }
        description
          "The result type of port probe.";
      }
    }
  }

  rpc cmd-nat-service-port-probe {
    description
      "probe DNAT service port.";

    input {
      uses cmd-probe-service-filter;
    }

    output {
      leaf info {
        description
          "Info of command result";
        type string;
      }
    }

    ntos-ext:feature "nat";
    ntos-ext:nc-cli-cmd "nat service port probe";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-nat-service-port-probe {
    description
      "Show NAT service port probe result.";

    input {
      uses show-probe-service-filter;
      uses page-filter;
    }

    output {
       uses nat-service-probe-result;
    }

    ntos-ext:feature "nat";
    ntos-ext:nc-cli-show "nat service port probe";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc flush-nat-service-port-probe {
    description
      "Flush NAT service port probe result.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
      leaf rule-name {
        type ntos-types:ntos-obj-name-type;
        mandatory true;
        ntos-ext:nc-cli-one-liner;
        description
          "The DNAT rule name.";
      }
    }

    output {
      leaf info {
        description
          "Info of command result";
        type string;
      }
    }

    ntos-ext:feature "nat";
    ntos-ext:nc-cli-flush "nat service port probe";
  }

  grouping nat-match-interface-result {
    leaf vrf {
      type ntos-types:ntos-obj-name-type;
      description
        "Specify the VRF.";
    }

    leaf total {
      type uint32;
      description
        "Number of interfaces.";
    }

    list interface-list {
      description
        "Interfaces that can matched by NAT.";

      uses match-if-info;
    }
  }
  
  rpc show-nat-match-interface {
    description
      "Show interfaces that can matched by NAT.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
    }

    output {
       uses nat-match-interface-result;
    }

    ntos-ext:feature "nat";
    ntos-ext:nc-cli-show "nat match interface";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "NAT config.";
    uses nat-config;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Port mapping config.";
    uses port-mapping-config;
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "NAT state.";
	container nat {
      leaf enabled {
        type boolean;
      }

      leaf version {
        type uint32;
      }

	  leaf send-error {
	    type uint32;
	  }
	}
  }
}
