module ntos-if-mon {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:if-mon";
  prefix ntos-if-mon;
  
  import ntos {
    prefix ntos;
  }
  
  import ntos-types {
    prefix ntos-types;
  }
  
  import ntos-interface {
    prefix ntos-interface;
  }
  
  import ntos-vlan {
    prefix ntos-vlan;
  }

  import ntos-bridge {
    prefix ntos-bridge;
  }

  import ntos-vswitch {
    prefix ntos-vswitch;
  }

  import ntos-lag {
    prefix ntos-lag;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS network interfaces monitoring module.";

  revision 2022-09-03 {
    description
      "Initial version.";
    reference "";
  }

  grouping monitor-drop-rate {
    description
      "The grouping data for interface drop rate monitoring.";

    leaf notify-up-drop-rate-threshold {
      type uint64;
      units "packets/second";
      default "1000";
      description
             "When the calculated rate of interface packet drops 
              (ntosIfDropRate) meets or exceeds the value 
              specified by this object, a ntosIfDropRateNotify 
              notification is sent if ntosIfDropRateNotifyEnable 
              is set to true, and no such notification on this
		          interface for the hold-down period.
              Note that due to the calculation used for drop rate, 
              if there are less than n drop events in an n-second
              period the notification will not be generated. To allow
              for the detection of a small number of drop events, the
              value 0 (zero) is used to indicate that if any drop events
              occur during the interval, a notification is generated.";
	 }
	 
    leaf notify-down-drop-rate-threshold {
      type uint64;
      units "packets/second";
      default "1000";
      description
             "When the calculated rate of interface packet drops 
              (ntosIfDropRate) meets or exceeds the value 
              specified by this object, a ntosIfDropRateNotify 
              notification is sent if ntosIfDropRateNotifyEnable 
              is set to true, and no such notification on this
		          interface for the hold-down period.
              Note that due to the calculation used for drop rate, 
              if there are less than n drop events in an n-second
              period the notification will not be generated. To allow
              for the detection of a small number of drop events, the
              value 0 (zero) is used to indicate that if any drop events
              occur during the interval, a notification is generated.";
	 }
	 
    leaf up-drops {
      type ntos-types:counter64;
      units "packets";
      config false;
      description
             "The uplink packet drops on an interface of the managed device."; 
	 }
	 
    leaf down-drops {
      type ntos-types:counter64;
      units "packets";
      config false;
      description
             "The downlink packet drops on an interface of the managed device.";
	 }
	 
    leaf up-drop-rate {
      type uint64;
      units "packets/second";
      config false;
      description
            "The uplink rate of packet drops on an interface of the managed device.
		         The per-interface drop rate notification is issued on rates 
             exceeding a limit (rising rate).
             This object is the average rate of dropping over the most 
             recent window of time. The rate is computed by dividing 
             the number of packets dropped over a window by the window 
             time in seconds. The window time is specified by 
             drop-rate-window. Each time the drop rate is computed, 
             and at system startup, a snapshot is taken of the latest 
             value of ntosIfUpDrops. Subtracting from this the snapshot 
             of ntosIfUpDrops at the start of the current window of time 
             gives the number of packets dropped. 
             The drop rate is 
             computed every drop-rate-compute-interval seconds. As an 
             example, let drop-rate-compute-window be 300 seconds,
             and drop-rate-compute-interval 30 seconds. Every 30 seconds,
             the drop count five minutes previous is subtracted
             from the current drop count, and the result is divided
             by 300 to arrive at the drop rate.
             At device start-up, until the device has been up more than
             drop-rate-compute-window, when drop rate is computed,
             the value of up-drops is divided by the time the
             device has been up.
             After the device has been up for drop-rate-compute-window,
             when drop rate is computed, the number of packet drops counted 
             from interval start time to the computation time is divided 
             by drop-rate-compute-window.
             Changes to drop-rate-compute-window are not reflected in this
             object until the next computation time.
             The rate from the most recent computation is the value 
             fetched until the subsequent computation is performed.";
	 }
	 
    leaf down-drop-rate {
      type uint64;
      units "packets/second";
      config false;
      description
            "The downlink rate of packet drops on an interface of the managed device.
	           The per-interface drop rate notification is issued on rates 
             exceeding a limit (rising rate).
             This object is the average rate of dropping over the most 
             recent window of time. The rate is computed by dividing 
             the number of packets dropped over a window by the window 
             time in seconds. The window time is specified by 
             drop-rate-window. Each time the drop rate is computed, 
             and at system startup, a snapshot is taken of the latest 
             value of down-drops. Subtracting from this the snapshot 
             of down-drops at the start of the current window of time 
             gives the number of packets dropped. 
             The drop rate is 
             computed every drop-rate-compute-interval seconds. As an 
             example, let drop-rate-compute-window be 300 seconds,
             and drop-rate-compute-interval 30 seconds. Every 30 seconds,
             the drop count five minutes previous is subtracted
             from the current drop count, and the result is divided
             by 300 to arrive at the drop rate.
             At device start-up, until the device has been up more than
             drop-rate-compute-window, when drop rate is computed,
             the value of down-drops is divided by the time the
             device has been up.
             After the device has been up for drop-rate-compute-window,
             when drop rate is computed, the number of packet drops counted 
             from interval start time to the computation time is divided 
             by drop-rate-compute-window.
             Changes to drop-rate-compute-window are not reflected in this
             object until the next computation time.
             The rate from the most recent computation is the value 
             fetched until the subsequent computation is performed.";
    }
  }
  
  grouping monitor-usage {
    description
        "The grouping data for interface bandwidth usage monitoring.";

    leaf if-notify-enable {
      type boolean;
      default "false";
      description
            "This object specifies whether the system produces the 
            if-notify notification on this interface. 
            A false value prevents such notifications from 
            being generated by this system.";
    }

    leaf notify-up-usage-threshold {
      type uint32;
      default "100";
      description
            "When the calculated rate of interface packetmeets or exceeds 
            the value specified by this object, a ntosIfSpeedNotify 
            notification is sent if speed-notify-enable 
            is set to true, and no such notification on this
            interface for the hold-down period.";
    }
	 
    leaf notify-down-usage-threshold {
      type uint32;
      default "100";
      description
            "When the calculated rate of interface packetmeets or exceeds 
            the value specified by this object, a ntosIfSpeedNotify 
            notification is sent if speed-notify-enable 
            is set to true, and no such notification on this
            interface for the hold-down period.";
    }

    leaf notify-up-speed-threshold {
      type uint64;
      units "bits/second";
      default "100000000";
      description
            "When the calculated rate of interface packetmeets or exceeds 
            the value specified by this object, a ntosIfSpeedNotify 
            notification is sent if if-notify-enable 
            is set to true, and no such notification on this
            interface for the hold-down period.";
    }
	 
    leaf notify-down-speed-threshold {
      type uint64;
      units "bits/second";
      default "100000000";
      description
            "When the calculated rate of interface packetmeets or exceeds 
			      the value specified by this object, a ntosIfSpeedNotify 
            notification is sent if if-notify-enable 
            is set to true, and no such notification on this
            interface for the hold-down period.";
    }

    leaf up-speed {
      type uint64;
      units "bits/second";
      config false;
      description
            "An estimate of the interface's current uplink bandwidth in bits
            per second.  For interfaces which do not vary in bandwidth
            or for those where no accurate estimation can be made, this
            object should contain the nominal bandwidth.  If the
            bandwidth of the interface is greater than the maximum value
            reportable by this object then this object should report its
            maximum value (4,294,967,295). For a sub-layer which has
            no concept of bandwidth, this object should be zero.";
	 }
	 
    leaf down-speed {
      type uint64;
      units "bits/second";
      config false;
      description
            "An estimate of the interface's current downlink bandwidth in bits
            per second.  For interfaces which do not vary in bandwidth
            or for those where no accurate estimation can be made, this
            object should contain the nominal bandwidth.  If the
            bandwidth of the interface is greater than the maximum value
            reportable by this object then this object should report its
            maximum value (4,294,967,295). For a sub-layer which has
            no concept of bandwidth, this object should be zero.";
	 }

    leaf up-usage {
      type int32 {
        range "0..100";
      }
      config false;
      description
          "An estimate percentage value of the interface's uplink bandwidth usage. 
          The ratio of interface's current uplink speed to maximum uplink bandwidth
          multiply by a hundred to obtain this percentage value.";
	 }
	 
    leaf down-usage {
      type int32 {
        range "0..100";
      }
      config false;
      description
          "An estimate percentage value of the interface's downlink bandwidth usage. 
          The ratio of interface's current downlink speed to maximum downlink bandwidth
          multiply by a hundred to obtain this percentage value.";	 
	 }
  }
  
  grouping monitor-interface-config {
    description
      "The grouping data for interface monitoring.";
    container monitor {
      description
        "Interface monitoring configuration.";
      uses monitor-drop-rate;
      uses monitor-usage;
    }
  }
  
  grouping monitor-interface-state {
    description
      "The grouping data for interface monitoring.";
    container monitor {
      config false;
      description
        "Interface monitoring state.";
      uses monitor-drop-rate;
      uses monitor-usage;
    }
  }
  
  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
    description
      "Network interfaces monitoring configuration.";
    uses monitor-interface-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
    description
      "Network interfaces operational state data.";
    uses monitor-interface-state;
  }
  
  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-vlan:vlan" {
    description
      "Network interfaces monitoring configuration.";
    uses monitor-interface-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-vlan:vlan" {
    description
      "Network interfaces operational state data.";
    uses monitor-interface-state;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-bridge:bridge" {
    description
      "Network interfaces monitoring configuration.";
    uses monitor-interface-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-bridge:bridge" {
    description
      "Network interfaces operational state data.";
    uses monitor-interface-state;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-vswitch:vswitch" {
    description
      "Network interfaces monitoring configuration.";
    uses monitor-interface-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-vswitch:vswitch" {
    description
      "Network interfaces operational state data.";
    uses monitor-interface-state;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-lag:lag" {
    description
      "Network interfaces monitoring configuration.";
    uses monitor-interface-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-lag:lag" {
    description
      "Network interfaces operational state data.";
    uses monitor-interface-state;
  }
  
  notification if-up-drop-rate {
    description
        "This notification is generated when 
         drop-rate-notify-enable is set to true and
         the calculated interface uplink drop rate (up-drop-rate) 
         exceeds the notification threshold drop rate 
         (notify-drop-rate-threshold). Note the 
         exceptional value of 0 for threshold allows notification 
         generation if any drop events occur in an interval.
         After generating this notification, another such
         notification will not be sent out for a minimum of five 
         minutes (note the exception to this provided by 
         notify-dr-hold-down-reset).
         The object value present in the notification is the 
         the drop rate that exceeded the threshold.";
		 
    leaf vrf-name {
      type ntos:vrf-name;
    }
	
    leaf if-name {
      type ntos-types:ifname;
    }
	
    leaf up-drop-rate {
      type uint32;
      units "packets/second";
    }
  }

  notification if-down-drop-rate {
    description
        "This notification is generated when 
         drop-rate-notify-enable is set to true and
         the calculated interface uplink drop drop rate (down-drop-rate) 
         exceeds the notification threshold drop rate 
         (notify-drop-rate-threshold). Note the 
         exceptional value of 0 for threshold allows notification 
         generation if any drop events occur in an interval.
         After generating this notification, another such
         notification will not be sent out for a minimum of five 
         minutes (note the exception to this provided by 
         notify-dr-holddown-reset).
         The object value present in the notification is the 
         the drop rate that exceeded the threshold.";

    leaf vrf-name {
      type ntos:vrf-name;
    }
	
    leaf if-name {
      type ntos-types:ifname;
    }
	
    leaf down-drop-rate {
      type uint32;
      units "packets/second";
    }
  }

  notification if-up-speed {
    description
        "This notification is generated when 
         if-notify-enable is set to true and
         the up-speed exceeds the notification threshold.";
		 
    leaf vrf-name {
      type ntos:vrf-name;
    }
	
    leaf if-name {
      type ntos-types:ifname;
    }
	
    leaf up-speed {
      type uint64;
      units "bits/second";
      }  
  }
  
  notification if-down-speed {
    description
        "This notification is generated when 
         if-notify-enable is set to true and
         the down-speed exceeds the notification threshold.";
		 
    leaf vrf-name {
      type ntos:vrf-name;
    }
	
    leaf if-name {
      type ntos-types:ifname;
    }
	
    leaf down-speed {
      type uint64;
      units "bits/second";
    }
  }

  notification if-up-usage {
    description
        "This notification is generated when 
         ntosIfUsageNotifyEnable is set to true and
         the ntosIfUpSpeed exceeds the notification threshold.";
		 
    leaf vrf-name {
      type ntos:vrf-name;
    }
	
    leaf if-name {
      type ntos-types:ifname;
    }
	
    leaf up-usage {
      type int32 {
        range "0..100";
      }
    }
  }
  
  notification if-down-usage {
    description
        "This notification is generated when 
         ntosIfUsageNotifyEnable is set to true and
         the ntosIfDownUsage exceeds the notification threshold.";
		 
    leaf vrf-name {
      type ntos:vrf-name;
    }
	
    leaf if-name {
      type ntos-types:ifname;
    }
	
    leaf down-usage {
      type int32 {
        range "0..100";
      }
    }
  }
}
