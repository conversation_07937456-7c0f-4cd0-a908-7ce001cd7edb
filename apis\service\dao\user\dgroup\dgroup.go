package dgroup

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/user"
	"irisAdminApi/service/dao/user/ddepartment"
	"irisAdminApi/service/dao/user/dgroupdepartment"
	"strconv"
)

const ModelName = "用户组管理"

type Response struct {
	Id           uint   `json:"id"`
	DepartmentID uint   `json:"department_id"`
	Name         string `json:"name"`
	Description  string `json:"description"`
	UpdatedAt    string `json:"updated_at"`
	CreatedAt    string `json:"created_at"`
	Department   string `gorm:"-" json:"department"`
}

type ListResponse struct {
	Response
}

type GroupReq struct {
	Name         string `json:"name" `
	Description  string `json:"description"`
	DepartmentID uint   `json:"department_id"`
}

func (u *Response) ModelName() string {
	return ModelName
}

func Model() *user.Group {
	return &user.Group{}
}

func (this *Response) All(name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var groups []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model())
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&groups).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getDepartment(groups)
	list := map[string]interface{}{"items": groups, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) AllEx(filters []map[string]string, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var groups []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model())
	for _, filter := range filters {
		db = db.Where(fmt.Sprintf("%s %s ?", filter["column"], filter["condition"]), filter["value"])
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&groups).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	list := map[string]interface{}{"items": groups, "total": count, "limit": pageSize}
	return list, nil
}

func (this *Response) FindByUserName(username string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("name = ?", username).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user by username ", username, " err ", err)
		return err
	}
	return nil
}

// func (this *Response) Create(object map[string]interface{}) error {
// 	var name, department string
// 	if n, ok := object["Name"].(string); ok {
// 		name = n
// 		err := this.FindByUserName(name)
// 		if err != nil {
// 			logging.ErrorLogger.Errorf("create user find by username get err ", err)
// 			return err
// 		}

// 		if this.Id > 0 {
// 			return errors.New(fmt.Sprintf("username %s is being used", name))
// 		}
// 	}
// 	if d, ok := object["Department"].(string); ok {
// 		department = d
// 	}

// 	delete(object, "Department")

// 	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("create data err ", err)
// 		return err
// 	}

// 	err = this.FindByUserName(name)
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("create user find by username get err ", err)
// 		return err
// 	}

// 	if this.Id > 0 {
// 		UpdateDepartment(this.Id, department)
// 	}

// 	return nil
// }

func (this *Response) Create(object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}
	return nil
}

func (this *Response) CreateV2(object interface{}) error {
	return nil
}

func (this *Response) Update(id uint, object map[string]interface{}) error {
	err := this.Find(id)
	if err != nil {
		return err
	}

	if name, ok := object["Name"].(string); ok {
		err := this.FindByUserName(name)
		if err != nil {
			logging.ErrorLogger.Errorf("create user find by name get err ", err)
			return err
		}

		if this.Id > 0 && this.Id != id {
			return errors.New(fmt.Sprintf("name %s is being used", name))
		}
	}
	err = easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).UpdateColumns(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update user  get err ", err)
		return err
	}
	return nil
}

func (this *Response) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find user err ", err)
		return err
	}
	return nil
}

func (this *Response) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete user by id get  err ", err)
		return err
	}
	return nil
}

func FindById(id uint) (Response, error) {
	var group Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id = ?", id).Find(&group).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find role by id get  err ", err)
		return group, err
	}
	return group, nil
}

func UpdateDepartment(gid uint, did string) error {
	object := map[string]interface{}{
		"GroupId":      strconv.FormatUint(uint64(gid), 10),
		"DepartmentId": did,
	}
	groupDepartment := dgroupdepartment.Response{}
	if err := groupDepartment.Create(object); err != nil {
		logging.ErrorLogger.Errorf("create user group relation err ", err)
		return err
	}
	return nil
}

func getDepartment(groups []*ListResponse) {
	for _, group := range groups {
		department, _ := ddepartment.FindById(group.DepartmentID)
		group.Department = department.Name
	}
}

func FindInId(ids []uint) ([]*Response, error) {
	var groups []*Response
	err := easygorm.GetEasyGormDb().Model(Model()).Where("id in ?", ids).Find(&groups).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find group by id get  err ", err)
		return nil, err
	}
	return groups, nil
}

func AllByDepartmentID(id uint, name, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var groups []*ListResponse
	db := easygorm.GetEasyGormDb().Model(Model()).Where("department_id = ?", id)
	if len(name) > 0 {
		db = db.Where("name", "like", fmt.Sprintf("%%%s%%", name))
	}
	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&groups).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}
	getDepartment(groups)
	list := map[string]interface{}{"items": groups, "total": count, "limit": pageSize}
	return list, nil
}
