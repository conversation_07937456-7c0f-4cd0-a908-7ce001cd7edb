#config-version=FGT401F-7.4.2-FW-build2795-202507011110:opmode=0:vdom=0:user=admin
#conf_file_ver=1234567890123456
#buildno=2795
#global_vdom=1
config system global
    set hostname "FortiGate-IPPool-Test"
    set timezone "Asia/Shanghai"
end

config system interface
    edit "wan1"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https ssh snmp http
        set type physical
        set role wan
        set snmp-index 1
    next
    edit "dmz"
        set vdom "root"
        set ip ************* *************
        set allowaccess ping https ssh snmp
        set type physical
        set role dmz
        set snmp-index 2
    next
    edit "lan"
        set vdom "root"
        set ip *********** *************
        set allowaccess ping https ssh snmp
        set type physical
        set role lan
        set snmp-index 3
    next
end

config firewall ippool
    edit "EXTERNAL_POOL_1"
        set type overload
        set startip *************
        set endip *************
        set comments "Traditional pool name format"
    next
    edit "**************"
        set type overload
        set startip **************
        set endip **************
        set comments "IP address format pool name"
    next
    edit "************"
        set type overload
        set startip ************
        set endip ************
        set comments "Another IP address format pool"
    next
    edit "LOAD_BALANCE_POOL"
        set type overload
        set startip **************
        set endip **************
        set comments "Load balancing pool with range"
    next
end

config firewall vip
    edit "WEB_SERVER_VIP"
        set extip **************
        set mappedip "*************0"
        set extport 80-443
        set mappedport 80-443
        set comment "Web server VIP for twice-nat44 testing"
    next
    edit "MAIL_SERVER_VIP"
        set extip ************
        set mappedip "**************"
        set extport 25
        set mappedport 25
        set comment "Mail server VIP"
    next
    edit "FTP_SERVER_VIP"
        set extip *************
        set mappedip "**************"
        set extport 21
        set mappedport 21
        set comment "FTP server VIP"
    next
end

config firewall address
    edit "LAN_SUBNET"
        set subnet *********** *************
        set comment "LAN subnet"
    next
    edit "DMZ_SUBNET"
        set subnet ************* *************
        set comment "DMZ subnet"
    next
    edit "EXTERNAL_NET"
        set subnet *********** *************
        set comment "External network"
    next
end

config firewall service custom
    edit "HTTP_HTTPS"
        set tcp-portrange 80 443
        set comment "HTTP and HTTPS services"
    next
end

config firewall policy
    edit 1
        set name "WAN_to_DMZ_WEB_IPPOOL"
        set srcintf "wan1"
        set dstintf "dmz"
        set action accept
        set srcaddr "all"
        set dstaddr "WEB_SERVER_VIP"
        set service "HTTP_HTTPS"
        set schedule "always"
        set nat enable
        set ippool enable
        set poolname "**************"
        set comments "Twice-nat44 candidate: WAN to DMZ with IP address format pool"
    next
    edit 2
        set name "LAN_to_WAN_OUTBOUND_TRADITIONAL"
        set srcintf "lan"
        set dstintf "wan1"
        set action accept
        set srcaddr "LAN_SUBNET"
        set dstaddr "all"
        set service "ALL"
        set schedule "always"
        set nat enable
        set ippool enable
        set poolname "EXTERNAL_POOL_1"
        set comments "Twice-nat44 candidate: LAN to WAN with traditional pool name"
    next
    edit 3
        set name "DMZ_to_WAN_MAIL_MIXED"
        set srcintf "dmz"
        set dstintf "wan1"
        set action accept
        set srcaddr "DMZ_SUBNET"
        set dstaddr "MAIL_SERVER_VIP"
        set service "SMTP"
        set schedule "always"
        set nat enable
        set ippool enable
        set poolname "************" "EXTERNAL_POOL_1"
        set comments "Twice-nat44 candidate: Mixed pool format (IP + traditional)"
    next
    edit 4
        set name "LAN_to_DMZ_NO_POOL"
        set srcintf "lan"
        set dstintf "dmz"
        set action accept
        set srcaddr "LAN_SUBNET"
        set dstaddr "DMZ_SUBNET"
        set service "ALL"
        set schedule "always"
        set nat enable
        set comments "Traditional NAT: No IP pool usage"
    next
    edit 5
        set name "WAN_to_DMZ_COMPLEX_SERVICES"
        set srcintf "wan1"
        set dstintf "dmz"
        set action accept
        set srcaddr "EXTERNAL_NET"
        set dstaddr "FTP_SERVER_VIP"
        set service "FTP" "SSH" "TELNET" "HTTP" "HTTPS" "SMTP"
        set schedule "always"
        set nat enable
        set ippool enable
        set poolname "*************"
        set comments "Complex services test: Should get lower score due to service complexity"
    next
    edit 6
        set name "LOAD_BALANCE_SCENARIO"
        set srcintf "lan"
        set dstintf "wan1"
        set action accept
        set srcaddr "LAN_SUBNET"
        set dstaddr "all"
        set service "HTTP_HTTPS"
        set schedule "always"
        set nat enable
        set ippool enable
        set poolname "LOAD_BALANCE_POOL"
        set comments "Load balancing pool test"
    next
end

config system dns
    set primary *******
    set secondary *******
end

config router static
    edit 1
        set gateway *************
        set device "wan1"
        set comment "Default route"
    next
end
