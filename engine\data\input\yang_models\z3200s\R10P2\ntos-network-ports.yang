module ntos-network-ports {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:network-ports";
  prefix ntos-network-ports;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS network ports.";

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  rpc identify-port {
    description
      "Initiate adapter-specific action intended to enable an operator
       to easily identify a physical network interface by sight.

       Typically this involves blinking one or more LEDs on the specific
       network port.";
    input {

      leaf name {
        type ntos-types:pci-port-name;
        mandatory true;
        description
          "The port name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos-network-ports:network-port/ntos-network-ports:name";
        ntos-ext:nc-cli-no-name;
      }

      leaf duration {
        type uint16;
        units "seconds";
        default "60";
        description
          "Length of time to perform the identification, in seconds.";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-cmd "identify-port";
    ntos-api:internal;
  }

  augment "/ntos:state" {
    description
      "Network ports.";

    list network-port {
      key "name";
      description
        "The list of network ports on the device.";

      leaf name {
        type string;
        description
          "The port human-readable name.";
      }

      leaf pci-bus-addr {
        type string;
        description
          "The bus address of the PCI device.";
      }

      leaf device-tree-alias {
        type string;
        description
          "In case of device tree port, the alias if there is one.";
      }

      leaf device-tree-compatible {
        type string;
        description
          "In case of device tree port, the compatible field if there is one.";
      }

      leaf vendor {
        type string;
        description
          "The device vendor.";
      }

      leaf model {
        type string;
        description
          "The device model.";
      }

      leaf device-port {
        type uint16;
        description
          "The port number, in case there are several ports per PCI device.";
      }

      leaf mac-address {
        type ntos-if:mac-address;
        description
          "The port MAC address.";
      }

      leaf interface {
        type string;
        description
          "The interface name.";
      }
    }
  }
}
