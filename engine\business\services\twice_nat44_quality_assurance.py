"""
FortiGate twice-nat44质量保证服务

本模块提供twice-nat44功能的质量保证，包括代码质量检查、
配置验证、性能基准测试和回归测试。

主要功能：
- 代码质量检查
- 配置一致性验证
- 性能基准测试
- 回归测试检查
- 质量报告生成

版本: 1.0
作者: FortiGate转换系统
创建时间: 2025-08-06
"""

from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime
import time
import json
import os
from enum import Enum
from engine.utils.logger import log
from engine.utils.i18n import _


class QualityLevel(Enum):
    """质量等级枚举"""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class QualityIssue:
    """
    质量问题数据结构
    """
    category: str
    severity: str
    message: str
    component: str
    recommendation: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "category": self.category,
            "severity": self.severity,
            "message": self.message,
            "component": self.component,
            "recommendation": self.recommendation
        }


@dataclass
class QualityReport:
    """
    质量报告数据结构
    """
    timestamp: datetime
    overall_quality: QualityLevel
    quality_score: float
    issues: List[QualityIssue] = field(default_factory=list)
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    test_results: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)
    
    def add_issue(self, issue: QualityIssue):
        """添加质量问题"""
        self.issues.append(issue)
    
    def add_recommendation(self, recommendation: str):
        """添加改进建议"""
        self.recommendations.append(recommendation)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "overall_quality": self.overall_quality.value,
            "quality_score": self.quality_score,
            "issues": [issue.to_dict() for issue in self.issues],
            "performance_metrics": self.performance_metrics,
            "test_results": self.test_results,
            "recommendations": self.recommendations
        }


class TwiceNat44QualityAssurance:
    """
    twice-nat44质量保证服务
    
    提供全面的质量保证功能。
    """
    
    def __init__(self):
        """初始化质量保证服务"""
        self.performance_benchmarks = {
            "evaluation_time_ms": 10.0,
            "rule_creation_time_ms": 5.0,
            "xml_validation_time_ms": 20.0,
            "memory_usage_mb": 100.0,
            "cache_hit_rate_percent": 80.0
        }
        
        log(_("twice_nat44_qa.initialized"), "info")
    
    def run_quality_assessment(self) -> QualityReport:
        """
        运行完整的质量评估
        
        Returns:
            QualityReport: 质量报告
        """
        log(_("twice_nat44_qa.assessment_started"), "info")
        
        report = QualityReport(
            timestamp=datetime.now(),
            overall_quality=QualityLevel.GOOD,
            quality_score=0.0
        )
        
        try:
            # 1. 代码质量检查
            code_quality_score = self._check_code_quality(report)
            
            # 2. 配置一致性验证
            config_consistency_score = self._check_configuration_consistency(report)
            
            # 3. 性能基准测试
            performance_score = self._run_performance_benchmarks(report)
            
            # 4. 功能完整性检查
            functionality_score = self._check_functionality_completeness(report)
            
            # 5. 安全性检查
            security_score = self._check_security_aspects(report)
            
            # 计算总体质量分数
            total_score = (
                code_quality_score * 0.25 +
                config_consistency_score * 0.20 +
                performance_score * 0.25 +
                functionality_score * 0.20 +
                security_score * 0.10
            )
            
            report.quality_score = total_score
            report.overall_quality = self._determine_quality_level(total_score)
            
            # 生成改进建议
            self._generate_recommendations(report)
            
            log(_("twice_nat44_qa.assessment_completed", 
                 score=total_score, 
                 quality=report.overall_quality.value), "info")
            
            return report
            
        except Exception as e:
            log(_("twice_nat44_qa.assessment_failed", error=str(e)), "error")
            report.add_issue(QualityIssue(
                category="system",
                severity="critical",
                message=f"Quality assessment failed: {str(e)}",
                component="quality_assurance"
            ))
            report.overall_quality = QualityLevel.CRITICAL
            return report
    
    def _check_code_quality(self, report: QualityReport) -> float:
        """检查代码质量"""
        score = 100.0
        
        try:
            # 检查关键组件是否存在
            components_to_check = [
                "engine.business.models.twice_nat44_models",
                "engine.business.services.twice_nat44_evaluator",
                "engine.business.services.twice_nat44_config_manager",
                "engine.business.services.twice_nat44_fallback_handler",
                "engine.business.services.yang_validator"
            ]
            
            for component in components_to_check:
                try:
                    __import__(component)
                except ImportError as e:
                    score -= 20
                    report.add_issue(QualityIssue(
                        category="code_quality",
                        severity="critical",
                        message=f"Missing component: {component}",
                        component=component,
                        recommendation="Ensure all required components are implemented"
                    ))
            
            # 检查类型注解覆盖率（简化检查）
            if score > 80:
                log(_("twice_nat44_qa.code_quality_good"), "debug")
            else:
                report.add_issue(QualityIssue(
                    category="code_quality",
                    severity="warning",
                    message="Code quality issues detected",
                    component="overall",
                    recommendation="Review and fix missing components"
                ))
            
        except Exception as e:
            score = 0
            report.add_issue(QualityIssue(
                category="code_quality",
                severity="critical",
                message=f"Code quality check failed: {str(e)}",
                component="quality_checker"
            ))
        
        return score
    
    def _check_configuration_consistency(self, report: QualityReport) -> float:
        """检查配置一致性"""
        score = 100.0
        
        try:
            from engine.business.services.twice_nat44_config_manager import TwiceNat44ConfigManager
            
            # 创建配置管理器并检查默认配置
            config_manager = TwiceNat44ConfigManager()
            config = config_manager.get_config()
            
            # 检查关键配置项
            if not config.enabled:
                score -= 10
                report.add_issue(QualityIssue(
                    category="configuration",
                    severity="info",
                    message="twice-nat44 is disabled by default",
                    component="config_manager"
                ))
            
            if config.evaluation_threshold < 50 or config.evaluation_threshold > 95:
                score -= 15
                report.add_issue(QualityIssue(
                    category="configuration",
                    severity="warning",
                    message=f"Evaluation threshold ({config.evaluation_threshold}) may be too extreme",
                    component="config_manager",
                    recommendation="Consider using a threshold between 70-90"
                ))
            
        except Exception as e:
            score = 50
            report.add_issue(QualityIssue(
                category="configuration",
                severity="error",
                message=f"Configuration consistency check failed: {str(e)}",
                component="config_manager"
            ))
        
        return score
    
    def _run_performance_benchmarks(self, report: QualityReport) -> float:
        """运行性能基准测试"""
        score = 100.0
        
        try:
            from engine.business.services.twice_nat44_evaluator import TwiceNat44Evaluator
            from engine.business.models.twice_nat44_models import TwiceNat44Rule
            
            # 测试数据
            test_policy = {
                "name": "benchmark_policy",
                "dstaddr": ["TEST_VIP"],
                "service": ["HTTP"],
                "nat": "enable",
                "ippool": "disable"
            }
            
            test_vips = {
                "TEST_VIP": {
                    "name": "TEST_VIP",
                    "extip": "************",
                    "mappedip": "**************"
                }
            }
            
            test_context = {"ntos_version": "R11"}
            
            # 1. 评估器性能测试
            evaluator = TwiceNat44Evaluator()
            
            start_time = time.time()
            for _ in range(100):
                recommendation = evaluator.evaluate(test_policy, test_vips, test_context)
            evaluation_time = (time.time() - start_time) * 1000 / 100  # 平均时间（毫秒）
            
            if evaluation_time > self.performance_benchmarks["evaluation_time_ms"]:
                score -= 20
                report.add_issue(QualityIssue(
                    category="performance",
                    severity="warning",
                    message=f"Evaluation time ({evaluation_time:.2f}ms) exceeds benchmark ({self.performance_benchmarks['evaluation_time_ms']}ms)",
                    component="evaluator",
                    recommendation="Consider optimizing evaluation algorithm"
                ))
            
            # 2. 规则创建性能测试
            start_time = time.time()
            for _ in range(100):
                rule = TwiceNat44Rule.from_fortigate_policy(test_policy, test_vips["TEST_VIP"])
            rule_creation_time = (time.time() - start_time) * 1000 / 100
            
            if rule_creation_time > self.performance_benchmarks["rule_creation_time_ms"]:
                score -= 15
                report.add_issue(QualityIssue(
                    category="performance",
                    severity="warning",
                    message=f"Rule creation time ({rule_creation_time:.2f}ms) exceeds benchmark ({self.performance_benchmarks['rule_creation_time_ms']}ms)",
                    component="rule_creation",
                    recommendation="Consider optimizing rule creation process"
                ))
            
            # 记录性能指标
            report.performance_metrics = {
                "evaluation_time_ms": round(evaluation_time, 2),
                "rule_creation_time_ms": round(rule_creation_time, 2),
                "evaluation_benchmark_met": evaluation_time <= self.performance_benchmarks["evaluation_time_ms"],
                "rule_creation_benchmark_met": rule_creation_time <= self.performance_benchmarks["rule_creation_time_ms"]
            }
            
        except Exception as e:
            score = 30
            report.add_issue(QualityIssue(
                category="performance",
                severity="critical",
                message=f"Performance benchmark failed: {str(e)}",
                component="performance_tester"
            ))
        
        return score
    
    def _check_functionality_completeness(self, report: QualityReport) -> float:
        """检查功能完整性"""
        score = 100.0
        
        try:
            # 检查核心功能是否可用
            functionality_checks = [
                ("evaluation", self._test_evaluation_functionality),
                ("rule_creation", self._test_rule_creation_functionality),
                ("xml_generation", self._test_xml_generation_functionality),
                ("fallback", self._test_fallback_functionality),
                ("config_management", self._test_config_management_functionality)
            ]
            
            for func_name, test_func in functionality_checks:
                try:
                    if not test_func():
                        score -= 20
                        report.add_issue(QualityIssue(
                            category="functionality",
                            severity="error",
                            message=f"{func_name} functionality test failed",
                            component=func_name,
                            recommendation=f"Fix {func_name} implementation"
                        ))
                except Exception as e:
                    score -= 20
                    report.add_issue(QualityIssue(
                        category="functionality",
                        severity="error",
                        message=f"{func_name} functionality test error: {str(e)}",
                        component=func_name
                    ))
            
        except Exception as e:
            score = 0
            report.add_issue(QualityIssue(
                category="functionality",
                severity="critical",
                message=f"Functionality completeness check failed: {str(e)}",
                component="functionality_checker"
            ))
        
        return score
    
    def _check_security_aspects(self, report: QualityReport) -> float:
        """检查安全性方面"""
        score = 100.0
        
        try:
            # 基本安全检查
            security_checks = [
                ("input_validation", "检查输入验证"),
                ("error_handling", "检查错误处理"),
                ("logging_security", "检查日志安全性")
            ]
            
            for check_name, description in security_checks:
                # 简化的安全检查
                if check_name == "input_validation":
                    # 检查是否有输入验证
                    try:
                        from engine.business.models.twice_nat44_models import TwiceNat44SnatConfig, TwiceNat44AddressType
                        # 尝试创建无效配置，应该抛出异常
                        try:
                            TwiceNat44SnatConfig(
                                address_type=TwiceNat44AddressType.IP,
                                address_value="invalid.ip"
                            )
                            score -= 10
                            report.add_issue(QualityIssue(
                                category="security",
                                severity="warning",
                                message="Input validation may be insufficient",
                                component="data_models"
                            ))
                        except:
                            # 正确抛出异常，输入验证正常
                            pass
                    except Exception:
                        score -= 15
            
        except Exception as e:
            score = 70
            report.add_issue(QualityIssue(
                category="security",
                severity="warning",
                message=f"Security check failed: {str(e)}",
                component="security_checker"
            ))
        
        return score
    
    def _test_evaluation_functionality(self) -> bool:
        """测试评估功能"""
        try:
            from engine.business.services.twice_nat44_evaluator import TwiceNat44Evaluator
            
            evaluator = TwiceNat44Evaluator()
            test_policy = {"name": "test", "dstaddr": ["VIP"], "nat": "enable", "ippool": "disable"}
            test_vips = {"VIP": {"name": "VIP", "extip": "*******", "mappedip": "***********"}}
            
            recommendation = evaluator.evaluate(test_policy, test_vips)
            return recommendation is not None and hasattr(recommendation, 'should_use')
            
        except Exception:
            return False
    
    def _test_rule_creation_functionality(self) -> bool:
        """测试规则创建功能"""
        try:
            from engine.business.models.twice_nat44_models import TwiceNat44Rule
            
            test_policy = {"name": "test", "status": "enable", "nat": "enable"}
            test_vip = {"name": "VIP", "extip": "*******", "mappedip": "***********"}
            
            rule = TwiceNat44Rule.from_fortigate_policy(test_policy, test_vip)
            return rule is not None and rule.validate()
            
        except Exception:
            return False
    
    def _test_xml_generation_functionality(self) -> bool:
        """测试XML生成功能"""
        try:
            import xml.etree.ElementTree as ET
            from engine.business.services.yang_validator import YangValidator
            
            # 创建简单的XML元素
            element = ET.Element("twice-nat44")
            snat = ET.SubElement(element, "snat")
            ET.SubElement(snat, "output-address")
            dnat = ET.SubElement(element, "dnat")
            ip_elem = ET.SubElement(dnat, "ipv4-address")
            ip_elem.text = "***********"
            
            validator = YangValidator()
            result = validator.validate_twice_nat44_xml(element)
            return result is not None
            
        except Exception:
            return False
    
    def _test_fallback_functionality(self) -> bool:
        """测试回退功能"""
        try:
            from engine.business.services.twice_nat44_fallback_handler import TwiceNat44FallbackHandler, FallbackReason
            
            handler = TwiceNat44FallbackHandler()
            context = handler.create_fallback_context("test", FallbackReason.SYSTEM_ERROR)
            return context is not None
            
        except Exception:
            return False
    
    def _test_config_management_functionality(self) -> bool:
        """测试配置管理功能"""
        try:
            from engine.business.services.twice_nat44_config_manager import TwiceNat44ConfigManager
            
            manager = TwiceNat44ConfigManager()
            config = manager.get_config()
            return config is not None and hasattr(config, 'enabled')
            
        except Exception:
            return False
    
    def _determine_quality_level(self, score: float) -> QualityLevel:
        """确定质量等级"""
        if score >= 95:
            return QualityLevel.EXCELLENT
        elif score >= 85:
            return QualityLevel.GOOD
        elif score >= 70:
            return QualityLevel.ACCEPTABLE
        elif score >= 50:
            return QualityLevel.POOR
        else:
            return QualityLevel.CRITICAL
    
    def _generate_recommendations(self, report: QualityReport):
        """生成改进建议"""
        if report.quality_score < 95:
            if any(issue.category == "performance" for issue in report.issues):
                report.add_recommendation("考虑启用缓存机制以提升性能")
                report.add_recommendation("优化评估算法以减少计算时间")
            
            if any(issue.category == "functionality" for issue in report.issues):
                report.add_recommendation("完善功能测试覆盖率")
                report.add_recommendation("加强错误处理机制")
            
            if any(issue.category == "configuration" for issue in report.issues):
                report.add_recommendation("审查和优化默认配置参数")
                report.add_recommendation("添加配置验证机制")
            
            if report.quality_score < 70:
                report.add_recommendation("建议进行全面的代码审查")
                report.add_recommendation("增加单元测试和集成测试")
    
    def save_quality_report(self, report: QualityReport, output_path: str):
        """保存质量报告"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report.to_dict(), f, indent=2, ensure_ascii=False)
            
            log(_("twice_nat44_qa.report_saved", path=output_path), "info")
            
        except Exception as e:
            log(_("twice_nat44_qa.report_save_failed", path=output_path, error=str(e)), "error")
