module ntos-security-zone {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:security-zone";
  prefix ntos-security-zone;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-extensions;
  }

  import ntos-interface {
    prefix ntos-interface;
  }

  import ntos-vlan {
    prefix ntos-vlan;
  }

  import ntos-api {
    prefix ntos-api;
  }

  import extra-conditions {
    prefix extra-conditions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS security zone module.";

  revision 2024-03-26 {
    description
      "add rpc of security-interface";
    reference "";
  }

  revision 2022-03-24 {
    description
      "Initial version.";
    reference "";
  }

  typedef referenced-unit {
    type enumeration {
      enum interface {
        value 0;
        description
          "Interface.";
      }
    }
  }

  grouping policy-id {
    leaf id {
      config false;
      type uint64;
      description
        "Policy id.";
    }
  }

  grouping security-zone-config {
    leaf name {
      type ntos-types:ntos-obj-name-type;
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "The description of security zone.";
    }

    leaf priority {
      type uint32 {
        range "1..256" {
          error-message
            "The priority must >= 1 and <= 256.";
        }
      }
      description
        "The priority of security zone. Example:'1..256'.";
    }

    leaf be-ref {
      config false;
      type boolean;
      default "false";
      description
        "Whether the security zone is referenced by policy.";
    }

    uses policy-details;
  }

  grouping zone-interface-config {
    list interface {
      key "name";
      leaf name {
        type ntos-types:ifname;
      }
      description
        "The interface belonging to security zone.";
      ntos-extensions:nc-cli-one-liner;
    }
  }

  grouping zone-interface-state {
    list interface {
      key "name";
      leaf name {
        type ntos-types:ifname;
      }
      description
        "The interface belonging to security zone.";
      ntos-extensions:nc-cli-one-liner;
    }
  }

  rpc security-zone {
    description
      "Show security zone by offset or interface.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "VRF.";
      }

      container content {
        ntos-extensions:nc-cli-group "sub1";
        presence "Show security zone content.";
        leaf start {
          type uint32;
          description
            "Show security zone, start with the offset.";
        }
        leaf end {
          type uint32;
          description
            "Show security zone, end with the offset.";
        }

        leaf filter {
          ntos-extensions:nc-cli-group "sub2";
          type string;
        }

        leaf name {
          ntos-extensions:nc-cli-group "sub2";
          type string;
        }

        list interface {
          ntos-extensions:nc-cli-group "sub2";
          key "name";
          leaf name {
            type ntos-types:ifname;
          }
        }
      }
      leaf referenced {
        ntos-extensions:nc-cli-group "sub1";
        type referenced-unit;
      }
    }

    output {
      leaf total {
        type uint32;
        description
          "Total number of security zone.";
      }
      list zone {
        key "name";
        description
          "Configuration of security zone.";
        uses security-zone-config;
        uses zone-interface-state;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
      list interface {
        key "name";
        description
          "The interface belonging to security zone.";
        leaf name {
          type ntos-types:ifname;
        }
      }
    }

    ntos-extensions:nc-cli-show "security-zone";
    ntos-api:internal;
  }

  grouping security-interface-config {
    leaf name {
      type ntos-types:ifname;
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "The description of security interface.";
    }
    uses policy-details;
  }

  grouping policy-details {
    leaf policy-total {
      config false;
      type uint32;
      description
        "Total number of policies.";
    }

    list scy-policy-src {
      key id;
      config false;
      uses policy-id;
    }

    list scy-policy-dst {
      key id;
      config false;
      uses policy-id;
    }

    list sl-scy-policy-src {
      key id;
      config false;
      uses policy-id;
    }

    list sl-scy-policy-dst {
      key id;
      config false;
      uses policy-id;
    }

    list flow-ctrl-policy-src {
      key id;
      config false;
      uses policy-id;
    }

    list flow-ctrl-policy-dst {
      key id;
      config false;
      uses policy-id;
    }

    list content-audit-policy-src {
      key id;
      config false;
      uses policy-id;
    }

    list content-audit-policy-dst {
      key id;
      config false;
      uses policy-id;
    }

    list ssl-proxy-policy-src {
      key id;
      config false;
      uses policy-id;
    }

    list ssl-proxy-policy-dst {
      key id;
      config false;
      uses policy-id;
    }

    list snat-policy-src {
      key id;
      config false;
      uses policy-id;
    }

    list snat-policy-dst {
      key id;
      config false;
      uses policy-id;
    }

    list dnat-policy-src {
      key id;
      config false;
      uses policy-id;
    }

    list dnat-policy-dst {
      key id;
      config false;
      uses policy-id;
    }

    list anti-ddos-policy-src {
      key id;
      config false;
      uses policy-id;
    }

    list anti-ddos-policy-dst {
      key id;
      config false;
      uses policy-id;
    }

    list local-defend-policy-src {
      key id;
      config false;
      uses policy-id;
    }

    list local-defend-policy-dst {
      key id;
      config false;
      uses policy-id;
    }
    list pbr-policy-src {
      key id;
      config false;
      uses policy-id;
    }
    list ti-policy-dst {
      key id;
      config false;
      uses policy-id;
    }
    list dns-sec-policy-dst {
      key id;
      config false;
      uses policy-id;
    }
    list webauth-policy {
      key id;
      config false;
      uses policy-id;
    }
    list port-mapping-src {
      key id;
      config false;
      uses policy-id;
    }
  }

  rpc security-interface {
    description
      "Show state of security interface.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "VRF.";
      }

      container content {
        ntos-extensions:nc-cli-group "sub1";
        presence "Show security interface content.";
        leaf start {
          type uint32;
          description
            "Show security interface, start with the offset.";
        }
        leaf end {
          type uint32;
          description
            "Show security interface, end with the offset.";
        }

        leaf filter {
          ntos-extensions:nc-cli-group "sub2";
          type string;
        }

        list interface {
          ntos-extensions:nc-cli-group "sub2";
          key "name";
          leaf name {
            type ntos-types:ifname;
          }
        }
      }
      leaf referenced {
        ntos-extensions:nc-cli-group "sub1";
        type referenced-unit;
      }
    }

    output {
      leaf total {
        type uint32;
        description
          "Total number of security interface.";
      }
      list interface {
        key "name";
        description
          "The policy details of the interface.";
        uses security-interface-config;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }

    ntos-extensions:nc-cli-show "security-interface";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Security zone configuration.";

    container security-zone {
      description
        "Configuration of security zone.";
      extra-conditions:unique-values "zone/*[local-name()='priority']" {
        error-message "The priority must be unique in a VRF.";
      }
      extra-conditions:unique-values "zone/interface/*[local-name()='name']" {
        error-message "An intf cannot belong to multiple zones.";
      }
      list zone {
        key "name";
        description
          "Configuration of security zone.";
        uses security-zone-config;
        uses zone-interface-config;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Security zone state.";

    container security-zone {
      description
        "State of security zone.";
      list zone {
        key "name";
        description
          "State of security zone.";
        uses security-zone-config;
        uses zone-interface-config;
      }
    }
  }
}
