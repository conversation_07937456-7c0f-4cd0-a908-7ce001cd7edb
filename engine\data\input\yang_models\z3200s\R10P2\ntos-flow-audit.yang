module ntos-flow-audit {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:flow-audit";
  prefix ntos-flow-audit;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  /*import ntos-types {
    prefix ntos-types;
  }*/
  import ntos-system {
    prefix ntos-system;
  }
  /*import ntos-inet-types {
    prefix ntos-inet;
  }*/

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS flow audit module.";
  
  revision 2022-09-07 {
    description
      "add cmd of show operate state.";
    reference "revision 2022-09-07.";
  }

  typedef percent {
    type uint8 {
      range "1..100";
    }
  }

  grouping system-flow-audit-config {
    description
      "Configuration data for flow audit.";
    container flowrate {
      description
        "flowrate control.";
      leaf enable {
        type boolean;
        description
          "Enable the flow audit flowrate.";
      }   
    }
      
    container session {
      description
        "session control.";
      leaf enable {
        type boolean;
        description
          "Enable the flow audit session.";
      }
    }
    
    container flowtotal {
      description
        "flowtotal control.";
      leaf enabled {
        type boolean;
        description
          "Enable the flow audit flowtotal.";
      }
    }
    
    container flowspeed {
      description
        "flowspeed control.";
      leaf enabled {
        type boolean;
        description
          "Enable the flow audit flowspeed.";
      }
    }
    
    container database {
      description
        "Database configuration.";
      leaf type {
        description
          "The type of database.";
        type enumeration {
          enum "Default";
          enum "Sqlite";
          enum "Postgressql";
        }
      }
    }
  }

  grouping system-hard-disk-config {
    leaf hard-disk-quota {
      type percent {
        range "15..50";
      }
      default 20;
      description
        "percent value.";
    }
  }

  grouping system-TOP-N-refresh-time {
    leaf refresh-time {
      type uint32;
      default 30;
      description
        "refresh time value.";
    }
  }

  grouping fa-time {
    leaf year {
      type uint32;
      mandatory true;
      ntos-ext:nc-cli-no-name;
      description
        "Year.";
    } 
    leaf mon {
      type uint32;
      mandatory true;
      ntos-ext:nc-cli-no-name;
      description
        "Month.";
    }
    leaf day {
      type uint32;
      mandatory true;
      ntos-ext:nc-cli-no-name;
      description
        "Day.";
    }
    leaf hour {
      type uint32;
      ntos-ext:nc-cli-no-name;
      description
        "Hour.";
    }
    leaf min {
      type uint32;
      ntos-ext:nc-cli-no-name;
      description
        "Minute.";
    }

    leaf second {
      type uint32;
      ntos-ext:nc-cli-no-name;
      description
        "second.";
    }    
  }

  grouping filte-time {
    container start {      
      uses fa-time;
    }

    container end {
      uses fa-time;
    }
  }

  rpc show-session {
    description
      "Show flow-audit information.";
    input {
      leaf top {
        mandatory true;
        type uint32;
        description
          "top value.";
      }
      leaf sort-type {
        mandatory true;
        type enumeration {
          enum flow_total_num;
          enum tcp_num;
          enum udp_num;
          enum other_num;
          enum new_sec_num;
          enum new_sec_tcp_num;
          enum new_sec_udp_num;
        }
      }      
    }          
    output {
      leaf buffer {
        type string;
        description
          "session information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "session";
    ntos-api:internal;
  }

  rpc show-top-app-flowrate {
    description
      "Show flow-audit hiotory information.";
    input {
      container filter {
        description
          "Filter parameters.";
        leaf app {
           type string;
          description
            "APP should search";
        }

        leaf fuzzy-match {
          type boolean;
          description
          "If do fuzzy search.";
        }
      }

      leaf fold {
        type boolean;
          description
          "If the search result should fold.";
      }

      leaf top {
        mandatory true;
        type uint32;
      }

      leaf sort-type {
        mandatory true;
        type enumeration {
          enum up;
          enum down;
          enum sess_num;
          enum total;
        }
      }

      leaf not-refresh {
        type boolean;
          description
          "If should not refresh data";
      }

      uses filte-time;      
    }
    output {
      leaf buffer {
        type string;
        description
          "flow information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "flowrate application";
    ntos-api:internal;
  }

  rpc show-top-ip-flowrate {
    description
      "Show ip flowrate information.";
    input {
      container filter {
        description
          "Filter parameters.";
        leaf ip {
          type uint32;
          description
            "IP address";
        }

        leaf mask {
          type uint32;
          description
            "If do fuzzy search.";
        }
      }

      leaf fold {
        type boolean;
          description
          "If the search result should fold.";
      }

      leaf top {
        mandatory true;
        type uint32;
      }

      leaf sort-type {
        mandatory true;
        type enumeration {
          enum up;
          enum down;
          enum sess_num;
          enum total;
        }
      }

      leaf not-refresh {
        type boolean;
          description
          "If should not refresh data";
      }

      uses filte-time;
    }

    output {
      leaf buffer {
        type string;
        description
          "ip detail information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "flowrate ip";
    ntos-api:internal;
  }

  rpc show-ip-flowrate {
    description
      "Show ip flowrate information.";
    input {
      leaf ip {
        type uint32;
      }
      leaf top {
        mandatory true;
        type uint32;
      }
      leaf sort-type {
        mandatory true;
        type enumeration {
          enum up;
          enum down;
          enum sess_num;
          enum total;
        }
      }

      uses filte-time;
    }
    
    output {
      leaf buffer {
        type string;
        description
          "ip detail information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "ipflow";
    ntos-api:internal;
  }

  rpc show-app-flowrate {
    description
      "Show ip flowrate information.";
    input {
      leaf app {
        type string;
      }
      leaf top {
        mandatory true;
        type uint32;
      }
      leaf sort-type {
        mandatory true;
        type enumeration {
          enum up;
          enum down;
          enum sess_num;
          enum total;
        }
      }

      uses filte-time;
    }
    
    output {
      leaf buffer {
        type string;
        description
          "app detail information.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:feature "product";
    ntos-ext:nc-cli-show "appflow";
    ntos-api:internal;
  }

  rpc show-enable-status {
    description
      "Display enable status.";
    output {
      container flowrate {
        description
          "flowrate control.";
        leaf enable {
          type boolean;           
          description
            "Enable the flow audit flowrate.";
        }   
      } 
      container session {
        description
          "session control.";
        leaf enable {
          type boolean;
          description
            "Enable the flow audit session.";
        }
      }
      container flowtotal {
        description
          "flowtotal control.";
        leaf enabled {
          type boolean;
          description
            "Enable the flow audit flowtotal.";
        }
      }
      container flowspeed {
        description
          "flowspeed control.";
        leaf enabled {
          type boolean;
          description
            "Enable the flow audit flowspeed.";
        }
      }
      container log2cloud {
        description
          "log2cloud control.";
        leaf enabled {
          type boolean;
          description
            "Enable the flow audit log2cloud.";
        }
      }
      container flowauditservice {
        description
          "flowauditservice control.";
        leaf runstatus {
          type boolean;
          description
            "Show run status of the flow audit service.";
        }
      }
    }

    ntos-ext:nc-cli-show "flow audit enable status";
  }

  rpc set-debug-level {
    input {
    leaf module {
      type enumeration {
      enum "flow_audit";
      }
    }

    leaf level {
      type enumeration {
        enum "FATAL";
        enum "ERROR";
        enum "WARN";
        enum "INFO";
        enum "DEBUG";
        enum "TRACE";
        enum "DISABL";
        }
      }
    }

    output {
      leaf buffer {
        type string;

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "flow audit set-debug";
  }

  rpc show-operate-state {
    description
      "Display operate state.";
    output {      
      leaf operate-state {
        type string;           
        description
        "Flow audit operate state.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "flow audit operate state";
  }

  rpc show-ip-rate-speed {
    description
      "Display ip rate speed state.";

    input {
      leaf ip-list {
        description "ip list [ip1,ip2,ip3...].";
        type string;
      }
    }

    output {
      list ip-info {
        leaf ip {
          type string;
        }
        leaf up {
          type uint64;
        }        
        leaf down {
          type uint64;
        }  
      }
    }

    ntos-ext:nc-cli-show "flow audit ip rate speed";
  }

  rpc all-switch-off {
    description "Turn off all flow audit functions.";
    input {
      leaf enabled {
        type boolean;
      }
    }
    ntos-ext:nc-cli-cmd "flow audit all-switch-off";
  }

  augment "/ntos:config/ntos-system:system" {
    description 
      "flow audit configuration.";
    container flow-audit {
      description 
        "flow audit enable configuration.";
      ntos-ext:feature "product";
      uses system-flow-audit-config;
      uses system-hard-disk-config;
      uses system-TOP-N-refresh-time;
    }
  }

  augment "/ntos:state/ntos-system:system" {
    description 
      "flow audit enable configuration.";
    container flow-audit {
      description 
        "flow audit enable configuration.";
      ntos-ext:feature "product";
      uses system-flow-audit-config;
      uses system-hard-disk-config;
      uses system-TOP-N-refresh-time;
    }
  }
}