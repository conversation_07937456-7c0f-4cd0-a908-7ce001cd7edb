module ntos-bridge {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:bridge";
  prefix ntos-bridge;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-qos {
    prefix ntos-qos;
  }
  import ntos-dhcp-snooping {
    prefix ntos-dhcp-snp;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS bridge interfaces.";

  revision 2022-10-24 {
    description
      "Support session source mac check.";
    reference "";
  }

  revision 2022-04-29 {
    description
      "Add bridge fdb aging command.";
    reference "";
  }

  revision 2021-12-24 {
    description
      "Add show bridge command.";
    reference "";
  }

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity bridge {
    base ntos-types:INTERFACE_TYPE;
    description
      "Bridge interface.";
  }

  identity bridge-slave {
    base ntos-types:INTERFACE_TYPE;
    description
      "Bridge slave interface.";
  }

  grouping cmd-output-buffer {
    leaf buffer {
      description
        "Command output buffer since last request.";

      type string;
      ntos-extensions:nc-cli-stdout;
      ntos-extensions:nc-cli-hidden;
    }
  }

  rpc show-bridge-state {
    description
      "Show bridge.";

    input {
      leaf interface {
        mandatory true;
        type string;
        ntos-extensions:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-bridge:bridge/*[local-name()='name']";
      }

      leaf param {
        mandatory true;
        type enumeration {
          enum fdb;
        }
        ntos-extensions:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-extensions:nc-cli-show "bridge";
  }

  rpc fdb-delete {
    description
      "Remove an FDB entry from FDB table.";

    input {
      leaf mac {
        description
          "The MAC address required.";

        type ntos-if:mac-address;
        ntos-extensions:nc-cli-no-name;
      }

      leaf interface {
        description
          "The interface corresponding to FDB table entry.";

        type ntos-types:ifname;
        ntos-extensions:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-bridge:bridge/*[local-name()='name']";
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-extensions:nc-cli-cmd "bridge delete-fdb";
  }

  grouping bridge-config {
    description
      "Bridge configuration options.";

    list link-interface {
      key "slave";
      description
        "Set this interface as slave of this bridge.";
      ntos-extensions:nc-cli-one-liner;

      leaf slave {
        type ntos-types:ifname;
        // must '. != ../../name' {
        //   error-message "Cannot bind our own interface";
        // }
        // must "count(../../../*[local-name()='lag']/*[local-name()='link-interface']/*[local-name()='slave'][text()=current()]) = 0" {
        //   error-message "Cannot bind an interface already bound to a lag";
        // }
        // must "count(../../../*[local-name()='vswitch']/*[local-name()='link-interface']/*[local-name()='slave'][text()=current()]) = 0" {
        //   error-message "Cannot bind an interface already bound to a vswitch";
        // }
        // must 'count(../../../bridge/link-interface/slave[text()=current()]) = 1' {
        //   error-message "Cannot bind an interface already bound to another bridge";
        // }
        description
          "Set this interface as slave of this bridge.";
        ntos-extensions:nc-cli-completion-xpath
          "../*[.!=current()]/*[local-name()='name']";
      }
    }
  }

  typedef session-source-check {
    type enumeration {
      enum transparent-forward {
        description
            "Forward the packet transparently if mismatched.";
        value 1;
      }

      enum dont-check {
        description
            "Doesn't check.";
        value 0;
      }
    }
  }

  rpc show-bridge-interface-state {
    ntos-extensions:nc-cli-show "interface bridge state";
    description
      "Show interface state.";
    input {
      leaf vrf {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
        type string;
        default "main";
        description
          "VRF to look into.";
      }
      leaf name {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-bridge:bridge/*[local-name()='name']";
        type ntos-types:ifname;
        description
          "Show interface by this name.";
      }
      leaf start {
        type uint16 {
          range "1..65535";
        }
        description
          "Start interface number.";
      }
      leaf end {
        type uint16 {
          range "1..65535";
        }
        description
          "End interface number.";
      }
      leaf search-name {
        type string {
          length "1..15";
        }
        description
          "Search device with name.";
      }
    }

    output {
      leaf interface-total {
        type uint16 {
          range "0..65535";
        }
        description
          "Interface total number.";
      }
      list interface {
        description
          "Output for interface list";
        uses ntos-interface:interface-state;
        uses ntos-interface:eth-state;
        uses ntos-if:interface-common-state;
        uses ntos-ip:ntos-ipv4-state;
        uses ntos-ip:ntos-ipv6-state;
        list link-interface {
          key "slave";
          ntos-extensions:nc-cli-one-liner;
          leaf slave {
            type ntos-types:ifname;
          }
          leaf type {
            type string;
          }
        }
        container access-control {
          leaf https {
            type boolean;
            default "false";
          }
          leaf ping {
            type boolean;
            default "false";
          }
          leaf ssh {
            type boolean;
            default "false";
          }
        }

        leaf session-source-check {
            type session-source-check;
        }

      }
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network bridge configuration.";

    list bridge {
      max-elements 127;
      key "name";
      description
        "The list of bridge interfaces on the device.";
      ntos-extensions:feature "product";
      uses ntos-interface:nonphy-interface-config;
      uses ntos-interface:eth-config;
      uses ntos-ip:ntos-ipv4-config;
      uses ntos-ip:ntos-ipv6-config;
      uses ntos-ip:ntos-network-stack-parameters;
      uses bridge-config;
      uses ntos-qos:logical-if-qos-config;
      uses ntos-dhcp-snp:dhcp-snp-parameters;

      leaf session-source-check {
        type session-source-check;
        default "dont-check";
      }
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network bridge operational state data.";

    list bridge {
      key "name";
      description
        "The list of bridge interfaces on the device.";
      ntos-extensions:feature "product";
      uses ntos-interface:interface-state;
      uses ntos-interface:eth-state;
      uses ntos-ip:ntos-ipv4-state;
      uses ntos-ip:ntos-ipv6-state;
      uses ntos-ip:ntos-network-stack-parameters;
      uses bridge-config;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses ntos-qos:logical-if-qos-state;
      uses ntos-dhcp-snp:dhcp-snp-parameters;

      leaf session-source-check {
        type session-source-check;
        config false;
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    container bridge {
      description
        "bridge configuration.";

      container fdb {
        description
        "bridge fdb configuration.";

        leaf aging {
          description
            "The bridge fdb aging required.";

          type uint32;
          default 300;
        }
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    container bridge {
      description
        "bridge configuration.";

      container fdb {
        description
        "bridge fdb configuration.";

        leaf aging {
          description
            "The bridge fdb aging required.";

          type uint32;
          default 300;
        }
      }
    }
  }
}
