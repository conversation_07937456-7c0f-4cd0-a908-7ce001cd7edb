#config-version=FG100F-7.6.3-FW-build3510-250415:opmode=0:vdom=0:user=admin
config system interface
    edit "port1"
        set ip ************/24
        set allowaccess ping https ssh snmp http fgfm
    next
    edit "port2"
        set ip ***********/24
    next
    edit "port3"
        set vdom "root"                                
        set ip ************* *************     
        set allowaccess ping https ssh snmp http fgfm
        set status down                                 
        set type physical                              
        set description "port3-description"
        set alias "Ge0/3"                              
        set security-mode captive-portal
        set device-identification enable
        set role lan
        set snmp-index 7
        set secondary-IP enable
        config ipv6                                            
            set ip6-address 2003::1/96
            set ip6-allowaccess ping https ssh http
        end
        config secondaryip
            edit 1
                set ip ******* *************
            next
            edit 2
                set ip ******* *************
            next
        end
    next
    edit "wan1"
        set vdom "root"
        set mode dhcp
        set allowaccess ping https ssh snmp http fgfm
        set type physical
        set description "wan1-description"
        set alias "Ge0/3"
        set estimated-upstream-bandwidth 123
        set estimated-downstream-bandwidth 456
        set role wan
        set snmp-index 7
        config ipv6
            set ip6-mode dhcp
            set ip6-allowaccess ping https ssh http
        end
    next
    edit "pppoe1"
        set vdom "root"
        set mode pppoe
        set allowaccess ping https ssh http
        set type physical
        set description "pppoe1-description"
        set alias "Ge0/4"
        set device-identification enable
        set estimated-upstream-bandwidth 123
        set estimated-downstream-bandwidth 456
        set role lan
        set snmp-index 7
        config ipv6
            set ip6-mode dhcp
            set ip6-allowaccess ping https ssh http
        end
        set username "ruijie"
        set password ENC PVAIGyI1hcps/rDwuUl8tBPIwHcydLS+sqnheNSd0NxiaNNu+o5fE4ftN0VIn/FZBQwNwuR3HkAlYVc9q5rbe5NgEu9nTZL8WDeyuQZolEnGqL8/l2FJUIQs+iednQoAIp7/BR6ADIJ0LP9vzDgNUtcuFnYDfjzYPAlqb1kFH+4OEmOHqSrIvoJJUtezGaQN7JKT/A==
    next
    edit "vlan3-100"
        set vdom "root"
        set mode dhcp
        set allowaccess ping https ssh snmp http fgfm
        set role lan
        set snmp-index 23
        set interface "port3"
        set vlanid 100
    next
    edit "port1.10"
        set interface "port1"
        set vlanid 10
        set mode static
        set ip ************/24
    next
    edit "port1.20"
        set interface "port1"
        set vlanid 20
        set mode dhcp
    next
    edit "wan_pppoe"
        set interface "port2"
        set vlanid 30
        set mode pppoe
        set username "user123"
        set password "pass123"
    next
    edit "fortilink"
        set vdom "root123"
        set allowaccess ping fabric
        set status down
        set type aggregate
        set lldp-reception enable
        set lldp-transmission enable
        set snmp-index 25
    next
end

config router static
    edit 1
        set dst 0.0.0.0 0.0.0.0
        set gateway ************
        set device "port1"
    next
    edit 2
        set dst ********** *************
        set gateway *************
        set device "port2"
        set distance 10
    next
    edit 3
        set dst ************* *************
        set blackhole enable
    next
    edit 4
        set dst ********** ***********
        set gateway ************00
    next
end

config system zone
    edit "TEST1"
        set interface "port3" "port4"
    next
    edit "TEST2"
        set interface "port2" "port3"
    next
end

config firewall address
    edit "***********-*************"
        set uuid 987da852-89c0-51ee-db79-7f4778c644b7
        set type iprange
        set start-ip ***********
        set end-ip *************
    next
    edit "***********"
        set uuid 98896f70-89c0-51ee-8e3c-5be630957f68
        set subnet *********** *************
    next
end

config firewall service group
    edit "test"
            set member "***********-*************" "***********" "***********"
    next
end

config firewall service custom
    edit "TCP-1521"
            set tcp-portrange 1521
    next
    edit "KERBEROS"
            set category "Authentication"
            set tcp-portrange 88 464
            set udp-portrange 88 464
    next
    edit "UDP67-68"
            set udp-portrange 67 68
    next
end

config firewall service group
    edit "Exchange Server"
            set member "TCP-1521" "UDP67-68"
    next
end