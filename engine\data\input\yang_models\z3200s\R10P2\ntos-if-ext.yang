module ntos-if-ext {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:if-ext";
  prefix ntos-if-ext;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-interface {
    prefix ntos-interface;
  }

  import ntos-vlan {
    prefix ntos-vlan;
  }

  import ntos-bridge {
    prefix ntos-bridge;
  }

  import ntos-lag {
    prefix ntos-lag;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS network interfaces extend module.";

  revision 2022-09-03 {
    description
      "Initial version.";
    reference "";
  }


  grouping interface-base-info {
    description
       "The grouping data for interface base information.";
    leaf index {
	   type int32;
	   config false;
	   description
           "A unique value, greater than zero, for each interface. It
            is recommended that values are assigned contiguously
            starting from 1. The value for each interface sub-layer
            must remain constant at least from one re-initialization of
            the entity's network management system to the next re-initialization.";
	}

/*
	leaf mtu {
       type int32;
	   config false;
	   description
	      "The size of the largest packet which can be sent/received
           on the interface, specified in octets.  For interfaces that
           are used for transmitting network datagrams, this is the
           size of the largest network datagram that can be sent on the
           interface.";
	}
*/

	leaf speed {
	    type ntos-types:counter32;
		config false;
	    description
           "An estimate of the interface's current bandwidth in bits
            per second.  For interfaces which do not vary in bandwidth
            or for those where no accurate estimation can be made, this
            object should contain the nominal bandwidth.  If the
            bandwidth of the interface is greater than the maximum value
            reportable by this object then this object should report its
            maximum value (4,294,967,295) and ifHighSpeed must be used
            to report the interace's speed.  For a sub-layer which has
            no concept of bandwidth, this object should be zero.";

	}

    leaf admin-status {
	    type enumeration {
            enum up;
			enum down;
			enum testing;
		}
		description
		   "The desired state of the interface.  The testing(3) state
            indicates that no operational packets can be passed. When a
            managed system initializes, all interfaces start with
            ifAdminStatus in the down(2) state.  As a result of either
            explicit management action or per configuration information
            retained by the managed system, ifAdminStatus is then
            changed to either the up(1) or testing(3) states (or remains
            in the down(2) state).";
	}

	leaf op-status {
	    type enumeration {
           enum up;
		   enum down;
		   enum testing;
		   enum unknown;
		   enum dormant;
		   enum not-present;
           enum lower-layer-down;
		}
		description
		   "The current operational state of the interface.  The
            testing(3) state indicates that no operational packets can
            be passed. If ifAdminStatus is down(2) then ifOperStatus
            should be down(2). If ifAdminStatus is changed to up(1)
            then ifOperStatus should change to up(1) if the interface is
            ready to transmit and receive network traffic; it should
            change to dormant(5) if the interface is waiting for
            external actions (such as a serial line waiting for an
            incoming connection); it should remain in the down(2) state
            if and only if there is a fault that prevents it from going
            to the up(1) state; it should remain in the notPresent(6)
            state if the interface has missing (typically, hardware)
            components.";

	}
  }

  grouping interface-ext-info {
    description
      "The grouping data for interface extend information.";

    leaf in-multicast-pkts {
       type ntos-types:counter64;
	   config false;
	   description
	      "The number of packets, delivered by this sub-layer to a
           higher (sub-)layer, which were addressed to a multicast
           address at this sub-layer.  For a MAC layer protocol, this
           includes both Group and Functional addresses.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf in-broadcast-pkts {
       type ntos-types:counter64;
	   config false;
       description
          "The number of packets, delivered by this sub-layer to a
           higher (sub-)layer, which were addressed to a broadcast
           address at this sub-layer.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf out-multicast-pkts {
       type ntos-types:counter64;
       description
	      "The total number of packets that higher-level protocols
           requested be transmitted, and which were addressed to a
           multicast address at this sub-layer, including those that
           were discarded or not sent.  For a MAC layer protocol, this
           includes both Group and Functional addresses.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
    }

    leaf out-broadcast-pkts {
       type ntos-types:counter64;
	   config false;
       description
          "The total number of packets that higher-level protocols
           requested be transmitted, and which were addressed to a
           broadcast address at this sub-layer, including those that
           were discarded or not sent.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-in-octets {
       type ntos-types:counter64;
       config false;
       description
          "The total number of octets received on the interface,
           including framing characters.  This object is a 64-bit
           version of ifInOctets.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-in-ucast-pkts {
       type ntos-types:counter64;
       config false;
       description
          "The number of packets, delivered by this sub-layer to a
           higher (sub-)layer, which were not addressed to a multicast
           or broadcast address at this sub-layer.  This object is a
           64-bit version of ifInUcastPkts.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-in-multicast-pkts {
       type ntos-types:counter64;
	   config false;
	   description
          "The number of packets, delivered by this sub-layer to a
           higher (sub-)layer, which were addressed to a multicast
           address at this sub-layer.  For a MAC layer protocol, this
           includes both Group and Functional addresses.  This object
           is a 64-bit version of ifInMulticastPkts.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-in-broadcast-pkts {
       type ntos-types:counter64;
	   config false;
	   description
          "The number of packets, delivered by this sub-layer to a
           higher (sub-)layer, which were addressed to a broadcast
           address at this sub-layer.  This object is a 64-bit version
           of ifInBroadcastPkts.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-in-ipv4-octets {
       type ntos-types:counter64;
       config false;
       description
          "The total number of ipv4 octets received on the interface,
           including framing characters.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-in-ipv6-octets {
       type ntos-types:counter64;
       config false;
       description
          "The total number of ipv6 octets received on the interface,
           including framing characters.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-in-ipv4-pkts {
       type ntos-types:counter64;
       config false;
       description
          "The number of ipv4 packets, delivered by this sub-layer to a
           higher (sub-)layer.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-in-ipv6-pkts {
       type ntos-types:counter64;
       config false;
       description
          "The number of ipv6 packets, delivered by this sub-layer to a
           higher (sub-)layer.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-out-octets {
       type ntos-types:counter64;
	   config false;
	   description
          "The total number of octets transmitted out of the
           interface, including framing characters.  This object is a
           64-bit version of ifOutOctets.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-out-ucast-pkts {
       type ntos-types:counter64;
	   config false;
	   description
          "The total number of packets that higher-level protocols
           requested be transmitted, and which were not addressed to a
           multicast or broadcast address at this sub-layer, including
           those that were discarded or not sent.  This object is a
           64-bit version of ifOutUcastPkts.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-out-multicast-pkts {
       type ntos-types:counter64;
	   config false;
	   description
          "The total number of packets that higher-level protocols
           requested be transmitted, and which were addressed to a
           multicast address at this sub-layer, including those that
           were discarded or not sent.  For a MAC layer protocol, this
           includes both Group and Functional addresses.  This object
           is a 64-bit version of ifOutMulticastPkts.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-out-broadcast-pkts {
       type ntos-types:counter64;
	   config false;
	   description
          "The total number of packets that higher-level protocols
           requested be transmitted, and which were addressed to a
           broadcast address at this sub-layer, including those that
           were discarded or not sent.  This object is a 64-bit version
           of ifOutBroadcastPkts.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-out-ipv4-octets {
       type ntos-types:counter64;
       config false;
       description
          "The total number of ipv4 octets transmitted out of the
           interface, including framing characters.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-out-ipv6-octets {
       type ntos-types:counter64;
       config false;
       description
          "The total number of ipv6 octets transmitted out of the
           interface, including framing characters.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-out-ipv4-pkts {
       type ntos-types:counter64;
       config false;
       description
          "The total number of ipv4 packets that higher-level protocols
           requested be transmitted, including those that
           were discarded or not sent..
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-out-ipv6-pkts {
       type ntos-types:counter64;
       config false;
       description
          "The total number of ipv6 packets that higher-level protocols
           requested be transmitted, including those that
           were discarded or not sent.
           Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf hc-in-crcerror-pkts {
       type ntos-types:counter64;
	   config false;
	   description
           "Discontinuities in the value of this counter can occur at
           re-initialization of the management system, and at other
           times as indicated by the value of ifCounterDiscontinuityTime.";
	}

    leaf high-speed {
       type uint32;
	   config false;
       units "1,000,000 bits/second";
	   description
          "An estimate of the interface's current bandwidth in units
           of 1,000,000 bits per second.  If this object reports a
           value of `n' then the speed of the interface is somewhere in
           the range of `n-500,000' to `n+499,999'.  For interfaces
           which do not vary in bandwidth or for those where no
           accurate estimation can be made, this object should contain
           the nominal bandwidth.  For a sub-layer which has no concept
           of bandwidth, this object should be zero.";
	}
  }

  grouping interface-ext-config {
    description
      "The grouping data for interface extend configuration.";

	leaf link-up-down-trap-enabled {
	   type boolean;
	   description
          "Indicates whether linkUp/linkDown traps should be generated
           for this interface.
           By default, this object should have the value enabled(1) for
           interfaces which do not operate on 'top' of any other
           interface (as defined in the ifStackTable), and disabled(2)
           otherwise.";
	}
  }

  grouping interface-statistics {
     description
        "The grouping data for interface statistics.";

	 container statistics {
        description
           "Interface statistics state.";
		uses interface-ext-info;
	 }

  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
     uses interface-base-info;
     uses interface-ext-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
     uses interface-statistics;
     uses interface-ext-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-vlan:vlan" {
     uses interface-base-info;
     uses interface-ext-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-vlan:vlan" {
     uses interface-statistics;
     uses interface-ext-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-bridge:bridge" {
     uses interface-base-info;
     uses interface-ext-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-bridge:bridge" {
     uses interface-statistics;
     uses interface-ext-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-lag:lag" {
     uses interface-base-info;
     uses interface-ext-config;
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-lag:lag" {
     uses interface-statistics;
     uses interface-ext-config;
  }

  notification if-up-trap {
    description
        "A linkUp trap signifies that the SNMP entity, acting in an
         agent role, has detected that the ifOperStatus object for
         one of its communication links left the down state and
         transitioned into some other state (but not into the
         notPresent state).  This other state is indicated by the
         included value of ifOperStatus.";

    leaf if-index {
      type int32 {
        range "1..2147483647";
      }
      description
        "A unique value, greater than zero, for each interface.  It
         is recommended that values are assigned contiguously
         starting from 1.  The value for each interface sub-layer
         must remain constant at least from one re-initialization of
         the entity's network management system to the next re-
         initialization.";
    }

    leaf if-admin-status {
      type enumeration {
        enum up;
        enum down;
        enum testing;
      }
      description
        "The desired state of the interface.  The testing(3) state
         indicates that no operational packets can be passed. When a
         managed system initializes, all interfaces start with
         ifAdminStatus in the down(2) state.  As a result of either
         explicit management action or per configuration information
         retained by the managed system, ifAdminStatus is then
         changed to either the up(1) or testing(3) states (or remains
         in the down(2) state).";
    }

	leaf if-oper-status {
      type enumeration {
        enum up;
        enum down;
        enum testing;
        enum unknown;
        enum dormant;
        enum not-present;
        enum lower-layer-down;
      }
      description
        "The current operational state of the interface.  The
         testing(3) state indicates that no operational packets can
         be passed. If ifAdminStatus is down(2) then ifOperStatus
         should be down(2). If ifAdminStatus is changed to up(1)
         then ifOperStatus should change to up(1) if the interface is
         ready to transmit and receive network traffic; it should
         change to dormant(5) if the interface is waiting for
         external actions (such as a serial line waiting for an
         incoming connection); it should remain in the down(2) state
         if and only if there is a fault that prevents it from going
         to the up(1) state; it should remain in the notPresent(6)
         state if the interface has missing (typically, hardware)
         components.";
	}
  }

  notification if-down-trap {
    description
        "A linkDown trap signifies that the SNMP entity, acting in
         an agent role, has detected that the ifOperStatus object for
         one of its communication links is about to enter the down
         state from some other state (but not from the notPresent
         state).  This other state is indicated by the included value
         of ifOperStatus.";

    leaf if-index {
      type int32 {
        range "1..2147483647";
      }
      description
        "A unique value, greater than zero, for each interface.  It
         is recommended that values are assigned contiguously
         starting from 1.  The value for each interface sub-layer
         must remain constant at least from one re-initialization of
         the entity's network management system to the next re-
         initialization.";
    }

    leaf if-admin-status {
      type enumeration {
        enum up;
        enum down;
        enum testing;
      }
      description
        "The desired state of the interface.  The testing(3) state
         indicates that no operational packets can be passed. When a
         managed system initializes, all interfaces start with
         ifAdminStatus in the down(2) state.  As a result of either
         explicit management action or per configuration information
         retained by the managed system, ifAdminStatus is then
         changed to either the up(1) or testing(3) states (or remains
         in the down(2) state).";
    }

	leaf if-oper-status {
      type enumeration {
        enum up;
        enum down;
        enum testing;
        enum unknown;
        enum dormant;
        enum not-present;
        enum lower-layer-down;
      }
      description
        "The current operational state of the interface.  The
         testing(3) state indicates that no operational packets can
         be passed. If ifAdminStatus is down(2) then ifOperStatus
         should be down(2). If ifAdminStatus is changed to up(1)
         then ifOperStatus should change to up(1) if the interface is
         ready to transmit and receive network traffic; it should
         change to dormant(5) if the interface is waiting for
         external actions (such as a serial line waiting for an
         incoming connection); it should remain in the down(2) state
         if and only if there is a fault that prevents it from going
         to the up(1) state; it should remain in the notPresent(6)
         state if the interface has missing (typically, hardware)
         components.";
	}
  }
}
