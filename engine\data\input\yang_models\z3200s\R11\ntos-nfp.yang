module ntos-nfp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:nfp";
  prefix ntos-nfp;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-system {
    prefix ntos-sys;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-fast-path {
    prefix ntos-fp;
  }
  import ntos-interface {
    prefix ntos-interface;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS NFP module.";

  revision 2024-11-14 {
    description
      "Support npf speed test mode.";
    reference "";
  }

  revision 2024-06-19 {
    description
      "Support npf tunnel inspect ability.";
    reference "";
  }

  revision 2023-05-05 {
    description
      "Support show flows filter mllb.";
    reference "";
  }

  revision 2022-12-12 {
    description
      "Support npf-ts show flow-count.";
    reference "";
  }

  revision 2022-11-18 {
    description
      "Support IPv6 filter.";
    reference "";
  }

  revision 2022-05-25 {
    description
      "Add RPC show-flows-json and update flow-filter.";
    reference "";
  }

  revision 2022-02-25 {
    description
      "Initial version.";
    reference "";
  }

  identity nfp {
    base ntos-types:SERVICE_LOG_ID;
    description
      "NFP service.";
    ntos-ext:nc-cli-identity-name "nfp";
  }

  typedef protocol {
    type enumeration {
      enum tcp {
        description
          "Transmission Control Protocol.";
      }
      enum udp {
        description
          "User Datagram Protocol.";
      }
      enum icmp {
        description
          "Internet Control Message Protocol.";
      }
    }
    description
      "Layer 4 protocol.";
  }

  typedef conntrack-state-t {
    type enumeration {
      enum tcp-syn-sent {
        description
          "TCP State SYN-SENT.";
      }
      enum tcp-simsyn-sent {
        description
          "TCP State SIMSYN-SENT.";
      }
      enum tcp-syn-received {
        description
          "TCP State SYN-RECEIVED.";
      }
      enum tcp-established {
        description
          "TCP State ESTABLISHED.";
      }
      enum tcp-fin-sent {
        description
          "TCP State FIN-SENT.";
      }
      enum tcp-fin-received {
        description
          "TCP State FIN-RECEIVED.";
      }
      enum tcp-closed {
        description
          "TCP State CLOSED.";
      }
      enum tcp-close-wait {
        description
          "TCP State CLOSE-WAIT.";
      }
      enum tcp-fin-wait {
        description
          "TCP State FIN-WAIT.";
      }
      enum tcp-last-ack {
        description
          "TCP State LAST-ACK.";
      }
      enum tcp-time-wait {
        description
          "TCP State TIME-WAIT.";
      }
      enum udp-new {
        description
          "UDP State NEW.";
      }
      enum udp-established {
        description
          "UDP State ESTABLISHED.";
      }
      enum udp-closed {
        description
          "UDP State CLOSED.";
      }
      enum icmp-new {
        description
          "ICMP State NEW.";
      }
      enum icmp-established {
        description
          "ICMP State ESTABLISHED.";
      }
      enum icmp-closed {
        description
          "ICMP State CLOSED.";
      }
      enum generic-new {
        description
          "Generic State NEW.";
      }
      enum generic-established {
        description
          "Generic State ESTABLISHED.";
      }
      enum generic-closed {
        description
          "Generic State CLOSED.";
      }
      enum session-deny {
        description
          "session deny.";
      }
    }
    description
      "Conntrack state.";
  }

  grouping outbound-interface {
    description
      "Interface match.";

    leaf outbound-interface {
      type ntos-types:ifname;
      mandatory true;
      description
        "Interface to match on outbound.";
      ntos-ext:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*/*[local-name()='name']";
    }
  }

  grouping inbound-interface {
    description
      "Interface match.";

    leaf inbound-interface {
      type ntos-types:ifname;
      mandatory true;
      description
        "Interface to match on inbound.";
      ntos-ext:nc-cli-completion-xpath
        "../../../ntos-interface:interface/*/*[local-name()='name']";
    }
  }

  grouping vrf {
    description
      "VRF.";

    leaf vrf {
      type string;
      default "main";
      description
        "VRF.";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos:name";
    }
  }

  grouping cmd-output-buffer {
    description
      "Command output buffer.";

    leaf buffer {
      type string;
      description
        "Command output buffer since last request.";
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  grouping page-filter {
    description
      "Page filter parameters.";

    leaf page-index {
      type uint32{
          range "1..10000000";
        }
      description
        "Page index.";
    }

    leaf page-size {
      type uint32{
          range "1..100";
        }
      description
        "Number entries of one page.";
    }
  }

  grouping packet-filter {
    description
      "Packet filter parameters.";

    leaf equal {
      type uint32 {
          range "1..4294967295";
      }
      description
        "Filter by session packets count.";
    }

    leaf less-then {
      type uint32 {
          range "1..4294967295";
      }
      description
        "Filter by session packets count(include equal).";
    }

    leaf more-then {
      type uint32 {
          range "1..4294967295";
      }
      description
        "Filter by session packets count(include equal).";
    }
  }

  grouping flow-filter {
    description
      "Flow filter parameters.";

    leaf mllb {
      type uint16;
      description
        "Filter by mllb flag.";
    }

    leaf session-id {
      type string {
        length "1..1023";
      }
      description
        "Filter by session ids, Multiple session id seperated by ','";
    }

    leaf proto {
      type union {
        type protocol;
        type uint8 {
          range "0..255";
        }
      }

      description
        "Filter by protocol.";
    }

    leaf saddr {
      /* note: 支持IPv4和IPv6 */
      type ntos-inet:ip-address;
      description
        "Filter by source address.";
    }

    leaf daddr {
      /* note: 支持IPv4和IPv6 */
      type ntos-inet:ip-address;
      description
        "Filter by destination address.";
    }

    leaf sport {
      type ntos-inet:port-number;
      description
        "Filter by source port.";
    }

    leaf dport {
      type ntos-inet:port-number;
      description
        "Filter by destination port.";
    }

    leaf srcif {
      type union {
        type ntos-types:ifname;
      }
      description
        "Filter by inbound interface.";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']|
        /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='ipv4']/
         *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface'] |
         /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='ipv4']/
         *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
    }

    leaf dstif {
      type union {
        type ntos-types:ifname;
      }
      description
        "Filter by outbound interface.";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']|
        /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='physical']/*[local-name()='ipv4']/
         *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface'] |
         /ntos:state/ntos:vrf/ntos-interface:interface/*[local-name()='vlan']/*[local-name()='ipv4']/
         *[local-name()='pppoe']/*[local-name()='connection']/*[local-name()='tunnel-interface']";
    }

    leaf policy {
      type string {
        length "1..127";
      }
      description
        "Filter by policy id or name.";
    }

    leaf app {
      type string {
        length "1..127";
      }
      description
        "Filter by application name or application ID(format as A-B-C-D).";
    }

    leaf alg {
      type string {
        length "1..12";
      }
      description
        "Filter by ALG name.";
    }

    leaf nat-saddr {
      /* note: 支持IPv4和IPv6 */
      type ntos-inet:ip-address;
      description
        "Filter by NAT source address.";
    }

    leaf nat-daddr {
      /* note: 支持IPv4和IPv6 */
      type ntos-inet:ip-address;
      description
        "Filter by NAT destination address.";
    }

    leaf nat-sport {
      type ntos-inet:port-number;
      description
        "Filter by NAT source port.";
    }

    leaf nat-dport {
      type ntos-inet:port-number;
      description
        "Filter by NAT destination port.";
    }

    container forward-packets {
      uses packet-filter;
      description
        "Filter by session forward packets count.";
    }

    container backward-packets {
      uses packet-filter;
      description
        "Filter by session backward packets count .";
    }

    container two-way-packets {
      uses packet-filter;
      description
        "Filter by session forward and backward packets count.";
    }

    leaf create-time-within-minutes {
      type uint32 {
          range "1..60";
      }
      description
        "Filter by create time in minutes(not more then).";
    }

    leaf create-time-within-hours {
      type uint32 {
          range "1..24";
      }
      description
        "Filter by create time in hours(not more then).";
    }

    leaf create-time-within-days {
      type uint32 {
          range "1..31";
      }
      description
        "Filter by create time in days(not more then).";
    }

    leaf ha-group {
      type uint32 {
          range "0..1";
      }
      description
        "Filter by ha group.";
    }

    leaf vrrp-group {
      type uint32 {
          range "0..0";
      }
      description
        "Filter by vrrp group.";
    }

    leaf ha-status {
      type enumeration {
          enum master;
          enum backup;
          enum all;
      }
      description
        "Filter by ha status.";
    }

    leaf family {
      type enumeration {
          enum ipv4;
          enum ipv6;
      }
      description
        "Filter by IP address family(ipv4 or ipv6).";
    }
  }

  grouping protocol-state-inspection {
    description
      "Protocol state inspection.";

    leaf tcp {
      description
        "Enable tcp state inspection.";

      type boolean;
      default "true";
    }

    leaf tcp-mode {
      type enumeration {
        enum loose {
          description
            "Loose mode for TCP state inspection.";
        }
        enum standard {
          description
            "Standard mode for TCP state inspection.";
        }
      }
      default standard;
      description
        "Mode for TCP state inspection.";
    }

    leaf icmp {
      description
        "Enable icmp state inspection.";

      type boolean;
      default "true";
    }
  }

  grouping tunnel-inspection {
    description "Tunnel inner packet inspection configuration.";

    container bridge {
      description "Bridge transparency inspection configuration.";

      leaf VLAN {
        description "Enable VLAN/QINQ packet transparency inspection.";
        type boolean;
        default "true";
      }

      leaf PPPoE {
        description "Enable PPPoE packet transparency inspection.";
        type boolean;
        default "false";
      }

      leaf GRE {
        description "Enable GRE packet transparency inspection.";
        type boolean;
        default "false";
      }

      leaf L2TPv2 {
        description "Enable L2TPv2 packet transparency inspection.";
        type boolean;
        default "false";
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "NFP configuration.";

    container nfp {
      description
        "NFP configuration.";

      container session {
        description
          "NFP session configuration.";

        list timeout {
          key "state-type";
          description
            "Session timeout configuration.";
          ntos-ext:nc-cli-one-liner;

          leaf state-type {
            type conntrack-state-t;
            description
              "Protocol state.";
          }

          leaf num{
            mandatory true;
            type uint32 {
              range "1..1296000";
            }
            description
              "Expiration timeout in seconds.";
            ntos-ext:nc-cli-no-name;
          }
        }

        container state-inspection {
          uses protocol-state-inspection;
        }
      }
      container tunnel-inspection {
        uses tunnel-inspection;
      }
    }
  }

  rpc show-flows {
    description
      "Show NFP flows information.";
    input {
      container filter {
        uses flow-filter;
      }
      uses page-filter;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-show "nfp flows";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-flows-json {
    description
      "Show NFP flows information.";
    input {
      container filter {
        uses flow-filter;
      }
      uses page-filter;
    }
    output {
      uses cmd-output-buffer;
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-show "nfp-flows-json";
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

   rpc show-flows-expect {
    description
      "Show NFP expected flows information.";
    input {
      container filter {
        uses flow-filter;
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-show "nfp flows expect";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-plugin {
    description
      "Show NFP plugin.";
    input {
      leaf session-id {
        type uint32;
        description
          "Match on session id.";
      }
    }
    output {
      uses cmd-output-buffer;
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-show "nfp plugin";
    ntos-api:internal;
  }

  rpc show-cookie {
    description
      "Show NFP cookie.";
    input {
      leaf pid {
        type uint8 {
          range "1..63";
        }
        description
          "Match on plugin id.";
      }

      must "count(pid) = 1" {
        error-message "pid must be set.";
      }

      container filter {
        uses flow-filter;
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-show "nfp cookie";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-pri {
    description
      "Show NFP module private date.";
    input {
      leaf module {
        type string {
          length "1..32";
        }
        description
          "Match by module name.";
      }

      must "count(module) = 1" {
        error-message "module name must be set.";
      }

      container filter {
        uses flow-filter;
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-show "nfp pri";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
  }

  rpc show-flows-stats {
    description
      "Show flows statistics.";
    output {
      uses cmd-output-buffer;
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-show "nfp flows stats";
    ntos-api:internal;
  }

  rpc show-stats {
    description
      "Show global statistics.";
    output {
      uses cmd-output-buffer;
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-show "nfp stats";
    ntos-api:internal;
  }

  rpc show-session-state-inspection {
    description
      "Show tcp state inspection status.";
    output {
      uses protocol-state-inspection;
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-show "nfp session state-inspection";
    ntos-api:internal;
  }

  rpc show-session-timeout {
    description
      "Show session timeout configuration.";
    output {
      uses cmd-output-buffer;
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-show "nfp session timeout";
    ntos-api:internal;
  }

  rpc flush-flows {
    description
      "Flush NFP flows.";
    input {
      container filter {
        uses flow-filter;
      }
    }
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-flush "nfp flows";
  }

  rpc flush-stats {
    description
      "Flush global statistics.";
    ntos-ext:feature "nfp";
    ntos-ext:nc-cli-flush "nfp stats";
  }

  rpc show-flow-count {
    description "Show current seesion record data, or under the specified condition.";

    input {
      leaf name {
        description "Specified the information type what you need.";
        type enumeration {
          enum "data";
        }
        default "data";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "flow-count";
  }

  grouping npf-tunnel-inspect-arguments {
    leaf path-mode {
      description "Specified the path mode.";
      type enumeration {
        enum "bridge";
      }
      default "bridge";
      ntos-ext:nc-cli-no-name;
    }

    leaf packet-type {
      description "Specified the tunnel packet type.";
      type enumeration {
        enum "all";
        enum "VLAN";
        enum "PPPoE";
        enum "GRE";
        enum "L2TPv2";
      }
      default "all";
      ntos-ext:nc-cli-no-name;
    }
  }

  rpc npf-tunnel-inspect {
    description "Show/enable/disable tunnel packet transparency inspection.";

    input {
      leaf operation {
        description "Specified the operation.";
        type enumeration {
          enum "show";
          enum "enable";
          enum "disable";
        }
        mandatory true;
        ntos-ext:nc-cli-no-name;
      }

      uses npf-tunnel-inspect-arguments;
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-cmd "npf-tunnel";
  }

  rpc npf-tunnel-inspect-show {
    description "Show tunnel packet transparency inspection.";

    input {
      uses npf-tunnel-inspect-arguments;
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "npf-tunnel";
  }

  rpc npf-speed-test-mode {
    description "Enable/disable speed test mode.";

    input {
      leaf operation {
        description "Specified the operation.";
        type enumeration {
          enum "enable";
          enum "disable";
        }
        mandatory true;
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-cmd "npf-speed-test-mode";
  }

  rpc show-npf-speed-test-mode {
    description
      "Show speed test mode.";
    output {
      leaf speed-test-mode {
        type boolean;
        description
          "Show speed test mode.";
      }
    }
    ntos-ext:nc-cli-show "npf-speed-test-mode";
    ntos-api:internal;
  }

}
