package dconfigtrans

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/application/models/firewallflextrans"
	"path/filepath"
	"time"
)

const ModelName = "配置转换作业表"

type User struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	Username string `json:"username"`
}

// ConfigTrans 配置转换任务表
type ConfigTrans struct {
	models.ModelBase
	JobID            string    `gorm:"column:job_id" json:"job_id"`                         // 任务ID
	InputFileName    string    `gorm:"column:input_file_name" json:"input_file_name"`       // 输入文件名
	InputMD5         string    `gorm:"column:input_md5" json:"input_md5"`                   // 输入文件MD5
	Status           uint      `gorm:"column:status" json:"status"`                         // 状态：0-处理中 1-成功 2-失败
	Mode             string    `gorm:"column:mode" json:"mode"`                             // 工作模式：convert-转换
	Model            string    `gorm:"column:model" json:"model"`                           // 设备型号全称
	Version          string    `gorm:"column:version" json:"version"`                       // 设备版本全称
	ModelShortName   string    `gorm:"column:model_short_name" json:"model_short_name"`     // 设备型号简写
	VersionShortName string    `gorm:"column:version_short_name" json:"version_short_name"` // 设备版本简写
	Vendor           string    `gorm:"column:vendor" json:"vendor"`                         // 厂商
	MappingFilePath  string    `gorm:"column:mapping_file_path" json:"mapping_file_path"`   // 映射文件路径
	MappingFileName  string    `gorm:"column:mapping_file_name" json:"mapping_file_name"`   // 映射文件名
	OutputFileName   string    `gorm:"column:output_file_name" json:"output_file_name"`     // 输出文件名
	OutputMD5        string    `gorm:"column:output_md5" json:"output_md5"`                 // 输出文件MD5
	XmlFileName      string    `gorm:"column:xml_file_name" json:"xml_file_name"`           // XML文件名
	XmlMD5           string    `gorm:"column:xml_md5" json:"xml_md5"`                       // XML文件MD5
	ReportPath       string    `gorm:"column:report_path" json:"report_path"`               // 转换报告文件路径
	UserID           uint      `gorm:"column:user_id" json:"user_id"`                       // 用户ID
	Language         string    `gorm:"column:language" json:"language"`                     // 语言
	Result           string    `gorm:"column:result" json:"result"`                         // 处理结果或错误信息
	CreatedAt        time.Time `gorm:"column:created_at" json:"created_at"`                 // 创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at" json:"updated_at"`                 // 更新时间
	User             *User     `json:"user"`
}

type ListResponse struct {
	ConfigTrans
}

type Request struct {
	Vendor      string `json:"vendor" form:"vendor"`             // 厂商名称
	Mode        string `json:"mode" form:"mode"`                 // 工作模式
	Model       string `json:"model" form:"model"`               // 设备型号
	Version     string `json:"version" form:"version"`           // 设备版本
	MappingJSON string `json:"mapping_json" form:"mapping_json"` // 接口映射JSON字符串
}

func (this *ConfigTrans) ModelName() string {
	return ModelName
}

func Model() *firewallflextrans.ConfigTrans {
	return &firewallflextrans.ConfigTrans{}
}

func (this *ConfigTrans) Create(object map[string]interface{}) error {
	object["app"] = "config-trans"
	err := easygorm.GetEasyGormDb().Model(Model()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create data err ", err)
		return err
	}
	return nil
}

func (this *ConfigTrans) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", "config-trans").Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update config trans get err ", err)
		return err
	}
	return nil
}

func (this *ConfigTrans) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("app = ?", "config-trans").Delete(Model(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete config trans by id get err ", err)
		return err
	}
	return nil
}

func (this *ConfigTrans) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("app = ?", "config-trans").Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find config trans err ", err)
		return err
	}
	return nil
}

func (this *ConfigTrans) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(Model()).Where("app = ?", "config-trans").Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find config trans err ", err)
		return err
	}
	return nil
}

func All(userID, jobID, start, end, status, mode, vendor, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*ListResponse

	db := easygorm.GetEasyGormDb().Model(Model()).Preload("User").Where("app = ?", "config-trans")

	if len(jobID) > 0 {
		db = db.Where("job_id = ? ", jobID)
	}

	if len(start) > 0 {
		db = db.Where("created_at >= ?", start+" 00:00:00.000")
	}

	if len(end) > 0 {
		db = db.Where("created_at <= ?", end+" 23:59:59.999")
	}

	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}

	if len(mode) > 0 {
		db = db.Where("mode = ?", mode)
	}

	if len(vendor) > 0 {
		db = db.Where("vendor = ?", vendor)
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get list data err ", err)
		return nil, err
	}

	// 处理结果，为每个任务添加状态文本和失败原因
	items := make([]map[string]interface{}, 0, len(res))
	for _, item := range res {
		// 获取状态文本
		var statusText string
		switch item.Status {
		case 0:
			statusText = "处理中" // 默认中文
		case 1:
			statusText = "成功"
		case 2:
			statusText = "失败"
		default:
			statusText = "未知状态"
		}

		itemMap := map[string]interface{}{
			"id":               item.ID,
			"job_id":           item.JobID,
			"vendor":           item.Vendor,
			"model":            item.Model,
			"version":          item.Version,
			"status":           item.Status,
			"status_text":      statusText,
			"input_file_name":  item.InputFileName,
			"output_file_name": item.OutputFileName,
			"created_at":       item.CreatedAt,
			"user":             item.User,
		}

		// 如果任务失败，添加失败原因
		if item.Status == 2 && item.Result != "" {
			itemMap["error_reason"] = item.Result
		}

		items = append(items, itemMap)
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

func (this *ConfigTrans) Dir() string {
	return filepath.Join(libs.Config.ConfigTrans.Upload, this.CreatedAt.Format("20060102"), this.JobID)
}

// All 获取所有配置转换任务列表（结构体方法）
func (this *ConfigTrans) All(userID, jobID, start, end, status, mode, vendor, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	return All(userID, jobID, start, end, status, mode, vendor, sort, orderBy, page, pageSize)
}
