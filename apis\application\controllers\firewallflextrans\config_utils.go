package firewallflextrans

import (
	"fmt"
	"irisAdminApi/application/libs"
	"os/exec"
	"path/filepath"
	"strings"
)

// 验证厂商是否支持
func isVendorSupportedUtil(vendor string) bool {
	// 从配置文件获取支持的厂商列表
	if _, ok := libs.Config.ConfigTrans.Vendors[vendor]; ok {
		return true
	}
	return false
}

// 获取厂商参数，如果没有指定则返回默认厂商
func getVendorParamUtil(ctx interface{}) string {
	// 尝试通过iris.Context获取参数
	if ctx, ok := ctx.(interface{ URLParamDefault(string, string) string }); ok {
		vendor := ctx.URLParamDefault("vendor", "fortigate")
		if isVendorSupportedUtil(vendor) {
			return vendor
		}
		return "fortigate" // 默认厂商
	}

	// 尝试通过form获取参数
	if ctx, ok := ctx.(interface{ FormValue(string) string }); ok {
		vendor := ctx.FormValue("vendor")
		if vendor != "" && isVendorSupportedUtil(vendor) {
			return vendor
		}
		return "fortigate" // 默认厂商
	}

	return "fortigate" // 默认厂商
}

// 获取引擎路径
func getEnginePaths(vendor string) (string, string) {
	// 获取绝对路径，确保可以正确设置工作目录
	enginePath := libs.Config.ConfigTrans.EnginePath

	// 确保enginePath是绝对路径
	if !filepath.IsAbs(enginePath) {
		// 如果不是绝对路径，尝试转换为绝对路径
		absPath, err := filepath.Abs(enginePath)
		if err == nil {
			enginePath = absPath
		}
	}

	// 返回engine目录和main.py的路径
	mainPath := filepath.Join(enginePath, "main.py")

	pythonPath := libs.Config.ConfigTrans.PythonPath
	return mainPath, pythonPath
}

// 获取接口映射文件路径
func getVendorMappingFilePath(vendor string) string {
	// 从配置获取该厂商的映射文件名
	vendorConfig := libs.Config.ConfigTrans.Vendors[vendor]
	mappingFileName := vendorConfig.MappingFile

	// 构建完整路径
	return filepath.Join(libs.Config.ConfigTrans.MappingBaseDir, vendor, mappingFileName)
}

// 获取命令行模板
func getVendorCommandTemplate(vendor, mode string) string {
	// 从配置中获取特定厂商和模式的命令行参数模板
	vendorConfig := libs.Config.ConfigTrans.Vendors[vendor]

	var cmdTemplate string
	switch mode {
	case "verify":
		cmdTemplate = vendorConfig.Modes.Verify
	case "extract":
		cmdTemplate = vendorConfig.Modes.Extract
	case "convert":
		cmdTemplate = vendorConfig.Modes.Convert
	default:
		cmdTemplate = ""
	}

	// 确保命令模板包含日志参数
	if mode == "convert" && !strings.Contains(cmdTemplate, "--log-file") {
		cmdTemplate += " --log-file {log-file} --log-level {log-level}"
	}

	// 添加用户日志参数
	if mode == "convert" && !strings.Contains(cmdTemplate, "--user-log") {
		cmdTemplate += " --user-log {user-log}"
	}

	// 添加完整日志参数
	if mode == "convert" && !strings.Contains(cmdTemplate, "--full-log") {
		cmdTemplate += " --full-log {full-log}"
	}

	return cmdTemplate
}

// 执行Python命令
func executePythonCommand(enginePath, pythonPath, cmdTemplate string, params map[string]string) ([]byte, error) {
	// 构建命令行参数，使用模板替换
	cmdArgs := cmdTemplate
	for key, value := range params {
		placeholder := fmt.Sprintf("{%s}", key)
		cmdArgs = strings.Replace(cmdArgs, placeholder, value, -1)
	}

	// 分割命令行参数
	args := strings.Fields(cmdArgs)
	allArgs := append([]string{enginePath}, args...)

	// 创建命令
	cmd := exec.Command(pythonPath, allArgs...)

	// 执行命令并返回输出
	return cmd.CombinedOutput()
}
