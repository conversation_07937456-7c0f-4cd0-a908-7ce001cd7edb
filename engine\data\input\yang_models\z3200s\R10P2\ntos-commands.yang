module ntos-commands {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:commands";
  prefix ntos-commands;

  import ntos-extensions {
    prefix ext;
  }
  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS background commands management.";

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  grouping long-cmd-output {
    description
      "Long command output.";

    leaf buffer {
      type string;
      description
        "The command output buffer since last request.";
      ext:nc-cli-stdout;
      ext:nc-cli-hidden;
    }

    leaf exit-code {
      type int8;
      description
        "The exit code of the command. If defined, it means that the command
         is stopped and that its uid cannot be used anymore.";
      ext:nc-cli-command-stopped;
      ext:nc-cli-hidden;
    }
  }

  grouping long-cmd-status {
    description
      "Long command status.";

    leaf status-rpc {
      type string;
      description
        "XML data to be sent as NETCONF RPC body to get further status
         for this background command.";
      ext:nc-cli-hidden;
      ext:status-rpc;
    }

    leaf refresh-rpc {
      type string;
      description
        "XML data to be sent as NETCONF RPC body to keep a background
         command running.";
      ext:nc-cli-hidden;
      ext:refresh-rpc;
    }

    leaf stop-rpc {
      type string;
      description
        "XML data to be sent as NETCONF RPC body to stop this background
         command.";
      ext:nc-cli-hidden;
      ext:stop-rpc;
    }
  }

  rpc get-command-status {
    description
      "Get further status of a command running in the background.";
    input {

      leaf uid {
        type uint64;
        mandatory true;
        description
          "The identifier of the command.";
      }
    }
    output {
      uses long-cmd-output;
    }
    ntos-api:internal;
  }

  rpc refresh-command {
    description
      "Refresh a command ttl running in the background.";
    input {

      leaf uid {
        type uint64;
        mandatory true;
        description
          "The identifier of the command.";
      }
    }
    output {
      uses long-cmd-output;
    }
    ntos-api:internal;
  }

  rpc stop-command {
    description
      "Stop a background command.";
    input {

      leaf uid {
        type uint64;
        mandatory true;
        description
          "The identifier of the command to stop.";
      }
    }
    output {
      uses long-cmd-output;
    }
    ntos-api:internal;
  }

  rpc get-completion {
    description
      "Get completion.";
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
      }
    }
    ntos-api:internal;
  }

  rpc get-features {
    description
      "Get features.";
    output {
      list feature {
        key "name";
        description
          "The list of features on the device.";

        leaf name {
          type string;
          description "The name of the feature.";
        }

        leaf enabled {
          type boolean;
          description "The status of the feature.";
        }
      }
    }
    ext:nc-cli-cmd "get-features";
  }

  rpc show-summary {
    description
      "Show a summary of the system state.";
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ext:nc-cli-stdout;
        ext:nc-cli-hidden;
      }
    }
    ext:nc-cli-show "summary";
    ntos-api:internal;
  }
}
