module ntos-webauth {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:webauth";
  prefix ntos-webauth;
  
  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-inet-types {
    prefix ntos-inet-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }
  
  import ntos-api {
    prefix ntos-api;
  }

  import ntos-if-types {
    prefix ntos-if;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS Web Authentication module.";

  revision 2022-12-02 {
    description
      "Initial version.";
    reference "";
  }

  identity webauth {
    base ntos-types:SERVICE_LOG_ID;
    description
      "webauth service.";
  }

  typedef url-param-name {
    type string {
      length "1..16";
      pattern '[a-zA-Z0-9_.~-]+' {
        error-message "Url parameter's object name should be : `[a-zA-Z0-9_.~-]+`";
      }
    }
    description
      "Url parameter's object name type.";
  }

  typedef policy-name {
    description
      "The type of policy name.";
    type ntos-types:ntos-obj-name-type;
  }

  typedef portal-template-name {
    description
      "The type of portal template name.";
    type ntos-types:ntos-obj-name-type;
  }

  typedef action-policy-flag {
    description
      "The type of Authentication policy action.";
    type enumeration {
      enum none {
        value 0;
      }
      enum auth {
        value 1;
      }
      enum exempt-auth {
        value 2;
      }
      enum sms-auth {
        value 3;
      }
      enum anonymous-auth {
        value 4;
      }
    }
  }

  grouping url-custom-param-config {
    description
      "The parameter's config for custom in the URL.";
    leaf enabled {
      description
        "Enable parameter's config.";
      type boolean;
    }
    leaf name {
      description
        "The parameter's key";
      type ntos-types:ntos-obj-name-type;
    }
    leaf param {
      description
        "The parameter's name";
      type url-param-name;
    }
    leaf value {
      description
        "The parameter's value";
      type string;
    }
    leaf encrypt {
      description
        "Configue the encrypt type for parameter.";
      type enumeration {
        enum none {
          description
            "Configue none encrypt type for parameter.";
          value 0;
        }
        enum aes {
          description
            "Configue aes encrypt type for parameter.";
          value 1;
        }
        enum des {
          description
            "Configue des encrypt type for parameter.";
          value 2;
        }
        enum md5 {
          description
            "Configue md5 encrypt type for parameter.";
          value 3;
        }
      }
    }
  }

  grouping url-param-config {
    description
      "The parameter's config in the URL.";
    leaf enabled {
      description
        "Enable parameter's config.";
      type boolean;
    }
    leaf name {
      description
        "The parameter's name";
      type url-param-name;
    }
    leaf encrypt {
      description
        "Configue the encrypt type for parameter.";
      type enumeration {
        enum none {
          description
            "Configue none encrypt type for parameter.";
          value 0;
        }
        enum aes {
          description
            "Configue aes encrypt type for parameter.";
          value 1;
        }
        enum des {
          description
            "Configue des encrypt type for parameter.";
          value 2;
        }
        enum md5 {
          description
            "Configue md5 encrypt type for parameter.";
          value 3;
        }
      }
    }
  }

  grouping address-set-config {
    leaf name {
      type ntos-inet-types:ip-address-type;
      description
        "IPv4/IPv6 address.";
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "Indicate the description of ip.";
    }
  }

  grouping address-set {
    list source-ip-set {
      key "name";
      description
        "The list of IP source address.";
      uses address-set-config;
    }

    list dest-ip-set {
      key "name";
      description
        "The list of IP dest address.";
      uses address-set-config;
    }
  }

  grouping mac-set-config {
    leaf name {
      type ntos-if:mac-address;
      description
        "MAC address of the host.";
      ntos-ext:nc-cli-no-name;
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "Indicate the description of Mac address.";
    }
  }

  grouping mac-set {
    list source-mac-set {
      key 'name';
      description
        "The list of source mac address.";
      uses mac-set-config;
    }
  }

  grouping domain-name-set {
    list domain-name {
      key "name";
      description
        "The list of domain name.";
      leaf name {
        type ntos-inet-types:wildcard-domain-name;
        description
          "The domain-name of destination device, for example www.ruijie.com.cn";
      }
      leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "Indicate the description of domain name.";
      }
    }
  }

  grouping portal-config {
    description
      "Configuration of the Portal service.";

    leaf version {
      description
        "Version of the protocol for Portal authentication service.";
      type enumeration {
        enum "v1";
        enum "v2";
        enum "v3";
      }
    }

    leaf portal-server-ip {
      description
        "The IP address of the portal server.";
      type ntos-inet-types:ip-address;
    }

    leaf listening-port {
      description
        "The port on the device for local listening.";
      type ntos-inet-types:port-number;
    }

    leaf destination-port {
      description
        "Port for the Portal service.";
        type ntos-inet-types:port-number;
    }

    leaf authentication-url {
      description
        "Configure the authentication url of the wifidog server which must start with \"http://\" or \"https://\".";
      type ntos-types:http-dual-stack-url;
    }

    choice nas-type {
      description
        "Configure the nas type of the portal template.";

      case ip-address {
        description
          "Choice this to set the nas is based on ip address.";
        leaf nas-ip-address {
          description
            "Configure the nas ip address.";
          type ntos-inet-types:ip-address;
        }
      }
      case interface {
        description
          "Choice this to set the nas is based on interface";
        leaf nas-interface {
          description
            "Configure the nas interface namse.";
          type ntos-types:ifname;
        }
      }
    }

    leaf shared-key {
      description
        "Shared key for the Portal service.";
      type string;
    }

    container url-template {
      leaf iv-parameter-name {
        description
          "Name of the IV field.";
        type string;
      }

      leaf key {
        description
          "Encrypt key";
        type string;
      }
    }

    container server-detect {
      description
        "Method of service detection.";

      leaf detect-enabled {
        description
          "Server detect enable";
        type boolean;
      }

      leaf escape-enabled {
        description
          "User authentication escape enable";
        type boolean;
        default "false";
      }

      leaf type {
        description
          "Detect type";
        type enumeration {
          enum "icmp";
          enum "portal";
        }
        default "icmp";
      }

      leaf interval {
        description
          "Interval of service detection.";
        units "second";
        type uint16 {
          range "1..65535";
        }
      }

      leaf max-times {
        description
          "The server monitors the timeout duration.";
        type uint8 {
          range "1..255";
        }
      }

      leaf action {
        description
          "Service detection action.";
        type enumeration {
          enum "log";
          enum "trap";
        }
      }
    }
    container temp-permit-mode {
      description
        "Configure the temporary permit mode.";
      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable temporary permit.";
      }
      list application {
        key "name";
        ntos-ext:nc-cli-one-liner;
        description
          "The list of temporary permit applications.";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
          description
            "The name of application.";
        }
      }
    }
  }

  grouping portal-server-for-template {
    description
      "Configure the portal server.";
    leaf url {
      description
        "Configure the url of the portal server which must start with \"http://\" or \"https://\".";
      type ntos-types:http-dual-stack-url;
    }

    leaf set-ssid {
      description
        "Name of the default ssid.";
      type string;
    }

    choice nas-type {
      description
        "Configure the nas type of the portal template.";

      case ip-address {
        description
          "Choice this to set the nas is based on ip address.";
        leaf nas-ip-address {
          description
            "Configure the nas ip address.";
          type ntos-inet-types:ip-address;
        }
      }
      case interface {
        description
          "Choice this to set the nas is based on interface";
        leaf nas-interface {
          description
            "Configure the nas interface names.";
          type ntos-types:ifname;
        }
      }
    }

    leaf redirect-type {
      description
        "Configure url redirect type";
      type enumeration {
        enum "302";
        enum "200";
      }
    }

    container url-parameter {
      description
        "URL parameter configuration";

      container parameter-config {
        description
          "The parameter config in the URL";

        container user-ip {
          description
            "Configuration of the user IP field in the URL.";
          uses url-param-config;
        }

        container user-mac {
          description
            "Configuration of the user MAC field in the URL.";
          uses url-param-config;
        }

        container nas-ip {
          description
            "Configuration of the user Nas-ip field in the URL.";
          uses url-param-config;
        }

        container redirect-url {
          description
            "Configuration of the user Redirect-Url field in the URL.";
          uses url-param-config;
        }

        container hostname {
          description
            "Configuration of the user Host-Name field in the URL.";
          uses url-param-config;
        }

        list custom-param {
          description
            "Configuration of the user custom field in the URL.";
          key "name";
          uses url-custom-param-config;
        }
      }

      container mac-address-format {
        description
          "Configuration of the MAC format in the URL.";

        leaf delimiter {
          description
            "Delimiters in the MAC address.";
          type string {
            length "1";
          }
          default "-";
        }

        leaf format {
          description
            "Configuring MAC address format.";
          type enumeration {
            enum "none";
            enum "compact";
            enum "normal";
          }
          default "normal";
        }
      }
    }
  }

  grouping ad-server-config {
    description
      "Configure the Active Directory server.";
    leaf name {
      description
        "The Active Directory Controller's key";
      type ntos-types:ntos-obj-name-type;
    }
    leaf ip {
      type ntos-inet-types:ip-address-type;
      description
        "IPv4/IPv6 address.";
    }
    leaf listening-port {
      description
        "The listening port of Active Directory Controller";
      type ntos-inet-types:port-number;
    }
    leaf shared-key {
      description
        "Shared key for the Active Directory Controller.";
      type string;
    }
  }

  grouping ad-single-sign-on-conifg {
    leaf enabled {
      description
        "Enable Single Sign-On for Active Directory config.";
      type boolean;
    }

    leaf method {
      description
        "AD domain Single Sign-On method.";
      type enumeration {
        enum "plugin";
        enum "no-plugin";
      }
      default "plugin";
    }

    container plugin {
      leaf ip {
        type ntos-inet-types:ip-address-type;
        description
          "IPv4/IPv6 address.";
      }
      leaf listening-port {
        description
          "The listening port of Active Directory Controller";
        type ntos-inet-types:port-number;
      }
      leaf shared-key {
        description
          "Shared key for the Active Directory Controller.";
        type string;
      }
    }

    list server-config {
      key "name";
      description
        "Config list for Active Directory Controllers.";
      uses ad-server-config;
    }
  }
  
  grouping rrnsp-server-config {
    leaf listening-port {
      description
        "The listening port of rrnsp";
      type ntos-inet-types:port-number;
    }
    leaf shared-key {
      description
        "Shared key for the rrnsp.";
      type string;
    }
  }
  
  grouping srun-server-config {
    leaf srun-ip {
      type ntos-inet-types:ip-address-type;
      description
        "Srun IPv4 address.";
    }
    leaf listening-port {
      description
        "The listening port of srun";
      type ntos-inet-types:port-number;
    }
  }
  
  grouping rrnsp-client-config {
    leaf server-ip {
      type ntos-inet-types:ip-address-type;
      description
        "Rrnsp server IPv4 address.";
    }
    leaf server-port {
      description
        "The listening port of rrnsp-server";
      type ntos-inet-types:port-number;
    }
    leaf shared-key {
      description
        "Shared key for the rrnsp.";
      type string;
    }
    choice nas-type {
      description
        "Configure the nas type of the rrnsp client.";

      case ip-address {
        description
          "Choice this to set the nas is based on ip address.";
        leaf nas-ip-address {
          description
            "Configure the nas ip address.";
          type ntos-inet-types:ip-address;
        }
      }
      case interface {
        description
          "Choice this to set the nas is based on interface";
        leaf nas-interface {
          description
            "Configure the nas interface names.";
          type ntos-types:ifname;
        }
      }
    }
  }

  rpc webauth-policy-stat-clear {
    description
      "Clear auth statistics of auth policy.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
      container content {
        leaf policy-id-list {
          type string;
          description
            "Clear auth statistics of auth policy by policy id list.";
        }
      }
    }
    ntos-ext:nc-cli-cmd "webauth auth-policy stat clear";
    ntos-api:internal;
  }

  rpc webauth-policy {
    description
      "Show state of auth policy.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";

        leaf filter {
          type string;
          description
            "Filter.";
        }

        leaf name {
          type string;
          description
            "Show auth policy by name";
        }

        leaf start {
          type uint32;
          description
            "Start offset of result.";
        }

        leaf end {
          type uint32;
          description
            "End offset of result.";
        }

        leaf policy-id {
          type string;
          description
            "Filter by policy id";
        }

        leaf permit-all-policy {
          type empty;
          description
            "Show all permit policy";
        }
      }
    }

    output {
      leaf rule-total {
        description
          "Total number of auth policy.";
        type uint32;
      }

      list rule {
        key "name";
        description
          "The detail of auth policy.";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The name of auth policy.";
        }

        leaf policy-id {
          type string;
          description
            "Filter by policy id";
        }

        leaf position-id {
          type uint32;

          description
            "The position id of auth policy.";
        }

        leaf enabled {
          type boolean;
          description
            "Enable or disable auth policy.";
        }

        leaf description {
          type string;
          description
            "The description of auth policy.";
        }

        list source-zone {
          description
            "The name of source zone.";
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        list source-network {
          description
            "The name of source network.";
          key "name";
          leaf name {
            type ntos-types:ntos-obj-name-type;
          }
        }

        leaf action {
          type action-policy-flag;
          description
            "The action of controlling session.";
        }

        leaf portal-template {
          type portal-template-name;
          description
            "Portal auth need portal template.";
        }

        leaf template-enabled {
          type boolean;
          description
            "Enable or disable Portal template.";
        }

        leaf match-count {
          type uint64;
          description
            "The matching times for the policy.";
        }

        leaf create-time {
          description
            "Policy creation time.";
          type string;
        }

        leaf first-match-time {
          description
            "The first auth time of policy";
          type string;
        }

        leaf last-match-time {
          description
            "The last auth time of policy.";
          type string;
        }
      }
    }

    ntos-ext:nc-cli-show "webauth auth-policy";
    ntos-api:internal;
  }

  rpc webauth-ssid-policy {
    description
      "Show state of ssid policy.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";

        leaf all-ssid {
          type empty;
          description
            "Show all ssid Configure";
        }

      }
    }

    output {
      leaf ssid-total {
        description
          "Total number of ssid policy.";
        type uint32;
      }

      list ssid {
        key "ip-set";
        description
          "The detail of ssid policy.";
        leaf ip-set {
          type ntos-inet-types:ip-address-type;
          description
            "IPv4/IPv6 address.";
        }

        leaf description {
          description
            "The description of the ssid";
          type string;
        }

        leaf set-ssid {
          description
            "Name of the ssid.";
          type string;
        }
      }
    }

    ntos-ext:nc-cli-show "webauth ssid-policy";
    ntos-api:internal;
  }

  rpc webauth-portal-group-list {
    description
      "Show base config of authentication portal group.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        leaf all-group {
          type empty;
          description
            "Show all group name and protocol";
        }
      }
    }
    output {
      list portal-group {
        leaf name {
          type string;
          description
            "The name of portal group.";
        }
        leaf enabled {
          description
            "Enable group's config.";
          type boolean;
        }
        leaf protocol {
          description
            "The protocol of portal group.";
          type enumeration {
            enum "portal";
            enum "wifidog";
          }
        }
      }
    }
    ntos-ext:nc-cli-show "webauth portal-group";
    ntos-api:internal;
  }

  rpc webauth-portal {
    description
      "Show detailed information of authentication portal.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        description
          "Configuring portal group.";
        choice info-type {
          case group-info {
            leaf group-name {
              type string;
              description
                "Show portal group config by name";
            }
          }
          case template-info {
            leaf portal-template {
              type enumeration {
                enum "all";
                enum "name";
              }
              description
                "Show portal template config by template name.";
            }
            leaf search-basis {
              type string;
              description
                "Enter the search criteria based on the type of portal-template.";
            }
          }
        }
      }
    }
    output {
      leaf enabled {
        description
          "Enable group's config.";
        type boolean;
      }
      leaf protocol {
        description
          "The protocol of portal group.";
        type enumeration {
          enum "portal";
          enum "wifidog";
        }
      }
      leaf server-total {
        description
          "Total number of portal server.";
        type uint32;
      }
      leaf template-total {
        description
          "Total number of portal template.";
        type uint32;
      }
      list portal-server {
        description
          "Configuring portal service.";
        
        leaf name {
          description
            "Name of the portal service.";
          type string;
        }

        leaf enabled {
          description
            "Enable portal server's config.";
          type boolean;
          default true;
        }

        leaf no-perception-enabled {
          description
            "Enable no-perception config.";
          type boolean;
        }
        uses portal-config;
      }
      list portal-template {
        description
          "Configure the portal authentication template.";

        leaf name {
          description
            "Configure the name of the portal-template.";
          type portal-template-name;
        }

        leaf enabled {
          type boolean;
          description
            "Enable or disable Portal template.";
        }

        container rule-referenced {
          description
            "This configuration indicates the rules where this template is referenced.";
          leaf rule-total {
            description
              "Total number of the rules where this template is referenced.";
            type uint32;
          }
          leaf-list rule {
            description
              "The name of the rule where this template is referenced.";
            type string;
          }
        }

        container portal-server {
          description
            "Configure the portal server.";
          uses portal-server-for-template;
        }
      }
    }
    ntos-ext:nc-cli-show "webauth portal";
    ntos-api:internal;
  }

  rpc webauth-whitelist {
    description
      "Show state of auth policy.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        leaf filter {
          type string;
          description
            "Filter.";
        }

        leaf type {
          description
            "The type of white list.";
          type enumeration {
            enum source-ip;
            enum dest-ip;
            enum domain-name;
            enum source-mac;
            enum all;
          }
        }

        leaf name {
          description
            "Name of the portal service.";
          type string;
        }

        leaf start {
          type uint32;
          description
            "Start offset of result.";
        }

        leaf end {
          type uint32;
          description
            "End offset of result.";
        }
      }
    }

    output {
      leaf case-total {
        description
          "Total number of whitelist for each case type.";
        type uint32;
      }
      list source-ip-set {
        description
          "The list of IP source address.";
        uses address-set-config;
      }

      list dest-ip-set {
        description
          "The list of IP dest address.";
        uses address-set-config;
      }
      list domain-name {
        description
          "The list of domain name.";
        leaf name {
          type ntos-inet-types:wildcard-domain-name;
          description
            "The domain-name of destination device, for example www.ruijie.com.cn";
        }
        leaf description {
          type ntos-types:ntos-obj-description-type;
          description
            "Indicate the description of domain name.";
        }
      }
      list source-mac-set {
        description
          "The list of source mac address.";
        uses mac-set-config;
      }
    }

    ntos-ext:nc-cli-show "webauth whitelist";
    ntos-api:internal;
  }

  rpc webauth-link-sam {
    description
      "Show state of link-sam.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
    }

    output {
      leaf enabled {
        description
          "Enable link-sam config.";
        type boolean;
      }
      leaf listening-port {
        description
          "The listening port of link-sam";
        type ntos-inet-types:port-number;
      }
      leaf already-link {
        description
          "The name of already link sam.";
        type string;
      }
    }

    ntos-ext:nc-cli-show "webauth link-sam";
    ntos-api:internal;
  }
  
  
  rpc webauth-realname-sync {
    description
      "Show state of realname sync.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
    }

    output {
      leaf enabled {
        description
          "Enable realname sync config.";
        type boolean;
      }
      leaf realname-type {
        description
          "The type of realname sync";
        type string;
      }
      leaf protocol-type {
        description
          "The protocol type of receive realname packet";
        type string;
      }
      container linksam-server {
        leaf listening-port {
          description
            "The listening port of link-sam";
          type ntos-inet-types:port-number;
        }
        leaf already-link {
          description
            "The name of already link sam.";
          type string;
        }
      }
      container rrnsp-server {
        uses rrnsp-server-config;
        leaf last-conn-ip {
          description
            "IP address of the last received message.";
          type string;
        }
        leaf last-conn-time {
          description
            "Time of the last received message.";
          type string;
        }
      }
      container srun-server {
        uses srun-server-config;
        leaf last-conn-ip {
          description
            "IP address of the last received message.";
          type string;
        }
        leaf last-conn-time {
          description
            "Time of the last received message.";
          type string;
        }
      }
      container rrnsp-client {
        uses rrnsp-client-config;
      }
    }

    ntos-ext:nc-cli-show "webauth realname-sync";
    ntos-api:internal;
  }

  rpc webauth-sso-ad {
    description
      "Show state of Single Sign-On for Active Directory.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
    }

    output {
      container ad {
        description
          "Configure Single Sign-On for Active Directory.";
        uses ad-single-sign-on-conifg;
      }
    }

    ntos-ext:nc-cli-show "webauth sso ad";
    ntos-api:internal;
  }

  rpc webauth-sso-ad-package {
    description
      "Package Config of Single Sign-On for Active Directory.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
    }

    output {
      leaf status {
        description
          "The status for package.";
        type boolean;
      }
    }

    ntos-ext:nc-cli-cmd "webauth sso ad package";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    container webauth {
      container auth-policy {
        list rule {
          key "name";
          ordered-by user;
          leaf name {
            description
              "The name of the authentication policy rule.";
            type policy-name;
          }

          leaf description {
            description
              "The description of the rule";
            type string;
          }

          list source-zone {
            description
              "The name of source zone.";
            ntos-ext:nc-cli-one-liner;
            key "name";
            leaf name {
              type ntos-types:ntos-obj-name-type;
              ntos-ext:nc-cli-no-name;
            }
          }

          list source-network {
            description
              "The name of source network.";
            ntos-ext:nc-cli-one-liner;
            key "name";
            leaf name {
              type ntos-types:ntos-obj-name-type;
              ntos-ext:nc-cli-no-name;
            }
          }

          leaf enabled {
            description
              "Enable";
            type boolean;
          }

          leaf action {
            description
              "Auth-policy action";
            type action-policy-flag;
          }

          leaf portal-template {
            description
              "Name of the portal authentication template.";
            type portal-template-name;
          }
        }

      }

      container ssid-policy {
        list ssid {
          key "ip-set";
          ordered-by user;
          leaf ip-set {
            type ntos-inet-types:ip-address-type;
            description
              "IPv4/IPv6 address.";
          }

          leaf description {
            description
              "The description of the ssid";
            type string;
          }

          leaf set-ssid {
            description
              "Name of the ssid.";
            type string;
          }
        }
      }

      container white-list {
        uses address-set;
        uses domain-name-set;
        uses mac-set;
      }

      container authentication-options {
        container portal-authentication {
          description
            "Configure the portal authentication";

          list portal-group {
            key 'name';
            leaf name {
              description
                "Name of the portal group.";
              type string; 
            }
            leaf enabled {
              description
                "Enable group's config.";
              type boolean;
            }
            leaf protocol {
              mandatory true;
              description
                "The protocol of portal group.";
              type enumeration {
                enum "portal";
                enum "wifidog";
              }
            }
          }

          list portal-server {
            description
              "Configuring portal service.";
            key "name";
            
            leaf name {
              description
                "Name of the portal service.";
              type string;
            }

            leaf group-name {
              mandatory true;
              description
                "Configure the group name of the portal server template.";
              type string;
            }

            leaf enabled {
              description
                "Enable portal server's config.";
              type boolean;
              default true;
            }

            leaf no-perception-enabled {
              description
                "Enable no-perception config.";
              type boolean;
            }
            uses portal-config;
          }

          list portal-template {
            description
              "Configure the portal authentication template.";
            key "name";

            leaf name {
              description
                "Configure the name of the portal-template.";
              type portal-template-name;
            }

            leaf group-name {
              mandatory true;
              description
                "Configure the group name of the portal template";
              type string;
            }

            container portal-server {
              description
                "Configure the portal server.";
              uses portal-server-for-template;
            }
          }
        }
      }

      container link-sam {
        leaf enabled {
          description
            "Enable link-sam config.";
          type boolean;
        }
        leaf listening-port {
          description
            "The listening port of link-sam";
          type ntos-inet-types:port-number;
        }
      }
      
      container rrnsp-server {
        leaf enabled {
          description
            "Enable rrnsp-server config.";
          type boolean;
        }
        uses rrnsp-server-config;
      }
      
      container srun-server {
        leaf enabled {
          description
            "Enable srun config.";
          type boolean;
        }
        uses srun-server-config;
      }
      
      container rrnsp-client {
        leaf enabled {
          description
            "Enable rrnsp-client config.";
          type boolean;
        }
        uses rrnsp-client-config;
      }

      container single-sign-on {
        description
          "Configure Single Sign-On config.";
        container ad {
          description
            "Configure Single Sign-On for Active Directory.";
          uses ad-single-sign-on-conifg;
        }
      }
    }
  }
}
