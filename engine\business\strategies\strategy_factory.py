# -*- coding: utf-8 -*-
"""
策略工厂 - 创建和管理厂商特定的转换策略
"""

from typing import Dict, Optional, Type, List, Any
from engine.utils.logger import log
from engine.utils.i18n import _
from .conversion_strategy import ConversionStrategy
from .fortigate_strategy import FortigateConversionStrategy


class StrategyFactory:
    """
    策略工厂
    负责创建和管理厂商特定的转换策略
    """
    
    def __init__(self, config_manager, template_manager, yang_manager):
        """
        初始化策略工厂
        
        Args:
            config_manager: 配置管理器
            template_manager: 模板管理器
            yang_manager: YANG管理器
        """
        self.config_manager = config_manager
        self.template_manager = template_manager
        self.yang_manager = yang_manager
        
        # 注册策略类
        self._strategies: Dict[str, Type[ConversionStrategy]] = {
            "fortigate": FortigateConversionStrategy
        }
        
        # 策略实例缓存
        self._strategy_cache: Dict[str, ConversionStrategy] = {}
        
        log(_("strategy_factory.initialized"), "info", 
            strategies=list(self._strategies.keys()))
    
    def create_strategy(self, vendor: str) -> Optional[ConversionStrategy]:
        """
        创建厂商特定的转换策略
        
        Args:
            vendor: 厂商名称
            
        Returns:
            Optional[ConversionStrategy]: 转换策略实例
        """
        if vendor not in self._strategies:
            log(_("strategy_factory.unsupported_vendor"), "error", vendor=vendor)
            return None
        
        # 检查缓存
        if vendor in self._strategy_cache:
            log(_("strategy_factory.strategy_cache_hit"), "debug", vendor=vendor)
            return self._strategy_cache[vendor]
        
        try:
            # 创建策略实例
            strategy_class = self._strategies[vendor]
            strategy = strategy_class(
                self.config_manager,
                self.template_manager,
                self.yang_manager
            )
            
            # 缓存策略实例
            self._strategy_cache[vendor] = strategy
            
            log(_("strategy_factory.strategy_created"), "info", vendor=vendor)
            return strategy
            
        except Exception as e:
            error_msg = _("strategy_factory.strategy_creation_failed", 
                         vendor=vendor, error=str(e))
            log(error_msg, "error")
            return None
    
    def register_strategy(self, vendor: str, strategy_class: Type[ConversionStrategy]):
        """
        注册新的转换策略
        
        Args:
            vendor: 厂商名称
            strategy_class: 策略类
        """
        if not issubclass(strategy_class, ConversionStrategy):
            raise ValueError(_("strategy_factory.invalid_strategy_class", 
                             class_name=strategy_class.__name__))
        
        self._strategies[vendor] = strategy_class
        
        # 清除缓存中的旧实例
        if vendor in self._strategy_cache:
            del self._strategy_cache[vendor]
        
        log(_("strategy_factory.strategy_registered"), "info", 
            vendor=vendor, class_name=strategy_class.__name__)
    
    def get_supported_vendors(self) -> List[str]:
        """
        获取支持的厂商列表
        
        Returns: List[str]: 支持的厂商列表
        """
        return list(self._strategies.keys())
    
    def is_vendor_supported(self, vendor: str) -> bool:
        """
        检查厂商是否支持
        
        Args:
            vendor: 厂商名称
            
        Returns:
            bool: 是否支持
        """
        return vendor in self._strategies
    
    def clear_cache(self):
        """清空策略缓存"""
        self._strategy_cache.clear()
        log(_("strategy_factory.cache_cleared"), "info")
    
    def get_factory_info(self) -> Dict[str, Any]:
        """
        获取工厂信息
        
        Returns: Dict[str, any]: 工厂信息
        """
        return {
            "supported_vendors": self.get_supported_vendors(),
            "cached_strategies": list(self._strategy_cache.keys()),
            "total_strategies": len(self._strategies)
        }
