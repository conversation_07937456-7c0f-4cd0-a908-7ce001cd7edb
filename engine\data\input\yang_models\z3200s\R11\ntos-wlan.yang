module ntos-wlan {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:wlan";
  prefix ntos-wlan;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-wlan-apmg {
    prefix ntos-wlan-apmg;
  }
  import ntos-wlan-stamg {
    prefix ntos-wlan-stamg;
  }
  import ntos-wlan-capwap {
    prefix ntos-wlan-capwap;
  }
  import ntos-wlan-wbs {
    prefix ntos-wlan-wbs;
  }
  import ntos-wlan-wids {
    prefix ntos-wlan-wids;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS WLAN module.";

  revision 2024-07-23 {
    description
      "Initial version.";
    reference "";
  }

  identity wlan {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Wlan service.";
  }

  typedef time-point {
    type string {
      pattern '([01][0-9]|2[0-3]):[0-5][0-9]' {
        error-message "Incorrect time format, expecting: hh:mm.";
      }
      length 5;
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Wlan configuration.";

    container wlan {
      description
        "Wlan configuration.";

      container web-ac {
        leaf topology {
          type enumeration {
            enum switch-connect;
            enum ac-connect;
          }
          default "ac-connect";
        }
      }

      container schedule {
        list session {
          key "session-id";

          leaf session-id {
            type uint8 {
              range "1..64";
            }
          }

          list time-range {
            key "time-id";

            leaf time-id {
              type uint8 {
                range "1..8";
              }
            }

            container period {
              leaf start {
                type enumeration {
                  enum Mon;
                  enum Tue;
                  enum Wed;
                  enum Thu;
                  enum Fri;
                  enum Sat;
                  enum Sun;
                  enum everyday;
                }
                ntos-ext:nc-cli-no-name;
              }

              leaf to {
                type enumeration {
                  enum Mon;
                  enum Tue;
                  enum Wed;
                  enum Thu;
                  enum Fri;
                  enum Sat;
                  enum Sun;
                }
                must "../start";
              }
            }

            container time {
              leaf start {
                type union {
                  type time-point;
                  type enumeration {
                    enum all-day;
                  }
                }
                ntos-ext:nc-cli-no-name;
              }

              leaf to {
                type time-point;
                must "../start";
              }
            }
          }
        }
      }

      container diag {
        leaf enabled {
          type boolean;
          default "false";
        }
      }

      container ac-controller {
        description
          "Ac Controller Mode.";

        uses ntos-wlan-apmg:apmg-ac-config;
        uses ntos-wlan-stamg:stamg-ac-config;
        uses ntos-wlan-capwap:capwap-ac-config;
        uses ntos-wlan-wbs:wbs-ac-config;
      }

      container black-white-list {
        list ssid {
          key "name";

          leaf name {
            description
              "Modify the wlan's ssid.";
            type ntos-types:ntos-obj-name-type;
          }
          uses ntos-wlan-stamg:stamg-bw-config;
        }
      }

      list ap-config {
        key "ap-name";
        description
          " Ap Config Mode.";

        leaf ap-name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
        }

        uses ntos-wlan-apmg:apmg-ap-config;
        uses ntos-wlan-stamg:stamg-ap-config;
        uses ntos-wlan-capwap:capwap-ap-config;
        uses ntos-wlan-wbs:wbs-ap-config;
        uses ntos-wlan-wids:wids-ap-config;
      }

      list wlan-config {
        key "wlan-id";
        description
          "Wlan Mode.";

        leaf wlan-id {
          type uint16 {
            range "1..4094";
          }
          must "../ssid";
          ntos-ext:nc-cli-no-name;
        }

        container wlansec {
          uses ntos-wlan-wbs:wbs-sec-config;
        }

        uses ntos-wlan-apmg:apmg-wlan-config;
        uses ntos-wlan-stamg:stamg-wlan-config;
        uses ntos-wlan-wbs:wbs-wlan-config;
      }

      list ap-group {
        key "ap-group-name";
        description
          "Ap Group Mode.";

        leaf ap-group-name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
        }

        uses ntos-wlan-apmg:apmg-apg-config;
        uses ntos-wlan-stamg:stamg-apg-config;
        uses ntos-wlan-capwap:capwap-apg-config;
      }

      uses ntos-wlan-wids:wids-configure;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Wlan state.";

    container wlan {
      description
        "Wlan configuration.";

      container web-ac {
        leaf topology {
          type enumeration {
            enum switch-connect;
            enum ac-connect;
          }
          default "ac-connect";
        }
      }

      container schedule {
        list session {
          key "session-id";

          leaf session-id {
            type uint8 {
              range "1..64";
            }
          }

          list time-range {
            key "time-id";

            leaf time-id {
              type uint8 {
                range "1..8";
              }
            }

            container period {
              leaf start {
                type enumeration {
                  enum Mon;
                  enum Tue;
                  enum Wed;
                  enum Thu;
                  enum Fri;
                  enum Sat;
                  enum Sun;
                  enum everyday;
                }
                ntos-ext:nc-cli-no-name;
              }

              leaf to {
                type enumeration {
                  enum Mon;
                  enum Tue;
                  enum Wed;
                  enum Thu;
                  enum Fri;
                  enum Sat;
                  enum Sun;
                }
                must "../start";
              }
            }

            container time {
              leaf start {
                type union {
                  type time-point;
                  type enumeration {
                    enum all-day;
                  }
                }
                ntos-ext:nc-cli-no-name;
              }

              leaf to {
                type time-point;
                must "../start";
              }
            }
          }
        }
      }

      container diag {
        leaf enabled {
          type boolean;
          default "false";
        }
      }

      container ac-controller {
        description
          "Ac Controller Mode.";

        uses ntos-wlan-apmg:apmg-ac-config;
        uses ntos-wlan-stamg:stamg-ac-config;
        uses ntos-wlan-capwap:capwap-ac-config;
        uses ntos-wlan-wbs:wbs-ac-config;
      }

      list ap-config {
        key "ap-name";
        description
          " Ap Config Mode.";

        leaf ap-name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
        }

        uses ntos-wlan-apmg:apmg-ap-config;
        uses ntos-wlan-stamg:stamg-ap-config;
        uses ntos-wlan-capwap:capwap-ap-config;
        uses ntos-wlan-wbs:wbs-ap-config;
        uses ntos-wlan-wids:wids-ap-config;
      }

      list wlan-config {
        key "wlan-id";
        description
          "Wlan Mode.";

        leaf wlan-id {
          type uint16 {
            range "1..4094";
          }
          ntos-ext:nc-cli-no-name;
        }

        container wlansec {
          uses ntos-wlan-wbs:wbs-sec-config;
        }

        uses ntos-wlan-apmg:apmg-wlan-config;
        uses ntos-wlan-stamg:stamg-wlan-config;
        uses ntos-wlan-wbs:wbs-wlan-config;
      }

      list ap-group {
        key "ap-group-name";
        description
          "Ap Group Mode.";

        leaf ap-group-name {
          type ntos-types:ntos-obj-name-type;
          ntos-ext:nc-cli-no-name;
        }

        uses ntos-wlan-apmg:apmg-apg-config;
        uses ntos-wlan-stamg:stamg-apg-config;
        uses ntos-wlan-capwap:capwap-apg-config;
      }

      uses ntos-wlan-wids:wids-configure;
    }
  }

  rpc wlan-cmd {
    description
      "Wlan rpc cmd.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      uses ntos-wlan-apmg:cmd-apmg;
      uses ntos-wlan-stamg:cmd-stamg;

      container ac-controller {
        ntos-ext:nc-cli-group "subcommand";
        description
          "Command of ac-controller.";

        list factory-reset {
          key "name";
          description
            "Set AP's to restore the config to be factory default.";
          leaf name {
            type string;
          }
        }

        container reset {
          leaf all {
            description
              "Reset the all APs in this AC.";
            type empty;
          }

          list single {
            key "name";
            description
              "Reset the single ap.";
            leaf name {
              type string;
            }
          }
        }

        leaf client-kick {
          description
            "Remove client from the network.";
          type ntos-if:mac-address;
        }

        leaf kick-ap {
          description
            "Kick ap Debug.";
          type union {
            type ntos-if:mac-address;
            type enumeration {
              enum all;
            }
          }
        }

        leaf del-ap {
          description
            "Del ap Debug.";
          type string;
        }
      }

      container ap-config {
        ntos-ext:nc-cli-group "subcommand";
        description
          "Command of ap-controller.";

        container del {
          leaf ap {
            type string;
          }
        }

        leaf ap-name {
          type string;
        }

        leaf ap-image {
          type string;
        }
      }

      uses ntos-wlan-wids:wids-cmd;
    }

    output {
      leaf result {
        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "wlan";
    ntos-api:internal;
  }

  rpc wlan-show {
    description
      "Wlan rpc show.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container web-ac {
        leaf topology {
          type empty;
        }
      }

      container schedule {
        leaf session {
          type union {
            type uint8;
            type empty;
          }
        }
        leaf state {
          type union {
            type uint8;
            type empty;
          }
        }
      }

      container diag {
        container sta {
          leaf sta-mac {
            type string;
          }
        }

        container debug {
          leaf ctrl {
            type enumeration {
              enum info;
            }
          }

          leaf ac {
            type enumeration {
              enum sta-info;
            }
          }

          container ap {
            leaf ap-mac {
              type string;
              ntos-ext:nc-cli-no-name;
            }

            leaf flow {
              type empty;
            }

            leaf state {
              type empty;
            }
          }
        }
      }

      uses ntos-wlan-apmg:show-apmg;
      uses ntos-wlan-stamg:show-stamg;
      uses ntos-wlan-capwap:show-capwap;

      container ap-config {
        ntos-ext:nc-cli-group "subcommand";
        description
          "Show content of ap.";

        container content {
          leaf type {
            description
              "Show type of ap-config.";
            type enumeration {
              enum detail;
              enum cb;
              enum bssid;
              enum inherit-wlan;
              enum product;
              enum summary;
              enum radio-info;
              enum running;
              enum flow;
              enum sta-limit;
              enum offline-ap;
              enum chanwidth-limit;
            }
          }

          leaf ap-name {
            type string;
          }

          leaf ap-mac {
            type ntos-if:mac-address;
          }

          container filter {
            leaf ap-name {
              type string;
            }
            leaf ap-group {
              type string;
            }
            leaf ap-mac {
              type string;
            }
            leaf ap-ip {
              type string;
            }
          }

          leaf debug {
            type empty;
          }

          leaf start {
            type uint32;
            description
              "Start offset of result.";
          }

          leaf end {
            type uint32;
            description
              "End offset of result.";
          }

          leaf ap-auth {
            type empty;
          }

          leaf deny-ap {
            type empty;
          }
        }
      }

      container ac-config {
        ntos-ext:nc-cli-group "subcommand";
        description
          "Show content of ac.";

        container content {
          leaf type {
            description
              "Show type of wlan.";
            type enumeration {
              enum rssi;
              enum client;
              enum summary;
              enum capwap-peer-state;
              enum ap-backup-group;
              enum black-white-list;
              enum active-file;
              enum num-balance;
              enum flow-balance;
              enum version;
              enum ac-control-state;
              enum country-code;
              enum sta-limit;
            }
          }

          choice bw-type {
            case blacklist {
              leaf blacklist {
                description
                  "Show blacklist.";
                type empty;
              }
            }
            case whitelist {
              leaf whitelist {
                description
                  "Show whitelist.";
                type empty;
              }
            }
            case vendor-blacklist {
              leaf vendor-blacklist {
                description
                  "Show vendor-blacklist.";
                type empty;
              }
            }
            case vendor-whitelist {
              leaf vendor-whitelist {
                description
                  "Show vendor-whitelist.";
                type empty;
              }
            }
          }

          leaf status {
            type empty;
          }

          leaf total-count {
            type empty;
            when "../type = 'client'";
          }

          leaf pre-mac {
            type string;
            must "../count";
            when "../type = 'client'";
          }

          leaf count {
            type uint16;
            when "../type = 'client'";
          }

          leaf rssi-min {
            type uint16;
            when "../type = 'client'";
          }

          leaf rssi-max {
            type uint16;
            when "../type = 'client'";
          }

          leaf flow-rate {
            description
              "mac address: xx:xx:xx:xx:xx:xx.";
            type ntos-if:mac-address;
            when "../type = 'client'";
          }

          leaf debug {
            type empty;
          }

          leaf ap-name {
            description
              "Single AP's name or show the client information by ap name.";
            type string;
            when "../type = 'version' or ../type = 'client'";
          }

          leaf ap-mac {
            description
              "Show the client information by ap mac.";
            type string;
            when "../type = 'client'";
          }

          leaf sta-mac {
            description
              "Show the client information by sta mac.";
            type string;
            when "../type = 'client'";
          }

          leaf ssid {
            description
              "Show the client information by ssid.";
            type string;
            when "../type = 'client'";
          }

          leaf all {
            description
              "Show all APs' version.";
            type empty;
            when "../type = 'version'";
          }

          leaf real {
            description
              "Show slot version.";
            type empty;
            when "../type = 'version'";
          }

          leaf slot {
            description
              "Show real version.";
            type empty;
            when "../type = 'version'";
          }

          leaf start {
            type uint32;
          }

          leaf end {
            type uint32;
          }

          leaf filter {
            type string;
          }
        }
      }

      container ap-group {
        ntos-ext:nc-cli-group "subcommand";
        description
          "Show content of ap group.";

        container content {
          leaf type {
            description
              "Show type of ap-group.";
            type enumeration {
              enum aps;
              enum intf-wlan-map;
              enum cb;
              enum summary;
            }
          }

          leaf summary {
            type empty;
          }

          leaf ap-group-name {
            type string;
            description
              "Select ap group.";
          }

          leaf debug {
            type empty;
          }
        }
      }

      container wlan-config {
        ntos-ext:nc-cli-group "subcommand";
        description
          "Show content of wlan.";

        container content {
          leaf type {
            type enumeration {
              enum webinfo;
              enum wlan;
              enum cb;
              enum summary;
              enum secinfo;
              enum black-white-list;
              enum sta-limit;
              enum wlan-limit;
            }

            description
              "Show type of wlan.";
          }

          choice bw-type {
            case blacklist {
              leaf blacklist {
                description
                  "Show blacklist.";
                type empty;
              }
            }
            case whitelist {
              leaf whitelist {
                description
                  "Show whitelist.";
                type empty;
              }
            }
            case vendor-blacklist {
              leaf vendor-blacklist {
                description
                  "Show vendor-blacklist.";
                type empty;
              }
            }
            case vendor-whitelist {
              leaf vendor-whitelist {
                description
                  "Show vendor-whitelist.";
                type empty;
              }
            }
          }

          leaf status {
            type empty;
          }

          leaf ssid {
            type string;
            description
              "Set filter condition of ssid.";
          }

          leaf wlan-id {
            type uint16;
            description
              "Show wlan by wlan-id";
          }

          leaf start {
            type uint32;
            description
              "Start offset of result.";
          }

          leaf end {
            type uint32;
            description
              "End offset of result.";
          }

          leaf filter {
            type string;
          }

          leaf debug {
            type empty;
          }
        }
      }

      container debugging {
        description
          "Show every module debug set.";
        leaf in {
          type enumeration {
            enum apmg;
            enum capwap;
          }
        }
      }

      uses ntos-wlan-wids:show-wids-config-input;
    }

    output {
      container data {
        leaf totalCount {
          type uint16;
        }

        uses ntos-wlan-apmg:show-ap-detail;

        uses ntos-wlan-stamg:show-bw-list;
      }

      uses ntos-wlan-apmg:show-ac-config;

      uses ntos-wlan-capwap:show-ac-control;
      uses ntos-wlan-capwap:show-ap-version;

      uses ntos-wlan-stamg:show-ac-client;
      uses ntos-wlan-stamg:show-balance-group;

      uses ntos-wlan-wbs:show-rssi;
      uses ntos-wlan-wbs:show-secinfo;

      uses ntos-wlan-wids:show-wids-config-output;

      leaf result {
        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "wlan";
    ntos-api:internal;
  }

  rpc wlan-import {
    description
      "Blacklist and whitelist config import.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      leaf file-name {
        type string;
        description
          "The path of configurations, '.csv' files are supported.";
      }

      leaf to-ssid {
        type string;
      }

      leaf import-type {
        type enumeration {
          enum blacklist;
          enum whitelist;
        }
      }

      leaf conflicts-type {
        type enumeration {
          enum warning;
          enum cover;
        }
        description
          "Specifies how conflicts are handled.";
      }
    }

    output {
      leaf result {
        description
          "Info of import result.";
        type string;
      }

      list detail {
        key msg;

        leaf data {
          description
            "Error detail of import.";
          type string;
        }

        leaf msg {
          description
            "Error prompt of import.";
          type string;
        }

        leaf error-type {
          description
            "Error type of import.";
          type string;
        }
      }

      leaf success-cnt {
        description
          "Count of success import.";
        type string;
      }

      leaf cover-cnt {
        description
          "Count of cover import.";
        type string;
      }

      leaf fail-cnt {
        description
          "Count of fail import.";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "wlan-import-bw";
    ntos-api:internal;
  }
}