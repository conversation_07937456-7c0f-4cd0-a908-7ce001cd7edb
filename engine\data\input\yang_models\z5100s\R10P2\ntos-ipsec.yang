module ntos-ipsec {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ipsec";
  prefix ntos-ipsec;

  import ietf-yang-types {
    prefix ietf-yang;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:gao<PERSON><PERSON><PERSON>@ruijie.com.cn";
  description
    "Ruijie NTOS IPSec module.";

  revision 2023-08-02 {
    description
      "Add ike protocol version and ike profile configure.";
  }

  revision 2022-12-23 {
    description
      "Add ipsec config and state.";
    reference "";
  }

  identity ipsec {
    base ntos-types:SERVICE_LOG_ID;
    description
      "IPSec service.";
  }

  grouping profile-config {
    description
      "Configuration data for ipsec profile.";
    list profile {
      key "name";
      description
        "The detail of ipsec profile.";
      ordered-by user;

      leaf name {
        type string {
          length "1..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
          }
        }
        description
          "Set the name of profile.";
      }
      
      leaf preshare-key {
        type string {
          length "3..64";
        }
        description
          "Set the preshare-key for profile with length 3..64.";
      }

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable profile.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        default "";
        description
          "The description of profile.";
      }

      leaf create-time {
        description
          "Ipsec Profile creation time.";
        type ietf-yang:timestamp;
      }

      leaf version {
        type bits {
          bit ikev1 {
            description
              "The ikev1 protocol.";
          }
          bit ikev2 {
            description
              "The ikev2 protocol.";
          }
        }
        default "ikev1";
        description
          "Set IKE protocol version.";
      }

      leaf exchange-mode {
        type enumeration {
          enum main {
            description
              "Set main mode.";
          }
          enum aggressive {
            description
              "Set aggressive mode.";
          }
          enum auto {
            description
              "Set auto mode.";
          }
        }
        default "main";
        description
          "Set the exchange-mode of ipsec profile.";
      }

      leaf autoup {
        type boolean;
        default "false";
        description
          "Set the autoup of ipsec profile.";
      }

      container local {
        choice local {
          case address {
            container address {
              leaf address {
                type union {
                  type ntos-inet:ipv4-address;
                  type ntos-inet:ipv6-address;
                }
                description
                  "Set the local ip.";
                ntos-ext:nc-cli-no-name;
              }
            }
          }

          case interface {
            container interface {
              leaf interface {
                type ntos-types:ifname;
                ntos-ext:nc-cli-completion-xpath
                  "/ntos:config/ntos:vrf[ntos:name='main']/ntos-interface:interface/*[local-name()='physical']/*[local-name()='name']";
                description
                  "Set the local interface name.";
                ntos-ext:nc-cli-no-name;
              }
            }
          }
          mandatory true;
        }
      }

      leaf proxyid {
        type string {
          length "1..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
          }
        }
        ntos-ext:nc-cli-completion-xpath
                "/ntos:config/ntos:vrf/ntos-ipsec:ipsec/*[local-name()='proxyid']/*[local-name()='proxyid-name']";
        mandatory true;
        description
          "Set the reference of proxyid.";
      }

      leaf ike-profile {
        type string {
          length "1..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
          }
        }
        ntos-ext:nc-cli-completion-xpath
                "/ntos:config/ntos:vrf/ntos-ike:ike/*[local-name()='profile']/*[local-name()='name']";
        default "temporary";
        description
          "Set the reference of ike profile.";
      }

      leaf ike-proposal {
        type string {
          length "1..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
          }
        }
        ntos-ext:nc-cli-completion-xpath
                "/ntos:config/ntos:vrf/ntos-ike:ike/*[local-name()='proposal']/*[local-name()='name']";
        default "default";
        description
          "Set the reference of ike proposal.";
      }

      leaf ipsec-proposal {
        type string {
          length "1..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
          }
        }
        ntos-ext:nc-cli-completion-xpath
            "/ntos:config/ntos:vrf/ntos-ipsec:ipsec/*[local-name()='proposal']/*[local-name()='name']";
        mandatory true;
        description
          "Set the reference of ipsec proposal.";
      }

      leaf life-seconds {
        type uint32 {
          range "60..604800";
        }
        units second;
        default "3600";
        description
          "Set the life-seconds of ipsec proposal.";
      }

      container reverse-route {
        leaf enabled {
          type boolean;
          default "false";
          description
            "Enable or disable reverse-route.";
        }

        leaf distance {
          type uint32 {
            range "1..254";
          }
          default "5";
          description
            "Set the reverse-route distance.";
        }

        leaf peer {
          type union {
            type ntos-inet:ipv4-address;
            type ntos-inet:ipv6-address;
          }
          description
            "Set the reverse-route peer.";
        }
      }

      leaf fragmentation-mtu {
        type uint32 {
          range "512..1500";
        }
        default "1400";
        description
          "Set the mtu size.";
      }

      list peer-address {
        key "peer-address";
        description
          "The peer address of ipsec profile.";
        ordered-by user;
        leaf peer-address {
          type union {
            type ntos-inet:ipv4-address;
            type ntos-inet:ipv6-address;
            type ntos-inet:domain-name;
          }
          description
            "Set the peer ip address or domain name.";
        }
        max-elements 1;
        ntos-ext:nc-cli-one-liner;
      }

      leaf type {
        type enumeration {
          enum static {
            description
              "Set static type tunnel.";
          }
          enum dynamic {
            description
              "Set dynamic type tunnel.";
          }
        }
        default "static";
        description
          "Set the type of ipsec profile.";
      }

      container local-identity {
        choice local-identity {
          case ipv4-address {
            leaf ipv4-address {
              type union {
                type ntos-inet:ipv4-address;
                type empty;
              }
              description
                  "Set the local ipv4-address (profile local address).";
            }
          }
          case ipv6-address {
            leaf ipv6-address {
              type union {
                type ntos-inet:ipv6-address;
                type empty;
              }
              description
                  "Set the local ipv6-address (profile local address).";
            }
          }
          case fqdn {
            leaf fqdn {
              type string {
                length "1..253";
              }
              description
                  "Set the value of fully qualified domain name, length 1..253.";
            }
          }
          case user-fqdn {
            leaf user-fqdn {
              type string {
                length "1..255";
              }
              description
                  "Set the value of user fully qualified domain name, length 1..255.";
            }
          }
          case key-id {
            leaf key-id {
              type string {
                length "1..255";
              }
              description
                  "Set the value of key id, length 1..255.";
            }
          }
        }
      }

      leaf check-id {
        type boolean;
        default "false";
        description
          "Enable or disable peer-identity.";
      }

      container peer-identity {
        description
          "Set peer-identity type and value.";
        status deprecated {
          ntos-ext:status-deprecated-revision "2023-08-02";
          ntos-ext:status-description "Use ike profile peer-id replace.";
          ntos-ext:status-replacement "/ntos:config/ntos:vrf/ntos-ike:ike/profile/name/peer-id";
        }

        list ipv4-address {
          key "value";
            description
              "Set the peer ipv4-addresss.";
          leaf value {
            type union {
              type ntos-inet:masked-ipv4-address;
              type source-masked-ipv4;
              type source-ipv4;
            }
            ntos-ext:nc-cli-no-name;
            description
              "The value of peer ipv4-address";
          }
          ordered-by user;
          ntos-ext:nc-cli-one-liner;
        }

        list ipv6-address {
          key "value";
            description
              "Set the peer ipv6-address.";
          leaf value {
            type union {
              type ntos-inet:masked-ipv6-address;
              type ntos-inet:ipv6-address;
            }
            ntos-ext:nc-cli-no-name;
            description
              "The value of peer ipv6-address";
          }
          ordered-by user;
          ntos-ext:nc-cli-one-liner;
        }

        list fqdn {
          key "value";
            description
              "Set the peer fqdn.";
          leaf value {
            type string {
              length "1..253";
            }
            ntos-ext:nc-cli-no-name;
            description
              "The value of fully qualified domain name, length 1..253";
          }
          ordered-by user;
          ntos-ext:nc-cli-one-liner;
        }

        list user-fqdn {
          key "value";
            description
              "Set the peer user-fqdn.";
          leaf value {
            type string {
              length "1..255";
            }
            ntos-ext:nc-cli-no-name;
            description
              "The value of user fully qualified domain name, length 1..255";
          }
          ordered-by user;
          ntos-ext:nc-cli-one-liner;
        }

        list key-id {
          key "value";
            description
              "Set the peer key-id.";
          leaf value {
            type string {
              length "1..255";
            }
            ntos-ext:nc-cli-no-name;
            description
              "The value of key id, length 1..255";
          }
          ordered-by user;
          ntos-ext:nc-cli-one-liner;
        }
      }

      leaf pfs {
        description
          "Set the groups of ipsec proposal.";

        type enumeration {
          enum group1 {
            value 1;
            description
              "D-H Group1 (MODP768).";
          }
          enum group2 {
            value 2;
            description
              "D-H Group2 (MODP1024).";
          }
          enum group5 {
            value 5;
            description
              "D-H Group5 (MODP1536).";
          }
          enum group14 {
            value 14;
            description
              "D-H Group14 (MODP2048).";
          }
          enum group15 {
            value 15;
            description
              "D-H Group15 (MODP3072).";
          }
          enum group16 {
            value 16;
            description
              "D-H Group16 (MODP4096).";
          }
        }
      }

      container dpd {
        leaf interval {
          type uint32 {
            range "10..3600";
          }
          units second;
          default 30;
          ntos-ext:nc-cli-no-name;
          description
            "Set the DPD R-U-THERE interval.";
        }

        leaf retry-interval {
          type uint32 {
            range "2..10";
          }
          units second;
          default 5;
          ntos-ext:nc-cli-no-name;
          description
            "Set the DPD Retry Interval.";
        }

        leaf type {
          type enumeration {
            enum periodic {
              description
                "Periodic send DPD queries at regular intervals.";
            }
            enum idle {
              description
                "idle mode.";
            }
          }
          default "idle";
          ntos-ext:nc-cli-no-name;
          description
            "Set the type of DPD.";
        }
        ntos-ext:nc-cli-one-liner;
      }

      leaf tunnel-interface {
        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-vti:vti/*[local-name()='name']";
        mandatory true;
        description
          "Set the tunnel interface name.";
      }

      leaf is-template {
        type boolean;
        default "false";
        description
          "Used to distinguish whether to deliver from the web page of the wizard configuration.";
      }

      leaf wizard-type {
        type enumeration {
          enum l2tp-over-ipsec;
        }
        description
          "Set IPsec scenario type.";
      }

      max-elements 1000;
    }
  }

  grouping ipsec-proposal-config {
    description
      "Configuration data for ipsec proposal.";

    list proposal {
      key "name";
      ordered-by user;
      description
        "Configuration of ipsec proposal.";

      leaf name {
        type string {
            length "1..64";
            pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
              error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
            }
        }
        description
           "Set the name of ipsec proposal.";
      }

      leaf protocol {
        type enumeration {
          enum esp {
            description
                "Set the esp protocol.";
          }
        }
        default "esp";
        description
            "Set the protocol of ipsec profile.";
      }

      leaf encap-mode {
        type enumeration {
          enum tunnel {
            description
                "Set the tunnel mode.";
          }
          enum transport {
            description
                "Set the transport mode.";
          }
        }
        default "tunnel";
        description
            "Set the encap-mode of ipsec profile.";
      }
      leaf esn {
        type boolean;
        description
            "Extended Sequence Number for IPSec SA.";
        default true;
      }

      leaf esp-encrypt-alg {
        type bits {
          bit des {
            description
              "Encryption algorithm des.";
          }
          bit des3 {
            description
              "Encryption algorithm 3des.";
          }
          bit null {
            description
              "Encryption algorithm null.";
          }
          bit aes-128 {
            description
              "Encryption algorithm aes-128.";
          }
          bit aes-192 {
            description
              "Encryption algorithm aes-192.";
          }
          bit aes-256 {
            description
              "Encryption algorithm aes-256.";
          }
        }
        description
          "Set the encryption algorithm of ipsec proposal.";
      }

      leaf esp-auth-alg {
        type bits {
          bit md5 {
            description
              "Auth algorithm md5.";
          }
          bit sha {
            description
              "Auth algorithm sha.";
          }
          bit sha-256 {
            description
              "Auth algorithm md5.";
          }
          bit sha-384 {
            description
              "Auth algorithm sha.";
          }
          bit sha-512 {
            description
              "Auth algorithm sha.";
          }
        }
        mandatory true;
        description
           "Set the hash algorithm of proposal.";
      }
      max-elements 1000;
    }
  }

  typedef source-masked-ipv4-cidr {
    type ntos-inet:masked-ipv4-address;
    description
      "Source address.";
  }

  typedef source-masked-ipv4 {
    type ntos-inet:ipv4-mask;
    description
      "Source address.";
  }

  typedef source-ipv4 {
    type ntos-inet:ipv4-address;
    description
      "A single source host.";
  }

  typedef dest-masked-ipv4-cidr {
    type ntos-inet:masked-ipv4-address;
    description
      "Dest address.";
  }

  typedef dest-masked-ipv4 {
    type ntos-inet:ipv4-mask;
    description
      "Dest address.";
  }

  typedef dest-ipv4 {
    type ntos-inet:ipv4-address;
    description
      "A single dest host.";
  }

  grouping proxyid-config {
    description
      "Configuration data for proxyid.";

    list proxyid {
      description
        "The detail of ipsec proxyid.";
      key "proxyid-name";
      ordered-by user;

      leaf proxyid-name {
        type string {
          length "1..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
          }
        }
        description
            "The name of proxyid.";
      }

      list ip {
        description
          "Set the ip address of proxyid.";

        key "local remote";
        ordered-by user;

        leaf local {
          type union {
            type source-masked-ipv4-cidr;
            type source-masked-ipv4;
            type source-ipv4;
            type enumeration {
              enum any {
                description
                  "Any source host.";
              }
            }
          }
          description
            "Set the local address of proxyid.";
        }

        leaf remote {
          type union {
            type dest-masked-ipv4-cidr;
            type dest-masked-ipv4;
            type dest-ipv4;
            type enumeration {
              enum any {
                description
                  "Any dest host";
              }
            }
          }
          description
            "Set the remote address of proxyid.";
        }

        ntos-ext:nc-cli-one-liner;
        max-elements 64;
      }

      list ipv6 {
        description
          "Set the ipv6 address of proxyid.";

        key "local remote";
        ordered-by user;

        leaf local {
          type union {
            type ntos-inet:masked-ipv6-address;
            type ntos-inet:ipv6-address;
            type enumeration {
              enum any {
                description
                  "Any source host.";
              }
            }
          }
          description
            "Set the local address of proxyid.";
        }

        leaf remote {
          type union {
            type ntos-inet:masked-ipv6-address;
            type ntos-inet:ipv6-address;
            type enumeration {
              enum any {
                description
                  "Any dest host";
              }
            }
          }
          description
            "Set the remote address of proxyid.";
        }

        ntos-ext:nc-cli-one-liner;
        max-elements 64;
      }

      max-elements 1000;
    }
  }

  grouping ipsec-global-config {

    container anti-replay {
      leaf check {
        type boolean;
        default true;
        description
          "Enable ipsec anti-replay checking.";
      }

      leaf window-size {
        type enumeration {
          enum 64 {
            value 64;
            description
                "Set the size of ipsec anti-replay window 64.";
          }
          enum 128 {
            value 128;
            description
                "Set the size of ipsec anti-replay window 128.";
          }
          enum 256 {
            value 256;
            description
                "Set the size of ipsec anti-replay window 256.";
          }
          enum 512 {
            value 512;
            description
                "Set the size of ipsec anti-replay window 512.";
          }
          enum 1024 {
            value 1024;
            description
                "Set the size of ipsec anti-replay window 1024.";
          }
        }
        default 64;
        description
          "Set the size of ipsec anti-replay window.";
      }
    }

    leaf df-bit {
      type enumeration {
        enum clear {
          value 0;
          description
              "Clear df-bit to 0.";
        }
        enum copy {
          value 1;
          description
              "Copy df-bit from original packet.";
        }
        enum set {
          value 2;
          description
              "Set df-bit to 1.";
        }
      }
      default "clear";
      description
        "Set the df-bit of ipsec.";
    }

    leaf prefrag {
      type boolean;
      default "true";
        description
          "Enable ipsec prefrag.";
    }

    container inbound-sp {
      leaf check {
        type boolean;
        default "true";
          description
            "Enable ipsec inbound-sp.";
      }
    }

    container spd-hash-bits {
      leaf src-bits {
        type uint32 {
          range "1..32";
        }
        default "16";
        ntos-ext:nc-cli-no-name;
        description
          "Set the ipsec spd-hash-bits src-bits.";
      }

      leaf dst-bits {
        type uint32 {
          range "1..32";
        }
        default "16";
        ntos-ext:nc-cli-no-name;
        description
          "Set the ipsec spd-hash-bits dst-bits.";
      }
      ntos-ext:nc-cli-one-liner;
    }

    leaf hardware-crypto-offload {
      type boolean;
      default "true";
      description
        "Enable hardware crypto offload.";
    }
  }

  grouping cmd-output-buffer {
    description
      "Command output buffer.";

    leaf buffer {
      type string;
      description
        "Command output buffer since last request.";
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  rpc ipsec-get-list {
    description
      "Get algorithm and dh-group";

    input {
      leaf get-type {
        type enumeration {
          enum dh-group {
            description
              "Get dh-group list.";
          }
          enum ike-encrypt-alg {
            description
              "Get ike-encrypt-alg list.";
          }
          enum ike-hash-alg {
            description
              "Get ike-hash-alglist.";
          }
          enum ipsec-encrypt-alg {
            description
              "Get ipsec-encrypt-alg list.";
          }
          enum ipsec-auth-alg {
            description
              "Get ipsec-auth-alg list.";
          }
          enum ike-prf-alg {
            description
              "Get ikev2 prf alg list.";
          }
        }
      }
    }

    output {
      leaf list-num {
        type uint32;
      }

      list ipsec-list {
        key "name";
        leaf name {
          type string;
        }
        leaf label {
          type string;
        }
      }
    }
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "ipsec list";
  }

  rpc check-env {
    input {
      leaf vrf {
        type string;
        description
          "The vrf name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf item {
        type enumeration {
          enum route-mode {
            description
              "Check deployment mode.";
          }
          enum interface-state {
            description
              "Check interface state.";
          }
          enum service-port {
            description
              "Check ipsec service port is available.";
          }
          enum network {
            description
              "Check device network.";
          }
          enum all {
            description
              "Check all item.";
          }
        }
      }
    }
    output {
      container check-env {
        list data {
          key "item";
          leaf item {
            type string; 
            description
              "Check item status.";
          }
          leaf result {
            type boolean;
            description
              "Check item status.";
          }
          leaf detail {
            type string;
            description
              "Check item message.";
          }
        }
      }
      uses cmd-output-buffer;
    }
      ntos-ext:nc-cli-cmd "ipsec check-env";
  }
  rpc show-ipsec-tunnel-status {
    input {
      leaf vrf {
        type string;
        default "main";
        description
          "vrf name.";
      }

      leaf start {
        type uint32;
        description
          "Start offset.";
      }

      leaf end {
        type uint32;
        description
          "End offset.";
      }

      leaf filter-type {
        description
          "Filter-type.";
        type enumeration {
          enum peer-address {
            description
              "Display tunnel state filter by peer-address.";
          }
          enum name {
            description
              "Display tunnel state filter by name.";
          }
        }
      }

      leaf filter {
        type string;
        description
          "Filter.";
      }

      leaf json {
        type empty;
      }
    }

    output {
      leaf total {
        type uint32;
      }
      leaf up {
        type uint32;
      }
      leaf down {
        type uint32;
      }

      list id {
        key "tunnel-id";

        leaf tunnel-id {
          type uint16;
          description
            "Display the tunnel-id of profile.";
        }

        leaf parent-id {
          type string;
          description
            "Display the parent-id of profile.";
        }

        leaf name {
          type string;
          description
            "Display the name of profile.";
        }

        leaf enabled {
          type boolean;
        }

        leaf type {
          type string;
          description
            "Display the type of profile.";
        }

        leaf peer {
          type string;
          description
            "Display the peer of profile.";
        }

        leaf localnet {
          type string;
          description
            "Display the localnet of profile.";
        }

        leaf remotenet {
          type string;
          description
            "Display the remotenet of profile.";
        }

        leaf interest-flow {
          type string;
          description
            "Display the interest_flow of profile.";
        }

        leaf ipsec-lifetime-sec {
          type string;
          description
            "Display the liftime_lft of profile.";
        }

        leaf tx-bytes {
          type uint64;
          description
            "Display the tx_bytes of profile.";
        }

        leaf rx-bytes {
          type uint64;
          description
            "Display the rx_bytes of profile.";
        }

        leaf status {
          type string;
          description
            "Display the status of profile.";
        }

        leaf ph1-status {
          type string;
          description
            "Display the status of profile.";
        }

        leaf ph2-status {
          type string;
          description
            "Display the status of profile.";
        }

        leaf dpd-send-detect {
          type uint32;
          description
            "Display the count of dpd-send-detect.";
        }

        leaf dpd-recv-detect {
          type uint32;
          description
            "Display the count of dpd-recv-detect.";
        }

        leaf dpd-send-ack {
          type uint32;
          description
            "Display the count of dpd-send-ack.";
        }

        leaf dpd-recv-ack {
          type uint32;
          description
            "Display the count of dpd-recv-ack.";
        }
      }

      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-show "tunnel status";
  }

  rpc show-ike-sa {
    description
      "Display state of ike sa";

    input {
      container filter {
        leaf peer-address {
          type string;
          description
            "Display state of ike sa filtered by peer-address.";
        }

        leaf status {
          type string;
          description
            "Display state of ike sa filtered by status.";
        }

        leaf connection-id {
          type string;
          description
            "Display state of ike sa filtered by connection-id.";
        }

        leaf state-id {
          type int32;
          description
            "Display state of ike sa filtered by state-id.";
        }
      }

      leaf json {
        type empty;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-show "ike sa";
  }

  rpc show-ipsec-statistics {
    description
      "Display state of ipsec statistics";

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-show "ipsec statistics";
  }

  rpc show-ipsec-sa {
    description
      "Display ipsec sa";

    input {
      leaf index {
        type uint32;
        description
            "Display ipsec sa by index.";
      }

      leaf spi {
        type string {
          length "1..8";
          pattern '[a-fA-F0-9]+';
        }
        description
            "Display ipsec sa by SPI(hexadecimal, e.g. aabb1122).";
      }

      leaf peer {
        type union {
          type ntos-inet:ipv4-address;
          type ntos-inet:ipv6-address;
        }
        description
            "Display ipsec sa by peer address.";
      }
    }

    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-show "ipsec sa";
  }

  rpc show-ipsec-sp {
    description
      "Display ipsec sp";

    input {
      leaf index {
        type uint32;
        description
            "Display ipsec sp by index.";
      }

      leaf local {
        type union {
          type ntos-inet:masked-ipv4-address;
          type ntos-inet:masked-ipv6-address;
        }
        description
            "Display ipsec sp by local subnet.";
      }

      leaf peer {
        type union {
          type ntos-inet:masked-ipv4-address;
          type ntos-inet:masked-ipv6-address;
        }
        description
            "Display ipsec sp by peer subnet.";
      }
    }

    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-show "ipsec sp";
  }

  rpc tunnel-enable {
    description
      "Set enabled info of ipsec tunnel";

    input {
      leaf tunnel-id {
        type string;
      }

      leaf action {
        type enumeration {
          enum up;
          enum down;
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-cmd "ipsec tunnel";
  }

  rpc ipsec-load-config {
    ntos-ext:nc-cli-cmd "ipsec load config";
  }

  rpc show-ipsec-diagnosis-result {
    description
      "Display diagnosis result";

    output {
      uses cmd-output-buffer;
    }
  }

  rpc show-ipsec-diagnosis-log {
    description
      "Display diagnosis log";
    input {
      leaf session {
        type uint32;
      }
    }
    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-show "ipsec diagnosis log";
  }

  rpc ipsec-check-proxyid {
    description
      "Check proxyid legitimacy for web";
    input {
      list rule {
        key "type local remote";
        leaf type {
          type string;
          description
            "Input rule type";
        }
        leaf local {
          type string;
          description
            "Input rule local subnet";
        }
        leaf remote {
          type string;
          description
            "Input rule remote subnet";
        }
      }
    }
  }

  rpc set-ipsec-config {
    description
      "IPsec configuration settings.";

    input {
      leaf type {
        type enumeration {
          enum l2tp-over-ipsec {
            description
              "Set IPsec by l2tp.";
          }
        }
        mandatory true;
        description
          "Set IPsec scenario type.";
      }

      leaf name {
        type string {
          length "1..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
          }
        }
        mandatory true;
        description
          "Set the name of IPsec profile.";
      }

      leaf enabled {
        type boolean;
        mandatory true;
        description
          "Enable or disable IPsec profile.";
      }

      container local {
        description
          "Set IPsec local address container.";
        choice local {
          case address {
            container address {
              leaf address {
                type union {
                  type ntos-inet:ipv4-address;
                  type ntos-inet:ipv6-address;
                }
                description
                  "Set IPsec local site address.";
                ntos-ext:nc-cli-no-name;
              }
            }
          }

          case interface {
            container interface {
              leaf interface {
                type ntos-types:ifname;
                description
                  "Set IPsec local site interface.";
                ntos-ext:nc-cli-no-name;
              }
            }
          }
        }
      }

      list remote {
        key "address";
        description
          "Set IPsec remote site address.";
        leaf address {
          type union {
            type ntos-inet:ipv4-address;
            type ntos-inet:ipv6-address;
            type ntos-inet:domain-name;
          }
          description
            "Set IPsec remote site address.";
        }
        max-elements 1;
        ntos-ext:nc-cli-one-liner;
      }

      list key {
        key "type";
        description
          "Configuration of IPsec key.";

        leaf type  {
          type enumeration {
            enum pre-share {
              description
                "Preshared-key type.";
            }
          }
          description
            "Set the type of IPsec key.";
        }

        list ipv4-address {
          key "ipv4-address";
          description
            "Address configuration of IPsec key.";

          leaf ipv4-address {
            type ntos-inet:ipv4-address {
              ntos-ext:nc-cli-shortdesc "<ipv4-address>";
            }
            description
              "Set the ipv4 address.";
          }

          leaf key {
            type string {
              length "3..64";
            }
            mandatory true;
            description
              "Set the key of ipv4 address length 3..64.";
          }

          ntos-ext:nc-cli-one-liner;
        }

        list ipv6-address {
          key "ipv6-address";
          description
            "Address configuration of IPsec key.";

          leaf ipv6-address {
            type ntos-inet:ipv6-address {
              ntos-ext:nc-cli-shortdesc "<ipv6-address>";
            }
            description
              "Set the ipv6 address.";
          }

          leaf key {
            type string {
              length "3..64";
            }
            description
              "Set the key of ipv6 address length 3..64.";
          }

          ntos-ext:nc-cli-one-liner;
        }

        list fqdn {
          key "fqdn";
          description
            "fqdn configuration of IPsec preshare key.";

          leaf fqdn {
            type string {
              length "1..253";
            }
            description
              "Set the fqdn length 1..253.";
          }

          leaf key {
            type string {
              length "3..64";
            }
            description
              "Set the key of fqdn length 3..64.";
          }

          ntos-ext:nc-cli-one-liner;
        }

        list user-fqdn {
          key "user-fqdn";
          description
            "user-fqdn configuration of IPsec preshare key.";

          leaf user-fqdn {
            type string {
              length "1..255";
            }
            description
              "Set the user-fqdn length 1..255.";
          }

          leaf key {
            type string {
              length "3..64";
            }
            description
              "Set the key of user-fqdn length 3..64.";
          }

          ntos-ext:nc-cli-one-liner;
        }

        list key-id {
          key "key-id";
          description
            "key-id configuration of IPsec preshare key.";

          leaf key-id {
            type string {
              length "1..255";
            }
            description
              "Set the key-id length 1..255.";
          }

          leaf key {
            type string {
              length "3..64";
            }
            description
              "Set the key of key-id length 3..64.";
          }

          ntos-ext:nc-cli-one-liner;
        }

        max-elements 1000;
      }

      container local-identity {
        description
          "Set IPsec local-identity container.";
        choice local-identity {
          case ipv4-address {
            leaf ipv4-address {
              type union {
                type ntos-inet:ipv4-address;
                type empty;
              }
              description
                  "Set the IPsec local-identity ipv4-address.";
            }
          }
          case ipv6-address {
            leaf ipv6-address {
              type union {
                type ntos-inet:ipv6-address;
                type empty;
              }
              description
                  "Set the IPsec local-identity ipv6-address.";
            }
          }
          case fqdn {
            leaf fqdn {
              type string {
                length "1..253";
              }
              description
                  "Set the IPsec local-identity value of fully qualified domain name, length 1..253.";
            }
          }
          case user-fqdn {
            leaf user-fqdn {
              type string {
                length "1..255";
              }
              description
                  "Set the IPsec local-identity value of user fully qualified domain name, length 1..255.";
            }
          }
          case key-id {
            leaf key-id {
              type string {
                length "1..255";
              }
              description
                  "Set the IPsec local-identity value of key id, length 1..255.";
            }
          }
        }
      }

      leaf exchange-mode {
        type enumeration {
          enum main {
            description
              "Set main mode.";
          }
          enum aggressive {
            description
              "Set aggressive mode.";
          }
          enum auto {
            description
              "Set auto mode.";
          }
        }
        default "auto";
        description
          "Set IPsec exchange-mode.";
      }

      leaf ike-life-seconds {
        type uint32 {
          range "120..604800";
        }
        units second;
        default "86400";
        description
          "Set IKE SA life-seconds.";
      }

      leaf ipsec-life-seconds {
        type uint32 {
          range "60..604800";
        }
        units second;
        default "3600";
        description
          "Set IPsec SA life-seconds.";
      }

      leaf pfs {
        description
          "Set the DH groups of IPsec.";

        type enumeration {
          enum group1 {
            value 1;
            description
              "D-H Group1 (MODP768).";
          }
          enum group2 {
            value 2;
            description
              "D-H Group2 (MODP1024).";
          }
          enum group5 {
            value 5;
            description
              "D-H Group5 (MODP1536).";
          }
          enum group14 {
            value 14;
            description
              "D-H Group14 (MODP2048).";
          }
          enum group15 {
            value 15;
            description
              "D-H Group15 (MODP3072).";
          }
          enum group16 {
            value 16;
            description
              "D-H Group16 (MODP4096).";
          }
        }
      }

      leaf fragmentation-mtu {
        type uint32 {
          range "512..1500";
        }
        default "1400";
        description
          "Set IPsec mtu size.";
      }

      container dpd {
        description
          "Set IPsec dpd container.";
        leaf interval {
          type uint32 {
            range "10..3600";
          }
          default 30;
          units second;
          description
            "Set the IPsec DPD R-U-THERE interval.";
        }

        leaf retry-interval {
          type uint32 {
            range "2..10";
          }
          default 5;
          units second;
          description
            "Set the IPsec DPD Retry Interval.";
        }

        leaf type {
          type enumeration {
            enum periodic {
              description
                "Periodic send DPD queries at regular intervals.";
            }
            enum idle {
              description
                "idle mode.";
            }
          }
          default periodic;
          description
            "Set the IPsec type of DPD.";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-cmd "set vpn ipsec profile";
  }

  rpc delete-ipsec-config {
    input {
      leaf name {
        type string;
        mandatory true;
        description
          "The name of IPsec profile.";
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-cmd "delete vpn ipsec profile";
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Set IPSec configuration.";

    container ipsec {
      description
        "Configuration of ipsec.";

      uses profile-config;
      uses ipsec-proposal-config;
      uses proxyid-config;
      uses ipsec-global-config;
    }
  }
}
