package firewallflextrans

// 标准化的验证响应模型
type VerificationResponse struct {
	Success bool   `json:"success"`           // 验证是否成功
	Message string `json:"message"`           // 验证结果消息
	Details string `json:"details,omitempty"` // 详细信息（可选）
}

// 接口信息模型
type InterfaceInfo struct {
	Name        string                 `json:"name"`                  // 接口名称
	IP          string                 `json:"ip,omitempty"`          // IP地址
	Mask        string                 `json:"mask,omitempty"`        // 子网掩码
	Type        string                 `json:"type,omitempty"`        // 接口类型
	Status      string                 `json:"status,omitempty"`      // 接口状态
	Description string                 `json:"description,omitempty"` // 接口描述
	Attributes  map[string]interface{} `json:"attributes,omitempty"`  // 其他属性
}

// 标准化的接口提取响应模型
type InterfaceExtractionResponse struct {
	Success         bool                     `json:"success"`                  // 提取是否成功
	Message         string                   `json:"message"`                  // 提取结果消息
	Interfaces      []InterfaceInfo          `json:"interfaces"`               // 接口列表
	InterfacesMap   map[string]InterfaceInfo `json:"interfaces_map,omitempty"` // 接口映射
	InterfacesCount int                      `json:"interfaces_count"`         // 接口数量
	MappingJSON     string                   `json:"mapping_json,omitempty"`   // 接口映射JSON字符串
}

// 简化后的接口信息模型
type SimpleInterfaceInfo struct {
	Name           string `json:"name"`                  // 接口名称
	Zone           string `json:"zone"`                  // 区域，默认为NA
	IP             string `json:"ip"`                    // IP地址，对于DHCP和PPPoE模式分别显示为"DHCP"和"PPPoE"
	Mask           string `json:"mask"`                  // 子网掩码，对于DHCP和PPPoE模式显示为"NA"
	IsSubinterface bool   `json:"is_subinterface"`       // 是否为子接口
	Mode           string `json:"mode"`                  // 接口模式，如static、dhcp、pppoe等
	Allowaccess    string `json:"allowaccess,omitempty"` // 访问控制设置，如果有
	Type           string `json:"type"`                  // 接口类型
	Required       bool   `json:"required"`              // 是否为必需接口
	TypeName       string `json:"type_name"`             // 接口类型名称，国际化
}

// 简化后的接口提取响应模型
type SimpleInterfaceExtractionResponse struct {
	Interfaces []SimpleInterfaceInfo `json:"interfaces"` // 接口列表（使用数组格式）
	Count      int                   `json:"count"`      // 接口数量
}

// 路由信息模型
type RouteInfo struct {
	Destination string `json:"destination"` // 目标网络
	Gateway     string `json:"gateway"`     // 网关地址
	Interface   string `json:"interface"`   // 出接口
	Distance    int    `json:"distance"`    // 管理距离
	Metric      int    `json:"metric"`      // 度量值
	Type        string `json:"type"`        // 路由类型
}

// 安全区域信息模型
type ZoneInfo struct {
	Name        string   `json:"name"`        // 区域名称
	Description string   `json:"description"` // 区域描述
	Interfaces  []string `json:"interfaces"`  // 区域包含的接口
}

// 地址对象模型
type AddressObject struct {
	Name        string `json:"name"`        // 对象名称
	Type        string `json:"type"`        // 对象类型
	Value       string `json:"value"`       // 对象值
	Description string `json:"description"` // 对象描述
}

// 服务对象模型
type ServiceObject struct {
	Name        string `json:"name"`        // 服务名称
	Protocol    string `json:"protocol"`    // 协议
	SourcePort  string `json:"source_port"` // 源端口
	DestPort    string `json:"dest_port"`   // 目标端口
	Description string `json:"description"` // 服务描述
}

// 日志条目模型
type LogEntry struct {
	Time    string `json:"time"`             // 时间戳
	Level   string `json:"level"`            // 日志级别
	Message string `json:"message"`          // 日志消息
	Module  string `json:"module,omitempty"` // 日志模块
}

// 跳过的配置项
type SkippedItem struct {
	Type   string `json:"type"`             // 项目类型
	Name   string `json:"name"`             // 项目名称
	Reason string `json:"reason"`           // 跳过原因
	Detail string `json:"detail,omitempty"` // 详细信息
}

// 需要手动配置的项目
type ManualConfigItem struct {
	Type   string `json:"type"`             // 项目类型
	Name   string `json:"name"`             // 项目名称
	Reason string `json:"reason"`           // 原因
	Detail string `json:"detail,omitempty"` // 详细信息
}

// 转换总结信息
type ConversionSummary struct {
	TotalInterfaces      int                `json:"total_interfaces"`       // 总接口数
	MappedInterfaces     int                `json:"mapped_interfaces"`      // 已映射接口数
	UnmappedInterfaces   int                `json:"unmapped_interfaces"`    // 未映射接口数
	Zones                int                `json:"zones"`                  // 区域数
	StaticRoutes         int                `json:"static_routes"`          // 静态路由数
	AddressObjects       int                `json:"address_objects"`        // 地址对象数
	ServiceObjects       int                `json:"service_objects"`        // 服务对象数
	PolicyRules          int                `json:"policy_rules"`           // 策略规则数
	SkippedItems         []SkippedItem      `json:"skipped_items"`          // 跳过的配置项
	ManualConfigRequired []ManualConfigItem `json:"manual_config_required"` // 需要手动配置的项目
}

// 标准化的配置转换响应模型
type ConversionResponse struct {
	Success      bool                   `json:"success"`                 // 转换是否成功
	Message      string                 `json:"message"`                 // 转换结果消息
	OutputFile   string                 `json:"output_file,omitempty"`   // 输出文件路径
	ReportPath   string                 `json:"report_path,omitempty"`   // 报告文件路径
	ErrorDetails map[string]interface{} `json:"error_details,omitempty"` // 错误详情
	Logs         []LogEntry             `json:"logs,omitempty"`          // 详细日志
	Summary      *ConversionSummary     `json:"summary,omitempty"`       // 转换总结
}

// Python引擎原始响应解析器接口
type ResponseParser interface {
	Parse(rawData map[string]interface{}) (interface{}, error)
}

// 验证响应解析器
type VerificationResponseParser struct{}

// 接口提取响应解析器
type InterfaceExtractionResponseParser struct{}

// 配置转换响应解析器
type ConversionResponseParser struct{}
