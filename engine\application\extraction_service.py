# -*- coding: utf-8 -*-
"""
提取服务 - 应用服务层的接口信息提取协调器
保持与现有提取逻辑的完全兼容性
"""

import os
from typing import Dict, Any, List, Optional
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.infrastructure.config.config_manager import ConfigManager


class ExtractionService:
    """
    提取服务
    应用服务层的提取协调器，负责协调接口信息提取功能
    保持与现有extract函数的兼容性
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化提取服务
        
        Args:
            config_manager: 配置管理器实例，如果为None则创建新实例
        """
        self.config_manager = config_manager or ConfigManager()
        
        log(_("extraction_service.initialized"), "info")
    
    def extract_interfaces(self, cli_file: str, vendor: str = "fortigate", 
                          output_json: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        提取接口信息 - 委托给现有的提取逻辑
        
        Args:
            cli_file: 配置文件路径
            vendor: 厂商标识
            output_json: 输出JSON文件路径
            **kwargs: 其他参数
            
        Returns: Dict[str, Any]: 提取结果
        """
        try:
            # 验证厂商支持
            if not self.config_manager.is_vendor_supported(vendor):
                return {
                    "success": False,
                    "error": _("extraction_service.unsupported_vendor", vendor=vendor),
                    "interfaces": []
                }
            
            # 验证配置文件存在
            if not os.path.exists(cli_file):
                return {
                    "success": False,
                    "error": _("extraction_service.cli_file_not_found", file=cli_file),
                    "interfaces": []
                }
            
            # 委托给现有的extract模块
            from engine.extract import extract_interfaces as legacy_extract_interfaces
            
            result = legacy_extract_interfaces(
                cli_file=cli_file,
                vendor=vendor,
                output_json=output_json,
                **kwargs
            )
            
            # 增强结果信息
            if isinstance(result, dict):
                result["extraction_service_version"] = "2.0"
                result["architecture"] = "layered"
            
            log(_("extraction_service.extraction_completed"), "info")
            return result
            
        except Exception as e:
            error_msg = _("extraction_service.extraction_failed", error=str(e))
            log(error_msg, "error")
            return {
                "success": False,
                "error": error_msg,
                "interfaces": []
            }
    
    def get_supported_vendors(self) -> List[str]:
        """
        获取支持的厂商列表
        
        Returns: List[str]: 支持的厂商列表
        """
        return list(self.config_manager.get_config("supported_vendors", {}).keys())
    
    def get_extraction_capabilities(self) -> Dict[str, Any]:
        """
        获取当前提取能力信息
        
        Returns: Dict[str, Any]: 提取能力信息
        """
        return {
            "supported_vendors": self.get_supported_vendors(),
            "interface_extraction": True,
            "static_route_extraction": True,
            "policy_extraction": True,
            "service_extraction": True
        }
