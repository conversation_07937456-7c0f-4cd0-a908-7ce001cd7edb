package user

import (
	"irisAdminApi/application/middleware"

	"github.com/kataras/iris/v12"
)

var Party = func(party iris.Party) { // casbin for gorm                                                   // <- IMPORTANT, register the middleware.
	party.Post("/login", Login).Name = "登录"
	// party.Get("/login/sid", LoginBySid).Name = "SID登录"
	party.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) // 登录验证
	party.Get("/logout", Logout).Name = "退出"
	party.Get("/expire", Expire).Name = "刷新 token"
	party.Get("/clear", Clear).Name = "清空 token"
	party.Get("/profile", Profile).Name = "个人信息"
	party.Post("/change_avatar", ChangeAvatar).Name = "修改头像"
	party.Post("/profile", UpdateProfile).Name = "修改资料"
	party.Post("/change_password", ChangePassword).Name = "修改密码"
	party.Get("/token", GetUserToken).Name = "查看用户 Token"
	party.Post("/token", CreateUserToken).Name = "添加用户 Token"

	party.PartyFunc("/users", func(party iris.Party) {
		party.Get("/", GetUsers).Name = "用户列表"
		party.Get("/{id:uint}", GetUser).Name = "用户详情"
		party.Post("/", CreateUser).Name = "创建用户"
		party.Post("/{id:uint}", UpdateUser).Name = "编辑用户"
		party.Delete("/{id:uint}", DeleteUser).Name = "删除用户"
		// party.Post("/whitelist", CreateOrUpdateAutoAuditWhiteList).Name = "更新或者创建白名单"
	})
	party.PartyFunc("/groups", func(party iris.Party) {
		party.Get("/", GetGroups).Name = "用户组列表"
		party.Get("/{id:uint}", GetGroup).Name = "用户组详情"
		party.Post("/", CreateGroup).Name = "创建用户组"
		party.Post("/{id:uint}", UpdateGroup).Name = "编辑用户组"
		party.Delete("/{id:uint}", DeleteGroup).Name = "删除用户组"
	})
	party.PartyFunc("/departments", func(party iris.Party) {
		party.Get("/", GetDepartments).Name = "部门列表"
		party.Get("/{id:uint}", GetDepartment).Name = "部门详情"
		party.Post("/", CreateDepartment).Name = "创建部门"
		party.Post("/{id:uint}", UpdateDepartment).Name = "编辑部门"
		party.Delete("/{id:uint}", DeleteDepartment).Name = "删除部门"
		party.Get("/{id:uint}/groups", GetDeparmentGroups).Name = "部门组列表"
	})
	party.PartyFunc("/roles", func(party iris.Party) {
		party.Get("/", GetAllRoles).Name = "角色列表"
		party.Get("/{id:uint}", GetRole).Name = "角色详情"
		party.Post("/", CreateRole).Name = "创建角色"
		party.Post("/{id:uint}", UpdateRole).Name = "编辑角色"
		party.Delete("/{id:uint}", DeleteRole).Name = "删除角色"
	})
	party.PartyFunc("/perms", func(party iris.Party) {
		party.Get("/", GetAllPermissions).Name = "权限列表"
		party.Get("/{id:uint}", GetPermission).Name = "权限详情"
		party.Post("/", CreatePermission).Name = "创建权限"
		party.Post("/{id:uint}", UpdatePermission).Name = "编辑权限"
		party.Delete("/{id:uint}", DeletePermission).Name = "删除权限"
	})
}
