module ietf-netconf-with-defaults {
   namespace "urn:ietf:params:xml:ns:yang:ietf-netconf-with-defaults";
   prefix ncwd;

   import ietf-netconf { prefix nc; }

   organization
    "IETF NETCONF (Network Configuration Protocol) Working Group";

   contact
    "WG Web:   <http://tools.ietf.org/wg/netconf/>

     WG List:  <<EMAIL>>

     WG Chair: <PERSON>
               <<EMAIL>>

     WG Chair: <PERSON>hm<PERSON>
               <<EMAIL>>

     Editor: <PERSON>
             <<EMAIL>>

     Editor: <PERSON><PERSON><PERSON><PERSON>
             <<EMAIL>>";

   description
    "This module defines an extension to the NETCONF protocol
     that allows the NETCONF client to control how default
     values are handled by the server in particular NETCONF
     operations.

     Copyright (c) 2011 IETF Trust and the persons identified as
     the document authors.  All rights reserved.

     Redistribution and use in source and binary forms, with or
     without modification, is permitted pursuant to, and subject
     to the license terms contained in, the Simplified BSD License
     set forth in Section 4.c of the IETF Trust's Legal Provisions
     Relating to IETF Documents
     (http://trustee.ietf.org/license-info).

     This version of this YANG module is part of RFC 6243; see
     the RFC itself for full legal notices.";

   revision 2011-06-01 {
     description
       "Initial version.";
     reference
      "RFC 6243: With-defaults Capability for NETCONF";
   }

   typedef with-defaults-mode {
      description
        "Possible modes to report default data.";
      reference
         "RFC 6243; Section 3.";
      type enumeration {
         enum report-all {
             description
               "All default data is reported.";
             reference
               "RFC 6243; Section 3.1";
         }
         enum report-all-tagged {
             description
               "All default data is reported.
                Any nodes considered to be default data
                will contain a 'default' XML attribute,
                set to 'true' or '1'.";
             reference
               "RFC 6243; Section 3.4";
         }
         enum trim {
             description
               "Values are not reported if they contain the default.";
             reference
               "RFC 6243; Section 3.2";
         }
         enum explicit {
             description
               "Report values that contain the definition of
                explicitly set data.";
             reference
               "RFC 6243; Section 3.3";
         }
     }
   }

   grouping with-defaults-parameters {
     description
       "Contains the <with-defaults> parameter for control
        of defaults in NETCONF retrieval operations.";

     leaf with-defaults {
       description
         "The explicit defaults processing mode requested.";
       reference
         "RFC 6243; Section 4.5.1";

       type with-defaults-mode;
     }
   }

   // extending the get-config operation
   augment /nc:get-config/nc:input {
       description
         "Adds the <with-defaults> parameter to the
          input of the NETCONF <get-config> operation.";
       reference
         "RFC 6243; Section 4.5.1";

       uses with-defaults-parameters;
   }

   // extending the get operation
   augment /nc:get/nc:input {
       description
         "Adds the <with-defaults> parameter to
          the input of the NETCONF <get> operation.";
       reference
         "RFC 6243; Section 4.5.1";

       uses with-defaults-parameters;
   }

   // extending the copy-config operation
   augment /nc:copy-config/nc:input {
       description
         "Adds the <with-defaults> parameter to
          the input of the NETCONF <copy-config> operation.";
       reference
         "RFC 6243; Section 4.5.1";

       uses with-defaults-parameters;
   }

}
