module ntos-types {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:types";
  prefix ntos-types;

  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS yang types.";


  revision 2022-12-06 {
    description
      "Add ntos-ipv6-obj-description-type.";
  }

  revision 2021-10-25 {
    description
      "Add type for ntos object";
    reference "";
  }

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }


  identity RTG_AUTO_TRACKER {
    description
      "Auto-configured tracker identity for routing protocols.
       Each module that implements a tracker with an automatic configuration
       system for routing protocols must derive from it.";
  }

  identity ROUTE4_FRR_ID {
    description
      "Ipv4 routing protocol base identity.
       Each module that supports ipv4 routing must derive from it.";
  }

  identity ROUTE6_FRR_ID {
    description
      "Ipv6 routing protocol base identity.
       Each module that supports ipv6 routing must derive from it.";
  }

  identity SERVICE_LOG_ID {
    description
      "Log service base identity.
       Each module that supports logging must derive from it.";
  }

  identity DEBUG_SERVICE_ID {
    description
      "Debug service name base identity.
       Each service(process) that supports set debug level must drive from it.";
  }

  identity INTERFACE_TYPE {
    description
      "Interface type.
       Each module that implements an interface must derive from it.";
  }

  typedef mark {
    type string {
      pattern '0x[0-9a-fA-F]{1,8}';
      ntos-ext:nc-cli-shortdesc "<0x0-0xffffffff>";
    }
    description
      "Firewall mark.";
  }

  typedef mark-mask {
    type string {
      pattern '0x[0-9a-fA-F]{1,8}/0x[0-9a-fA-F]{1,8}';
      ntos-ext:nc-cli-shortdesc "<0x0-0xffffffff/0x0-0xffffffff>";
    }
    description
      "Firewall mark filter.";
  }

  typedef ifname {
    type string {
      length "1..15";
      pattern '[-A-Za-z0-9._@/]+' {
        error-message "Interface name can only contain characters from [-A-Za-z0-9._@/].";
      }
      pattern '.*[A-Za-z].*' {
        error-message "Interface name must at least contain one letter.";
      }
      ntos-ext:nc-cli-shortdesc "<ifname>";
    }
    description
      "An interface name.";
  }

  typedef ha-group {
    type string {
      length "1..128";
      pattern '[-A-Za-z0-9._@]+';
      ntos-ext:nc-cli-shortdesc "<ha-group>";
    }
    description
      "An high-availability group.";
  }

  typedef sftp-url {
    type string {
      pattern 'sftp://[^:]+:[^@]+@[^/:]+(:[0-9]+)?/.*';
      ntos-ext:nc-cli-shortdesc "<s**********************[:port]/path/to/file>";
    }
    description
      "An SFTP file URL.";
  }

  typedef scp-url {
    type string {
      pattern 'scp://[^:]+:[^@]+@[^/:]+(:[0-9]+)?/.*';
      ntos-ext:nc-cli-shortdesc "<scp://user:passwd@host[:port]/path/to/file>";
    }
    description
      "An SCP file URL.";
  }

  typedef http-url {
    type string {
      pattern 'https?://([^:]+:[^@]+@)?[^/:]+(:[0-9]+)?/.*';
      ntos-ext:nc-cli-shortdesc "<http[s]://[user:passwd@]host[:port]/path/to/file>";
    }
    description
      "An HTTP(S) file URL.";
  }

  typedef http6-url {
    type string {
      pattern 'https?://([^:/]+:[^@]+@)?(\[[0-9a-fA-F:]+\])(:[0-9]+)?/.*';
      ntos-ext:nc-cli-shortdesc "<http[s]://[user:passwd@]host[:port]/path/to/file>";
    }
    description
      "An IPv6 HTTP(S) file URL.";
  }

  typedef http-dual-stack-url {
    type union {
      type http-url;
      type http6-url;
    }
    description
      "An IPv4 or IPv6 HTTP(S) file URL.";
  }

  typedef ftp-url {
    type string {
      pattern 'ftp://[^:]+:[^@]+@[^/:]+(:[0-9]+)?/.*';
      ntos-ext:nc-cli-shortdesc "<**********************[:port]/path/to/file>";
    }
    description
      "An FTP file URL.";
  }

  typedef tftp-url {
    type string {
      pattern 'tftp://[^/:]+(:[0-9]+)?/.*';
      ntos-ext:nc-cli-shortdesc "<tftp://host[:port]/path/to/file>";
    }
    description
      "A TFTP file URL.";
  }

  typedef smtp-url {
    type string {
      pattern 'smtps?://([^:]+:[^@]+@)?[^/]+/[^@]+@.+';
      ntos-ext:nc-cli-shortdesc "<smtp[s]://[user:passwd@]host/<EMAIL>>";
    }
    description
      "An SMTP(S) email URL.";
  }

  typedef timeticks64 {
    type uint64;
    description
      "This type is based on the timeticks type defined in
       RFC 6991, but with 64-bit width.  It represents the time,
       modulo 2^64, in hundredths of a second between two epochs.";
    reference "RFC 6991 - Common YANG Data Types";
  }

  typedef counter32 {
    type uint32;
    description
      "A 32-bit counter. A counter value is a monotonically increasing
       value which is used to express a count of a number of
       occurrences of a particular event or entity. When a counter64
       reaches its maximum value, 2^32-1, it loops to zero.
       Discontinuities in a counter are generally triggered only when
       the counter is reset to zero, through operator or system
       intervention.";
  }

  typedef counter64 {
    type uint64;
    description
      "A 64-bit counter. A counter value is a monotonically increasing
       value which is used to express a count of a number of
       occurrences of a particular event or entity. When a counter64
       reaches its maximum value, 2^64-1, it loops to zero.
       Discontinuities in a counter are generally triggered only when
       the counter is reset to zero, through operator or system
       intervention.";
  }

  typedef date-and-time {
    type string {
      pattern '\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?(Z|[\+\-]\d{2}:\d{2})';
      ntos-ext:nc-cli-shortdesc "<YYYY-MM-DD>T<HH:MM:SS>[. MS]Z|(+|-)HH:MM";
    }
    description
      "The date-and-time type is a profile of the ISO 8601
       standard for representation of dates and times using the
       Gregorian calendar.  The profile is defined by the
       date-time production in Section 5.6 of RFC 3339.

       The date-and-time type is compatible with the dateTime XML
       schema type with the following notable exceptions:

       (a) The date-and-time type does not allow negative years.

       (b) The date-and-time time-offset -00:00 indicates an unknown
           time zone (see RFC 3339) while -00:00 and +00:00 and Z
           all represent the same time zone in dateTime.

       (c) The canonical format (see below) of data-and-time values
           differs from the canonical format used by the dateTime XML
           schema type, which requires all times to be in UTC using
           the time-offset 'Z'.

       This type is not equivalent to the DateAndTime textual
       convention of the SMIv2 since RFC 3339 uses a different
       separator between full-date and full-time and provides
       higher resolution of time-secfrac.

       The canonical format for date-and-time values with a known time
       zone uses a numeric time zone offset that is calculated using
       the device's configured known offset to UTC time.  A change of
       the device's offset to UTC time will cause date-and-time values
       to change accordingly.  Such changes might happen periodically
       in case a server follows automatically daylight saving time
       (DST) time zone offset changes.  The canonical format for
       date-and-time values with an unknown time zone (usually
       referring to the notion of local time) uses the time-offset
       -00:00.";
    reference
      "RFC 3339: Date and Time on the Internet: Timestamps
       RFC 2579: Textual Conventions for SMIv2
       XSD-TYPES: XML Schema Part 2: Datatypes Second Edition";
  }

  typedef coremask {
    type string {
      pattern '[0-9]+(-[0-9]+)?(,[0-9]+(-[0-9]+)?)*' {
        error-message "Invalid core list format. Example: '1,4-7,10-12'";
      }
      ntos-ext:nc-cli-shortdesc "<cores-list>";
    }
    description
      "A comma-separated list of cores or core ranges.
       Example: '1,4-7,10-12'.";
  }

  typedef log-level {
    type enumeration {
      enum emergency {
        description
          "System is unusable.";
      }
      enum alert {
        description
          "Action must be taken immediately.";
      }
      enum critical {
        description
          "Critical conditions.";
      }
      enum error {
        description
          "Error conditions.";
      }
      enum warning {
        description
          "Warning conditions.";
      }
      enum notice {
        description
          "Normal but significant condition.";
      }
      enum info {
        description
          "Informational messages.";
      }
      enum debug {
        description
          "Debug-level messages.";
      }
    }
    description
      "Logging level message levels.";
  }

  typedef cert-name {
    type string {
      length "1..32";
      pattern '[-A-Za-z0-9._@]+';
      ntos-ext:nc-cli-shortdesc "<cert-name>";
    }
    description
      "Certificate name.";
  }

  typedef ike-object-name {
    type string {
      pattern '[0-9A-Za-z!$&()*+/_|~-]+';
    }
    description
      "IKE object name type.";
  }

  typedef pci-port-name {
    type string {
      pattern 'pci-(d[0-9]+)?(b[0-9]+)(s[0-9]+)(f[0-9]+)?(p[0-9]+)?' {
        error-message "Invalid PCI port name.";
      }
      ntos-ext:nc-cli-shortdesc "<pci-port>";
    }
    description
      "PCI port name.";
  }

  typedef pci-bus-addr {
    type string {
      pattern '[0-9a-fA-F]{4}:[0-9a-fA-F]{2}:(0[1-9a-fA-F]|[1-9a-fA-F][0-9a-fA-F]).[0-7]' {
        error-message "Invalid PCI bus address.";
      }
      ntos-ext:nc-cli-shortdesc "<XXXX:XX:XX.X>";
    }
    description
      "PCI bus address.";
  }

  typedef device-tree-port-name {
    type string {
      pattern 'dt-(.+)' {
        error-message "Invalid device tree port name.";
      }
      ntos-ext:nc-cli-shortdesc "<device-tree-port>";
    }
    description
      "Device tree port name.";
  }

  typedef ntos-obj-description-type {
    type string {
      pattern "[^`~!#$%^&*+/|{};:\"\\\\<>?]*" {
        error-message "cannot include character: `~!#$%^&*+|{};:\"\\/<>?";
      }
    }
  }

  typedef ntos-ipv6-obj-description-type {
    type string {
      pattern "[^`~!#$%^&*+/|{};\"\\\\<>?]*" {
        error-message "cannot include character: `~!#$%^&*+|\\{};\"/<>?";
      }
    }
  }

  typedef ntos-obj-name-type {
    type string {
      pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
        error-message "cannot include character: `~!#$%^&*+|{};:\"',\\/<>?";
      }
    }
  }

  typedef linux-timezone {
    type enumeration {
      // enum "ACT" {
      //   value 0;
      // }
      // enum "AET" {
      //   value 1;
      // }
      enum "Africa/Abidjan" {
        value 2;
      }
      enum "Africa/Accra" {
        value 3;
      }
      enum "Africa/Addis_Ababa" {
        value 4;
      }
      enum "Africa/Algiers" {
        value 5;
      }
      enum "Africa/Asmara" {
        value 6;
      }
      enum "Africa/Bamako" {
        value 7;
      }
      enum "Africa/Bangui" {
        value 8;
      }
      enum "Africa/Banjul" {
        value 9;
      }
      enum "Africa/Bissau" {
        value 10;
      }
      enum "Africa/Blantyre" {
        value 11;
      }
      enum "Africa/Brazzaville" {
        value 12;
      }
      enum "Africa/Bujumbura" {
        value 13;
      }
      enum "Africa/Cairo" {
        value 14;
      }
      enum "Africa/Casablanca" {
        value 15;
      }
      enum "Africa/Ceuta" {
        value 16;
      }
      enum "Africa/Conakry" {
        value 17;
      }
      enum "Africa/Dakar" {
        value 18;
      }
      enum "Africa/Dar_es_Salaam" {
        value 19;
      }
      enum "Africa/Djibouti" {
        value 20;
      }
      enum "Africa/Douala" {
        value 21;
      }
      enum "Africa/El_Aaiun" {
        value 22;
      }
      enum "Africa/Freetown" {
        value 23;
      }
      enum "Africa/Gaborone" {
        value 24;
      }
      enum "Africa/Harare" {
        value 25;
      }
      enum "Africa/Johannesburg" {
        value 26;
      }
      enum "Africa/Juba" {
        value 27;
      }
      enum "Africa/Kampala" {
        value 28;
      }
      enum "Africa/Khartoum" {
        value 29;
      }
      enum "Africa/Kigali" {
        value 30;
      }
      enum "Africa/Kinshasa" {
        value 31;
      }
      enum "Africa/Lagos" {
        value 32;
      }
      enum "Africa/Libreville" {
        value 33;
      }
      enum "Africa/Lome" {
        value 34;
      }
      enum "Africa/Luanda" {
        value 35;
      }
      enum "Africa/Lubumbashi" {
        value 36;
      }
      enum "Africa/Lusaka" {
        value 37;
      }
      enum "Africa/Malabo" {
        value 38;
      }
      enum "Africa/Maputo" {
        value 39;
      }
      enum "Africa/Maseru" {
        value 40;
      }
      enum "Africa/Mbabane" {
        value 41;
      }
      enum "Africa/Mogadishu" {
        value 42;
      }
      enum "Africa/Monrovia" {
        value 43;
      }
      enum "Africa/Nairobi" {
        value 44;
      }
      enum "Africa/Ndjamena" {
        value 45;
      }
      enum "Africa/Niamey" {
        value 46;
      }
      enum "Africa/Nouakchott" {
        value 47;
      }
      enum "Africa/Ouagadougou" {
        value 48;
      }
      enum "Africa/Porto-Novo" {
        value 49;
      }
      enum "Africa/Sao_Tome" {
        value 50;
      }
      enum "Africa/Timbuktu" {
        value 51;
      }
      enum "Africa/Tripoli" {
        value 52;
      }
      enum "Africa/Tunis" {
        value 53;
      }
      enum "Africa/Windhoek" {
        value 54;
      }
      enum "America/Adak" {
        value 55;
      }
      enum "America/Anchorage" {
        value 56;
      }
      enum "America/Anguilla" {
        value 57;
      }
      enum "America/Antigua" {
        value 58;
      }
      enum "America/Araguaina" {
        value 59;
      }
      enum "America/Argentina/Buenos_Aires" {
        value 60;
      }
      enum "America/Argentina/Catamarca" {
        value 61;
      }
      enum "America/Argentina/ComodRivadavia" {
        value 62;
      }
      enum "America/Argentina/Cordoba" {
        value 63;
      }
      enum "America/Argentina/Jujuy" {
        value 64;
      }
      enum "America/Argentina/La_Rioja" {
        value 65;
      }
      enum "America/Argentina/Mendoza" {
        value 66;
      }
      enum "America/Argentina/Rio_Gallegos" {
        value 67;
      }
      enum "America/Argentina/Salta" {
        value 68;
      }
      enum "America/Argentina/San_Juan" {
        value 69;
      }
      enum "America/Argentina/San_Luis" {
        value 70;
      }
      enum "America/Argentina/Tucuman" {
        value 71;
      }
      enum "America/Argentina/Ushuaia" {
        value 72;
      }
      enum "America/Aruba" {
        value 73;
      }
      enum "America/Asuncion" {
        value 74;
      }
      enum "America/Atikokan" {
        value 75;
      }
      enum "America/Atka" {
        value 76;
      }
      enum "America/Bahia" {
        value 77;
      }
      enum "America/Bahia_Banderas" {
        value 78;
      }
      enum "America/Barbados" {
        value 79;
      }
      enum "America/Belem" {
        value 80;
      }
      enum "America/Belize" {
        value 81;
      }
      enum "America/Blanc-Sablon" {
        value 82;
      }
      enum "America/Boa_Vista" {
        value 83;
      }
      enum "America/Bogota" {
        value 84;
      }
      enum "America/Boise" {
        value 85;
      }
      enum "America/Buenos_Aires" {
        value 86;
      }
      enum "America/Cambridge_Bay" {
        value 87;
      }
      enum "America/Campo_Grande" {
        value 88;
      }
      enum "America/Cancun" {
        value 89;
      }
      enum "America/Caracas" {
        value 90;
      }
      enum "America/Catamarca" {
        value 91;
      }
      enum "America/Cayenne" {
        value 92;
      }
      enum "America/Cayman" {
        value 93;
      }
      enum "America/Chicago" {
        value 94;
      }
      enum "America/Chihuahua" {
        value 95;
      }
      enum "America/Coral_Harbour" {
        value 96;
      }
      enum "America/Cordoba" {
        value 97;
      }
      enum "America/Costa_Rica" {
        value 98;
      }
      enum "America/Creston" {
        value 99;
      }
      enum "America/Cuiaba" {
        value 100;
      }
      enum "America/Curacao" {
        value 101;
      }
      enum "America/Danmarkshavn" {
        value 102;
      }
      enum "America/Dawson" {
        value 103;
      }
      enum "America/Dawson_Creek" {
        value 104;
      }
      enum "America/Denver" {
        value 105;
      }
      enum "America/Detroit" {
        value 106;
      }
      enum "America/Dominica" {
        value 107;
      }
      enum "America/Edmonton" {
        value 108;
      }
      enum "America/Eirunepe" {
        value 109;
      }
      enum "America/El_Salvador" {
        value 110;
      }
      enum "America/Ensenada" {
        value 111;
      }
      enum "America/Fort_Wayne" {
        value 112;
      }
      enum "America/Fortaleza" {
        value 113;
      }
      enum "America/Glace_Bay" {
        value 114;
      }
      enum "America/Godthab" {
        value 115;
      }
      enum "America/Goose_Bay" {
        value 116;
      }
      enum "America/Grand_Turk" {
        value 117;
      }
      enum "America/Grenada" {
        value 118;
      }
      enum "America/Guadeloupe" {
        value 119;
      }
      enum "America/Guatemala" {
        value 120;
      }
      enum "America/Guayaquil" {
        value 121;
      }
      enum "America/Guyana" {
        value 122;
      }
      enum "America/Halifax" {
        value 123;
      }
      enum "America/Havana" {
        value 124;
      }
      enum "America/Hermosillo" {
        value 125;
      }
      enum "America/Indiana/Indianapolis" {
        value 126;
      }
      enum "America/Indiana/Knox" {
        value 127;
      }
      enum "America/Indiana/Marengo" {
        value 128;
      }
      enum "America/Indiana/Petersburg" {
        value 129;
      }
      enum "America/Indiana/Tell_City" {
        value 130;
      }
      enum "America/Indiana/Vevay" {
        value 131;
      }
      enum "America/Indiana/Vincennes" {
        value 132;
      }
      enum "America/Indiana/Winamac" {
        value 133;
      }
      enum "America/Indianapolis" {
        value 134;
      }
      enum "America/Inuvik" {
        value 135;
      }
      enum "America/Iqaluit" {
        value 136;
      }
      enum "America/Jamaica" {
        value 137;
      }
      enum "America/Jujuy" {
        value 138;
      }
      enum "America/Juneau" {
        value 139;
      }
      enum "America/Kentucky/Louisville" {
        value 140;
      }
      enum "America/Kentucky/Monticello" {
        value 141;
      }
      enum "America/Knox_IN" {
        value 142;
      }
      enum "America/Kralendijk" {
        value 143;
      }
      enum "America/La_Paz" {
        value 144;
      }
      enum "America/Lima" {
        value 145;
      }
      enum "America/Los_Angeles" {
        value 146;
      }
      enum "America/Louisville" {
        value 147;
      }
      enum "America/Lower_Princes" {
        value 148;
      }
      enum "America/Maceio" {
        value 149;
      }
      enum "America/Managua" {
        value 150;
      }
      enum "America/Manaus" {
        value 151;
      }
      enum "America/Marigot" {
        value 152;
      }
      enum "America/Martinique" {
        value 153;
      }
      enum "America/Matamoros" {
        value 154;
      }
      enum "America/Mazatlan" {
        value 155;
      }
      enum "America/Mendoza" {
        value 156;
      }
      enum "America/Menominee" {
        value 157;
      }
      enum "America/Merida" {
        value 158;
      }
      enum "America/Metlakatla" {
        value 159;
      }
      enum "America/Mexico_City" {
        value 160;
      }
      enum "America/Miquelon" {
        value 161;
      }
      enum "America/Moncton" {
        value 162;
      }
      enum "America/Monterrey" {
        value 163;
      }
      enum "America/Montevideo" {
        value 164;
      }
      enum "America/Montreal" {
        value 165;
      }
      enum "America/Montserrat" {
        value 166;
      }
      enum "America/Nassau" {
        value 167;
      }
      enum "America/New_York" {
        value 168;
      }
      enum "America/Nipigon" {
        value 169;
      }
      enum "America/Nome" {
        value 170;
      }
      enum "America/Noronha" {
        value 171;
      }
      enum "America/North_Dakota/Beulah" {
        value 172;
      }
      enum "America/North_Dakota/Center" {
        value 173;
      }
      enum "America/North_Dakota/New_Salem" {
        value 174;
      }
      enum "America/Ojinaga" {
        value 175;
      }
      enum "America/Panama" {
        value 176;
      }
      enum "America/Pangnirtung" {
        value 177;
      }
      enum "America/Paramaribo" {
        value 178;
      }
      enum "America/Phoenix" {
        value 179;
      }
      enum "America/Port_of_Spain" {
        value 180;
      }
      enum "America/Port-au-Prince" {
        value 181;
      }
      enum "America/Porto_Acre" {
        value 182;
      }
      enum "America/Porto_Velho" {
        value 183;
      }
      enum "America/Puerto_Rico" {
        value 184;
      }
      enum "America/Rainy_River" {
        value 185;
      }
      enum "America/Rankin_Inlet" {
        value 186;
      }
      enum "America/Recife" {
        value 187;
      }
      enum "America/Regina" {
        value 188;
      }
      enum "America/Resolute" {
        value 189;
      }
      enum "America/Rio_Branco" {
        value 190;
      }
      enum "America/Rosario" {
        value 191;
      }
      enum "America/Santa_Isabel" {
        value 192;
      }
      enum "America/Santarem" {
        value 193;
      }
      enum "America/Santiago" {
        value 194;
      }
      enum "America/Santo_Domingo" {
        value 195;
      }
      enum "America/Sao_Paulo" {
        value 196;
      }
      enum "America/Scoresbysund" {
        value 197;
      }
      enum "America/Shiprock" {
        value 198;
      }
      enum "America/Sitka" {
        value 199;
      }
      enum "America/St_Barthelemy" {
        value 200;
      }
      enum "America/St_Johns" {
        value 201;
      }
      enum "America/St_Kitts" {
        value 202;
      }
      enum "America/St_Lucia" {
        value 203;
      }
      enum "America/St_Thomas" {
        value 204;
      }
      enum "America/St_Vincent" {
        value 205;
      }
      enum "America/Swift_Current" {
        value 206;
      }
      enum "America/Tegucigalpa" {
        value 207;
      }
      enum "America/Thule" {
        value 208;
      }
      enum "America/Thunder_Bay" {
        value 209;
      }
      enum "America/Tijuana" {
        value 210;
      }
      enum "America/Toronto" {
        value 211;
      }
      enum "America/Tortola" {
        value 212;
      }
      enum "America/Vancouver" {
        value 213;
      }
      enum "America/Virgin" {
        value 214;
      }
      enum "America/Whitehorse" {
        value 215;
      }
      enum "America/Winnipeg" {
        value 216;
      }
      enum "America/Yakutat" {
        value 217;
      }
      enum "America/Yellowknife" {
        value 218;
      }
      enum "Antarctica/Casey" {
        value 219;
      }
      enum "Antarctica/Davis" {
        value 220;
      }
      enum "Antarctica/DumontDUrville" {
        value 221;
      }
      enum "Antarctica/Macquarie" {
        value 222;
      }
      enum "Antarctica/Mawson" {
        value 223;
      }
      enum "Antarctica/McMurdo" {
        value 224;
      }
      enum "Antarctica/Palmer" {
        value 225;
      }
      enum "Antarctica/Rothera" {
        value 226;
      }
      enum "Antarctica/South_Pole" {
        value 227;
      }
      enum "Antarctica/Syowa" {
        value 228;
      }
      enum "Antarctica/Troll" {
        value 229;
      }
      enum "Antarctica/Vostok" {
        value 230;
      }
      enum "Arctic/Longyearbyen" {
        value 231;
      }
      enum "Asia/Aden" {
        value 232;
      }
      enum "Asia/Almaty" {
        value 233;
      }
      enum "Asia/Amman" {
        value 234;
      }
      enum "Asia/Anadyr" {
        value 235;
      }
      enum "Asia/Aqtau" {
        value 236;
      }
      enum "Asia/Aqtobe" {
        value 237;
      }
      enum "Asia/Ashgabat" {
        value 238;
      }
      enum "Asia/Baghdad" {
        value 239;
      }
      enum "Asia/Bahrain" {
        value 240;
      }
      enum "Asia/Baku" {
        value 241;
      }
      enum "Asia/Bangkok" {
        value 242;
      }
      enum "Asia/Beirut" {
        value 243;
      }
      enum "Asia/Bishkek" {
        value 244;
      }
      enum "Asia/Brunei" {
        value 245;
      }
      enum "Asia/Calcutta" {
        value 246;
      }
      enum "Asia/Choibalsan" {
        value 247;
      }
      enum "Asia/Chongqing" {
        value 248;
      }
      enum "Asia/Colombo" {
        value 249;
      }
      enum "Asia/Dacca" {
        value 250;
      }
      enum "Asia/Damascus" {
        value 251;
      }
      enum "Asia/Dili" {
        value 252;
      }
      enum "Asia/Dubai" {
        value 253;
      }
      enum "Asia/Dushanbe" {
        value 254;
      }
      enum "Asia/Gaza" {
        value 255;
      }
      enum "Asia/Harbin" {
        value 256;
      }
      enum "Asia/Hebron" {
        value 257;
      }
      enum "Asia/Ho_Chi_Minh" {
        value 258;
      }
      enum "Asia/Hong_Kong" {
        value 259;
      }
      enum "Asia/Hovd" {
        value 260;
      }
      enum "Asia/Irkutsk" {
        value 261;
      }
      enum "Asia/Istanbul" {
        value 262;
      }
      enum "Asia/Jakarta" {
        value 263;
      }
      enum "Asia/Jayapura" {
        value 264;
      }
      enum "Asia/Jerusalem" {
        value 265;
      }
      enum "Asia/Kabul" {
        value 266;
      }
      enum "Asia/Kamchatka" {
        value 267;
      }
      enum "Asia/Karachi" {
        value 268;
      }
      enum "Asia/Kashgar" {
        value 269;
      }
      enum "Asia/Kathmandu" {
        value 270;
      }
      enum "Asia/Khandyga" {
        value 271;
      }
      enum "Asia/Kolkata" {
        value 272;
      }
      enum "Asia/Krasnoyarsk" {
        value 273;
      }
      enum "Asia/Kuala_Lumpur" {
        value 274;
      }
      enum "Asia/Kuching" {
        value 275;
      }
      enum "Asia/Kuwait" {
        value 276;
      }
      enum "Asia/Macau" {
        value 277;
      }
      enum "Asia/Magadan" {
        value 278;
      }
      enum "Asia/Makassar" {
        value 279;
      }
      enum "Asia/Manila" {
        value 280;
      }
      enum "Asia/Muscat" {
        value 281;
      }
      enum "Asia/Nicosia" {
        value 282;
      }
      enum "Asia/Novokuznetsk" {
        value 283;
      }
      enum "Asia/Novosibirsk" {
        value 284;
      }
      enum "Asia/Omsk" {
        value 285;
      }
      enum "Asia/Oral" {
        value 286;
      }
      enum "Asia/Phnom_Penh" {
        value 287;
      }
      enum "Asia/Pontianak" {
        value 288;
      }
      enum "Asia/Pyongyang" {
        value 289;
      }
      enum "Asia/Qatar" {
        value 290;
      }
      enum "Asia/Qyzylorda" {
        value 291;
      }
      enum "Asia/Riyadh" {
        value 292;
      }
      // enum "Asia/Riyadh87" {
      //   value 293;
      // }
      // enum "Asia/Riyadh88" {
      //   value 294;
      // }
      // enum "Asia/Riyadh89" {
      //   value 295;
      // }
      enum "Asia/Saigon" {
        value 296;
      }
      enum "Asia/Sakhalin" {
        value 297;
      }
      enum "Asia/Samarkand" {
        value 298;
      }
      enum "Asia/Seoul" {
        value 299;
      }
      enum "Asia/Shanghai" {
        value 300;
      }
      enum "Asia/Singapore" {
        value 301;
      }
      enum "Asia/Srednekolymsk" {
        value 302;
      }
      enum "Asia/Taipei" {
        value 303;
      }
      enum "Asia/Tashkent" {
        value 304;
      }
      enum "Asia/Tbilisi" {
        value 305;
      }
      enum "Asia/Tehran" {
        value 306;
      }
      enum "Asia/Tel_Aviv" {
        value 307;
      }
      enum "Asia/Thimbu" {
        value 308;
      }
      enum "Asia/Tokyo" {
        value 309;
      }
      enum "Asia/Ujung_Pandang" {
        value 310;
      }
      enum "Asia/Ulaanbaatar" {
        value 311;
      }
      enum "Asia/Urumqi" {
        value 312;
      }
      enum "Asia/Ust-Nera" {
        value 313;
      }
      enum "Asia/Vientiane" {
        value 314;
      }
      enum "Asia/Vladivostok" {
        value 315;
      }
      enum "Asia/Yakutsk" {
        value 316;
      }
      enum "Asia/Yangon" {
        value 317;
      }
      enum "Asia/Yekaterinburg" {
        value 318;
      }
      enum "Asia/Yerevan" {
        value 319;
      }
      enum "Atlantic/Azores" {
        value 320;
      }
      enum "Atlantic/Bermuda" {
        value 321;
      }
      enum "Atlantic/Canary" {
        value 322;
      }
      enum "Atlantic/Cape_Verde" {
        value 323;
      }
      enum "Atlantic/Faeroe" {
        value 324;
      }
      enum "Atlantic/Jan_Mayen" {
        value 325;
      }
      enum "Atlantic/Madeira" {
        value 326;
      }
      enum "Atlantic/Reykjavik" {
        value 327;
      }
      enum "Atlantic/South_Georgia" {
        value 328;
      }
      enum "Atlantic/St_Helena" {
        value 329;
      }
      enum "Atlantic/Stanley" {
        value 330;
      }
      enum "Australia/ACT" {
        value 331;
      }
      enum "Australia/Adelaide" {
        value 332;
      }
      enum "Australia/Brisbane" {
        value 333;
      }
      enum "Australia/Broken_Hill" {
        value 334;
      }
      enum "Australia/Canberra" {
        value 335;
      }
      enum "Australia/Currie" {
        value 336;
      }
      enum "Australia/Darwin" {
        value 337;
      }
      enum "Australia/Eucla" {
        value 338;
      }
      enum "Australia/Hobart" {
        value 339;
      }
      enum "Australia/LHI" {
        value 340;
      }
      enum "Australia/Lindeman" {
        value 341;
      }
      enum "Australia/Lord_Howe" {
        value 342;
      }
      enum "Australia/Melbourne" {
        value 343;
      }
      enum "Australia/North" {
        value 344;
      }
      enum "Australia/NSW" {
        value 345;
      }
      enum "Australia/Perth" {
        value 346;
      }
      enum "Australia/Queensland" {
        value 347;
      }
      enum "Australia/South" {
        value 348;
      }
      enum "Australia/Sydney" {
        value 349;
      }
      enum "Australia/Tasmania" {
        value 350;
      }
      enum "Australia/Victoria" {
        value 351;
      }
      enum "Australia/West" {
        value 352;
      }
      enum "Australia/Yancowinna" {
        value 353;
      }
      enum "Brazil/Acre" {
        value 354;
      }
      enum "Brazil/DeNoronha" {
        value 355;
      }
      enum "Brazil/East" {
        value 356;
      }
      enum "Brazil/West" {
        value 357;
      }
      enum "Canada/Atlantic" {
        value 358;
      }
      enum "Canada/Central" {
        value 359;
      }
      enum "Canada/Eastern" {
        value 360;
      }
      // enum "Canada/East-Saskatchewan" {
      //   value 361;
      // }
      enum "Canada/Mountain" {
        value 362;
      }
      enum "Canada/Newfoundland" {
        value 363;
      }
      enum "Canada/Pacific" {
        value 364;
      }
      enum "Canada/Saskatchewan" {
        value 365;
      }
      enum "Canada/Yukon" {
        value 366;
      }
      enum "Chile/Continental" {
        value 367;
      }
      enum "Chile/EasterIsland" {
        value 368;
      }
      enum "Cuba" {
        value 369;
      }
      enum "EET" {
        value 370;
      }
      enum "Egypt" {
        value 371;
      }
      enum "Eire" {
        value 372;
      }
      enum "EST" {
        value 373;
      }
      enum "Etc/Greenwich" {
        value 374;
      }
      enum "Etc/UCT" {
        value 375;
      }
      enum "Etc/Universal" {
        value 376;
      }
      enum "Etc/UTC" {
        value 377;
      }
      enum "Etc/Zulu" {
        value 378;
      }
      enum "Europe/Amsterdam" {
        value 379;
      }
      enum "Europe/Andorra" {
        value 380;
      }
      enum "Europe/Athens" {
        value 381;
      }
      enum "Europe/Belfast" {
        value 382;
      }
      enum "Europe/Belgrade" {
        value 383;
      }
      enum "Europe/Berlin" {
        value 384;
      }
      enum "Europe/Bratislava" {
        value 385;
      }
      enum "Europe/Brussels" {
        value 386;
      }
      enum "Europe/Bucharest" {
        value 387;
      }
      enum "Europe/Budapest" {
        value 388;
      }
      enum "Europe/Busingen" {
        value 389;
      }
      enum "Europe/Chisinau" {
        value 390;
      }
      enum "Europe/Copenhagen" {
        value 391;
      }
      enum "Europe/Dublin" {
        value 392;
      }
      enum "Europe/Gibraltar" {
        value 393;
      }
      enum "Europe/Guernsey" {
        value 394;
      }
      enum "Europe/Helsinki" {
        value 395;
      }
      enum "Europe/Isle_of_Man" {
        value 396;
      }
      enum "Europe/Istanbul" {
        value 397;
      }
      enum "Europe/Jersey" {
        value 398;
      }
      enum "Europe/Kaliningrad" {
        value 399;
      }
      enum "Europe/Kiev" {
        value 400;
      }
      enum "Europe/Lisbon" {
        value 401;
      }
      enum "Europe/Ljubljana" {
        value 402;
      }
      enum "Europe/London" {
        value 403;
      }
      enum "Europe/Luxembourg" {
        value 404;
      }
      enum "Europe/Madrid" {
        value 405;
      }
      enum "Europe/Malta" {
        value 406;
      }
      enum "Europe/Mariehamn" {
        value 407;
      }
      enum "Europe/Minsk" {
        value 408;
      }
      enum "Europe/Monaco" {
        value 409;
      }
      enum "Europe/Moscow" {
        value 410;
      }
      enum "Europe/Nicosia" {
        value 411;
      }
      enum "Europe/Oslo" {
        value 412;
      }
      enum "Europe/Paris" {
        value 413;
      }
      enum "Europe/Podgorica" {
        value 414;
      }
      enum "Europe/Prague" {
        value 415;
      }
      enum "Europe/Riga" {
        value 416;
      }
      enum "Europe/Rome" {
        value 417;
      }
      enum "Europe/Samara" {
        value 418;
      }
      enum "Europe/San_Marino" {
        value 419;
      }
      enum "Europe/Sarajevo" {
        value 420;
      }
      enum "Europe/Simferopol" {
        value 421;
      }
      enum "Europe/Skopje" {
        value 422;
      }
      enum "Europe/Sofia" {
        value 423;
      }
      enum "Europe/Stockholm" {
        value 424;
      }
      enum "Europe/Tallinn" {
        value 425;
      }
      enum "Europe/Tirane" {
        value 426;
      }
      enum "Europe/Tiraspol" {
        value 427;
      }
      enum "Europe/Uzhgorod" {
        value 428;
      }
      enum "Europe/Vaduz" {
        value 429;
      }
      enum "Europe/Vatican" {
        value 430;
      }
      enum "Europe/Vienna" {
        value 431;
      }
      enum "Europe/Vilnius" {
        value 432;
      }
      enum "Europe/Volgograd" {
        value 433;
      }
      enum "Europe/Warsaw" {
        value 434;
      }
      enum "Europe/Zagreb" {
        value 435;
      }
      enum "Europe/Zaporozhye" {
        value 436;
      }
      enum "Europe/Zurich" {
        value 437;
      }
      enum "GB-Eire" {
        value 438;
      }
      enum "GMT" {
        value 439;
      }
      enum "Greenwich" {
        value 440;
      }
      enum "Iceland" {
        value 441;
      }
      enum "Indian/Antananarivo" {
        value 442;
      }
      enum "Indian/Chagos" {
        value 443;
      }
      enum "Indian/Christmas" {
        value 444;
      }
      enum "Indian/Cocos" {
        value 445;
      }
      enum "Indian/Comoro" {
        value 446;
      }
      enum "Indian/Kerguelen" {
        value 447;
      }
      enum "Indian/Mahe" {
        value 448;
      }
      enum "Indian/Maldives" {
        value 449;
      }
      enum "Indian/Mauritius" {
        value 450;
      }
      enum "Indian/Mayotte" {
        value 451;
      }
      enum "Indian/Reunion" {
        value 452;
      }
      enum "Iran" {
        value 453;
      }
      enum "Israel" {
        value 454;
      }
      // enum "IST" {
      //   value 455;
      // }
      enum "Jamaica" {
        value 456;
      }
      enum "Libya" {
        value 457;
      }
      enum "Mexico/BajaNorte" {
        value 458;
      }
      enum "Mexico/BajaSur" {
        value 459;
      }
      enum "Mexico/General" {
        value 460;
      }
      enum "Navajo" {
        value 461;
      }
      enum "Pacific/Auckland" {
        value 462;
      }
      enum "Pacific/Bougainville" {
        value 463;
      }
      enum "Pacific/Chatham" {
        value 464;
      }
      enum "Pacific/Chuuk" {
        value 465;
      }
      enum "Pacific/Easter" {
        value 466;
      }
      enum "Pacific/Efate" {
        value 467;
      }
      enum "Pacific/Fiji" {
        value 468;
      }
      enum "Pacific/Funafuti" {
        value 469;
      }
      enum "Pacific/Galapagos" {
        value 470;
      }
      enum "Pacific/Gambier" {
        value 471;
      }
      enum "Pacific/Guadalcanal" {
        value 472;
      }
      enum "Pacific/Guam" {
        value 473;
      }
      enum "Pacific/Honolulu" {
        value 474;
      }
      enum "Pacific/Johnston" {
        value 475;
      }
      enum "Pacific/Kosrae" {
        value 476;
      }
      enum "Pacific/Kwajalein" {
        value 477;
      }
      enum "Pacific/Majuro" {
        value 478;
      }
      enum "Pacific/Marquesas" {
        value 479;
      }
      enum "Pacific/Midway" {
        value 480;
      }
      enum "Pacific/Nauru" {
        value 481;
      }
      enum "Pacific/Niue" {
        value 482;
      }
      enum "Pacific/Norfolk" {
        value 483;
      }
      enum "Pacific/Noumea" {
        value 484;
      }
      enum "Pacific/Pago_Pago" {
        value 485;
      }
      enum "Pacific/Palau" {
        value 486;
      }
      enum "Pacific/Pitcairn" {
        value 487;
      }
      enum "Pacific/Pohnpei" {
        value 488;
      }
      enum "Pacific/Ponape" {
        value 489;
      }
      enum "Pacific/Port_Moresby" {
        value 490;
      }
      enum "Pacific/Rarotonga" {
        value 491;
      }
      enum "Pacific/Saipan" {
        value 492;
      }
      enum "Pacific/Samoa" {
        value 493;
      }
      enum "Pacific/Tahiti" {
        value 494;
      }
      enum "Pacific/Tarawa" {
        value 495;
      }
      enum "Pacific/Wake" {
        value 496;
      }
      enum "Pacific/Wallis" {
        value 497;
      }
      enum "Pacific/Yap" {
        value 498;
      }
      enum "Poland" {
        value 499;
      }
      enum "Portugal" {
        value 500;
      }
      enum "PRC" {
        value 501;
      }
      // enum "PST" {
      //   value 502;
      // }
      enum "ROK" {
        value 503;
      }
      enum "Singapore" {
        value 504;
      }
      enum "Turkey" {
        value 505;
      }
      enum "UCT" {
        value 506;
      }
      enum "Universal" {
        value 507;
      }
      enum "US/Alaska" {
        value 508;
      }
      enum "US/Aleutian" {
        value 509;
      }
      enum "US/Arizona" {
        value 510;
      }
      enum "US/Central" {
        value 511;
      }
      enum "US/Eastern" {
        value 512;
      }
      enum "US/East-Indiana" {
        value 513;
      }
      enum "US/Hawaii" {
        value 514;
      }
      enum "US/Indiana-Starke" {
        value 515;
      }
      enum "US/Michigan" {
        value 516;
      }
      enum "US/Mountain" {
        value 517;
      }
      enum "US/Pacific" {
        value 518;
      }
      // enum "US/Pacific-New" {
      //   value 519;
      // }
      enum "US/Samoa" {
        value 520;
      }
      enum "UTC" {
        value 521;
      }
      enum "Zulu" {
        value 522;
      }
    }
    description
      "A timezone location as defined by linux";
  }
}
