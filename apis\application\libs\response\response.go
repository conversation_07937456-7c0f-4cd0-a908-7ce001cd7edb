package response

import (
	"irisAdminApi/application/libs/i18n"

	"github.com/kataras/iris/v12"
)

type Response struct {
	Code int64       `json:"code"`
	Msg  interface{} `json:"message"`
	Data interface{} `json:"data"`
}

type MoreResponse struct {
	Data  interface{} `json:"data"`
	Total int         `json:"total"`
}

// NewResponseI18n 创建国际化响应
func NewResponseI18n(ctx iris.Context, code int64, objects interface{}, messageID string, args ...interface{}) *Response {
	// 从上下文获取语言
	lang := ctx.Values().GetString("language")
	if lang == "" {
		lang = i18n.GetDefaultLang()
	}

	// 调试日志
	ctx.Application().Logger().Debugf("NewResponseI18n - 语言: %s, 消息ID: %s", lang, messageID)

	// 准备模板数据
	templateData := make(map[string]interface{})
	for i := 0; i < len(args); i += 2 {
		if i+1 < len(args) {
			key, ok := args[i].(string)
			if ok {
				templateData[key] = args[i+1]
			}
		}
	}

	// 获取翻译消息
	msg := i18n.Translate(lang, messageID, templateData)

	// 调试日志
	ctx.Application().Logger().Debugf("NewResponseI18n - 翻译结果: %s", msg)

	return &Response{Code: code, Data: objects, Msg: msg}
}

// 保留原有的NewResponse方法以兼容现有代码
func NewResponse(code int64, objects interface{}, msg string) *Response {
	return &Response{Code: code, Data: objects, Msg: msg}
}

// 原有的错误消息结构
type ErrMsg struct {
	Code int64
	Msg  string
}

// 新的错误码结构，使用消息ID
type ErrCode struct {
	Code  int64
	MsgID string // 使用消息ID而不是直接的消息
}

// 原有的错误消息定义，保持兼容性
var (
	NoErr             = ErrMsg{20000, "请求成功"}
	AuthNameOrPassErr = ErrMsg{50007, "用户名密码错误"}
	AuthErr           = ErrMsg{50008, "Token 无效"}
	AuthExpireErr     = ErrMsg{50014, "Token 过期"}
	AuthActionErr     = ErrMsg{4003, "权限错误"}
	SystemErr         = ErrMsg{5000, "系统错误，请联系管理员"}
	DataEmptyErr      = ErrMsg{5001, "数据为空"}
	TokenCacheErr     = ErrMsg{50012, "TOKEN CACHE 错误"}
	ParamentErr       = ErrMsg{30000, "参数错误"}
	DuplicateErr      = ErrMsg{5002, "存在重复记录"}
	PermitErr         = ErrMsg{5003, "禁止访问"}
	FileNotExistsErr  = ErrMsg{5004, "文件不存在"}
)

// 国际化错误码定义
var (
	I18nNoErr             = ErrCode{20000, "common.success"}
	I18nAuthNameOrPassErr = ErrCode{50007, "auth.name_pass_error"}
	I18nAuthErr           = ErrCode{50008, "auth.token_invalid"}
	I18nAuthExpireErr     = ErrCode{50014, "auth.token_expired"}
	I18nAuthActionErr     = ErrCode{4003, "auth.permission_error"}
	I18nSystemErr         = ErrCode{5000, "system.error"}
	I18nDataEmptyErr      = ErrCode{5001, "data.empty"}
	I18nTokenCacheErr     = ErrCode{50012, "auth.token_cache_error"}
	I18nParamentErr       = ErrCode{30000, "param.error"}
	I18nDuplicateErr      = ErrCode{5002, "data.duplicate"}
	I18nPermitErr         = ErrCode{5003, "auth.access_denied"}
	I18nFileNotExistsErr  = ErrCode{5004, "file.not_exists"}
)
