"""
地址池使用分析器

提供地址池使用率分析和效率评估功能，包括：
- 使用率统计和趋势分析
- 效率评估和性能指标
- 使用模式识别和预测
- 优化建议生成
"""

import ipaddress
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict
from engine.utils.logger import log
from engine.utils.i18n import _


@dataclass
class PoolUsageStatistics:
    """地址池使用统计信息"""
    pool_name: str
    total_addresses: int = 0
    allocated_addresses: int = 0
    available_addresses: int = 0
    utilization_rate: float = 0.0
    efficiency_score: float = 0.0
    reference_count: int = 0
    active_connections: int = 0
    peak_usage: int = 0
    average_usage: float = 0.0


@dataclass
class UsagePattern:
    """使用模式"""
    pattern_type: str  # "steady", "burst", "periodic", "random"
    confidence: float  # 0.0-1.0
    description: str
    recommendations: List[str] = field(default_factory=list)


@dataclass
class PoolUsageAnalysisResult:
    """池使用分析结果"""
    pool_statistics: Dict[str, PoolUsageStatistics] = field(default_factory=dict)
    usage_patterns: Dict[str, UsagePattern] = field(default_factory=dict)
    efficiency_rankings: List[Tuple[str, float]] = field(default_factory=list)
    optimization_suggestions: List[Dict[str, Any]] = field(default_factory=list)
    overall_metrics: Dict[str, Any] = field(default_factory=dict)


class IPPoolUsageAnalyzer:
    """地址池使用分析器"""
    
    def __init__(self):
        # 使用率阈值配置
        self.high_utilization_threshold = 0.8  # 80%
        self.low_utilization_threshold = 0.2   # 20%
        self.efficiency_weight_factors = {
            "utilization": 0.4,
            "reference_consistency": 0.3,
            "address_efficiency": 0.3
        }
        
        # 模式识别参数
        self.pattern_analysis_window = 24  # 小时
        self.burst_threshold = 2.0  # 突发使用倍数
        
    def analyze_pool_usage(self, pools: List[Dict[str, Any]],
                          nat_rules: List[Dict[str, Any]] = None,
                          reference_manager = None) -> PoolUsageAnalysisResult:
        """
        分析地址池使用情况
        
        Args:
            pools: 地址池配置列表
            nat_rules: NAT规则列表（可选）
            reference_manager: 引用管理器实例（可选）
            
        Returns:
            PoolUsageAnalysisResult: 使用分析结果
        """
        result = PoolUsageAnalysisResult()
        
        # 分析每个池的统计信息
        for pool in pools:
            pool_name = pool.get("name")
            if not pool_name:
                continue
                
            stats = self._calculate_pool_statistics(pool, nat_rules, reference_manager)
            result.pool_statistics[pool_name] = stats
        
        # 识别使用模式
        result.usage_patterns = self._identify_usage_patterns(result.pool_statistics)
        
        # 计算效率排名
        result.efficiency_rankings = self._calculate_efficiency_rankings(result.pool_statistics)
        
        # 生成优化建议
        result.optimization_suggestions = self._generate_optimization_suggestions(
            result.pool_statistics, result.usage_patterns)
        
        # 计算整体指标
        result.overall_metrics = self._calculate_overall_metrics(result.pool_statistics)
        
        return result
    
    def _calculate_pool_statistics(self, pool: Dict[str, Any],
                                 nat_rules: List[Dict[str, Any]] = None,
                                 reference_manager = None) -> PoolUsageStatistics:
        """计算单个池的统计信息"""
        pool_name = pool.get("name", "")
        stats = PoolUsageStatistics(pool_name=pool_name)
        
        # 计算地址总数
        stats.total_addresses = self._calculate_total_addresses(pool)
        
        # 计算引用次数
        if reference_manager:
            try:
                pool_ref = reference_manager.pool_references.get(pool_name)
                if pool_ref:
                    stats.reference_count = pool_ref.reference_count
            except:
                pass
        
        # 从NAT规则中统计引用
        if nat_rules:
            stats.reference_count += self._count_nat_rule_references(pool_name, nat_rules)
        
        # 模拟使用率计算（实际环境中需要从监控数据获取）
        stats.allocated_addresses = self._estimate_allocated_addresses(stats.total_addresses, stats.reference_count)
        stats.available_addresses = stats.total_addresses - stats.allocated_addresses
        
        # 计算使用率
        if stats.total_addresses > 0:
            stats.utilization_rate = stats.allocated_addresses / stats.total_addresses
        
        # 计算效率分数
        stats.efficiency_score = self._calculate_efficiency_score(stats)
        
        # 模拟其他统计数据
        stats.peak_usage = int(stats.allocated_addresses * 1.2)  # 假设峰值比平均高20%
        stats.average_usage = stats.allocated_addresses * 0.8    # 假设平均比当前低20%
        
        return stats
    
    def _calculate_total_addresses(self, pool: Dict[str, Any]) -> int:
        """计算池中的总地址数"""
        address_config = pool.get("address")
        if not address_config:
            return 0
        
        total = 0
        
        try:
            if isinstance(address_config, dict) and "value" in address_config:
                # 单个地址配置
                total = self._count_addresses_in_range(address_config["value"])
            elif isinstance(address_config, list):
                # 多个地址配置
                for addr_item in address_config:
                    if isinstance(addr_item, dict) and "value" in addr_item:
                        total += self._count_addresses_in_range(addr_item["value"])
                    elif isinstance(addr_item, str):
                        total += self._count_addresses_in_range(addr_item)
            elif isinstance(address_config, str):
                # 简单字符串格式
                total = self._count_addresses_in_range(address_config)
        except Exception as e:
            log(_("usage_analyzer.address_count_error", 
                 pool_name=pool.get("name", "unknown"), error=str(e)), "warning")
        
        return total
    
    def _count_addresses_in_range(self, address_range: str) -> int:
        """计算地址范围中的地址数量"""
        try:
            if '-' in address_range:
                # IP范围格式 (e.g., "***********-***********00")
                start_ip, end_ip = address_range.split('-', 1)
                start_addr = ipaddress.IPv4Address(start_ip.strip())
                end_addr = ipaddress.IPv4Address(end_ip.strip())
                return int(end_addr) - int(start_addr) + 1
            elif '/' in address_range:
                # CIDR格式 (e.g., "***********/24")
                network = ipaddress.IPv4Network(address_range, strict=False)
                return network.num_addresses - 2  # 减去网络地址和广播地址
            else:
                # 单个IP地址
                ipaddress.IPv4Address(address_range.strip())
                return 1
        except ValueError:
            log(_("usage_analyzer.invalid_address_range", range=address_range), "warning")
            return 0
    
    def _count_nat_rule_references(self, pool_name: str, nat_rules: List[Dict[str, Any]]) -> int:
        """统计NAT规则中对池的引用次数"""
        count = 0
        
        for rule in nat_rules:
            # 检查动态转换参数
            dyn_params = rule.get("dynamic-translate-parameters", {})
            if dyn_params.get("pool-name") == pool_name:
                count += 1
        
        return count
    
    def _estimate_allocated_addresses(self, total_addresses: int, reference_count: int) -> int:
        """估算已分配的地址数量"""
        if reference_count == 0:
            return 0
        
        # 基于引用次数的简单估算模型
        # 实际环境中应该从监控系统获取真实数据
        base_allocation = min(total_addresses * 0.1, 10)  # 基础分配
        reference_allocation = reference_count * 5        # 每个引用估算5个地址
        
        estimated = int(base_allocation + reference_allocation)
        return min(estimated, total_addresses)
    
    def _calculate_efficiency_score(self, stats: PoolUsageStatistics) -> float:
        """计算效率分数"""
        if stats.total_addresses == 0:
            return 0.0
        
        # 使用率分数 (0.0-1.0)
        utilization_score = min(stats.utilization_rate / self.high_utilization_threshold, 1.0)
        
        # 引用一致性分数 (有引用但使用率低会降低分数)
        reference_score = 1.0
        if stats.reference_count > 0 and stats.utilization_rate < self.low_utilization_threshold:
            reference_score = 0.5
        
        # 地址效率分数 (地址数量合理性)
        address_efficiency = 1.0
        if stats.total_addresses > 1000 and stats.utilization_rate < 0.1:
            address_efficiency = 0.3  # 大池但使用率极低
        elif stats.total_addresses < 10 and stats.reference_count > 3:
            address_efficiency = 0.7  # 小池但引用很多
        
        # 加权计算总分
        weights = self.efficiency_weight_factors
        total_score = (
            utilization_score * weights["utilization"] +
            reference_score * weights["reference_consistency"] +
            address_efficiency * weights["address_efficiency"]
        )
        
        return round(total_score, 3)
    
    def _identify_usage_patterns(self, pool_stats: Dict[str, PoolUsageStatistics]) -> Dict[str, UsagePattern]:
        """识别使用模式"""
        patterns = {}
        
        for pool_name, stats in pool_stats.items():
            pattern = self._analyze_single_pool_pattern(stats)
            patterns[pool_name] = pattern
        
        return patterns
    
    def _analyze_single_pool_pattern(self, stats: PoolUsageStatistics) -> UsagePattern:
        """分析单个池的使用模式"""
        utilization = stats.utilization_rate
        reference_count = stats.reference_count
        
        # 稳定模式
        if 0.3 <= utilization <= 0.7 and reference_count > 0:
            return UsagePattern(
                pattern_type="steady",
                confidence=0.8,
                description=_("usage_analyzer.pattern_steady_desc"),
                recommendations=[_("usage_analyzer.recommend_maintain_current")]
            )
        
        # 突发模式
        elif stats.peak_usage > stats.average_usage * self.burst_threshold:
            return UsagePattern(
                pattern_type="burst",
                confidence=0.7,
                description=_("usage_analyzer.pattern_burst_desc"),
                recommendations=[
                    _("usage_analyzer.recommend_increase_capacity"),
                    _("usage_analyzer.recommend_monitor_peaks")
                ]
            )
        
        # 低使用模式
        elif utilization < self.low_utilization_threshold:
            return UsagePattern(
                pattern_type="underutilized",
                confidence=0.9,
                description=_("usage_analyzer.pattern_underutilized_desc"),
                recommendations=[
                    _("usage_analyzer.recommend_reduce_capacity"),
                    _("usage_analyzer.recommend_consolidate_pools")
                ]
            )
        
        # 高使用模式
        elif utilization > self.high_utilization_threshold:
            return UsagePattern(
                pattern_type="high_usage",
                confidence=0.8,
                description=_("usage_analyzer.pattern_high_usage_desc"),
                recommendations=[
                    _("usage_analyzer.recommend_expand_capacity"),
                    _("usage_analyzer.recommend_load_balancing")
                ]
            )
        
        # 默认随机模式
        else:
            return UsagePattern(
                pattern_type="random",
                confidence=0.5,
                description=_("usage_analyzer.pattern_random_desc"),
                recommendations=[_("usage_analyzer.recommend_continue_monitoring")]
            )
    
    def _calculate_efficiency_rankings(self, pool_stats: Dict[str, PoolUsageStatistics]) -> List[Tuple[str, float]]:
        """计算效率排名"""
        rankings = [(name, stats.efficiency_score) for name, stats in pool_stats.items()]
        rankings.sort(key=lambda x: x[1], reverse=True)
        return rankings
    
    def _generate_optimization_suggestions(self, pool_stats: Dict[str, PoolUsageStatistics],
                                         patterns: Dict[str, UsagePattern]) -> List[Dict[str, Any]]:
        """生成优化建议"""
        suggestions = []
        
        # 全局优化建议
        total_pools = len(pool_stats)
        underutilized_pools = sum(1 for stats in pool_stats.values() 
                                if stats.utilization_rate < self.low_utilization_threshold)
        
        if underutilized_pools > total_pools * 0.3:
            suggestions.append({
                "type": "consolidation",
                "priority": "high",
                "description": _("usage_analyzer.suggest_pool_consolidation"),
                "affected_pools": [name for name, stats in pool_stats.items() 
                                 if stats.utilization_rate < self.low_utilization_threshold],
                "potential_savings": f"{underutilized_pools} pools"
            })
        
        # 个别池的优化建议
        for pool_name, stats in pool_stats.items():
            pattern = patterns.get(pool_name)
            
            if stats.efficiency_score < 0.5:
                suggestions.append({
                    "type": "efficiency_improvement",
                    "priority": "medium",
                    "description": _("usage_analyzer.suggest_improve_efficiency", pool_name=pool_name),
                    "affected_pools": [pool_name],
                    "current_score": stats.efficiency_score,
                    "recommendations": pattern.recommendations if pattern else []
                })
        
        return suggestions
    
    def _calculate_overall_metrics(self, pool_stats: Dict[str, PoolUsageStatistics]) -> Dict[str, Any]:
        """计算整体指标"""
        if not pool_stats:
            return {}
        
        total_addresses = sum(stats.total_addresses for stats in pool_stats.values())
        total_allocated = sum(stats.allocated_addresses for stats in pool_stats.values())
        total_references = sum(stats.reference_count for stats in pool_stats.values())
        
        avg_utilization = sum(stats.utilization_rate for stats in pool_stats.values()) / len(pool_stats)
        avg_efficiency = sum(stats.efficiency_score for stats in pool_stats.values()) / len(pool_stats)
        
        return {
            "total_pools": len(pool_stats),
            "total_addresses": total_addresses,
            "total_allocated": total_allocated,
            "total_available": total_addresses - total_allocated,
            "overall_utilization": total_allocated / total_addresses if total_addresses > 0 else 0,
            "average_utilization": avg_utilization,
            "average_efficiency": avg_efficiency,
            "total_references": total_references,
            "pools_underutilized": sum(1 for stats in pool_stats.values() 
                                     if stats.utilization_rate < self.low_utilization_threshold),
            "pools_overutilized": sum(1 for stats in pool_stats.values() 
                                    if stats.utilization_rate > self.high_utilization_threshold)
        }
