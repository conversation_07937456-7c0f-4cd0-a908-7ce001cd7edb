<config xmlns="urn:ruijie:ntos">
  <vrf>
    <name>main</name>
    <aaa xmlns="urn:ruijie:ntos:params:xml:ns:yang:aaad">
      <enabled>true</enabled>
      <domain-enabled>true</domain-enabled>
      <domain>
        <name>default</name>
        <authentication>
          <sslvpn>
            <method>default</method>
            <enabled>true</enabled>
          </sslvpn>
          <webauth>
            <method>default</method>
            <enabled>true</enabled>
          </webauth>
        </authentication>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
      </domain>
      <authentication>
        <sslvpn>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </sslvpn>
        <webauth>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </webauth>
      </authentication>
      <accounting>
        <update>
          <periodic>5</periodic>
          <enabled>false</enabled>
        </update>
      </accounting>
    </aaa>
    <anti-virus xmlns="urn:ruijie:ntos:params:xml:ns:yang:anti-virus">
      <profile>
        <name>default-block</name>
        <template-name>default</template-name>
        <action>block</action>
      </profile>
    </anti-virus>
    <app-parse-mgmt xmlns="urn:ruijie:ntos:params:xml:ns:yang:app-parse-mgmt">
      <overload-protection>
        <enabled>false</enabled>
        <action>bypass</action>
      </overload-protection>
      <http>
        <decompress-length>2048</decompress-length>
      </http>
    </app-parse-mgmt>
    <appid xmlns="urn:ruijie:ntos:params:xml:ns:yang:appid">
      <mode>dynamic-identify</mode>
      <custom-group>
        <app-group>
          <group-name>Default_AppCtrl</group-name>
          <app-name>翻墙代理</app-name>
          <app-name>RFC</app-name>
          <app-name>ICMP-DETAIL-V6</app-name>
          <app-name>ICMP-DETAIL</app-name>
          <app-name>IP-RAW</app-name>
          <app-name>HTTP</app-name>
          <app-name>IP-Voip</app-name>
          <app-name>Online-Games</app-name>
          <app-name>Online-Shopping</app-name>
          <app-name>InternetFinance</app-name>
          <app-name>InstantMessenger</app-name>
          <app-name>InstantMessaging-APP</app-name>
          <app-name>Social-Media</app-name>
          <app-name>VideoStreamingMediaSoftware</app-name>
          <app-name>Web-Videos</app-name>
          <app-name>Videos-APP</app-name>
          <app-name>Video-Live</app-name>
          <app-name>Music-Audio</app-name>
          <app-name>InternetFileTransfer</app-name>
          <app-name>ProtocolClass</app-name>
          <app-name>InternetofThings</app-name>
          <app-name>RemoteControl</app-name>
          <app-name>VPN-Applications</app-name>
          <app-name>Software-Updates</app-name>
          <app-name>Online-Banking-BPayment</app-name>
          <app-name>OnlineSocial</app-name>
          <app-name>Email</app-name>
          <app-name>Work-OA</app-name>
          <app-name>VideoconFerencing</app-name>
          <app-name>Weibo</app-name>
          <app-name>Forum-Blog</app-name>
          <app-name>NetworkStorage</app-name>
          <app-name>LearningEducation</app-name>
          <app-name>JobRecruitment</app-name>
          <app-name>Travel</app-name>
          <app-name>Online-Gambling</app-name>
          <app-name>LifeServices</app-name>
          <app-name>News-Media</app-name>
          <app-name>Read-Novels</app-name>
          <app-name>Cloud-systems</app-name>
          <app-name>Video-Photography</app-name>
          <app-name>IPProtocol</app-name>
        </app-group>
      </custom-group>
    </appid>
    <arp xmlns="urn:ruijie:ntos:params:xml:ns:yang:arp">
      <proxy-enabled>false</proxy-enabled>
      <gratuitous-send>
        <enabled>false</enabled>
        <interval>30</interval>
      </gratuitous-send>
    </arp>
    <bridge xmlns="urn:ruijie:ntos:params:xml:ns:yang:bridge">
      <fdb>
        <aging>300</aging>
      </fdb>
    </bridge>
    <dhcp xmlns="urn:ruijie:ntos:params:xml:ns:yang:dhcp">
      <server>
        <enabled>true</enabled>
        <default-lease-time>43200</default-lease-time>
        <max-lease-time>86400</max-lease-time>
        <subnet>
          <prefix>********/24</prefix>
          <interface>br0</interface>
          <default-gateway>********</default-gateway>
          <range>
            <start-ip>********</start-ip>
            <end-ip>********54</end-ip>
          </range>
          <ping-check>true</ping-check>
          <default-lease-time>14400</default-lease-time>
          <max-lease-time>14400</max-lease-time>
          <lease-id-format>hex</lease-id-format>
          <warning-high-threshold>90</warning-high-threshold>
          <warning-low-threshold>80</warning-low-threshold>
          <dhcp-options>
            <domain-name-server>*******</domain-name-server>
            <domain-name-server>*******</domain-name-server>
          </dhcp-options>
        </subnet>
        <subnet>
          <prefix>*************/24</prefix>
          <interface>Ge0/0</interface>
          <default-gateway>***************</default-gateway>
          <range>
            <start-ip>***************</start-ip>
            <end-ip>***************</end-ip>
          </range>
          <ping-check>true</ping-check>
          <default-lease-time>3600</default-lease-time>
          <max-lease-time>3600</max-lease-time>
          <lease-id-format>hex</lease-id-format>
          <warning-high-threshold>90</warning-high-threshold>
          <warning-low-threshold>80</warning-low-threshold>
          <dhcp-options>
            <domain-name-server>*******</domain-name-server>
          </dhcp-options>
        </subnet>
      </server>
    </dhcp>
    <discovery xmlns="urn:ruijie:ntos:params:xml:ns:yang:discovery">
      <network>
        <id>dev_H1SL62M000507_1712049973</id>
        <name>defaultNetwork</name>
      </network>
      <parent-id>0</parent-id>
    </discovery>
    <dns xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns">
      <server>
        <address>*******</address>
      </server>
      <server>
        <address>*******</address>
      </server>
      <proxy>
        <enabled>false</enabled>
      </proxy>
    </dns>
    <dns-client xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-client">
      <ip-domain-lookup>
        <enabled>true</enabled>
      </ip-domain-lookup>
    </dns-client>
    <dns-transparent-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-transparent-proxy">
      <enabled>true</enabled>
      <mode>mllb</mode>
      <bind-interface>
        <name>ppp14</name>
        <enabled>true</enabled>
        <kind>physical</kind>
        <dns-server>
          <preferred>*******</preferred>
          <alternate>*******</alternate>
        </dns-server>
        <ref-dns-track>dns-ttp1</ref-dns-track>
      </bind-interface>
      <policy>
        <name>dns-tpp1</name>
        <enabled>true</enabled>
        <action>tpdns</action>
        <service>
          <name>dns-t</name>
        </service>
        <service>
          <name>dns-u</name>
        </service>
      </policy>
    </dns-transparent-proxy>
    <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
      <enabled>false</enabled>
    </flow-control>
    <ha xmlns="urn:ruijie:ntos:params:xml:ns:yang:ha">
      <mode>A-P</mode>
      <heart-interval>1000</heart-interval>
      <heart-alert-count>3</heart-alert-count>
      <gra-arp-count>5</gra-arp-count>
      <vmac-prefix>00:00:5e</vmac-prefix>
      <preempt>false</preempt>
      <preempt-delay>60</preempt-delay>
      <session-sync>true</session-sync>
      <switch-link-time>1</switch-link-time>
      <enabled>false</enabled>
      <log-level>
        <group>on</group>
        <adv>off</adv>
        <dbus>on</dbus>
        <monitor>off</monitor>
      </log-level>
    </ha>
    <ike xmlns="urn:ruijie:ntos:params:xml:ns:yang:ike">
      <proposal>
        <name>default</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>des des3 aes-128 aes-192 aes-256</encrypt-alg>
        <hash-alg>md5 sha</hash-alg>
        <dh-group>group1 group2 group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <proposal>
        <name>hub</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>aes-128</encrypt-alg>
        <hash-alg>sha</hash-alg>
        <dh-group>group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <key>
        <type>pre-share</type>
        <ipv4-address>
          <ipv4-address>0.0.0.0</ipv4-address>
          <key>=*-#!$B4Q2BCBDO_k=</key>
        </ipv4-address>
      </key>
      <profile>
        <name>default</name>
      </profile>
      <profile>
        <name>temporary</name>
      </profile>
      <profile>
        <name>hub</name>
        <default-psk>=*-#!$B4Q2BCBDO_k=</default-psk>
      </profile>
      <dpd>
        <interval>30</interval>
        <retry-interval>5</retry-interval>
      </dpd>
      <nat-traversal>
        <enabled>true</enabled>
      </nat-traversal>
      <nat>
        <keepalive>20</keepalive>
      </nat>
    </ike>
    <interface xmlns="urn:ruijie:ntos:params:xml:ns:yang:interface">
      <snmp>
        <if-usage-compute-interval>30</if-usage-compute-interval>
        <if-global-notify-enable>false</if-global-notify-enable>
      </snmp>
      <physical>
        <name>Ge0/0</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>wan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <address>
            <ip>************/26</ip>
            <nexthop>************</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/1</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>wan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <address>
            <ip>***********/24</ip>
            <nexthop>***********</nexthop>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/2</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <address>
            <ip>***********/24</ip>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/3</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <working-mode>bridge</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/4</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <working-mode>bridge</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/5</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <working-mode>bridge</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/6</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <working-mode>bridge</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/7</name>
        <mtu>1500</mtu>
        <promiscuous>false</promiscuous>
        <description/>
        <enabled>true</enabled>
        <wanlan>wan</wanlan>
        <working-mode>route</working-mode>
        <ipv4>
          <address>
            <ip>*********/24</ip>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/0</name>
        <enabled>true</enabled>
        <working-mode>bridge</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>Ge0/8</name>
        <enabled>true</enabled>
        <working-mode>bridge</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <bridge xmlns="urn:ruijie:ntos:params:xml:ns:yang:bridge">
        <name>br0</name>
        <description/>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>********/24</ip>
          </address>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <link-interface>
          <slave>Ge0/3</slave>
        </link-interface>
        <link-interface>
          <slave>Ge0/4</slave>
        </link-interface>
        <link-interface>
          <slave>Ge0/5</slave>
        </link-interface>
        <link-interface>
          <slave>Ge0/6</slave>
        </link-interface>
        <link-interface>
          <slave>Ge0/8</slave>
        </link-interface>
        <link-interface>
          <slave>TenGe0/0</slave>
        </link-interface>
        <session-source-check>dont-check</session-source-check>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </bridge>
      <loopback xmlns="urn:ruijie:ntos:params:xml:ns:yang:loopback">
        <name>vgwSSLVPN</name>
        <enabled>true</enabled>
        <ipv4>
          <address>
            <ip>**********/24</ip>
          </address>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
      </loopback>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/1.500</name>
        <mtu>1492</mtu>
        <description>Unifi01</description>
        <enabled>true</enabled>
        <ethernet>
          <mac-address>48:81:d4:c7:42:41</mac-address>
        </ethernet>
        <ipv4>
          <enabled>true</enabled>
          <pppoe>
            <connection>
              <tunnel>14</tunnel>
              <enabled>true</enabled>
              <user>mbouti100@unifibiz</user>
              <password>@*|_=#_bnt2AJnT8sNxrVkJoSRbg==</password>
              <gateway>true</gateway>
              <timeout>5</timeout>
              <retries>3</retries>
              <ppp>
                <negotiation-timeout>3</negotiation-timeout>
                <lcp-echo-interval>10</lcp-echo-interval>
                <lcp-echo-retries>10</lcp-echo-retries>
              </ppp>
              <ppp-mtu>1492</ppp-mtu>
            </connection>
          </pppoe>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>500</vlan-id>
        <link-interface>Ge0/1</link-interface>
        <protocol>802.1q</protocol>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/2.500</name>
        <mtu>1492</mtu>
        <description>Unifi02</description>
        <enabled>false</enabled>
        <ethernet>
          <mac-address>3a:e3:b2:42:c3:16</mac-address>
        </ethernet>
        <ipv4>
          <enabled>true</enabled>
          <pppoe>
            <connection>
              <tunnel>12</tunnel>
              <enabled>true</enabled>
              <user>mbouti30@unifibiz</user>
              <password>=*-#!$fI26hT1bZ7c7SRs4fwRLPA==</password>
              <gateway>true</gateway>
              <timeout>5</timeout>
              <retries>3</retries>
              <ppp>
                <negotiation-timeout>3</negotiation-timeout>
                <lcp-echo-interval>10</lcp-echo-interval>
                <lcp-echo-retries>10</lcp-echo-retries>
              </ppp>
              <ppp-mtu>1492</ppp-mtu>
            </connection>
          </pppoe>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <vlan-id>500</vlan-id>
        <link-interface>Ge0/2</link-interface>
        <protocol>802.1q</protocol>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vti xmlns="urn:ruijie:ntos:params:xml:ns:yang:vti">
        <name>vti1</name>
        <description>by tunnel wizard hub</description>
        <enabled>false</enabled>
        <ipv4>
          <address>
            <ip>**********/24</ip>
          </address>
          <enabled>true</enabled>
        </ipv4>
        <reverse-path>true</reverse-path>
        <local>*******</local>
        <is-template>true</is-template>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vti>
    </interface>
    <ips-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:intrusion-prevention">
      <profile>
        <name>client-block</name>
        <template-name>client</template-name>
        <action>block</action>
      </profile>
    </ips-config>
    <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
      <ipv4>
        <enabled>false</enabled>
        <no-match-action-drop>false</no-match-action-drop>
      </ipv4>
      <ipv6>
        <enabled>false</enabled>
        <no-match-action-drop>false</no-match-action-drop>
      </ipv6>
    </ip-mac-bind>
    <track xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-track">
      <enabled>false</enabled>
      <rule>
        <name>dns-ttp1</name>
        <enabled>true</enabled>
        <rns>
          <name>dns-ttp1_preferred</name>
          <dns>
            <ip-address>*******</ip-address>
            <out-interface>ppp14</out-interface>
            <domain-name>secloud1.ruijie.com.cn</domain-name>
            <port>53</port>
          </dns>
          <enabled>true</enabled>
          <frequency>6000</frequency>
          <retry-times>4</retry-times>
          <resume-times>3</resume-times>
        </rns>
        <rns>
          <name>dns-ttp1_alternate</name>
          <dns>
            <ip-address>*******</ip-address>
            <out-interface>ppp14</out-interface>
            <domain-name>secloud1.ruijie.com.cn</domain-name>
            <port>53</port>
          </dns>
          <enabled>true</enabled>
          <frequency>6000</frequency>
          <retry-times>4</retry-times>
          <resume-times>3</resume-times>
        </rns>
        <delay>
          <up>0</up>
          <down>0</down>
        </delay>
        <least-activenum>1</least-activenum>
        <config-source>DNS-PROXY</config-source>
        <type>rns</type>
        <thread-weight>255</thread-weight>
      </rule>
    </track>
    <ipsec xmlns="urn:ruijie:ntos:params:xml:ns:yang:ipsec">
      <profile>
        <name>hub</name>
        <enabled>true</enabled>
        <description>by tunnel wizard hub</description>
        <create-time>1743400300</create-time>
        <version>ikev1 ikev2</version>
        <exchange-mode>main</exchange-mode>
        <autoup>false</autoup>
        <local>
          <interface>
            <interface>Ge0/0</interface>
          </interface>
        </local>
        <proxyid>hub</proxyid>
        <ike-profile>hub</ike-profile>
        <ike-proposal>hub</ike-proposal>
        <ipsec-proposal>hub</ipsec-proposal>
        <life-seconds>3600</life-seconds>
        <reverse-route>
          <enabled>true</enabled>
          <distance>5</distance>
        </reverse-route>
        <fragmentation-mtu>1400</fragmentation-mtu>
        <peer-address>
          <peer-address>0.0.0.0</peer-address>
        </peer-address>
        <type>dynamic</type>
        <local-identity>
          <ipv4-address/>
        </local-identity>
        <check-id>false</check-id>
        <dpd>
          <interval>30</interval>
          <retry-interval>5</retry-interval>
          <type>periodic</type>
        </dpd>
        <tunnel-interface>vti1</tunnel-interface>
        <is-template>true</is-template>
      </profile>
      <proposal>
        <name>hub</name>
        <protocol>esp</protocol>
        <encap-mode>tunnel</encap-mode>
        <esn>true</esn>
        <esp-encrypt-alg>aes-128</esp-encrypt-alg>
        <esp-auth-alg>sha</esp-auth-alg>
      </proposal>
      <proxyid>
        <proxyid-name>hub</proxyid-name>
        <ip>
          <local>**********/24</local>
          <remote>**********/24</remote>
        </ip>
      </proxyid>
      <anti-replay>
        <check>true</check>
        <window-size>64</window-size>
      </anti-replay>
      <df-bit>clear</df-bit>
      <prefrag>true</prefrag>
      <inbound-sp>
        <check>true</check>
      </inbound-sp>
      <spd-hash-bits>
        <src-bits>16</src-bits>
        <dst-bits>16</dst-bits>
      </spd-hash-bits>
      <hardware-crypto-offload>true</hardware-crypto-offload>
    </ipsec>
    <isp xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
      <distance>10</distance>
    </isp>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <policy>
        <name>deny_all</name>
        <enabled>true</enabled>
        <action>deny</action>
        <limit>false</limit>
        <pps>600</pps>
        <description/>
      </policy>
      <policy>
        <name>limit_local</name>
        <enabled>true</enabled>
        <action>permit</action>
        <limit>true</limit>
        <pps>1500</pps>
        <description/>
      </policy>
    </local-defend>
    <misn xmlns="urn:ruijie:ntos:params:xml:ns:yang:misn">
      <enabled>true</enabled>
    </misn>
    <mllb xmlns="urn:ruijie:ntos:params:xml:ns:yang:mllb">
      <arithmetic>
        <src-ip-hash/>
      </arithmetic>
      <wan-interface>
        <name>ppp12</name>
        <enabled>true</enabled>
        <kind>ppp</kind>
        <weight>50</weight>
        <priority>1</priority>
        <max-conn>2000</max-conn>
        <upload-bandwidth>
          <upload-bandwidth-unit>mbps</upload-bandwidth-unit>
          <upload-bandwidth-value>1000</upload-bandwidth-value>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
        </upload-bandwidth>
        <download-bandwidth>
          <download-bandwidth-unit>mbps</download-bandwidth-unit>
          <download-bandwidth-value>1000</download-bandwidth-value>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
        </download-bandwidth>
      </wan-interface>
      <wan-interface>
        <name>ppp14</name>
        <enabled>true</enabled>
        <kind>ppp</kind>
        <weight>1000000</weight>
        <priority>1</priority>
        <max-conn>2000</max-conn>
        <upload-bandwidth>
          <upload-bandwidth-unit>mbps</upload-bandwidth-unit>
          <upload-bandwidth-value>1000</upload-bandwidth-value>
          <upload-bandwidth-threshold>80</upload-bandwidth-threshold>
        </upload-bandwidth>
        <download-bandwidth>
          <download-bandwidth-unit>mbps</download-bandwidth-unit>
          <download-bandwidth-value>1000</download-bandwidth-value>
          <download-bandwidth-threshold>80</download-bandwidth-threshold>
        </download-bandwidth>
      </wan-interface>
      <advanced-options>
        <refresh-session>false</refresh-session>
        <cache-timeout>300</cache-timeout>
        <cache-once>256</cache-once>
        <cache-disable>false</cache-disable>
        <alarm-threshold>90</alarm-threshold>
      </advanced-options>
      <all-if-switch>false</all-if-switch>
    </mllb>
    <port-mapping xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <enabled>true</enabled>
    </port-mapping>
    <nat xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <enabled>true</enabled>
      <rule>
        <name>nat_rule</name>
        <rule_en>false</rule_en>
        <static-snat44>
          <match>
            <dest-zone>
              <name>untrust</name>
            </dest-zone>
            <source-zone>
              <name>trust</name>
            </source-zone>
          </match>
          <translate-to>
            <output-address/>
            <no-pat>false</no-pat>
          </translate-to>
        </static-snat44>
      </rule>
      <alg>ftp sip-tcp sip-udp tftp dns-udp</alg>
      <sip-port-check>
        <enabled>true</enabled>
      </sip-port-check>
    </nat>
    <netconf-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:netconf-server">
      <enabled>false</enabled>
      <idle-timeout>3600</idle-timeout>
    </netconf-server>
    <network-measure xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-measure">
      <enabled>true</enabled>
    </network-measure>
    <network-obj xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-obj">
      <address-set>
        <name>LAN</name>
        <ip-set>
          <ip-address>********/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>ippool_SSLVPN</name>
        <description>sslvpn gateway[SSLVPN] virtual address pool</description>
        <ip-set>
          <ip-address>**********/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>hub_local</name>
        <description>by tunnel wizard hub</description>
        <ip-set>
          <ip-address>**********/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>hub_remote</name>
        <description>by tunnel wizard hub</description>
        <ip-set>
          <ip-address>**********/24</ip-address>
        </ip-set>
      </address-set>
    </network-obj>
    <nfp xmlns="urn:ruijie:ntos:params:xml:ns:yang:nfp">
      <session>
        <state-inspection>
          <tcp>true</tcp>
          <icmp>true</icmp>
        </state-inspection>
      </session>
    </nfp>
    <ntp xmlns="urn:ruijie:ntos:params:xml:ns:yang:ntp">
      <enabled>true</enabled>
      <server>
        <address>ntp.ntsc.ac.cn</address>
        <version>4</version>
        <association-type>SERVER</association-type>
        <iburst>false</iburst>
        <prefer>false</prefer>
      </server>
      <server>
        <address>ntp1.aliyun.com</address>
        <version>4</version>
        <association-type>SERVER</association-type>
        <iburst>false</iburst>
        <prefer>false</prefer>
      </server>
    </ntp>
    <wba-portal xmlns="urn:ruijie:ntos:params:xml:ns:yang:portal">
      <enabled>false</enabled>
      <port>8081</port>
      <ssl-enabled>false</ssl-enabled>
      <redirection-mode>no-redirection</redirection-mode>
    </wba-portal>
    <replacement-messages xmlns="urn:ruijie:ntos:params:xml:ns:yang:replacement-messages">
      <management>
        <cache-enable-status>true</cache-enable-status>
      </management>
    </replacement-messages>
    <reputation-center xmlns="urn:ruijie:ntos:params:xml:ns:yang:reputation-center">
      <enabled>false</enabled>
    </reputation-center>
    <routing xmlns="urn:ruijie:ntos:params:xml:ns:yang:routing">
      <static>
        <ipv4-route>
          <destination>***********/24</destination>
          <next-hop>
            <next-hop>************</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
        <ipv4-route>
          <destination>0.0.0.0/0</destination>
          <next-hop>
            <next-hop>************%Ge0/0</next-hop>
            <distance>5</distance>
            <enable>true</enable>
          </next-hop>
        </ipv4-route>
      </static>
    </routing>
    <security-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-defend">
      <basic-protocol-control-enabled>false</basic-protocol-control-enabled>
      <enabled>false</enabled>
    </security-defend>
    <security-policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
      <policy>
        <name>hub_in</name>
        <enabled>true</enabled>
        <description>by tunnel wizard hub</description>
        <group-name>def-group</group-name>
        <source-zone>
          <name>hub</name>
        </source-zone>
        <source-network>
          <name>hub_remote</name>
        </source-network>
        <dest-network>
          <name>hub_local</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>hub_out</name>
        <enabled>true</enabled>
        <description>by tunnel wizard hub</description>
        <group-name>def-group</group-name>
        <dest-zone>
          <name>hub</name>
        </dest-zone>
        <source-network>
          <name>hub_local</name>
        </source-network>
        <dest-network>
          <name>hub_remote</name>
        </dest-network>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
      <policy>
        <name>allow_all</name>
        <enabled>true</enabled>
        <group-name>def-group</group-name>
        <source-zone>
          <name>trust</name>
        </source-zone>
        <dest-zone>
          <name>untrust</name>
        </dest-zone>
        <time-range>any</time-range>
        <action>permit</action>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
      </policy>
    </security-policy>
    <security-zone xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-zone">
      <zone>
        <name>trust</name>
        <description>Trust Zone.</description>
        <interface>
          <name>br0</name>
        </interface>
        <interface>
          <name>Ge0/5</name>
        </interface>
        <interface>
          <name>Ge0/2</name>
        </interface>
      </zone>
      <zone>
        <name>untrust</name>
        <description>Untrust Zone.</description>
        <interface>
          <name>Ge0/1.500</name>
        </interface>
        <interface>
          <name>Ge0/2.500</name>
        </interface>
        <interface>
          <name>Ge0/1</name>
        </interface>
        <interface>
          <name>vgwSSLVPN</name>
        </interface>
        <interface>
          <name>Ge0/0</name>
        </interface>
        <interface>
          <name>Ge0/7</name>
        </interface>
      </zone>
      <zone>
        <name>DMZ</name>
        <description>Demilitarized Zone.</description>
        <priority>50</priority>
      </zone>
      <zone>
        <name>monitor</name>
      </zone>
      <zone>
        <name>hub</name>
        <description>by tunnel wizard hub</description>
        <interface>
          <name>vti1</name>
        </interface>
      </zone>
    </security-zone>
    <session-limit xmlns="urn:ruijie:ntos:params:xml:ns:yang:session-limit">
      <pps-limit>
        <enabled>false</enabled>
        <global-pps>0</global-pps>
      </pps-limit>
      <sps-limit>
        <enabled>false</enabled>
        <global-sps>0</global-sps>
      </sps-limit>
      <total-session>
        <enabled>false</enabled>
      </total-session>
    </session-limit>
    <sim-status xmlns="urn:ruijie:ntos:params:xml:ns:yang:sim-security-policy">
      <action>free</action>
    </sim-status>
    <ssh-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssh-server">
      <enabled>true</enabled>
      <port>22</port>
      <deny-count>3</deny-count>
      <unlock-time>60</unlock-time>
    </ssh-server>
    <ssl-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssl-proxy">
      <profile>
        <name>default</name>
        <description>Default Template, Traffic proxy for Internet access of users.</description>
        <outbound/>
      </profile>
      <ca-cert>
        <trust-cert>default_ca</trust-cert>
      </ca-cert>
    </ssl-proxy>
    <sslvpn xmlns="urn:ruijie:ntos:params:xml:ns:yang:sslvpn">
      <gateway>
        <name>SSLVPN</name>
        <type>exclusive</type>
        <address>
          <mannual-ip>
            <ipv4>************</ipv4>
            <port>8443</port>
          </mannual-ip>
        </address>
        <short-message>
          <enabled>false</enabled>
          <smsg-everyday-peruser-max>0</smsg-everyday-peruser-max>
          <smsg-extend-functions/>
          <smsg-code-policy>
            <validate-times>5</validate-times>
            <max-retries>5</max-retries>
          </smsg-code-policy>
        </short-message>
        <dns-order>client-first</dns-order>
        <protocol>tls1.2</protocol>
        <ciphersuit>tls-ecdhe-rsa-with-aes128-cbc-sha256</ciphersuit>
        <ciphersuit>tls-ecdhe-rsa-with-aes256-cbc-sha384</ciphersuit>
        <ciphersuit>tls-rsa-with-aes256-cbc-sha</ciphersuit>
        <local-cert>default</local-cert>
        <max-concur-users>1</max-concur-users>
        <auth-zone>default</auth-zone>
        <login-limit>
          <by-user>
            <enabled>true</enabled>
            <user-max-retries>5</user-max-retries>
            <user-lock-secs>300</user-lock-secs>
          </by-user>
          <by-ip>
            <enabled>true</enabled>
            <ip-max-retries>5</ip-max-retries>
            <ip-lock-secs>300</ip-lock-secs>
          </by-ip>
        </login-limit>
        <client-policy>
          <policy>any</policy>
        </client-policy>
        <session-timeout>30</session-timeout>
        <soft-keyboard-enabled>false</soft-keyboard-enabled>
        <img-verify>
          <enabled>true</enabled>
          <sess-retries>0</sess-retries>
        </img-verify>
        <hardid-verify>
          <enabled>false</enabled>
          <max-num>3</max-num>
          <auto-approval>false</auto-approval>
          <auto-approve-pub-term>false</auto-approve-pub-term>
          <hardid-self-unbind>false</hardid-self-unbind>
        </hardid-verify>
        <iptunnel>
          <timeout-idle>30</timeout-idle>
          <keep-alive>
            <interval>30</interval>
            <dead-time>180</dead-time>
          </keep-alive>
          <route-mode>split</route-mode>
          <sslvpn-line>false</sslvpn-line>
          <ip-pool>
            <net>
              <net>**********/24</net>
            </net>
          </ip-pool>
        </iptunnel>
        <authorize-policy>
          <name>default</name>
          <group>
            <name>/default</name>
          </group>
        </authorize-policy>
        <enabled>true</enabled>
      </gateway>
    </sslvpn>
    <network-stack xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
      <ipv4>
        <arp-ignore>check-interface-and-subnet</arp-ignore>
      </ipv4>
    </network-stack>
    <threat-intelligence xmlns="urn:ruijie:ntos:params:xml:ns:yang:threat-intelligence">
      <management>
        <enable-status>false</enable-status>
        <enable-ai>false</enable-ai>
        <security-zone>
          <auto>true</auto>
        </security-zone>
      </management>
    </threat-intelligence>
    <time-range xmlns="urn:ruijie:ntos:params:xml:ns:yang:time-range">
      <range>
        <name>any</name>
        <description>Time range of all the time.</description>
        <period>
          <start>00:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
        </period>
      </range>
    </time-range>
    <traffic-analy xmlns="urn:ruijie:ntos:params:xml:ns:yang:traffic-analy">
      <enabled>false</enabled>
    </traffic-analy>
    <upnp-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:upnp-proxy">
      <enabled>false</enabled>
      <bind-rule>ip</bind-rule>
      <advance>
        <automatic-enrollment>
          <enabled>false</enabled>
          <registration-time>1440</registration-time>
          <logout-check-period>3</logout-check-period>
        </automatic-enrollment>
        <terminal-authorization>
          <enabled>false</enabled>
        </terminal-authorization>
        <linkage-service>
          <enabled>false</enabled>
        </linkage-service>
        <offline-detect>
          <time-range>150</time-range>
          <flow-rate>0</flow-rate>
        </offline-detect>
        <scheduled-offline>
          <enabled>false</enabled>
          <time>00:00</time>
        </scheduled-offline>
        <quick-response-code-valid-time>
          <time>480</time>
        </quick-response-code-valid-time>
      </advance>
      <reserve>
        <single-ip-process>
          <interval-time>5</interval-time>
          <max-package>40</max-package>
        </single-ip-process>
        <unicast>
          <enabled>false</enabled>
        </unicast>
        <web-url-compatible>
          <enabled>false</enabled>
        </web-url-compatible>
        <map-cover-mode>
          <enabled>false</enabled>
        </map-cover-mode>
        <server-capacity>1</server-capacity>
      </reserve>
    </upnp-proxy>
    <url-filter xmlns="urn:ruijie:ntos:params:xml:ns:yang:url-filter">
      <name>Default</name>
      <unknown-category-action>allow</unknown-category-action>
      <category>
        <pre-defined>
          <subcategory>
            <name>Hacker</name>
            <action>block</action>
          </subcategory>
          <subcategory>
            <name>Virus</name>
            <action>block</action>
          </subcategory>
          <subcategory>
            <name>Gambling</name>
            <action>block</action>
          </subcategory>
          <subcategory>
            <name>Violence</name>
            <action>block</action>
          </subcategory>
          <subcategory>
            <name>Crime</name>
            <action>block</action>
          </subcategory>
          <subcategory>
            <name>Illegal</name>
            <action>block</action>
          </subcategory>
          <subcategory>
            <name>Adult</name>
            <action>block</action>
          </subcategory>
          <subcategory>
            <name>Porn</name>
            <action>block</action>
          </subcategory>
        </pre-defined>
      </category>
    </url-filter>
    <user-management xmlns="urn:ruijie:ntos:params:xml:ns:yang:user-management">
      <user>
        <name>zoupengju</name>
        <aaa-domain>default</aaa-domain>
        <enabled>true</enabled>
        <password>=*-#!$oUE7M2uaOd7JmonhxxPt7A==</password>
        <parent-group-path>/default</parent-group-path>
        <ip-mac-binding>
          <no-binding/>
        </ip-mac-binding>
        <expiration-time>
          <never-expire/>
        </expiration-time>
      </user>
    </user-management>
    <webauth xmlns="urn:ruijie:ntos:params:xml:ns:yang:webauth">
      <authentication-options>
        <portal-authentication>
          <portal-group>
            <name>cportal</name>
            <protocol>portal</protocol>
          </portal-group>
        </portal-authentication>
      </authentication-options>
      <single-sign-on>
        <ad>
          <method>plugin</method>
        </ad>
      </single-sign-on>
    </webauth>
  </vrf>
  <system xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
    <devicename>FIREWALL</devicename>
    <cp-mask>default</cp-mask>
    <nfp>
      <autoperf>
        <enabled>true</enabled>
      </autoperf>
    </nfp>
    <network-stack>
      <bridge>
        <call-ipv4-filtering>false</call-ipv4-filtering>
        <call-ipv6-filtering>false</call-ipv6-filtering>
      </bridge>
      <icmp>
        <rate-limit-icmp>1000</rate-limit-icmp>
        <rate-mask-icmp>destination-unreachable source-quench time-exceeded parameter-problem</rate-mask-icmp>
      </icmp>
      <ipv4>
        <forwarding>true</forwarding>
        <send-redirects>true</send-redirects>
        <accept-redirects>false</accept-redirects>
        <accept-source-route>false</accept-source-route>
        <arp-announce>any</arp-announce>
        <arp-filter>false</arp-filter>
        <arp-ignore>any</arp-ignore>
        <log-invalid-addresses>false</log-invalid-addresses>
      </ipv4>
      <ipv6>
        <forwarding>true</forwarding>
        <autoconfiguration>true</autoconfiguration>
        <accept-router-advert>never</accept-router-advert>
        <accept-redirects>false</accept-redirects>
        <accept-source-route>false</accept-source-route>
        <router-solicitations>-1</router-solicitations>
        <use-temporary-addresses>never</use-temporary-addresses>
      </ipv6>
    </network-stack>
    <timezone>Asia/Kuala_Lumpur</timezone>
    <scheduled-restart>
      <enabled>false</enabled>
      <hour>3</hour>
      <minute>0</minute>
      <weekday>
        <key>mon</key>
      </weekday>
      <weekday>
        <key>tue</key>
      </weekday>
      <weekday>
        <key>wed</key>
      </weekday>
      <weekday>
        <key>thu</key>
      </weekday>
      <weekday>
        <key>fri</key>
      </weekday>
      <weekday>
        <key>sat</key>
      </weekday>
      <weekday>
        <key>sun</key>
      </weekday>
      <once>false</once>
    </scheduled-restart>
    <anti-virus-file-exception xmlns="urn:ruijie:ntos:params:xml:ns:yang:anti-virus">
      <enabled>true</enabled>
    </anti-virus-file-exception>
    <auth xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:auth">
      <user>
        <name>admin</name>
        <role>admin</role>
        <password>=*-#!$zJLI867qGWiRRAKbx1QyoskF9nKDapePu0hNeTyJenqqCxGKydlw1RV2H4uKj6fX+KzaBCY9gNlxKwhCqA0Ynw==</password>
        <network-password>=*-#!$_2g+awtqJWZWf+C6zl4RLQ==</network-password>
      </user>
      <user>
        <name>securityadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>useradmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>auditadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>ruijietechsupport</name>
        <description/>
        <role>admin</role>
        <lock>false</lock>
        <password>=*-#!$AbhXPxfqQOD+Q3sDpM2ZiqXwOtsxbmC5egeonP7Jgab7xbEDUsg9uen7++4a9BpiOHzMZtM+0fASDunVTv5Ypw==</password>
      </user>
    </auth>
    <wis-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </wis-service>
    <macc-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </macc-service>
    <security-cloud-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </security-cloud-service>
    <log2cloud xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <upload-interval>5</upload-interval>
    </log2cloud>
    <collect xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:collect">
      <enabled>true</enabled>
      <max-records>3072</max-records>
      <record-interval>200</record-interval>
      <memory-storage-threshold>90</memory-storage-threshold>
      <statistics-enabled>false</statistics-enabled>
      <record-stats-enabled>true</record-stats-enabled>
      <flow-log-enabled>false</flow-log-enabled>
      <log-language>English</log-language>
    </collect>
    <dataplane xmlns="urn:ruijie:ntos:params:xml:ns:yang:dataplane-dsa">
      <hash>
        <tx-hash>tx-default</tx-hash>
      </hash>
    </dataplane>
    <flow-audit xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-audit">
      <flowrate>
        <enable>false</enable>
      </flowrate>
      <session>
        <enable>true</enable>
      </session>
      <flowtotal>
        <enabled>false</enabled>
      </flowtotal>
      <flowspeed>
        <enabled>false</enabled>
      </flowspeed>
      <hard-disk-quota>30</hard-disk-quota>
      <refresh-time>30</refresh-time>
    </flow-audit>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <enabled>true</enabled>
      <arp-monitor>
        <enabled>false</enabled>
        <scan-threshold>200</scan-threshold>
      </arp-monitor>
      <rate-limit>
        <arp>
          <req-token>5</req-token>
          <res-token>1</res-token>
          <req-threshold>100</req-threshold>
          <res-threshold>100</res-threshold>
        </arp>
      </rate-limit>
    </local-defend>
    <memory xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:mem">
      <warning-threshold>90</warning-threshold>
      <critical-threshold>90</critical-threshold>
    </memory>
    <trusted-host xmlns="urn:ruijie:ntos:params:xml:ns:yang:trusted-host">
      <host>
        <username>ruijietechsupport</username>
        <enabled>false</enabled>
      </host>
    </trusted-host>
    <usr-exp-plan xmlns="urn:ruijie:ntos:params:xml:ns:yang:user-experience-plan">
      <no-prompt>true</no-prompt>
      <enabled>false</enabled>
      <log-category>
        <security-log>false</security-log>
        <device-log>false</device-log>
        <engine-log>false</engine-log>
      </log-category>
    </usr-exp-plan>
    <web-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:web-server">
      <enabled>true</enabled>
      <port>443</port>
      <http-enabled>true</http-enabled>
      <smart-http-enabled>true</smart-http-enabled>
    </web-server>
  </system>
</config>
