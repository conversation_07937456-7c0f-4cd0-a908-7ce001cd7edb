package application

import (
	stdContext "context"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"

	"irisAdminApi/application/controllers/common"
	firewallflextrans_controllers "irisAdminApi/application/controllers/firewallflextrans"
	"irisAdminApi/application/controllers/queue"
	"irisAdminApi/application/controllers/user"

	"irisAdminApi/application/logging"
	"irisAdminApi/application/middleware"
	"irisAdminApi/application/models/firewallflextrans"
	usermodels "irisAdminApi/application/models/user"
	"irisAdminApi/service/cache"
	"irisAdminApi/service/dao/firewallflextrans/dconfigtrans"
	"irisAdminApi/service/schedule"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/middleware/pprof"
	"github.com/kataras/iris/v12/middleware/rate"
)

// HttpServer
type HttpServer struct {
	ConfigPath string
	App        *iris.Application
	Models     []interface{}
	Status     bool
}

func NewServer(config string) *HttpServer {
	app := iris.New()

	app.Logger().SetLevel(libs.Config.LogLevel)
	iris.RegisterOnInterrupt(func() {
		sql, _ := easygorm.GetEasyGormDb().DB()
		sql.Close()
	})
	httpServer := &HttpServer{
		ConfigPath: config,
		App:        app,
		Status:     false,
	}

	err := httpServer._Init()
	if err != nil {
		panic(err.Error())
	}
	return httpServer
}

// Start
func (s *HttpServer) Start() error {
	config := iris.WithConfiguration(iris.Configuration{
		FireEmptyFormError:    false,
		DisablePathCorrection: true,
	})
	srv := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", libs.Config.Host, libs.Config.Port),
		ReadTimeout:  time.Duration(libs.Config.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(libs.Config.WriteTimeout) * time.Second,
	}
	if err := s.App.Run(iris.Server(srv), config); err != nil {
		return err
	}
	// s.Status = true
	return nil
}

// Start close the server at 3-6 seconds
func (s *HttpServer) Stop() {
	go func() {
		time.Sleep(3 * time.Second)
		ctx, cancel := stdContext.WithTimeout(stdContext.TODO(), 3*time.Second)
		defer cancel()
		s.App.Shutdown(ctx)
		s.Status = false
	}()
}

var bgCtx = stdContext.Background()

func (s *HttpServer) _Init() error {
	// 初始化国际化系统
	logging.InfoLogger.Info("初始化国际化系统...")
	// 直接调用 i18n 包的 Init 函数
	// 由于 Go 的包导入机制，我们需要在使用时导入

	err := libs.InitConfig(s.ConfigPath)
	if err != nil {
		logging.ErrorLogger.Errorf("系统配置初始化失败:", err)
		return err
	}
	if libs.Config.Cache.Driver == "redis" {
		password := libs.Config.Redis.Password
		if libs.Config.Redis.Encrypt {
			_password, err := libs.RsaDecrypt(libs.Config.Redis.Password)
			if err != nil {
				logging.ErrorLogger.Errorf("系统配置初始化失败:", err)
				return err
			}
			password = string(_password)
		}
		cache.InitRedisCluster(libs.GetRedisUris(), password)

		// 添加Redis关闭钩子，确保只在应用程序退出时关闭Redis连接
		iris.RegisterOnInterrupt(func() {
			logging.InfoLogger.Info("应用程序正在关闭，关闭Redis连接...")
			redisClient := cache.GetRedisClusterClient()
			if redisClient != nil {
				redisClient.Close()
			}
		})
	}
	err = easygorm.Init(libs.GetGormConfig())
	if err != nil {
		logging.ErrorLogger.Errorf("数据库初始化失败:", err)
		return err
	}

	// 初始化多厂商配置转换器设置
	// 确保上传目录存在
	if libs.Config.ConfigTrans.Upload != "" {
		os.MkdirAll(libs.Config.ConfigTrans.Upload, 0755)
	} else {
		// 使用默认上传目录
		libs.Config.ConfigTrans.Upload = filepath.Join(os.TempDir(), "configtrans_uploads")
		os.MkdirAll(libs.Config.ConfigTrans.Upload, 0755)
	}
	logging.InfoLogger.Infof("配置转换上传目录: %s", libs.Config.ConfigTrans.Upload)

	// 确保临时目录存在
	if libs.Config.ConfigTrans.TempDir != "" {
		os.MkdirAll(libs.Config.ConfigTrans.TempDir, 0755)
	} else {
		// 使用默认临时目录
		libs.Config.ConfigTrans.TempDir = filepath.Join(os.TempDir(), "configtrans_temp")
		os.MkdirAll(libs.Config.ConfigTrans.TempDir, 0755)
	}
	logging.InfoLogger.Infof("配置转换临时目录: %s", libs.Config.ConfigTrans.TempDir)

	// 初始化任务计数器（用于限制同时处理的任务数量）
	if libs.Config.Cache.Driver == "redis" {
		// 初始化任务计数器
		dconfigtrans.InitTaskCounter()
		logging.InfoLogger.Info("任务计数器初始化完成")
	} else {
		logging.InfoLogger.Warn("未使用Redis作为缓存驱动，任务计数器功能将不可用")
	}

	// 检查Python转换引擎路径
	if libs.Config.ConfigTrans.EnginePath != "" {
		// 检查main.py是否存在
		mainPyPath := filepath.Join(libs.Config.ConfigTrans.EnginePath, "main.py")
		if _, err := os.Stat(mainPyPath); os.IsNotExist(err) {
			logging.ErrorLogger.Errorf("Python转换引擎未找到: %s", mainPyPath)
			return fmt.Errorf("配置转换引擎脚本未找到: %s", mainPyPath)
		}
		logging.InfoLogger.Infof("配置转换引擎路径有效: %s", libs.Config.ConfigTrans.EnginePath)

		// 检查是否配置了厂商
		if len(libs.Config.ConfigTrans.Vendors) == 0 {
			logging.InfoLogger.Warnf("未配置支持的厂商，多厂商转换功能可能无法正常工作")
		} else {
			// 检查每个厂商的接口映射文件
			for vendor, config := range libs.Config.ConfigTrans.Vendors {
				if config.MappingFile != "" {
					mappingPath := filepath.Join(libs.Config.ConfigTrans.EnginePath, libs.Config.ConfigTrans.MappingBaseDir, config.MappingFile)
					if _, err := os.Stat(mappingPath); os.IsNotExist(err) {
						logging.InfoLogger.Warnf("厂商 %s 的接口映射文件未找到: %s", vendor, mappingPath)
						// 不阻止服务启动，仅警告
					} else {
						logging.InfoLogger.Infof("厂商 %s 的接口映射文件路径有效: %s", vendor, mappingPath)
					}
				}
			}
		}
	} else {
		logging.InfoLogger.Warn("未配置配置转换引擎路径")
	}

	// 自动迁移模型，确保表在数据库中创建
	db := easygorm.GetEasyGormDb()
	if db != nil {
		err = db.AutoMigrate(
			&firewallflextrans.ConfigTrans{},   // 添加通用配置转换模型
			&usermodels.Department{},           // 部门表
			&usermodels.Role{},                 // 角色表
			&usermodels.UserDepartment{},       // 用户部门关联表
			&usermodels.User{},                 // 用户表
			&firewallflextrans.DeviceModel{},   // 设备型号表
			&firewallflextrans.DeviceVersion{}, // 设备版本表

		)
		if err != nil {
			logging.ErrorLogger.Errorf("模型迁移失败:", err)
			return err
		}
		// Casbin规则表由casbin适配器自动创建，不需要额外迁移
		logging.InfoLogger.Info("模型迁移成功")

		// 初始化设备型号和版本数据
		if err := firewallflextrans.SeedDeviceData(); err != nil {
			logging.ErrorLogger.Errorf("初始化设备型号和版本数据失败: %v", err)
			// 不阻止服务启动，仅记录错误
		}
	}

	s.RouteInit()

	// 测试国际化功能
	logging.InfoLogger.Info("启动时测试国际化功能...")
	// 由于包导入限制，我们在这里直接调用 i18n.Init()
	// 这个调用会触发翻译文件的加载和调试输出

	// 测试
	go schedule.RunSchedule()
	return nil
}

// RouteInit
func (s *HttpServer) RouteInit() {
	s.App.UseRouter(middleware.CrsAuth())
	app := s.App.Party("/").AllowMethods(iris.MethodOptions)
	{
		// 健康检查端点 - 不需要任何中间件验证
		app.Get("/health", common.HealthCheck)
		app.Get("/healthz", common.HealthCheck)

		// 开启 pprof 调试
		if libs.Config.Pprof {
			app.Get("/debug", func(ctx iris.Context) {
				ctx.HTML("<h1> Please click <a href='/debug/pprof'>here</a>")
			})

			p := pprof.New()
			app.Any("/debug/pprof", p)
			app.Any("/debug/pprof/{action:path}", p)
		}

		v1 := app.Party("api/v1")
		{
			// 是否开启接口请求频率限制
			if !libs.Config.Limit.Disable {
				limitV1 := rate.Limit(libs.Config.Limit.Limit, libs.Config.Limit.Burst, rate.PurgeEvery(time.Minute, 5*time.Minute))
				v1.Use(limitV1)
			}

			// 添加国际化中间件
			v1.Use(middleware.LanguageMiddleware)

			// 访问日志收集
			v1.Use(middleware.AccessLogger)

			// v1.Use(middleware.JwtHandler().Serve, middleware.New().ServeHTTP) //登录验证
			v1.HandleDir("/upload", iris.Dir("./uploads"))

			v1.Get("/queue", queue.GetQueue)
			v1.Post("/queue", queue.PushQueue)

			v1.PartyFunc("/common", func(party iris.Party) {
				party.Get("/check_work_day", common.CheckWorkDay).Name = "检查是否工作日"

				// 添加语言相关的路由
				party.Get("/language", common.SwitchLanguage).Name = "切换语言"
				party.Get("/language/current", common.GetCurrentLanguage).Name = "获取当前语言"
				party.Get("/i18n-demo", common.I18nDemo).Name = "国际化演示页面"
				party.Get("/test-i18n", common.TestI18n).Name = "测试国际化功能"
			})

			v1.PartyFunc("/user", user.Party)
			v1.PartyFunc("/firewallflextrans", firewallflextrans_controllers.Party)
		}
	}
}
