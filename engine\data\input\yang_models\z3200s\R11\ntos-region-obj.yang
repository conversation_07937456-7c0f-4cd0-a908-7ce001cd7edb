module ntos-region-obj {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:region-obj";
  prefix ntos-region-obj;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-if-types {
    prefix ntos-if;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS region object module.";

  revision 2023-10-28 {
    description
      "Initial version.";
    reference "";
  }

  identity region-obj {
    base ntos-types:SERVICE_LOG_ID;
    description
      "region-obj service.";
  }

  typedef region-ip-type {
    type union {
      type ntos-inet:ipv4-address;
      type ntos-inet:ipv4-prefix;
      type ntos-inet:ipv4-range;
      type ntos-inet:ipv4-mask;
    }
  }

  grouping region-db-state {
    description "The state of region database.";
      leaf err-code {
        type uint32;
        description "Error code.";
      }

      leaf buf {
        type string;
        description "Error code information.";
      }

  }

  typedef region-obj-name {
    description
      "This type represents a obj name.";
    type string {
      pattern "[^`~!#$%^&*+|{};:\",\\\\<>?]*" {
        error-message 'cannot include character: `~!#%^&*+|{};:",\<>?';
      }
    }
  }

  grouping region-obj-comm-config {
    leaf name {
      description
        "The name of the region.";
      type region-obj-name;
    }
    leaf description {
      description
        "The description of the region";
      type ntos-types:ntos-obj-description-type;
    }
    list ip-binding {
      key "ip";
      leaf ip {
        description
          "Configure the IP address bound to the region.";
        type region-ip-type;
      }
      ntos-ext:nc-cli-one-liner;
    }
  }

  grouping region-detail {
    list region-user {
      key "name";
      ntos-ext:nc-cli-show-key-name;
      description
        "The custom of the region.";
      uses region-obj-comm-config;
    }
    list region-pre {
      key "name";
      ntos-ext:nc-cli-show-key-name;
      description
        "The custom of the region.";
      uses region-obj-comm-config;
    }
  }

  grouping region-database-history-detail {
    leaf format-version {
      type string;
      description
        "The version of the region database.";
    }
    leaf version {
      type string;
      description
        "The version of the region database.";
    }
    leaf description {
      type string;
      description
        "The description of the region database.";
    }
    leaf change-version {
      type string;
      description
        "The version_change of the region database.";
    }
    list change-create {
      description
        "The create to the region.";
      key "name";
      leaf name {
        type string;
      }
    }
    list change-delete {
      description
        "The delete to the region.";
      key "name";
      leaf name {
        type string;
      }
    }
    list change-modify {
      description
        "The change to the region.";
      key "name";
      leaf name {
        type string;
      }
    }
  }

  grouping output-region-detail {
    leaf name {
      type string;
      description
        "The name of the custom region.";
    }
    leaf full-name {
      type string;
      description
        "Configure the name of the region.";
    }
    leaf description {
      type string;
      description
        "The description of custom region.";
    }
    leaf has-children {
      type boolean;
      description
        "Does it have child nodes.";
    }

    leaf parent-region {
      type string;
      description
        "The parent of custom region.";
    }
    leaf obj-type {
      type string;
      description
        "The type of region object.";
    }
    leaf ip-count {
      description
      "Total number of ref ip.";
      type uint32;
    }
    list ip-binding {
      description
        "The IP address bound to the region.";
      key "ip";
      leaf ip {
        type string;
      }
    }
    list ply-ref-type {
      description
        "The policy type referenced by the regional object.";
      key "ply-type";
      leaf ply-type {
        type string;
        description
          "The name of custom policy.";
      }
      leaf ply-count {
        type uint32;
      }

      list src-ply-id {
        key "id";
        leaf id {
          type uint32;
          description
            "The id of security policy.";
        }
      }

      list dst-ply-id {
        key "id";
        leaf id {
          type uint32;
          description
            "The id of security policy.";
        }
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Region object configuration.";

    container region-obj {
      presence
        "Region object configuration.";
      description
        "Region object configuration.";

      uses region-detail;
    }
  }

  rpc show-region-obj {
    description
      "Show state of region object.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
      container content {
        ntos-ext:nc-cli-group "subcommand";
        leaf type {
          type enumeration {
            enum region-pre;
            enum region-user;
            enum region-all;
            enum organization;
          }

          description
            "Show type of region object.";
        }

        leaf filter {
          type string;
          description
            "Filter.";
        }

        leaf name {
          type string;
          description
            "name.";
        }

        leaf children-region {
          type string;
          description
            "children-region.";
        }

        leaf start {
          type uint32;
          description
            "Start offset of result.";
        }

        leaf end {
          type uint32;
          description
            "End offset of result.";
        }
      }
    }

    output {
      leaf region-total {
        description
        "Total number of region.";
        type uint32;
      }

      list region {
        key "name";
        description
          "The detail of predefined region.";
        uses output-region-detail;
      }

      list organization {
        leaf json {
          description
            "The json data of result.";
          type string;
        }
      }
    }
    ntos-ext:nc-cli-show "region-obj";
    ntos-api:internal;
  }

  rpc region-db-update {
    description "Update region database.";

    input {
      leaf filename {
        type string;
        description "Path of the update file.";
      }
    }

    output {
      uses region-db-state;
    }
    ntos-ext:nc-cli-cmd "region-db update";
  }

  rpc region-db-state {
    description "Show region database state.";

    output {
      uses region-db-state;
    }
    ntos-ext:nc-cli-show "region-db state";
  }

  rpc region-db-version {
    description "Show region database version.";

    output {
      leaf version {
        type string;
        description "database version.";
      }
    }

    ntos-ext:nc-cli-show "region-db version";
  }

  rpc region-db-search {
    description "Search which region an IP belongs to.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }

      leaf ip {
        description
          "Configure the IP address bound to the region.";
        type ntos-inet:ipv4-address;
      }
    }

    output {
      list region {
        key "name";
        description
          "The detail of predefined region.";
        uses output-region-detail;
      }
    }
    ntos-ext:nc-cli-show "region-db search";
  }

  rpc region-db-update-history {
    description "Show region database update history.";

    output {
      leaf history-total {
        description
        "Total number of update history.";
        type uint32;
      }
      list update-history {
        key "format-version";
        description
          "The detail of database update hisotry.";
        uses region-database-history-detail;
      }
    }

    ntos-ext:nc-cli-show "region-db update-history";
  }
}
