[{"name": "root", "version": 90637, "ts": *************}, {"name": "physical", "version": 90637, "ts": *************}, {"name": "sub_interface", "version": 90637, "ts": *************}, {"name": "routing", "version": 90637, "ts": *************}, {"name": "dhcp", "version": 90637, "ts": *************}, {"name": "security_zone", "version": 90637, "ts": *************}, {"name": "auth", "version": 90637, "ts": *************}, {"name": "devicename", "version": 90637, "ts": *************}, {"name": "discovery", "version": 90637, "ts": *************}, {"name": "timezone", "version": 90637, "ts": *************}, {"name": "security-policy", "version": 90637, "ts": *************}, {"name": "network-obj", "version": 90637, "ts": *************}, {"name": "appid", "version": 90637, "ts": *************}, {"name": "service-obj", "version": 90637, "ts": *************}, {"name": "time-range", "version": 90637, "ts": *************}, {"name": "ips-config", "version": 90637, "ts": *************}, {"name": "anti-virus", "version": 90637, "ts": *************}, {"name": "url-filter", "version": 90637, "ts": *************}, {"name": "url-category", "version": 90637, "ts": *************}, {"name": "security-defend", "version": 90637, "ts": *************}, {"name": "threat-intelligence", "version": 90637, "ts": *************}, {"name": "mac-block", "version": 90637, "ts": *************}, {"name": "user-experience", "version": 90637, "ts": *************}]