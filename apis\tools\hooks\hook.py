#!/opt/gitlab/embedded/bin/python3
# coding=utf-8
import os
import sys
import subprocess
import gitlab
import requests
import traceback
import time

__author__ = "linjiakai"


class Trigger(object):
    def __init__(self):
        '''
        初始化文件列表信息，提交者信息，提交时间,当前操作的分支
        '''
        url = 'http://*************:8180/'
        private_token = 'Wx4dLVCHwR52VgamVRn-'
        self.gl = gitlab.Gitlab(url, private_token)
        self.sess = requests.session()
        self.ids = []

    def getInfo(self):
        '''
        '''
        self.id = os.getenv("GL_REPOSITORY").split('-')[-1]
        self.base, self.commit, self.ref = sys.stdin.readline().strip().split()
        self.project = self.gl.projects.get(id=self.id)
        self.forked_from_ssh_url_to_repo = self.project.forked_from_project.get('ssh_url_to_repo')
        self.branches = self.project.branches.list()

        self.branchname = self.ref.split('/')[-1]
        self.username = os.getenv("GL_USERNAME")

    def checkCreateAndDelete(self):
        if int(self.id) in self.ids:
            if self.base == '0'*40 and self.username != 'buildfarm':
                print('GL-HOOK-ERR:', '只允许通过版本发布系统创建主仓分支')
                sys.exit(1)

            if self.commit == '0'*40 and self.username != 'buildfarm':
                print('GL-HOOK-ERR:', '禁止删除主仓分支')
                sys.exit(1)

    def getProductRepos(self):
        for i in range(5):
            try:
                url = 'http://************:9090/mergerequest-api/api/v1/mr/unitpackages?pageSize=-1'
                resp = self.sess.get(url)
                repos = resp.json().get('data', {"items": []}).get('items')
                self.ids = [repo.get('gitlab_id') for repo in repos]
            except:
                traceback.print_exc()
                time.sleep(1)
        if len(self.ids) == 0:
            print('GL-HOOK-ERR:', "系统错误, 请重试, 多次出现, 请联系管理员！")
            sys.exit(1)

    def getPushInfo(self):
        '''
        git show命令获取push作者，时间，以及文件列表
        文件的路径为相对于版本库根目录的一个相对路径
        '''

        if self.base == '0'*40 or self.commit == '0'*40:
            return

        rev = subprocess.Popen('git rev-list --all ' + self.commit, shell=True, stdout=subprocess.PIPE)
        revList = rev.stdout.readlines()
        revList = [x.decode("utf-8").strip() for x in revList]

        # print("revList=", revList)
        # print("self.oldObject=", self.base)

        if self.base not in revList:
            print('GL-HOOK-ERR:', "提交存在异常, 请重试, 多次出现, 请联系管理员！")
            sys.exit(1)

        indexOld = revList.index(self.base)
        pushList = revList[:indexOld]
        pushList.reverse()

        for pObject in pushList:
            p = subprocess.Popen('git show ' + pObject, shell=True, stdout=subprocess.PIPE)
            lines = p.stdout.readlines()
            lines = [x.decode("utf-8").strip() for x in lines]
            # file = lines[6].strip("diff").strip()
            # print(file)

            for line in lines:
                self.check_libfpsig(line)

    def check_libfpsig(self, line):
        if line.startswith("+") and line.find("libfpsig_") > 0:
            if not ('/fast-path.git' in self.forked_from_ssh_url_to_repo or '/sig-security.git' in self.forked_from_ssh_url_to_repo):
                print('GL-HOOK-ERR:', "非FP、sig-security两个仓库，禁止调用libfpsig_开头的函数")
                sys.exit(1)

    def check(self):
        self.getInfo()
        self.getProductRepos()
        self.checkCreateAndDelete()
        self.getPushInfo()


if __name__ == "__main__":
    t = Trigger()
    t.check()
    sys.exit(0)
