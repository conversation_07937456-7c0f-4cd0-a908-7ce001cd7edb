import sys
with open(sys.argv[1], 'r', encoding='utf8') as f:
    l = list(f)
    for i in range(len(l)-3):
        if len(l[i].strip()) == 0:
            continue
        if "name" in l[i] and "act" in l[i+3]:
            url = l[i].split()[2].replace('"', '')
            act = l[i+3].split()[1].replace('"', '')
            # print(url, act)
            rule = 'insert into casbin_rule (ptype, v0, v1, v2) values ("{}","{}","{}","{}");'.format('p', '4', url, act)
            print(rule)