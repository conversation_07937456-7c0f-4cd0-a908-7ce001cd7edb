# FortiGate twice-nat44 API文档

## 概述

本文档描述了FortiGate twice-nat44转换功能的API接口，包括数据模型、转换方法、错误处理和性能优化等核心功能。

## 核心API

### 数据模型

#### TwiceNat44Rule

twice-nat44规则的核心数据结构。

```python
from engine.business.models.twice_nat44_models import TwiceNat44Rule

class TwiceNat44Rule:
    """twice-nat44规则数据模型"""
    
    def __init__(self, name: str, description: str = None, enabled: bool = True):
        """
        初始化twice-nat44规则
        
        Args:
            name: 规则名称
            description: 规则描述
            enabled: 是否启用
        """
```

**类方法**

##### from_fortigate_policy()

从FortiGate策略和VIP配置创建twice-nat44规则。

```python
@classmethod
def from_fortigate_policy(cls, policy: Dict[str, Any], vip_config: Dict[str, Any]) -> 'TwiceNat44Rule':
    """
    从FortiGate策略创建twice-nat44规则
    
    Args:
        policy: FortiGate策略配置
        vip_config: VIP对象配置
        
    Returns:
        TwiceNat44Rule: 创建的规则对象
        
    Raises:
        TwiceNat44ConfigError: 配置错误
        TwiceNat44ValidationError: 验证失败
    """
```

**示例**
```python
policy = {
    "name": "WEB_POLICY",
    "status": "enable",
    "service": ["HTTP", "HTTPS"],
    "fixedport": "disable"
}

vip = {
    "name": "WEB_VIP",
    "mappedip": "*************",
    "mappedport": "8080"
}

rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
print(f"创建规则: {rule.name}")
```

##### validate()

验证规则配置的完整性和正确性。

```python
def validate(self) -> bool:
    """
    验证规则配置
    
    Returns:
        bool: 验证是否通过
        
    Raises:
        TwiceNat44ValidationError: 验证失败时抛出
    """
```

##### to_xml()

将规则转换为XML格式。

```python
def to_xml(self) -> etree.Element:
    """
    转换为XML元素
    
    Returns:
        etree.Element: XML元素对象
    """
```

#### TwiceNat44Config

twice-nat44配置管理类。

```python
from engine.business.models.twice_nat44_models import TwiceNat44Config

class TwiceNat44Config:
    """twice-nat44配置管理"""
    
    def __init__(self, snat_config: Dict = None, dnat_config: Dict = None):
        """
        初始化配置
        
        Args:
            snat_config: SNAT配置
            dnat_config: DNAT配置
        """
```

### 转换策略

#### FortiGateStrategy扩展

FortiGate转换策略的twice-nat44扩展。

```python
from engine.strategies.fortigate_strategy import FortiGateStrategy

strategy = FortiGateStrategy()

# 检查是否支持twice-nat44
supports = strategy._supports_twice_nat44(policy, vip_config)

# 生成twice-nat44规则
rule = strategy._generate_twice_nat44_rule(policy, vip_config)
```

##### _supports_twice_nat44()

检查策略和VIP配置是否支持twice-nat44转换。

```python
def _supports_twice_nat44(self, policy: Dict[str, Any], vip_config: Dict[str, Any]) -> bool:
    """
    检查是否支持twice-nat44转换
    
    Args:
        policy: FortiGate策略
        vip_config: VIP配置
        
    Returns:
        bool: 是否支持转换
    """
```

##### _generate_twice_nat44_rule()

生成twice-nat44规则。

```python
def _generate_twice_nat44_rule(self, policy: Dict[str, Any], vip_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成twice-nat44规则
    
    Args:
        policy: FortiGate策略
        vip_config: VIP配置
        
    Returns:
        Dict[str, Any]: 生成的规则配置
    """
```

### XML生成

#### NATGenerator扩展

NAT生成器的twice-nat44扩展。

```python
from engine.generators.nat_generator import NATGenerator

generator = NATGenerator()

# 生成twice-nat44 XML
xml_element = generator._add_twice_nat44_config(rule_element, rule_config)
```

##### _add_twice_nat44_config()

为规则元素添加twice-nat44配置。

```python
def _add_twice_nat44_config(self, rule_element: etree.Element, rule_config: Dict[str, Any]) -> etree.Element:
    """
    添加twice-nat44配置到规则元素
    
    Args:
        rule_element: 规则XML元素
        rule_config: 规则配置
        
    Returns:
        etree.Element: 更新后的规则元素
    """
```

### 错误处理

#### 异常类型

```python
from engine.business.models.twice_nat44_models import (
    TwiceNat44ConfigError,
    TwiceNat44ValidationError
)

# 配置错误
try:
    rule = TwiceNat44Rule.from_fortigate_policy(invalid_policy, invalid_vip)
except TwiceNat44ConfigError as e:
    print(f"配置错误: {e}")

# 验证错误
try:
    rule.validate()
except TwiceNat44ValidationError as e:
    print(f"验证失败: {e}")
```

#### 错误处理装饰器

```python
from engine.infrastructure.error_handling import twice_nat44_error_handler

@twice_nat44_error_handler(
    operation="custom_operation",
    max_retries=3,
    handle_exceptions=(TwiceNat44ConfigError, ValueError),
    log_errors=True,
    attempt_recovery=True
)
def process_rules(rules):
    """带有自动错误处理的函数"""
    return processed_rules
```

#### 错误处理器

```python
from engine.infrastructure.error_handling import (
    TwiceNat44ErrorHandler, TwiceNat44ErrorContext
)

# 创建错误处理器
handler = TwiceNat44ErrorHandler()

# 创建错误上下文
context = TwiceNat44ErrorContext(
    operation="rule_conversion",
    policy_name="TEST_POLICY",
    vip_name="TEST_VIP"
)

# 处理错误
result = handler.handle_twice_nat44_error(exception, context)
```

### 性能优化

#### 性能优化器

```python
from engine.infrastructure.performance import get_twice_nat44_optimizer

# 获取优化器实例
optimizer = get_twice_nat44_optimizer()

# 批量处理优化
def rule_processor(rule_data):
    return TwiceNat44Rule.from_fortigate_policy(
        rule_data["policy"], rule_data["vip"]
    )

results, metrics = optimizer.optimize_batch_processing(rules, rule_processor)

print(f"处理{len(rules)}个规则")
print(f"吞吐量: {metrics.throughput:.1f}规则/秒")
print(f"成功率: {metrics.success_rate:.1f}%")
```

#### 性能装饰器

```python
from engine.infrastructure.error_handling import (
    twice_nat44_performance_optimized,
    twice_nat44_memory_optimized
)

@twice_nat44_performance_optimized(
    use_cache=True,
    use_object_pool=True,
    batch_size=50
)
@twice_nat44_memory_optimized(
    gc_threshold=100,
    memory_limit=512.0
)
def process_large_batch(rules):
    """高性能批量处理函数"""
    return processed_rules
```

#### 缓存管理

```python
from engine.infrastructure.performance import TwiceNat44Cache

# 创建缓存
cache = TwiceNat44Cache(max_size=1000, ttl=3600)

# 存储和获取
cache.put("rule_key", rule_data)
cached_data = cache.get("rule_key")

# 获取统计信息
stats = cache.get_stats()
print(f"缓存命中率: {stats['hit_rate']:.1f}%")
```

### 验证器

#### TwiceNat44Validator

XML验证器，用于验证生成的twice-nat44 XML的正确性。

```python
from engine.validators.twice_nat44_validator import TwiceNat44Validator

validator = TwiceNat44Validator()

# 验证XML元素
is_valid, errors = validator.validate_twice_nat44_rule(xml_element)

if not is_valid:
    for error in errors:
        print(f"验证错误: {error}")
```

## 配置参数

### 系统配置

在系统配置中添加以下参数来控制twice-nat44功能：

```python
# 启用twice-nat44转换
enable_twice_nat44_conversion = True

# twice-nat44优先级（高于传统NAT）
twice_nat44_priority = True

# 性能优化配置
twice_nat44_batch_size = 100
twice_nat44_cache_size = 1000
twice_nat44_cache_ttl = 3600
```

### 日志配置

```python
# 启用twice-nat44详细日志
twice_nat44_debug_logging = False

# 性能监控日志
twice_nat44_performance_logging = True

# 错误处理日志
twice_nat44_error_logging = True
```

## 最佳实践

### 1. 错误处理

```python
# 推荐：使用装饰器进行自动错误处理
@twice_nat44_error_handler(operation="batch_conversion", max_retries=2)
def convert_policies(policies, vips):
    results = []
    for policy, vip in zip(policies, vips):
        rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
        results.append(rule)
    return results

# 推荐：手动错误处理
try:
    rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
    rule.validate()
except TwiceNat44ConfigError as e:
    # 处理配置错误
    logger.error(f"配置错误: {e}")
    # 使用回退方案
    rule = create_fallback_rule(policy, vip)
```

### 2. 性能优化

```python
# 推荐：批量处理
optimizer = get_twice_nat44_optimizer()
results, metrics = optimizer.optimize_batch_processing(rules, processor)

# 推荐：使用缓存
@twice_nat44_performance_optimized(use_cache=True)
def process_rule(rule_data):
    return expensive_operation(rule_data)

# 推荐：内存管理
@twice_nat44_memory_optimized(gc_threshold=50)
def process_large_dataset(dataset):
    return process_data(dataset)
```

### 3. 验证和测试

```python
# 推荐：验证生成的XML
validator = TwiceNat44Validator()
is_valid, errors = validator.validate_twice_nat44_rule(xml_element)

if not is_valid:
    raise TwiceNat44ValidationError(f"XML验证失败: {errors}")

# 推荐：单元测试
def test_twice_nat44_creation():
    policy = create_test_policy()
    vip = create_test_vip()
    
    rule = TwiceNat44Rule.from_fortigate_policy(policy, vip)
    
    assert rule.name == "TEST_RULE"
    assert rule.validate() == True
    
    xml = rule.to_xml()
    assert xml is not None
```

## 故障排除

### 常见错误

1. **TwiceNat44ConfigError**: 配置不完整或无效
   - 检查VIP配置是否包含必需的mappedip字段
   - 验证策略配置的完整性

2. **TwiceNat44ValidationError**: 验证失败
   - 检查IP地址格式是否正确
   - 验证端口范围是否有效

3. **XML生成错误**: XML结构不正确
   - 检查模板配置
   - 验证YANG模型兼容性

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger('twice_nat44').setLevel(logging.DEBUG)

# 获取性能统计
optimizer = get_twice_nat44_optimizer()
stats = optimizer.get_performance_stats()
print(f"性能统计: {stats}")

# 获取错误统计
handler = get_twice_nat44_error_handler()
error_stats = handler.get_error_statistics()
print(f"错误统计: {error_stats}")
```

## 版本兼容性

- **最低要求**: Python 3.7+
- **依赖库**: lxml, psutil
- **向后兼容**: 完全兼容现有NAT转换功能
- **升级路径**: 无需修改现有代码，自动启用twice-nat44功能

## 支持和反馈

如有问题或建议，请联系开发团队或提交issue到项目仓库。

---

*本文档版本: 1.0.0*
*最后更新: 2025-08-01*
*适用版本: FortiGate转换器 v2.0+*
