"""
NTOS YANG模型验证服务

本模块提供对生成的XML配置进行YANG模型合规性验证的功能，
特别针对twice-nat44配置的验证。

主要功能：
- YANG模型结构验证
- twice-nat44配置验证
- 命名空间合规性检查
- 数据类型验证
- 约束条件检查

版本: 1.0
作者: FortiGate转换系统
创建时间: 2025-08-06
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import xml.etree.ElementTree as ET
from engine.utils.logger import log
from engine.utils.i18n import _


class ValidationSeverity(Enum):
    """验证问题严重程度"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


@dataclass
class ValidationIssue:
    """
    验证问题数据结构
    """
    severity: ValidationSeverity
    message: str
    xpath: Optional[str] = None
    element_name: Optional[str] = None
    expected_value: Optional[str] = None
    actual_value: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "severity": self.severity.value,
            "message": self.message,
            "xpath": self.xpath,
            "element_name": self.element_name,
            "expected_value": self.expected_value,
            "actual_value": self.actual_value
        }


@dataclass
class ValidationResult:
    """
    验证结果数据结构
    """
    is_valid: bool
    issues: List[ValidationIssue]
    warnings_count: int = 0
    errors_count: int = 0
    
    def __post_init__(self):
        """计算问题统计"""
        self.warnings_count = sum(1 for issue in self.issues if issue.severity == ValidationSeverity.WARNING)
        self.errors_count = sum(1 for issue in self.issues if issue.severity == ValidationSeverity.ERROR)
        
        # 如果有错误，则验证失败
        if self.errors_count > 0:
            self.is_valid = False
    
    def add_issue(self, issue: ValidationIssue):
        """添加验证问题"""
        self.issues.append(issue)
        if issue.severity == ValidationSeverity.ERROR:
            self.errors_count += 1
            self.is_valid = False
        elif issue.severity == ValidationSeverity.WARNING:
            self.warnings_count += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "is_valid": self.is_valid,
            "errors_count": self.errors_count,
            "warnings_count": self.warnings_count,
            "issues": [issue.to_dict() for issue in self.issues]
        }


class YangValidator:
    """
    NTOS YANG模型验证器
    
    提供对XML配置的YANG模型合规性验证功能。
    """
    
    def __init__(self):
        """初始化验证器"""
        self.nat_namespace = "urn:ruijie:ntos:params:xml:ns:yang:nat"
        self.ntos_namespace = "urn:ruijie:ntos"
        
        # twice-nat44 YANG模型约束
        self.twice_nat44_constraints = {
            "required_elements": ["snat", "dnat"],
            "optional_elements": ["match"],
            "snat_address_types": ["output-address", "ipv4-address", "pool-name"],
            "dnat_required_elements": ["ipv4-address"],
            "dnat_optional_elements": ["port"]
        }
        
        log(_("yang_validator.initialized"), "info")
    
    def validate_twice_nat44_xml(self, xml_element: ET.Element) -> ValidationResult:
        """
        验证twice-nat44 XML配置的YANG模型合规性
        
        Args:
            xml_element: twice-nat44 XML元素
            
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult(is_valid=True, issues=[])
        
        try:
            log(_("yang_validator.twice_nat44_validation_started"), "info")
            
            # 1. 基本结构验证
            self._validate_basic_structure(xml_element, result)
            
            # 2. 命名空间验证
            self._validate_namespace(xml_element, result)
            
            # 3. 必需元素验证
            self._validate_required_elements(xml_element, result)
            
            # 4. SNAT配置验证
            snat_element = xml_element.find("snat")
            if snat_element is not None:
                self._validate_snat_element(snat_element, result)
            
            # 5. DNAT配置验证
            dnat_element = xml_element.find("dnat")
            if dnat_element is not None:
                self._validate_dnat_element(dnat_element, result)
            
            # 6. 匹配条件验证（如果有）
            match_element = xml_element.find("match")
            if match_element is not None:
                self._validate_match_element(match_element, result)
            
            log(_("yang_validator.twice_nat44_validation_completed",
                 is_valid=result.is_valid,
                 errors=result.errors_count,
                 warnings=result.warnings_count), "info")
            
            return result
            
        except Exception as e:
            log(_("yang_validator.validation_failed", error=str(e)), "error")
            result.add_issue(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message=f"Validation process failed: {str(e)}"
            ))
            return result
    
    def _validate_basic_structure(self, xml_element: ET.Element, result: ValidationResult):
        """验证基本结构"""
        try:
            # 检查元素标签
            if xml_element.tag != "twice-nat44":
                result.add_issue(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    message=f"Expected 'twice-nat44' element, got '{xml_element.tag}'",
                    element_name=xml_element.tag
                ))
            
            # 检查是否为空元素
            if len(xml_element) == 0 and not xml_element.text:
                result.add_issue(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    message="twice-nat44 element cannot be empty",
                    element_name="twice-nat44"
                ))
            
        except Exception as e:
            result.add_issue(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message=f"Basic structure validation failed: {str(e)}"
            ))
    
    def _validate_namespace(self, xml_element: ET.Element, result: ValidationResult):
        """验证命名空间"""
        try:
            # 检查命名空间声明
            xmlns = xml_element.get("xmlns")
            if xmlns and xmlns != self.nat_namespace:
                result.add_issue(ValidationIssue(
                    severity=ValidationSeverity.WARNING,
                    message=f"Unexpected namespace: {xmlns}",
                    expected_value=self.nat_namespace,
                    actual_value=xmlns
                ))
            
        except Exception as e:
            result.add_issue(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message=f"Namespace validation failed: {str(e)}"
            ))
    
    def _validate_required_elements(self, xml_element: ET.Element, result: ValidationResult):
        """验证必需元素"""
        try:
            required_elements = self.twice_nat44_constraints["required_elements"]
            
            for required_elem in required_elements:
                if xml_element.find(required_elem) is None:
                    result.add_issue(ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        message=f"Missing required element: {required_elem}",
                        element_name=required_elem
                    ))
            
        except Exception as e:
            result.add_issue(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message=f"Required elements validation failed: {str(e)}"
            ))
    
    def _validate_snat_element(self, snat_element: ET.Element, result: ValidationResult):
        """验证SNAT元素"""
        try:
            # 检查地址类型（必须有一种）
            address_types = self.twice_nat44_constraints["snat_address_types"]
            has_address_type = any(snat_element.find(addr_type) is not None for addr_type in address_types)
            
            if not has_address_type:
                result.add_issue(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    message="SNAT element must have one address type",
                    element_name="snat",
                    expected_value=f"One of: {', '.join(address_types)}"
                ))
            
            # 验证IP地址格式（如果使用静态IP）
            ipv4_elem = snat_element.find("ipv4-address")
            if ipv4_elem is not None and ipv4_elem.text:
                if not self._is_valid_ipv4(ipv4_elem.text):
                    result.add_issue(ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        message=f"Invalid IPv4 address in SNAT: {ipv4_elem.text}",
                        element_name="ipv4-address",
                        actual_value=ipv4_elem.text
                    ))
            
            # 验证布尔值
            self._validate_boolean_element(snat_element, "no-pat", result)
            self._validate_boolean_element(snat_element, "try-no-pat", result)
            
        except Exception as e:
            result.add_issue(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message=f"SNAT element validation failed: {str(e)}"
            ))
    
    def _validate_dnat_element(self, dnat_element: ET.Element, result: ValidationResult):
        """验证DNAT元素"""
        try:
            # 检查必需的IPv4地址
            ipv4_elem = dnat_element.find("ipv4-address")
            if ipv4_elem is None:
                result.add_issue(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    message="DNAT element must have ipv4-address",
                    element_name="dnat"
                ))
            elif ipv4_elem.text and not self._is_valid_ipv4(ipv4_elem.text):
                result.add_issue(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    message=f"Invalid IPv4 address in DNAT: {ipv4_elem.text}",
                    element_name="ipv4-address",
                    actual_value=ipv4_elem.text
                ))
            
            # 验证端口号（如果有）
            port_elem = dnat_element.find("port")
            if port_elem is not None and port_elem.text:
                try:
                    port = int(port_elem.text)
                    if not (1 <= port <= 65535):
                        result.add_issue(ValidationIssue(
                            severity=ValidationSeverity.ERROR,
                            message=f"Invalid port number: {port}",
                            element_name="port",
                            actual_value=str(port),
                            expected_value="1-65535"
                        ))
                except ValueError:
                    result.add_issue(ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        message=f"Port must be a number: {port_elem.text}",
                        element_name="port",
                        actual_value=port_elem.text
                    ))
            
        except Exception as e:
            result.add_issue(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message=f"DNAT element validation failed: {str(e)}"
            ))
    
    def _validate_match_element(self, match_element: ET.Element, result: ValidationResult):
        """验证匹配条件元素"""
        try:
            # 基本的匹配条件验证
            # 这里可以根据需要添加更详细的验证逻辑
            if len(match_element) == 0:
                result.add_issue(ValidationIssue(
                    severity=ValidationSeverity.WARNING,
                    message="Match element is empty",
                    element_name="match"
                ))
            
        except Exception as e:
            result.add_issue(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message=f"Match element validation failed: {str(e)}"
            ))
    
    def _validate_boolean_element(self, parent_element: ET.Element, element_name: str, result: ValidationResult):
        """验证布尔值元素"""
        try:
            elem = parent_element.find(element_name)
            if elem is not None and elem.text:
                if elem.text.lower() not in ["true", "false"]:
                    result.add_issue(ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        message=f"Invalid boolean value for {element_name}: {elem.text}",
                        element_name=element_name,
                        actual_value=elem.text,
                        expected_value="true or false"
                    ))
        except Exception as e:
            result.add_issue(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message=f"Boolean validation failed for {element_name}: {str(e)}"
            ))
    
    def _is_valid_ipv4(self, ip: str) -> bool:
        """验证IPv4地址格式"""
        try:
            import ipaddress
            ipaddress.IPv4Address(ip)
            return True
        except (ipaddress.AddressValueError, ValueError):
            return False
