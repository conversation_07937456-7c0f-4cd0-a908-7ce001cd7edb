module ntos-firewall {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:firewall";
  prefix ntos-firewall;

  import extra-conditions {
    prefix ext-cond;
  }
  import ntos-firewall-modules {
    prefix ntos-mod;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ietf-inet-types {
    prefix inet;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS firewall.";

  revision 2020-02-12 {
    description
      "Sort rules by id.";
    reference "";
  }
  revision 2018-11-29 {
    description
      "Ensure that rules only reference existing user chains.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  grouping policy {
    description
      "Rule policy.";

    leaf policy {
      type ntos-mod:standard-actions;
      default "accept";
      description
        "Action when no rule match.";
    }
  }

  grouping filter-input-state {
    description
      "Filter input state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses filter-input-rule-config;
    }
    uses counters;
  }

  grouping filter-input-config {
    description
      "Filter input configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses filter-input-rule-config;
    }
  }

  grouping filter-input-rule-config {
    description
      "Filter input rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:inbound-interface-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:reject-action;
    }
  }

  grouping filter-forward-state {
    description
      "Filter forward state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses filter-forward-rule-config;
    }
    uses counters;
  }

  grouping filter-forward-config {
    description
      "Filter forward configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses filter-forward-rule-config;
    }
  }

  grouping filter-forward-rule-config {
    description
      "Filter forward rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:inbound-interface-match;
    uses ntos-mod:outbound-interface-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:reject-action;
    }
  }

  grouping filter-output-state {
    description
      "Filter output state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses filter-output-rule-config;
    }
    uses counters;
  }

  grouping filter-output-config {
    description
      "Filter output configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses filter-output-rule-config;
    }
  }

  grouping filter-output-rule-config {
    description
      "Filter output rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:outbound-interface-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:reject-action;
    }
  }

  grouping mangle-prerouting-state {
    description
      "Mangle prerouting state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses mangle-prerouting-rule-config;
    }
    uses counters;
  }

  grouping mangle-prerouting-config {
    description
      "Mangle prerouting configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses mangle-prerouting-rule-config;
    }
  }

  grouping mangle-prerouting-rule-config {
    description
      "Mangle prerouting rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:inbound-interface-match;
    uses ntos-mod:rpfilter-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:dscp-action;
      uses ntos-mod:tos-action;
    }
  }

  grouping mangle-input-state {
    description
      "Mangle input state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses mangle-input-rule-config;
    }
    uses counters;
  }

  grouping mangle-input-config {
    description
      "Mangle input configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses mangle-input-rule-config;
    }
  }

  grouping mangle-input-rule-config {
    description
      "Mangle input rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:inbound-interface-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:dscp-action;
      uses ntos-mod:tos-action;
    }
  }

  grouping mangle-forward-state {
    description
      "Mangle forward state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses mangle-forward-rule-config;
    }
    uses counters;
  }

  grouping mangle-forward-config {
    description
      "Mangle forward configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses mangle-forward-rule-config;
    }
  }

  grouping mangle-forward-rule-config {
    description
      "Mangle forward rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:inbound-interface-match;
    uses ntos-mod:outbound-interface-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:dscp-action;
      uses ntos-mod:tos-action;
    }
  }

  grouping mangle-output-state {
    description
      "Mangle output state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses mangle-output-rule-config;
    }
    uses counters;
  }

  grouping mangle-output-config {
    description
      "Mangle output configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses mangle-output-rule-config;
    }
  }

  grouping mangle-output-rule-config {
    description
      "Mangle output rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:outbound-interface-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:dscp-action;
      uses ntos-mod:tos-action;
    }
  }

  grouping mangle-postrouting-state {
    description
      "Mangle postrouting state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses mangle-postrouting-rule-config;
    }
    uses counters;
  }

  grouping mangle-postrouting-config {
    description
      "Mangle postrouting configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses mangle-postrouting-rule-config;
    }
  }

  grouping mangle-postrouting-rule-config {
    description
      "Mangle postrouting rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:outbound-interface-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:dscp-action;
      uses ntos-mod:tos-action;
    }
  }

  grouping raw-prerouting-state {
    description
      "Raw prerouting state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses raw-prerouting-rule-config;
    }
    uses counters;
  }

  grouping raw-prerouting-config {
    description
      "Raw prerouting configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses raw-prerouting-rule-config;
    }
  }

  grouping raw-prerouting-rule-config {
    description
      "Raw prerouting rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:inbound-interface-match;
    uses ntos-mod:rpfilter-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:notrack-action;
    }
  }

  grouping raw-output-state {
    description
      "Raw output state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses raw-output-rule-config;
    }
    uses counters;
  }

  grouping raw-output-config {
    description
      "Raw output configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses raw-output-rule-config;
    }
  }

  grouping raw-output-rule-config {
    description
      "Raw output rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:outbound-interface-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:notrack-action;
    }
  }

  grouping userchain-config {
    description
      "User chain configuration.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";
      uses userchain-rule-config;
    }
  }

  grouping userchain-state {
    description
      "User chain state.";
    uses policy;

    list rule {
      key "id";
      description
        "A rule to perform an action on matching packets.";
      ntos-extensions:nc-cli-one-liner;
      ntos-extensions:nc-cli-sort-by "id";

      container counters {
        description
          "The counters of this rule.";
        uses counters;
      }
      uses userchain-rule-config;
    }
    uses counters;
  }

  grouping userchain-rule-config {
    description
      "User chain rule configuration.";

    leaf id {
      type uint64;
      description
        "Priority of the rule. High number means lower priority.";
    }
    uses default-matches;
    uses ntos-mod:inbound-interface-match;
    uses ntos-mod:outbound-interface-match;
    uses ntos-mod:rpfilter-match;

    container action {
      must 'count(*) = 1' {
        error-message "A rule must have exactly one action.";
      }
      description
        "The action performed by this rule.";
      ntos-extensions:nc-cli-exclusive;
      uses default-actions;
      uses ntos-mod:dscp-action;
      uses ntos-mod:tos-action;
      uses ntos-mod:reject-action;
    }
  }

  grouping default-matches {
    description
      "Default matches.";

    leaf description {
      type string;
      description
        "A comment to describe the rule.";
    }
    uses ntos-mod:protocol-match;
    uses ntos-mod:dest-match;
    uses ntos-mod:source-match;
    uses ntos-mod:fragment-match;
    uses ntos-mod:icmp-type-match;
    uses ntos-mod:tcp-flags-match;
    uses ntos-mod:conntrack-match;
    uses ntos-mod:connmark-match;
    uses ntos-mod:limit-match;
    uses ntos-mod:dscp-match;
    uses ntos-mod:tos-match;
    uses ntos-mod:mark-match;
    uses ntos-mod:sctp-match;
  }

  grouping default-actions {
    description
      "Default actions.";

    leaf standard {
      type ntos-mod:standard-actions;
      description
        "Standard action.";
      ntos-extensions:nc-cli-no-name;
    }

    leaf chain {
      type leafref {
        path
          "../../../../chain/name";
      }
      description
        "Jump to the user chain by this name.";
    }
    uses ntos-mod:connmark-action;
    uses ntos-mod:log-action;
    uses ntos-mod:mark-action;
    uses ntos-mod:tcpmss-action;
  }

  grouping counters {
    description
      "Counters.";

    leaf packets {
      type uint64;
      description
        "Packets.";
    }

    leaf bytes {
      type uint64;
      description
        "Bytes.";
    }
  }

  rpc show-filter-protocols {
    description
      "Show filter protocols.";
    output {
      leaf buffer {
        type string;
        description
          "The content of /etc/protocols.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-show "filter protocols";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Top-level grouping for firewall config.";

    container firewall {
      presence "Makes firewall available";
      description
        "Firewall configuration.";
      ntos-extensions:feature "product";
      ext-cond:unique-values "*/*/*[local-name()='name']" {
        error-message "The address-group / network-group name must be unique in a vrf.";
      }
      ntos-api:extension-added "extra-conditions:unique-values \"*/*/*[local-name()='name']\"";

      container ipv4 {
        presence "Makes ipv4 available";
        description
          "Firewall IPv4 configuration.";

        container filter {
          description
            "Default table.";
          ntos-extensions:feature "product";

          container input {
            description
              "Packets destined to local sockets.";
            uses filter-input-config;
          }

          container forward {
            description
              "Packets being routed.";
            uses filter-forward-config;
          }

          container output {
            description
              "Locally-generated packets.";
            uses filter-output-config;
          }

          list chain {
            key "name";
            description
              "User chain.";

            leaf name {
              // XXX: check user chain format
              type string;
              description
                "The user chain name.";
            }
            uses userchain-config;
          }
        }

        container mangle {
          description
            "Packet alteration table.";
          ntos-extensions:feature "product";

          container prerouting {
            description
              "Altering packets as soon as they come in.";
            uses mangle-prerouting-config;
          }

          container input {
            description
              "Altering packets before routing.";
            uses mangle-input-config;
          }

          container forward {
            description
              "Altering packets being routed.";
            uses mangle-forward-config;
          }

          container output {
            description
              "Altering locally-generated packets before routing.";
            uses mangle-output-config;
          }

          container postrouting {
            description
              "Altering packets as they are about to go.";
            uses mangle-postrouting-config;
          }

          list chain {
            key "name";
            description
              "User chain.";

            leaf name {
              // XXX: check user chain format
              type string;
              description
                "The user chain name.";
            }
            uses userchain-config;
          }
        }

        container raw {
          description
            "Mainly used to exempt packets from connection tracking.";
          ntos-extensions:feature "product";

          container prerouting {
            description
              "Packets as soon as they come in.";
            uses raw-prerouting-config;
          }

          container output {
            description
              "Locally-generated packets before routing.";
            uses raw-output-config;
          }

          list chain {
            key "name";
            description
              "User chain.";

            leaf name {
              // XXX: check user chain format
              type string;
              description
                "The user chain name.";
            }
            uses userchain-config;
          }
        }

        list address-group {
          key "name";
          description
            "Address group.";
          ntos-extensions:feature "product";

          leaf name {
            type string {
              pattern '[0-9a-zA-Z_-]+';
            }
            description
              "Name of the address group.";
          }

          leaf-list address {
            type inet:ipv4-address-no-zone;
            description
              "List of addresses of the group.";
          }
        }

        list network-group {
          key "name";
          description
            "Network group.";
          ntos-extensions:feature "product";

          leaf name {
            type string {
              pattern '[0-9a-zA-Z_-]+';
            }
            description
              "Name of the network group.";
          }

          leaf-list network {
            type ntos-inet:ipv4-prefix;
            description
              "List of networks of the group.";
          }
        }
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Top-level grouping for firewall state data.";

    container firewall {
      description
        "Firewall state.";
      ntos-extensions:feature "product";

      container ipv4 {
        description
          "Firewall IPv4 state.";

        container filter {
          description
            "Default table.";
          ntos-extensions:feature "product";

          container input {
            description
              "Packets destined to local sockets.";
            uses filter-input-state;
          }

          container forward {
            description
              "Packets being routed.";
            uses filter-forward-state;
          }

          container output {
            description
              "Locally-generated packets.";
            uses filter-output-state;
          }

          list chain {
            key "name";
            description
              "User chain.";

            leaf name {
              // XXX: check user chain format
              type string;
              description
                "The user chain name.";
            }
            uses userchain-state;
          }
        }

        container mangle {
          description
            "Packet alteration table.";
          ntos-extensions:feature "product";

          container prerouting {
            description
              "Altering packets as soon as they come in.";
            uses mangle-prerouting-state;
          }

          container input {
            description
              "Altering packets before routing.";
            uses mangle-input-state;
          }

          container forward {
            description
              "Altering packets being routed.";
            uses mangle-forward-state;
          }

          container output {
            description
              "Altering locally-generated packets before routing.";
            uses mangle-output-state;
          }

          container postrouting {
            description
              "Altering packets as they are about to go.";
            uses mangle-postrouting-state;
          }

          list chain {
            key "name";
            description
              "User chain.";

            leaf name {
              // XXX: check user chain format
              type string;
              description
                "The user chain name.";
            }
            uses userchain-state;
          }
        }

        container raw {
          description
            "Mainly used to exempt packets from connection tracking.";
          ntos-extensions:feature "product";

          container prerouting {
            description
              "Packets as soon as they come in.";
            uses raw-prerouting-state;
          }

          container output {
            description
              "Locally-generated packets before routing.";
            uses raw-output-state;
          }

          list chain {
            key "name";
            description
              "User chain.";

            leaf name {
              // XXX: check user chain format
              type string;
              description
                "The user chain name.";
            }
            uses userchain-state;
          }
        }

        list address-group {
          key "name";
          description
            "Address group.";
          ntos-extensions:feature "product";

          leaf name {
            type string {
              pattern '[0-9a-zA-Z_-]+';
            }
            description
              "Name of the address group.";
          }

          leaf-list address {
            type inet:ipv4-address-no-zone;
            description
              "List of addresses of the group.";
          }
        }

        list network-group {
          key "name";
          description
            "Network group.";
          ntos-extensions:feature "product";

          leaf name {
            type string {
              pattern '[0-9a-zA-Z_-]+';
            }
            description
              "Name of the network group.";
          }

          leaf-list network {
            type union {
              type ntos-inet:ipv4-prefix;
              type inet:ipv4-address-no-zone;
            }
            description
              "List of networks of the group.";
            ntos-api:pattern-added '(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.){3}' +
                                 '([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(%[\p{N}\p{L}]+)?';
            ntos-api:pattern-added '[0-9\.]*';
          }
        }
      }
    }
  }
}
