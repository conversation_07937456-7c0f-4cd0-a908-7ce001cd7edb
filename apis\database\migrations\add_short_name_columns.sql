-- 添加短名称列到config_trans表
-- 创建日期: 2025-06-06

-- 检查model_short_name列是否已存在，如果不存在则添加
ALTER TABLE `config_trans` 
ADD COLUMN IF NOT EXISTS `model_short_name` varchar(64) DEFAULT NULL COMMENT '型号简写名称' AFTER `model`;

-- 检查version_short_name列是否已存在，如果不存在则添加
ALTER TABLE `config_trans` 
ADD COLUMN IF NOT EXISTS `version_short_name` varchar(64) DEFAULT NULL COMMENT '版本简写名称' AFTER `version`;

-- 更新现有记录，为其设置默认值
UPDATE `config_trans` SET `model_short_name` = '' WHERE `model_short_name` IS NULL;
UPDATE `config_trans` SET `version_short_name` = '' WHERE `version_short_name` IS NULL; 