name: I18n Quality Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点运行一次完整检查
    - cron: '0 2 * * *'

jobs:
  i18n-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 2  # 需要获取前一个提交用于diff检查
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install psutil
        # 安装其他必要的依赖
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Run I18n CI Check
      id: i18n-check
      run: |
        cd engine
        python tools/ci_i18n_check.py .
      continue-on-error: true
    
    - name: Run I18n Validation
      id: i18n-validation
      run: |
        cd engine
        python tools/i18n_validator.py
      continue-on-error: true
    
    - name: Run I18n Performance Test
      id: i18n-performance
      run: |
        cd engine
        python tools/i18n_performance_test.py
      continue-on-error: true
    
    - name: Upload I18n Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: i18n-reports
        path: |
          engine/reports/ci_i18n_check_report.json
          engine/reports/i18n_validation_report.json
          engine/reports/i18n_performance_report.json
          engine/reports/hardcoded_strings_report.json
    
    - name: Comment PR with I18n Results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          // 读取检查报告
          let comment = '## 🌐 国际化检查结果\n\n';
          
          try {
            // CI检查结果
            const ciReport = JSON.parse(fs.readFileSync('engine/reports/ci_i18n_check_report.json', 'utf8'));
            const ciPassed = ciReport.summary.passed;
            const ciIcon = ciPassed ? '✅' : '❌';
            
            comment += `### ${ciIcon} CI检查\n`;
            comment += `- 错误数: ${ciReport.summary.error_count}\n`;
            comment += `- 警告数: ${ciReport.summary.warning_count}\n`;
            comment += `- 总问题数: ${ciReport.summary.total_issues}\n\n`;
            
            if (!ciPassed && ciReport.issues.length > 0) {
              comment += '#### 主要问题:\n';
              const errors = ciReport.issues.filter(issue => issue.severity === 'error').slice(0, 5);
              for (const error of errors) {
                comment += `- **${error.file}:${error.line}** - ${error.message}\n`;
              }
              comment += '\n';
            }
          } catch (e) {
            comment += '### ⚠️ CI检查报告读取失败\n\n';
          }
          
          try {
            // 验证结果
            const validationReport = JSON.parse(fs.readFileSync('engine/reports/i18n_validation_report.json', 'utf8'));
            const totalIssues = validationReport.summary.total_issues;
            const validationIcon = totalIssues === 0 ? '✅' : '⚠️';
            
            comment += `### ${validationIcon} 翻译验证\n`;
            comment += `- 总问题数: ${totalIssues}\n`;
            comment += `- 影响文件: ${validationReport.summary.files_with_issues}\n\n`;
            
            if (totalIssues > 0) {
              comment += '#### 问题分类:\n';
              for (const [type, count] of Object.entries(validationReport.summary.type_stats)) {
                comment += `- ${type}: ${count}\n`;
              }
              comment += '\n';
            }
          } catch (e) {
            comment += '### ⚠️ 验证报告读取失败\n\n';
          }
          
          try {
            // 性能测试结果
            const perfReport = JSON.parse(fs.readFileSync('engine/reports/i18n_performance_report.json', 'utf8'));
            const avgOps = perfReport.summary.average_ops_per_second;
            const perfIcon = avgOps >= 1000 ? '✅' : '⚠️';
            
            comment += `### ${perfIcon} 性能测试\n`;
            comment += `- 平均操作/秒: ${avgOps}\n`;
            comment += `- 平均内存使用: ${perfReport.summary.average_memory_usage_mb}MB\n`;
            comment += `- 总错误数: ${perfReport.summary.total_errors}\n\n`;
          } catch (e) {
            comment += '### ⚠️ 性能报告读取失败\n\n';
          }
          
          comment += '---\n';
          comment += '*此检查由 GitHub Actions 自动生成*';
          
          // 发布评论
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });
    
    - name: Check I18n Quality Gate
      run: |
        # 检查CI检查是否通过
        if [ -f "engine/reports/ci_i18n_check_report.json" ]; then
          CI_PASSED=$(python -c "
          import json
          with open('engine/reports/ci_i18n_check_report.json', 'r') as f:
              report = json.load(f)
          print('true' if report['summary']['passed'] else 'false')
          ")
          
          if [ "$CI_PASSED" = "false" ]; then
            echo "❌ I18n CI检查失败"
            exit 1
          fi
        fi
        
        # 检查是否有新的硬编码字符串
        if [ -f "engine/reports/ci_i18n_check_report.json" ]; then
          NEW_HARDCODED=$(python -c "
          import json
          with open('engine/reports/ci_i18n_check_report.json', 'r') as f:
              report = json.load(f)
          new_hardcoded = [issue for issue in report['issues'] if issue['type'] == 'new_hardcoded_string']
          print(len(new_hardcoded))
          ")
          
          if [ "$NEW_HARDCODED" -gt "0" ]; then
            echo "❌ 发现 $NEW_HARDCODED 个新的硬编码字符串"
            exit 1
          fi
        fi
        
        echo "✅ I18n质量检查通过"

  i18n-coverage:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'  # 只在定时任务中运行
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install psutil
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    
    - name: Generate I18n Coverage Report
      run: |
        cd engine
        python tools/hardcoded_string_scanner.py
        python tools/i18n_content_generator.py
    
    - name: Upload Coverage Reports
      uses: actions/upload-artifact@v3
      with:
        name: i18n-coverage-reports
        path: |
          engine/reports/hardcoded_strings_report.json
          engine/reports/i18n_generation_summary.json
          engine/locales/zh-CN.new.json
          engine/locales/en-US.new.json
    
    - name: Create Coverage Issue
      if: always()
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          try {
            // 读取硬编码字符串报告
            const hardcodedReport = JSON.parse(fs.readFileSync('engine/reports/hardcoded_strings_report.json', 'utf8'));
            const totalStrings = hardcodedReport.summary.total_strings;
            
            // 读取生成摘要
            const generationReport = JSON.parse(fs.readFileSync('engine/reports/i18n_generation_summary.json', 'utf8'));
            const totalEntries = generationReport.summary.total_entries;
            const manualNeeded = generationReport.summary.manual_translation_needed;
            
            // 计算覆盖率
            const coverageRate = totalEntries > 0 ? ((totalEntries - manualNeeded) / totalEntries * 100).toFixed(2) : 0;
            
            const issueTitle = `🌐 国际化覆盖率报告 - ${new Date().toISOString().split('T')[0]}`;
            const issueBody = `
            ## 国际化覆盖率报告
            
            ### 📊 统计信息
            - **硬编码字符串总数**: ${totalStrings}
            - **生成的国际化条目**: ${totalEntries}
            - **需要手动翻译**: ${manualNeeded}
            - **自动化覆盖率**: ${coverageRate}%
            
            ### 📂 按类别统计
            ${Object.entries(hardcodedReport.summary.category_stats).map(([category, count]) => `- ${category}: ${count}`).join('\n')}
            
            ### ⚠️ 按严重程度统计
            ${Object.entries(hardcodedReport.summary.severity_stats).map(([severity, count]) => `- ${severity}: ${count}`).join('\n')}
            
            ### 🎯 改进建议
            ${manualNeeded > 0 ? `- 需要手动翻译 ${manualNeeded} 个条目` : '- 所有条目都已自动处理'}
            ${totalStrings > 5000 ? '- 硬编码字符串数量较多，建议逐步重构' : ''}
            ${coverageRate < 80 ? '- 自动化覆盖率偏低，建议改进翻译规则' : ''}
            
            ---
            *此报告由定时任务自动生成*
            `;
            
            // 创建或更新issue
            const { data: issues } = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['i18n', 'coverage-report'],
              state: 'open'
            });
            
            if (issues.length > 0) {
              // 更新现有issue
              await github.rest.issues.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issues[0].number,
                title: issueTitle,
                body: issueBody
              });
            } else {
              // 创建新issue
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: issueTitle,
                body: issueBody,
                labels: ['i18n', 'coverage-report']
              });
            }
          } catch (error) {
            console.error('创建覆盖率报告失败:', error);
          }
