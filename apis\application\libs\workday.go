package libs

import (
	"errors"
	"io"
	"os"
	"strings"
	"time"
)

var WorkDayMap map[string]bool

var statusI2B = map[int]bool{
	0: false,
	1: true,
}

var statusS2B = map[string]bool{
	"0": false,
	"1": true,
}

var statusB2I = map[bool]int{
	false: 0,
	true:  1,
}

// func InitData() {
// 	err := LoadData(Config.Workdaydatafile)
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("加载工作日数据失败:", err)
// 	}
// }

func WorkDayCount(start, end time.Time) int {
	count := 0

	for i := 0; i < int(end.Sub(start).Hours()/24); i++ {
		checkDay := start.Add(time.Hour * time.Duration(24*i))
		if IsWorkDay(checkDay) {
			count++
		}
	}
	return count
}

func IsWorkDay(d time.Time) bool {
	if v, ok := WorkDayMap[d.Format("2006-01-02")]; ok {
		if v {
			return true
		}
		return false
	}

	if d.Weekday() == time.Saturday || d.Weekday() == time.Sunday {
		return false
	}

	return true
}

func GetWorkDayBeforeToday(days int) time.Time {
	count := 0
	checkDay := time.Now()
	for i := 0; i <= 15; i++ {
		checkDay := checkDay.Add(-time.Duration(i*24) * time.Hour)
		if IsWorkDay(checkDay) {
			count++
		}
		if count > days {
			break
		}
	}
	return checkDay
}

func GetWorkDayAfterToday(days int) time.Time {
	count := 0
	checkDay := time.Now()
	for i := 0; i <= 15; i++ {
		checkDay := checkDay.Add(time.Duration(i*24) * time.Hour)
		if IsWorkDay(checkDay) {
			count++
		}
		if count > days {
			break
		}
	}
	return checkDay
}

func LoadData(dataFile string) error {
	WorkDayMap = map[string]bool{}
	if len(dataFile) == 0 {
		return errors.New("未配置工作日数据路径")
	}
	_, err := os.Stat(dataFile)
	if os.IsNotExist(err) {
		return errors.New("工作日数据文件不存在")
	}
	f, err := os.Open(dataFile)
	if err != nil {
		return err
	}
	contentByte, _ := io.ReadAll(f)

	for _, line := range strings.Split(string(contentByte), "\n") {
		lineArr := []string{}
		_lineArr := strings.Fields(line)
		if len(_lineArr) > 2 {
			for _, i := range _lineArr {
				if len(i) != 0 {
					lineArr = append(lineArr, i)
				}
			}
		} else if len(_lineArr) == 2 {
			lineArr = _lineArr
		} else {
			continue
		}

		if len(lineArr) == 2 && InArrayS([]string{"0", "1"}, lineArr[1]) {
			WorkDayMap[lineArr[0]] = statusS2B[lineArr[1]]
		} else {
			return errors.New(line + ", 非法数据")
		}
	}
	return nil
}
