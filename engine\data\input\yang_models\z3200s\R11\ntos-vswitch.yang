module ntos-vswitch {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:vswitch";
  prefix ntos-vswitch;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-qos {
    prefix ntos-qos;
  }
  import ntos-routing {
    prefix ntos-routing;
  }
  import ntos-dhcp-snooping {
    prefix ntos-dhcp-snp;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS vswitch interfaces.";

  revision 2024-10-21 {
    description
      "Initial version.";
    reference "";
  }

  identity vswitch {
    base ntos-types:INTERFACE_TYPE;
    description
      "Switch interface.";
  }

  identity vswitch-slave {
    base ntos-types:INTERFACE_TYPE;
    description
      "Switch slave interface.";
  }

  grouping vswitch-config {
    description
      "Switch configuration options.";

    list link-interface {
      key "slave";
      description
        "Set this interface as slave of this vswitch.";
      ntos-extensions:nc-cli-one-liner;

      leaf slave {
        type ntos-types:ifname;
        // must '. != ../../name' {
        //   error-message "Cannot bind our own interface";
        // }
        // must "count(../../../*[local-name()='lag']/*[local-name()='link-interface']/*[local-name()='slave'][text()=current()]) = 0" {
        //   error-message "Cannot bind an interface already bound to a lag";
        // }
        // must "count(../../../*[local-name()='bridge']/*[local-name()='link-interface']/*[local-name()='slave'][text()=current()]) = 0" {
        //   error-message "Cannot bind an interface already bound to a bridge";
        // }
        // must 'count(../../../vswitch/link-interface/slave[text()=current()]) = 1' {
        //   error-message "Cannot bind an interface already bound to another vswitch";
        // }
        description
          "Set this interface as slave of this vswitch.";
        ntos-extensions:nc-cli-completion-xpath
          "../*[.!=current()]/*[local-name()='name']";
      }
    }
  }

  typedef session-source-check {
    type enumeration {
      enum transparent-forward {
        description
            "Forward the packet transparently if mismatched.";
        value 1;
      }

      enum dont-check {
        description
            "Doesn't check.";
        value 0;
      }
    }
  }

  rpc show-vswitch-interface-state {
    ntos-extensions:nc-cli-show "interface vswitch state";
    description
      "Show interface state.";
    input {
      leaf vrf {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
        type string;
        default "main";
        description
          "VRF to look into.";
      }
      leaf name {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-vswitch:vswitch/*[local-name()='name']";
        type ntos-types:ifname;
        description
          "Show interface by this name.";
      }
      leaf start {
        type uint16 {
          range "1..65535";
        }
        description
          "Start interface number.";
      }
      leaf end {
        type uint16 {
          range "1..65535";
        }
        description
          "End interface number.";
      }
      leaf search-name {
        type string {
          length "1..15";
        }
        description
          "Search device with name.";
      }
    }

    output {
      leaf interface-total {
        type uint16 {
          range "0..65535";
        }
        description
          "Interface total number.";
      }
      list interface {
        description
          "Output for interface list";
        uses ntos-interface:interface-state;
        uses ntos-interface:eth-state;
        uses ntos-if:interface-common-state;
        uses ntos-ip:ntos-ipv4-state;
        uses ntos-ip:ntos-ipv6-state;
        list link-interface {
          key "slave";
          ntos-extensions:nc-cli-one-liner;
          leaf slave {
            type ntos-types:ifname;
          }
          leaf type {
            type string;
          }
        }
        container access-control {
          leaf https {
            type boolean;
            default "false";
          }
          leaf ping {
            type boolean;
            default "false";
          }
          leaf ssh {
            type boolean;
            default "false";
          }
        }

        leaf session-source-check {
            type session-source-check;
        }

      }
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network vswitch configuration.";

    list vswitch {
      max-elements 1;
      key "name";
      description
        "The list of vswitch interfaces on the device.";
      ntos-extensions:feature "product";
      uses ntos-interface:nonphy-interface-config;
      uses ntos-interface:eth-config;
      uses ntos-interface:physical-wanlan;
      uses ntos-interface:ha-group-config;
      uses ntos-ip:ntos-ipv4-config;
      uses ntos-ip:ntos-ipv6-config;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-routing:reverse-path;
      uses vswitch-config;
      uses ntos-qos:logical-if-qos-config;
      uses ntos-dhcp-snp:dhcp-snp-parameters;

      leaf session-source-check {
        type session-source-check;
        default "dont-check";
      }
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network vswitch operational state data.";

    list vswitch {
      key "name";
      description
        "The list of vswitch interfaces on the device.";
      ntos-extensions:feature "product";
      uses ntos-interface:interface-state;
      uses ntos-interface:eth-state;
      uses ntos-interface:physical-wanlan;
      uses ntos-interface:ha-group-config;
      uses ntos-ip:ntos-ipv4-state;
      uses ntos-ip:ntos-ipv6-state;
      uses ntos-ip:ntos-network-stack-parameters;
      uses vswitch-config;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses ntos-qos:logical-if-qos-state;
      uses ntos-dhcp-snp:dhcp-snp-parameters;

      leaf session-source-check {
        type session-source-check;
        config false;
      }
    }
  }

}
