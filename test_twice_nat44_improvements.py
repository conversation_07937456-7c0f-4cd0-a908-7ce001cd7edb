#!/usr/bin/env python3
"""
twice-nat44改进验证测试脚本

本脚本用于验证twice-nat44 IP池支持改进的效果，包括：
1. 验证新的评估逻辑是否正确处理IP池场景
2. 验证降低的阈值是否能支持更多适用场景
3. 验证FortiGate IP地址格式池名称的支持

作者: FortiGate转换系统
版本: 1.0
日期: 2025-08-08
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from engine.business.models.twice_nat44_models import TwiceNat44Rule, _validate_fortigate_pools, _is_valid_ipv4
from engine.utils.logger import log

def test_ip_validation():
    """测试IP地址验证功能"""
    print("🧪 测试IP地址验证功能...")
    
    # 测试有效IP地址
    valid_ips = ["***********", "********", "************", "*************"]
    for ip in valid_ips:
        result = _is_valid_ipv4(ip)
        print(f"  ✅ {ip}: {result}")
        assert result == True, f"IP {ip} 应该是有效的"
    
    # 测试无效IP地址
    invalid_ips = ["256.1.1.1", "192.168.1", "not_an_ip", ""]
    for ip in invalid_ips:
        result = _is_valid_ipv4(ip)
        print(f"  ❌ {ip}: {result}")
        assert result == False, f"IP {ip} 应该是无效的"
    
    print("✅ IP地址验证功能测试通过\n")

def test_fortigate_pool_validation():
    """测试FortiGate IP池验证功能"""
    print("🧪 测试FortiGate IP池验证功能...")
    
    # 测试IP地址格式的池名称（FortiGate常用格式）
    ip_pools = ["*************", "************", "***********00"]
    result = _validate_fortigate_pools(ip_pools)
    print(f"  IP格式池名称: {ip_pools} -> {result}")
    assert len(result) == 3, "所有IP格式的池名称都应该被验证为有效"
    
    # 测试混合格式
    mixed_pools = ["*************", "EXTERNAL_POOL", "invalid_ip", "********"]
    available_pools = ["EXTERNAL_POOL", "INTERNAL_POOL"]
    result = _validate_fortigate_pools(mixed_pools, available_pools)
    print(f"  混合格式池名称: {mixed_pools} -> {result}")
    assert "*************" in result, "IP格式池名称应该被验证为有效"
    assert "EXTERNAL_POOL" in result, "已定义的池对象应该被验证为有效"
    assert "********" in result, "IP格式池名称应该被验证为有效"
    assert "invalid_ip" not in result, "无效的池名称应该被排除"
    
    print("✅ FortiGate IP池验证功能测试通过\n")

def test_evaluation_logic():
    """测试新的评估逻辑"""
    print("🧪 测试新的评估逻辑...")
    
    # 测试使用IP池的策略（之前会被直接排除）
    policy_with_ippool = {
        "name": "test_policy_with_ippool",
        "srcintf": ["wan1"],
        "dstintf": ["dmz"],
        "srcaddr": ["all"],
        "dstaddr": ["WEB_SERVER_VIP"],
        "service": ["HTTP"],
        "nat": "enable",
        "ippool": "enable",
        "poolname": ["*************"]  # IP地址格式的池名称
    }
    
    vip_configs = {
        "WEB_SERVER_VIP": {
            "name": "WEB_SERVER_VIP",
            "extip": "************",
            "mappedip": "**************",
            "extport": "80",
            "mappedport": "8080"
        }
    }
    
    context = {
        "ntos_version": "R11",
        "twice_nat44_threshold": 65,  # 新的降低阈值
        "available_pools": []
    }
    
    # 执行评估
    recommendation = TwiceNat44Rule.evaluate_twice_nat44_suitability(
        policy_with_ippool, vip_configs, context
    )
    
    print(f"  策略评估结果:")
    print(f"    总分: {recommendation.total_score}")
    print(f"    是否推荐: {recommendation.should_use}")
    print(f"    置信度: {recommendation.confidence_score:.2f}")
    print(f"    原因: {recommendation.reasons}")
    if recommendation.warnings:
        print(f"    警告: {recommendation.warnings}")
    if recommendation.fallback_reason:
        print(f"    回退原因: {recommendation.fallback_reason}")
    
    # 验证结果
    assert recommendation.total_score > 0, "评分应该大于0"
    print(f"  ✅ 使用IP池的策略获得了 {recommendation.total_score} 分")
    
    # 测试不使用IP池的策略（对比）
    policy_without_ippool = policy_with_ippool.copy()
    policy_without_ippool["ippool"] = "disable"
    policy_without_ippool.pop("poolname", None)
    
    recommendation_no_pool = TwiceNat44Rule.evaluate_twice_nat44_suitability(
        policy_without_ippool, vip_configs, context
    )
    
    print(f"  不使用IP池的策略获得了 {recommendation_no_pool.total_score} 分")
    
    # IP池策略的分数应该只比非IP池策略低一点，而不是被直接排除
    score_diff = recommendation_no_pool.total_score - recommendation.total_score
    print(f"  分数差异: {score_diff} 分")
    assert score_diff <= 10, f"IP池策略与非IP池策略的分数差异不应超过10分，实际差异: {score_diff}"
    
    print("✅ 新的评估逻辑测试通过\n")

def test_threshold_impact():
    """测试阈值降低的影响"""
    print("🧪 测试阈值降低的影响...")
    
    # 创建一个中等评分的策略
    medium_score_policy = {
        "name": "medium_score_policy",
        "srcintf": ["wan1"],
        "dstintf": ["dmz"],
        "srcaddr": ["all"],
        "dstaddr": ["VIP1", "VIP2"],  # 多个VIP，会降低分数
        "service": ["HTTP", "HTTPS", "FTP"],  # 多个服务，会降低分数
        "nat": "enable",
        "ippool": "enable",
        "poolname": ["***********00"]  # 使用IP池
    }
    
    vip_configs = {
        "VIP1": {
            "name": "VIP1",
            "extip": "************",
            "mappedip": "**************"
        },
        "VIP2": {
            "name": "VIP2",
            "extip": "************",
            "mappedip": "**************"
        }
    }
    
    # 测试旧阈值（80分）
    old_context = {
        "ntos_version": "R11",
        "twice_nat44_threshold": 80,
        "available_pools": []
    }
    
    old_recommendation = TwiceNat44Rule.evaluate_twice_nat44_suitability(
        medium_score_policy, vip_configs, old_context
    )
    
    # 测试新阈值（65分）
    new_context = {
        "ntos_version": "R11",
        "twice_nat44_threshold": 65,
        "available_pools": []
    }
    
    new_recommendation = TwiceNat44Rule.evaluate_twice_nat44_suitability(
        medium_score_policy, vip_configs, new_context
    )
    
    print(f"  策略评分: {new_recommendation.total_score}")
    print(f"  旧阈值(80分)推荐: {old_recommendation.should_use}")
    print(f"  新阈值(65分)推荐: {new_recommendation.should_use}")
    
    # 验证阈值降低的效果
    if new_recommendation.total_score >= 65 and new_recommendation.total_score < 80:
        assert not old_recommendation.should_use, "旧阈值应该不推荐使用"
        assert new_recommendation.should_use, "新阈值应该推荐使用"
        print("  ✅ 阈值降低成功扩大了适用范围")
    else:
        print(f"  ℹ️  当前策略评分({new_recommendation.total_score})不在65-80分区间")
    
    print("✅ 阈值影响测试完成\n")

def main():
    """主测试函数"""
    print("🚀 开始twice-nat44改进验证测试\n")
    
    try:
        # 执行各项测试
        test_ip_validation()
        test_fortigate_pool_validation()
        test_evaluation_logic()
        test_threshold_impact()
        
        print("🎉 所有测试通过！twice-nat44改进验证成功")
        print("\n📊 改进总结:")
        print("  ✅ IP池支持：现在支持FortiGate IP地址格式的池名称")
        print("  ✅ 评估逻辑：不再直接排除IP池场景，而是智能评分")
        print("  ✅ 权重调整：IP池权重从25%降低到15%，减少绝对影响")
        print("  ✅ 阈值降低：从80分降低到65分，支持更多适用场景")
        print("  ✅ 配置质量：VIP完整性权重从20%提高到25%")
        print("  ✅ 服务复杂度：权重从10%提高到15%，更好反映影响")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
