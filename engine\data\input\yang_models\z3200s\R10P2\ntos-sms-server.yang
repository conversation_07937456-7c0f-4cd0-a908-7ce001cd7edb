module ntos-sms-server {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:sms-srv";
  prefix ntos-sms-server;

  import iana-crypt-hash {
    prefix ianach;
  }
  import ietf-netconf-acm {
    prefix nacm;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-system {
    prefix ntos-system;
  }
  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS auth management.";

  revision 2023-11-17 {
    description
      "Initial version.";
    reference "";
  }

  grouping sms-server-type {
    choice sms-server-type {
      description
        "SMS server type choice, display different configures on the type.";

      case alicloud {
        container alicloud {
          description
            "Alicloud sms server.";
          leaf accesskey {
            type string;
            description
              "Alicloud access key.";
          }
          leaf keysecret {
            type string;
            description
              "Alicloud access key secret.";
          }
          leaf signature {
            type string;
            description
              "The sms signature.";
          }
          leaf template {
            type string;
            description
              "The sms template.";
          }
        }
      }
      case twilio {
        container twilio {
          description
            "Twilio server.";
          leaf account-sid {
            type string;
            description
              "Twilio account sid.";
          }
          leaf auth-token {
            type string;
            description
              "Twilio auth token.";
          }
          leaf from-number {
            type string;
            description
              "Twilio's from phone number";
          }
          leaf template {
            type string;
            description
              "The sms template.";
          }
        }
      }

    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Sms authentication configuration.";

    container sms-server {
      description
        "Configuration sms authentication.";

      list server {
        key "name";

        leaf name {
          description
            "SMS server name.";
          type ntos-types:ntos-obj-name-type;
        }

        leaf enabled {
          type boolean;
          default true;
        }

        uses sms-server-type;
      }
    }
  }

  rpc show-sms-server {
    input {
      container server-json {
        leaf start {
          type uint32;
        }
        leaf end {
          type uint32;
        }
        leaf filter {
          type string;
        }
        leaf name {
          type string;
        }
      }

      leaf server {
        type empty;
      }
    }
    output {
      leaf buffer {
        type string;
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "sms-srv";
  }

  rpc sms-server-test {
    description
      "send test sms.";

    input {
      uses sms-server-type;

      leaf phone-number {
        type string {
          length 0..128;
        }
      }
    }
    output {
      leaf result {
        type string;
      }
    }

    ntos-ext:nc-cli-cmd "sms-server-test";
  }
}