module ntos-z5100s-interface {
    namespace "urn:ruijie:ntos:z5100s:params:xml:ns:yang:interface";
    prefix "z5100s-if";


    // 导入基础接口模块
    import ntos-interface {
        prefix "if";
    }
    
    import ntos {
        prefix "ntos";
    }


    organization
        "Ruijie Networks";
    description
        "NTOS Z5100S Interface YANG Model";

    revision "2022-01-01" {
        description
            "Initial revision for Z5100S device.";
    }

    // Z5100S特定的接口配置
    augment "/ntos:config/ntos:vrf/if:interface/if:physical" {
        description
            "Z5100S特定的接口扩展";
        
        // Z5100S特有的高级接口配置
        container advanced-settings {
            description
                "Z5100S特有的高级接口配置";
            
            leaf hardware-optimization {
                type enumeration {
                    enum "none" {
                        description "无硬件优化";
                    }
                    enum "forwarding" {
                        description "转发优化";
                    }
                    enum "filtering" {
                        description "过滤优化";
                    }
                }
                default "none";
                description
                    "接口硬件优化模式";
            }
            
            leaf port-isolation-group {
                type uint8 {
                    range "0..8";
                }
                default 0;
                description
                    "接口隔离组，0表示不属于任何隔离组";
            }
            
            leaf storm-control {
                type boolean;
                default false;
                description
                    "是否启用风暴控制";
            }
        }
        
        // Z5100S特有的接口状态监控配置
        container z5100s-monitoring {
            description
                "Z5100S特有的接口监控配置";
            
            leaf enhanced-statistics {
                type boolean;
                default false;
                description
                    "是否启用增强统计";
            }
            
            leaf error-detection {
                type boolean;
                default false;
                description
                    "是否启用错误检测";
            }
            
            leaf link-flap-detection {
                type boolean;
                default false;
                description
                    "是否启用链路抖动检测";
            }
        }
    }
} 