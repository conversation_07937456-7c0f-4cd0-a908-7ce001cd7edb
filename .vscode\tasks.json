{"version": "2.0.0", "tasks": [{"label": "I18n: 检查硬编码字符串", "type": "shell", "command": "python", "args": ["engine/tools/ci_i18n_check.py", "."], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "I18n: 验证翻译完整性", "type": "shell", "command": "python", "args": ["engine/tools/i18n_validator.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "I18n: 性能测试", "type": "shell", "command": "python", "args": ["engine/tools/i18n_performance_test.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "I18n: 扫描硬编码字符串", "type": "shell", "command": "python", "args": ["engine/tools/hardcoded_string_scanner.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "I18n: 生成翻译内容", "type": "shell", "command": "python", "args": ["engine/tools/i18n_content_generator.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "I18n: 完整检查", "dependsOrder": "sequence", "dependsOn": ["I18n: 检查硬编码字符串", "I18n: 验证翻译完整性", "I18n: 性能测试"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}