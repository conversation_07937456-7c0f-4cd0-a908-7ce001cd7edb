module ntos-pptp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:pptp";
  prefix ntos-pptp;
  
  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-ppp {
    prefix ntos-ppp;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS pptp module.";
    
  revision 2024-10-29 {
    description
      "Initial version.";
    reference
      "";
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
    }
  }

  typedef pptp-mode {
    type enumeration {
      enum pns {
        description
          "PNS.";
      }
      enum pac {
        description
          "PAC.";
      }
    }
  }

  typedef tunnel-mode {
    type enumeration {
      enum up {
        description
          "UP.";
      }
      enum down {
        description
          "DOWN.";
      }
    }
  }

  typedef access-mode {
    type enumeration {
      enum NAT {
        description
          "NAT.";
      }
      enum route {
        description
          "Route.";
      }
    }
  }

  grouping user-unit {
    leaf user-name {
      type string {
        length "1..127";
      }
      description
        "User name.";
    }
    leaf user-ip {
      type ntos-inet:ip-address;
      description
        "User ip.";
    }
    leaf insert-ip {
      type ntos-inet:ip-address;
      description
        "Insert ip.";
    }
    leaf access-ip {
      type ntos-inet:ip-address;
      description
        "Access ip.";
    }
    leaf host-name {
      type string {
        length "1..127";
      }
      description
        "Host name.";
    }
    leaf insert-time {
      type uint64;
      description
        "insert time.";
    }
    leaf online-time {
      type uint64;
      description
        "Online time.";
    }
    leaf interface-name {
      type string {
        length "1..127";
      }
      description
        "Interface name.";
    }
  }

  grouping pptp-status-unit {
    leaf session-id {
      type uint32;
      description
        "Pptp session id.";
    }
    leaf tunnel-status {
      type uint32;
      description
        "Status of tunnel.";
    }
    leaf flow-statistics {
      type uint32;
      description
        "Statistics of flow.";
    }
    uses user-unit;
  }

  grouping pptp-unit {
    leaf enabled {
      type boolean;
      description
        "Enable of pptp.";
    }
    leaf pptp-work-mode {
      type pptp-mode;
      default pns;
      description
        "PPTP work mode.";
    }
    leaf name {
      type string {
        length "1..127";
      }
      description
        "PPTP name.";
    }
    leaf pptp-echo-interval {
      type uint32;
      description
        "PPTP echo interval.";
    }
    leaf max-conn {
      type uint32;
      default 15;
      description
        "Max new connect count.";
    }
    leaf access-work-mode {
      type access-mode;
      description
        "Access work mode.";
    }
    leaf ppp-reference {
      type string {
        length "1..127";
      }
      description
        "Name of ppp reference.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "The configuration of pptp.";
    container pptp {
        list pptp-list {
            key "name";
            uses pptp-unit;
        }
    }
  }

  rpc pptp-config-show {
    description
      "Get pptp configuration";
    input {
      uses vrf;
    }
    output {
      container pptp {
        leaf total {
          type uint32;
        }
        list pptp-list {
            key "name";
            uses pptp-unit;
            leaf security-policy-status {
              type boolean;
            }
            container ppp {
              uses ntos-ppp:ppp-unit;
            }
        }
      }
    }
    ntos-ext:nc-cli-show "pptp";
  }

  rpc get-pptp-status {
    description
      "Get pptp status";
    input {
      uses vrf;
      leaf start {
        type uint32;
      }
      leaf end {
        type uint32;
      }
      leaf filter {
        type string;
      }
    }
    output {
      container pptp {
        leaf total {
          type uint32;
        }
        list session-list {
          key "session-id";
          uses pptp-status-unit;
        }
      }
    }
    ntos-ext:nc-cli-cmd "pptp status";
  }

  rpc pptp-up-down {
    description
      "Up or down pptp tunnel";
    input {
      uses vrf;
      leaf session-id {
        type uint32;
        description
          "Name of ppp reference.";
      }
      leaf status {
        type tunnel-mode;
        description
          "Up or down to control tunnel.";
      }
    }
    ntos-ext:nc-cli-cmd "pptp up-down";
  }

  rpc pptp-up-down-mutil {
    description
      "Up or down pptp tunnel mutil";
    input {
      uses vrf;
      leaf-list session-id {
        type uint32;
        description
          "Name list of ppp reference.";
      }
      leaf status {
        type tunnel-mode;
        description
          "Up or down to control tunnel.";
      }
    }
    ntos-ext:nc-cli-cmd "pptp up-down-multi";
  }

  rpc pptp-up-down-all {
    description
      "Up or down pptp tunnel all";
    input {
      uses vrf;
      leaf status {
        type tunnel-mode;
        description
          "Up or down to control tunnel.";
      }
    }
    ntos-ext:nc-cli-cmd "pptp up-down-all";
  }

  rpc pptp-set-log {
    description
      "Up or down pptp tunnel";
    input {
      uses vrf;
      leaf submodule {
        type string {
          length "1..127";
        }
      }
      leaf level {
        type ntos-types:log-level;
      }
    }
    ntos-ext:nc-cli-cmd "pptp set";
  }
}