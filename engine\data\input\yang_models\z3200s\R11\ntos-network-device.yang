module ntos-network-device {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:network-device";
  prefix ntos-network-device;

  import ietf-netconf-acm {
    prefix nacm;
  }
  
  import ntos {
    prefix ntos;
  }
  
  import ntos-extensions {
    prefix ntos-extensions;
  }
  
  import ntos-inet-types {
    prefix ntos-inet;
  }
  
  import ntos-types {
    prefix ntos-types;
  }
  
  import ntos-snmp-client {
    prefix ntos-snmp-client;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS network device.";

  revision 2024-08-08 {
    description
      "Add network device.";
    reference "";
  }

  grouping ntos-network-config {
    description
      "SNMP common options.";
    container mac-snooping {
      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable or disable the mac-snooping config.";
        }
    }
    container location-snooping {
      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable or disable the location-snooping config.";
        }
    }

    leaf frequency {
      type uint32;
      default "600";
      description
        "Execute operation every <frequency> period.";
    }

    list server {
      description
        "Configuration of the server.";
      ordered-by user;
      leaf enabled {
        type boolean;
        default "false";
        description
          "Enable or disable the engine.";
      }
      key "host";
      leaf host {
        type ntos-inet:host;
        description
          "Restrict send to requests from the specified address.";
      }
      leaf device-name {
        type string;
        description
          "The device name.";
      }
      
      leaf device-type {
          type enumeration {
            enum L2 {
              description
                "L2.";
            }
            enum L3 {
              description
                "L3.";
            }
            enum AC {
              description
                "AC.";
            }
          }
          default "L3";
        description
          "The device type L2|L3|AC.";
      }
      
      leaf manufacturer {
        type string;
        description
          "The device manufacturer(RUIJIE|HUAWEI|H3C|OTHER).";
        default "RUIJIE";
      }
      container static-info {
        description
          "Most of the information reported by the SNMP agent is retrieved from
           the underlying system. However, certain MIB objects can be
           configured with a static value.";

        leaf location {
          type string;
          description
            "System location (sysLocation.0) object value.";
        }
      }
      
      container snmp-snooping-params {
        description
            "SNMP snooping params.";

        //list oid-list {
        //    key "snooping-type";
        //    leaf snooping-type {
        //      type string;
        //        description
        //          "Snooping type such as arp|exchange-port|sta.";  
        //    }
        //    leaf snooping-oid {
        //        type string;
        //        description
        //          "SNMP object identifier either as a label or numeric form.";  
        //    }
        //}
        container access-params {
          uses ntos-snmp-client:snmp-client-params;
        }
      }
    }
  }
  
  augment "/ntos:config/ntos:vrf" {
    description
      "Net device configuration.";

    container network-device {
      presence "Make SNMP client available.";
      description
        "Net device configuration.";
      ntos-extensions:feature "product";
      uses ntos-network-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Net device operational state data.";

    container network-device {
      description
        "Net device operational state data.";
      ntos-extensions:feature "product";
      uses ntos-network-config;
    }
  }

  rpc show-snmp-snooping-status{
    description
      "Show snmp snooping status.";
    input {

     leaf server {
        type string;
        default "all";
        description
          "Server host.";
      }
      
    }
    
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-show "snmp-snooping-status";
  }

  rpc show-snmp-snooping-list{
    description
      "Show snmp snooping list.";
    input {
     leaf server {
        type string;
        default "all";
        description
          "The server host.";
      }
      
     leaf device-type {
        type string;
        default "all";
        description
          "The device type.";
      }
      
     leaf device-name {
        type string;
        default "all";
        description
          "The device name.";
      }
    leaf start {
      type uint32;
      default "0";
      description
        "The start index.";
    }
    
    leaf end {
      type uint32;
      default "10";
      description
        "The index end.";
     }
    }
    
    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-show "snmp-snooping-list";
  }    
}
