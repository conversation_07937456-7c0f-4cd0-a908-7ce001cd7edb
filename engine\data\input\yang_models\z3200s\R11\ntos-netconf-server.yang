module ntos-netconf-server {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:netconf-server";
  prefix ntos-netconf-server;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }

  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS NETCONF server.";

  revision 2021-12-09 {
    description
      "Add rpc to set edit validate.";
    reference "";
  }

  revision 2019-12-23 {
    description
      "Initial version.";
    reference "";
  }

  identity netconf-server {
    base ntos-types:SERVICE_LOG_ID;
    description
      "NETCONF server service.";
  }

  grouping system-netconf-server-config {
    description
      "Configuration data for NETCONF server.";

    leaf enabled {
      type boolean;
      description
        "Enable or disable NETCONF server. If no addresses are specified,
         NETCONF will listen on all IPv4 and IPv6 addresses on port 830.";
    }

    leaf idle-timeout {
      type uint16;
      units "seconds";
      default "3600";
      description
        "Specifies the maximum number of seconds that a NETCONF session may
         remain idle. A NETCONF session will be dropped if it is idle for an
         interval longer than this number of seconds.  If set to zero, then the
         server will never drop a session because it is idle.  Sessions that
         have a notification subscription active are never dropped.";
    }

    list address {
      key "ip";
      description
        "List of addresses on which to listen to NETCONF clients.";
      ntos-extensions:nc-cli-one-liner;

      leaf ip {
        type ntos-inet:ip-address;
        description
          "The IP address to listen on.";
      }

      leaf port {
        type ntos-inet:port-number;
        default "830";
        description
          "The port number to listen on (default: 830).";
      }

      leaf description {
        type string;
        description
          "NETCONF listen endpoint description.";
      }
    }

    container call-home {
      description
        "List of addresses on which to listen to NETCONF clients.";

      list endpoint {
        key "name";
        ordered-by user;
        max-elements 20;
        description
          "A non-empty user-ordered list of endpoints for this
          NETCONF server to try to connect to in sequence.
          Defining more than one enables high-availability.";
        leaf name {
          type string;
          description
            "An arbitrary name for this endpoint.";
        }
        choice transport {
          case ssh {
            container ssh {
              description
                "Specifies SSH-specific call-home transport
                configuration.";
              leaf remote-address {
                type ntos-inet:host;
                description
                  "The IP address or hostname of the remote peer to
                    establish a connection with.  If a domain name is
                    configured, then the DNS resolution should happen on
                    each connection attempt.  If the the DNS resolution
                    results in multiple IP addresses, the IP addresses
                    are tried according to local preference order until
                    a connection has been established or until all IP
                    addresses have failed.";
              }
              leaf remote-port {
                type ntos-inet:port-number;
                default "4334";
                description
                  "The IP port number for the remote peer to establish a
                    connection with.  An invalid default value (0) is used
                    (instead of 'mandatory true') so that as application
                    level data model may 'refine' it with an application
                    specific default port number value.";
              }
            }
          }
          case tls {
            container tls {
              description
                "Specifies TLS-specific call-home transport
                configuration.";
            }
          }
        }
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "NETCONF server configuration.";

    container netconf-server {
      presence "NETCONF server configuration";
      description
        "Configuration data for NETCONF server.";
      uses system-netconf-server-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "NETCONF server state.";

    container netconf-server {
      description
        "Operational state data for NETCONF server.";
      uses system-netconf-server-config;
    }
  }

  rpc edit-validate {
    ntos-api:internal;
    ntos-extensions:nc-cli-cmd "edit-validate";
    ntos-extensions:nc-cli-hidden;
    description
      "Enable or disable edit validate function.";

    input {
      leaf enabled {
        type boolean;
        mandatory true;
        description
          "Enable or disable edit validate function.";
      }
    }
  }

  rpc netconf-session-log {
    ntos-api:internal;
    ntos-extensions:nc-cli-cmd "netconf-session-log";
    description
      "Set per netconf session log.";

    input {
      leaf id {
        type uint32;
        mandatory true;
        description
          "Set per netconf session log, set id 0 will print any session";
      }
    }
  }

  rpc show-netconf-session {
    ntos-api:internal;
    ntos-extensions:nc-cli-show "netconf-session";
    description
      "Get netconf session function.";

    output {
      leaf session {
        type string;
        description
          "Netconf session info function.";
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc show-edit-validate {
    ntos-api:internal;
    ntos-extensions:nc-cli-show "edit-validate";
    ntos-extensions:nc-cli-hidden;
    description
      "Get edit validate function.";

    output {
      leaf enabled {
        type boolean;
        description
          "Enabled or disable edit validate function.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
  }
}
