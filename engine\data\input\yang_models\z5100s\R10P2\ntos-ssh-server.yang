module ntos-ssh-server {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ssh-server";
  prefix ntos-ssh-server;

  import ntos {
    prefix ntos;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS SSH server.";

  revision 2022-02-24 {
    description
      "add deny-count and unlock-time.";
    reference "";
  }

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity ssh-server {
    base ntos-types:SERVICE_LOG_ID;
    description
      "SSH server service.";
  }

  grouping system-ssh-server-config {
    description
      "Configuration data for system ssh configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable the ssh server.";
    }

    leaf address {
      type ntos-inet:ip-address;
      description
        "The IP address of the interface to listen on. The SSH
         server will listen on all interfaces if no value is
         specified.";
    }

    leaf port {
      type ntos-inet:port-number;
      default "22";
      description
        "The local port number on this interface the SSH server
         listens on.";
    }

    leaf deny-count {
      type uint8 {
        range "1..5" {
        error-message
          "The deny count must >= 1 and <= 5.";
        }
      }
      default "3";
      description
        "When a user fails to log in through SSH for a specified number of times,
         the account is locked.";
    }

    leaf unlock-time {
      type uint16 {
        range "60..3600" {
        error-message
          "The unlock time must >= 60 and <= 3600, unit: second.";
        }
      }
      default "60";
      description
        "Account lock duration.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "SSH server configuration.";

    container ssh-server {
      presence "Makes ssh available";
      description
        "Top-level container for ssh server.";
      uses system-ssh-server-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "SSH server state.";

    container ssh-server {
      description
        "Top-level container for ssh server.";
      uses system-ssh-server-config;
    }
  }
}
