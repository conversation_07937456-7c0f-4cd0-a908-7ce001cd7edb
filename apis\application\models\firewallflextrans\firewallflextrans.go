package firewallflextrans

import (
	"irisAdminApi/application/models"
	"path/filepath"
	"time"
)

// 配置转换模块版本
const ModuleVersion = "1.0.0"

// FirewallFlexTrans 防火墙配置转换基础模型
type FirewallFlexTrans struct {
	models.ModelBase
	Name        string `gorm:"not null; type:varchar(60)" json:"name"`
	Description string `gorm:"type:varchar(200)" json:"description"`
	UserID      uint   `gorm:"not null" json:"user_id"`
	App         string `gorm:"not null; type:varchar(60); default:''" json:"app"` // 应用标识
}

// ConfigTrans 通用配置转换模型（支持多厂商）
type ConfigTrans struct {
	models.ModelBase
	JobID string `gorm:"not null; type:varchar(60)" json:"job_id"`

	Vendor string `gorm:"not null; type:varchar(60)" json:"vendor"` // 厂商名称，如 fortigate, cisco 等

	InputFileName string `gorm:"not null; type:varchar(512)" json:"input_file_name"`
	InputMD5      string `gorm:"not null; type:varchar(60)" json:"input_md5"`

	// 接口映射文件相关字段
	MappingFileName string `gorm:"type:varchar(512)" json:"mapping_file_name"`          // 接口映射文件名称
	MappingFileMD5  string `gorm:"type:varchar(60)" json:"mapping_file_md5"`            // 接口映射文件MD5
	MappingFilePath string `gorm:"type:varchar(500)" json:"mapping_file_path"`          // 接口映射文件完整路径
	OutputFileName  string `gorm:"not null; type:varchar(512)" json:"output_file_name"` // 主要输出文件名
	OutputMD5       string `gorm:"not null; type:varchar(60)" json:"output_md5"`        // 主要输出文件MD5

	// XML文件相关字段
	XmlFileName string `gorm:"type:varchar(512)" json:"xml_file_name"` // XML文件名
	XmlMD5      string `gorm:"type:varchar(60)" json:"xml_md5"`        // XML文件MD5
	// 转换报告相关字段
	ReportPath       string `gorm:"type:varchar(500)" json:"report_path"`                      // 转换报告文件路径
	Model            string `gorm:"not null; type:varchar(60)" json:"model"`                   // 设备型号
	Version          string `gorm:"not null; type:varchar(60)" json:"version"`                 // 设备版本
	Mode             string `gorm:"not null; type:varchar(20); default:'convert'" json:"mode"` // 工作模式：convert-转换，verify-验证，extract-提取
	ModelShortName   string `gorm:"type:varchar(50)" json:"model_short_name"`                  // 设备型号简写
	VersionShortName string `gorm:"type:varchar(50)" json:"version_short_name"`                // 设备版本简写

	// 语言设置
	Language string `gorm:"type:varchar(10); default:'zh_CN'" json:"language"` // 语言设置，默认为中文

	Status uint   `gorm:"not null" json:"status"`  // 0: 进行中 1: 成功 2: 失败
	Result string `gorm:"type:text" json:"result"` // 处理结果或错误信息

	UserID uint   `gorm:"not null" json:"user_id"`
	App    string `gorm:"not null; type:varchar(60); default:''" json:"app"`
}

// Dir 返回作业的工作目录
func (c *ConfigTrans) Dir() string {
	// 如果创建时间未设置，返回空字符串
	if c.CreatedAt.IsZero() {
		return ""
	}
	return filepath.Join(c.DirBase(), c.JobID)
}

// DirBase 返回作业的基础目录
func (c *ConfigTrans) DirBase() string {
	createdAt := c.CreatedAt
	if createdAt.IsZero() {
		createdAt = time.Now()
	}
	return filepath.Join("configtrans_uploads", createdAt.Format("20060102"))
}

// DeviceModel 设备型号表，存储型号全称和简写的映射关系
type DeviceModel struct {
	models.ModelBase
	Name        string `gorm:"not null; type:varchar(100); uniqueIndex" json:"name"`          // 型号全称，如 RG-WALL 1600-Z3200-S
	ShortName   string `gorm:"not null; type:varchar(50); uniqueIndex" json:"short_name"`     // 型号简写，如 z3200s
	Description string `gorm:"type:varchar(200)" json:"description"`                          // 型号描述
	Vendor      string `gorm:"not null; type:varchar(60); index" json:"vendor"`               // 厂商名称，如 fortigate
	Status      uint   `gorm:"not null; default:1" json:"status"`                             // 状态：1-启用 0-禁用
	SortOrder   uint   `gorm:"not null; default:0" json:"sort_order"`                         // 排序顺序，数值越小越靠前
	App         string `gorm:"not null; type:varchar(60); default:'config-trans'" json:"app"` // 应用标识
}

// DeviceVersion 设备版本表，存储版本全称和简写的映射关系
type DeviceVersion struct {
	models.ModelBase
	Name        string `gorm:"not null; type:varchar(100); uniqueIndex" json:"name"`          // 版本全称，如 NGFW_NTOS 1.0R10P2
	ShortName   string `gorm:"not null; type:varchar(50); uniqueIndex" json:"short_name"`     // 版本简写，如 R10P2
	Description string `gorm:"type:varchar(200)" json:"description"`                          // 版本描述
	Vendor      string `gorm:"not null; type:varchar(60); index" json:"vendor"`               // 厂商名称，如 fortigate
	Status      uint   `gorm:"not null; default:1" json:"status"`                             // 状态：1-启用 0-禁用
	SortOrder   uint   `gorm:"not null; default:0" json:"sort_order"`                         // 排序顺序，数值越小越靠前
	App         string `gorm:"not null; type:varchar(60); default:'config-trans'" json:"app"` // 应用标识
}
