module ntos-loopback {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:loopback";
  prefix ntos-loopback;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-qos {
    prefix ntos-qos;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS loopback interfaces.";

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity loopback {
    base ntos-types:INTERFACE_TYPE;
    description
      "Loopback interface.";
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network loopback configuration.";

    list loopback {
      key "name";
      description
        "The list of loopback interfaces on the device.";
      ntos-ext:feature "product";
      uses ntos-interface:nonphy-interface-config;
      uses ntos-ip:ntos-ipv4-config;
      uses ntos-ip:ntos-ipv6-config;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-interface:eth-config;
      uses ntos-qos:logical-if-qos-config;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network loopback operational state data.";

    list loopback {
      key "name";
      description
        "The list of loopback interfaces on the device.";
      ntos-ext:feature "product";
      uses ntos-interface:interface-state;
      uses ntos-ip:ntos-ipv4-state;
      uses ntos-ip:ntos-ipv6-state;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses ntos-interface:eth-state;
      uses ntos-qos:logical-if-qos-state;
    }
  }
}
