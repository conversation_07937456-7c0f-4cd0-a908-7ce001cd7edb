module ntos-vrrp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:vrrp";
  prefix ntos-vrrp;

  import ntos {
    prefix ntos;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-vlan {
    prefix ntos-vlan;
  }
  import ntos-api {
    prefix ntos-api;
  }
  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS virtual route redundancy protocol module.";

  revision 2023-02-03 {
    description "Initial revision.";
    reference "";
  }

  grouping monitor-intf-priority {
    description "The priority of monitor interface.";
    leaf monitor-intf-priority {
      description "The priority of monitor interface.";
      type uint8 {
        range "1..50";
      }
    }
  }

  grouping ip-vrrp-config {
    description
      "Configuration data for VRRP on IP interfaces";

    leaf priority {
      type uint8 {
        range "1..254";
      }
      default "100";
      description "Virtual Router Priority";
    }

    leaf preempt-time {
      type uint8 {
        range "1..240";
      }
      default "1";
      units "seconds";
      description
        "Set preempt time, master router delay recover time";
    }

    leaf advertisement-interval {
      type uint8 {
        range "1..255";
      }
      units "seconds";
      default "1";
      description "Virtual Router Advertisement Interval";
    }

    list monitor-intf-name {
      max-elements 1;
      key "intf-name";
      leaf intf-name {
        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath 
            "../../*[local-name()='physical']/*[local-name()='name'] |
             ../../*[local-name()='vlan']/*[local-name()='name']";
        description "The intf name of monitor interface";
      }
      uses monitor-intf-priority;
    }

    leaf authentication {
      description "The authentication text of user.";
      type string;
    }

    leaf ip {
      mandatory true;
      description "Add IPv4 address";
      type ntos-inet:ipv4-filter-invalid-address;
    }
  }

  grouping ip-vrrp-top-config {
    description
      "Top-level grouping for Virtual Router Redundancy Protocol";
    list vrrp {
      max-elements 1;
      description
        "Enclosing container for VRRP groups handled by this
         IP interface";
      reference
        "RFC 5798 - Virtual Router Redundancy Protocol
         (VRRP) Version 3 for IPv4 and IPv6";
      key "vrid";
      leaf vrid {
        type uint8 {
            range "1..255";
        }
        description "List of VRRP groups, keyed by virtual router id";
      }
      uses ip-vrrp-config;
    }
  }

  rpc show-vrrp {
    ntos-ext:nc-cli-show "vrrp";
    ntos-api:internal;
    description
      "Show VRRP group.";
    input {
      leaf vrf {
        ntos-ext:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
        type string;
        default "main";
        description
          "Specify the VRF.";
      }

      leaf start {
        type uint16 {
          range "0..65535";
        }
        description
          "Start number.";
      }

      leaf end {
        type uint16 {
          range "0..65535";
        }
        description
          "End number.";
      }

      leaf vrid {
        type uint8 {
          range "1..255";
        }
        description
          "Show VRRP group by this ID.";
      }
    }

    output {
      leaf buffer {
        description
          "Command output buffer.";
        type string;
        ntos-ext:nc-cli-stdout;
      }
    }
  }
  
  typedef debug-vrrp {
    description "Debug Vrrp.";
    type enumeration {
      enum arp {
        description "Debug ARP.";
      }
      enum autoconfigure {
        description "Debug autoconfiguration.";
      }
      enum log {
        description "Debug Zebra system log";
      }
      enum ndisc {
        description "Debug Neighbor Discovery.";
      }
      enum packets {
        description "Debug sent and received packets.";
      }
      enum protocol {
        description "Debug protocol state.";
      }
      enum sockets {
        description "Debug socket creation and configuration.";
      }
      enum zebra {
        description "Debug Zebra events.";
      }
      enum all {
        description "Debug all.";
      }
    }
  }
  
  rpc vrrp-debug {
    ntos-ext:nc-cli-cmd "vrrp";
    ntos-api:internal;
    input {
      leaf debug {
        type debug-vrrp;
      }
    }
  }

  rpc no-vrrp-debug {
    ntos-ext:nc-cli-cmd "no-vrrp";
    ntos-api:internal;
    input {
      leaf debug {
        type empty;
      }
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
    uses ip-vrrp-top-config;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-vlan:vlan" {
    uses ip-vrrp-top-config;
  }
}
