package dconfigtrans

import (
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/firewallflextrans"
)

const DeviceModelName = "设备型号表"

type DeviceModel struct {
	firewallflextrans.DeviceModel
}

type DeviceModelListResponse struct {
	DeviceModel
}

func (this *DeviceModel) ModelName() string {
	return DeviceModelName
}

func DeviceModelModel() *firewallflextrans.DeviceModel {
	return &firewallflextrans.DeviceModel{}
}

// Create 创建设备型号
func (this *DeviceModel) Create(object map[string]interface{}) error {
	object["app"] = "config-trans"
	err := easygorm.GetEasyGormDb().Model(DeviceModelModel()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create device model err ", err)
		return err
	}
	return nil
}

// Update 更新设备型号
func (this *DeviceModel) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(DeviceModelModel()).Where("app = ?", "config-trans").Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update device model err ", err)
		return err
	}
	return nil
}

// Delete 删除设备型号
func (this *DeviceModel) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("app = ?", "config-trans").Delete(DeviceModelModel(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete device model err ", err)
		return err
	}
	return nil
}

// Find 根据ID查找设备型号
func (this *DeviceModel) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(DeviceModelModel()).Where("app = ?", "config-trans").Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find device model err ", err)
		return err
	}
	return nil
}

// FindEx 根据指定字段查找设备型号
func (this *DeviceModel) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(DeviceModelModel()).Where("app = ?", "config-trans").Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find device model err ", err)
		return err
	}
	return nil
}

// FindByShortName 根据简写名称查找设备型号
func (this *DeviceModel) FindByShortName(shortName string) error {
	return this.FindEx("short_name", shortName)
}

// FindByName 根据全称查找设备型号
func (this *DeviceModel) FindByName(name string) error {
	return this.FindEx("name", name)
}

// GetAllModels 获取所有设备型号列表
func (this *DeviceModel) GetAllModels(vendor, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*DeviceModelListResponse

	db := easygorm.GetEasyGormDb().Model(DeviceModelModel()).Where("app = ?", "config-trans")

	if len(vendor) > 0 {
		db = db.Where("vendor = ?", vendor)
	}

	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get device model list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get device model list data err ", err)
		return nil, err
	}

	// 处理结果
	items := make([]map[string]interface{}, 0, len(res))
	for _, item := range res {
		itemMap := map[string]interface{}{
			"id":          item.ID,
			"name":        item.Name,
			"short_name":  item.ShortName,
			"description": item.Description,
			"vendor":      item.Vendor,
			"status":      item.Status,
			"sort_order":  item.SortOrder,
			"created_at":  item.CreatedAt,
			"updated_at":  item.UpdatedAt,
		}
		items = append(items, itemMap)
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

// GetEnabledModels 获取已启用的设备型号列表
func (this *DeviceModel) GetEnabledModels(vendor string) ([]map[string]interface{}, error) {
	var models []*DeviceModel

	db := easygorm.GetEasyGormDb().Model(DeviceModelModel()).
		Where("app = ?", "config-trans").
		Where("status = ?", 1)

	if len(vendor) > 0 {
		db = db.Where("vendor = ?", vendor)
	}

	err := db.Order("sort_order asc").Find(&models).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get enabled models err ", err)
		return nil, err
	}

	// 处理结果
	result := make([]map[string]interface{}, 0, len(models))
	for _, model := range models {
		result = append(result, map[string]interface{}{
			"id":          model.ID,
			"name":        model.Name,
			"short_name":  model.ShortName,
			"description": model.Description,
			"vendor":      model.Vendor,
		})
	}

	return result, nil
}

// GetModelByName 根据全称或简写获取设备型号
func GetModelByName(name string, vendor string) (*DeviceModel, error) {
	model := &DeviceModel{}

	db := easygorm.GetEasyGormDb().Model(DeviceModelModel()).
		Where("app = ?", "config-trans").
		Where("status = ?", 1)

	if len(vendor) > 0 {
		db = db.Where("vendor = ?", vendor)
	}

	// 先尝试按全称查找
	err := db.Where("name = ?", name).First(model).Error
	if err == nil {
		return model, nil
	}

	// 如果全称查找失败，尝试按简写查找
	err = db.Where("short_name = ?", name).First(model).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get model by name err ", err)
		return nil, err
	}

	return model, nil
}
