module ntos-session-limit {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:session-limit";
  prefix ntos-session-limit;

  import ntos {
    prefix ntos;
  }

  import ntos-inet-types {
    prefix ntos-inet;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-extensions;
  }

  import ntos-user-management {
    prefix ntos-user-management;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS flow Control module.";

  revision 2023-07-12 {
    description
      "Initial version.";
    reference "";
  }

  typedef network-obj {
    type ntos-types:ntos-obj-name-type;
  }

  grouping cmd-output-buffer {
    leaf buffer {
      description
        "Command output buffer since last request.";

      type string;
      ntos-extensions:nc-cli-stdout;
      ntos-extensions:nc-cli-hidden;
    }
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos:name";
    }
  }

  grouping ip-address-pps {
    description "List of IP addresses and their packets per second limits.";

    leaf address {
      type ntos-inet:ip-address;
      description "IP address.";
    }

    leaf pps {
      type uint32;
      default 0;
      description "The packets per second limit value for this IP address.";
    }
  }

  grouping packets-per-second-limit {
    description "Configure packets per second limit functionality.";

    leaf enabled {
      type boolean;
      default false;
      description "Enable or disable packets per second limit module.";
    }

    leaf global-pps {
      type uint32;
      default 0;
      description "The packets per second limit value for global IP addresses.";
    }
  }

  grouping ip-address-sps {
    description "List of IP addresses and their session per second limits.";

    leaf address {
      type ntos-inet:ip-address;
      description "IP address.";
    }

    leaf sps {
      type uint32;
      default 0;
      description "The session per second limit value for this IP address.";
    }
  }

  grouping session-per-second-limit {
    description "Configure session per second limit functionality.";

    leaf enabled {
      type boolean;
      default false;
      description "Enable or disable session per second limit module.";
    }

    leaf global-sps {
      type uint32;
      default 0;
      description "The session per second limit value for global IP addresses.";
    }
  }

  grouping session-config-for-policy {
    leaf name {
      description
        "The name of policy.";
      type ntos-types:ntos-obj-name-type;
    }

    leaf enabled {
      description
        "Enable or disable policy.";
      type boolean;
      default "true";
    }

    leaf description {
      description
        "The description of policy.";
      type ntos-types:ntos-obj-description-type;
      default "";
    }

    list source-network {
      description
        "The source network of policy.";
      key "name";
      leaf name {
        description
          "The name of source network.";
        type network-obj;
        ntos-extensions:nc-cli-no-name;
      }
      ntos-extensions:nc-cli-one-liner;
    }

    list dest-network {
      description
        "The destination network of policy.";
      key "name";
      leaf name {
        description
          "The name of destination network.";
        type network-obj;
        ntos-extensions:nc-cli-no-name;
      }
      ntos-extensions:nc-cli-one-liner;
    }

    list service {
      description
        "The name of service.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-extensions:nc-cli-no-name;
      }
    }

    list app {
      description
        "The name of application.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-extensions:nc-cli-no-name;
      }
    }

    list user-name {
      description
        "The name of user.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-types:ntos-obj-name-type;
        ntos-extensions:nc-cli-no-name;
      }
    }

    list user-group {
      description
        "The name of user group.";
      ntos-extensions:nc-cli-one-liner;
      key "name";
      leaf name {
        type ntos-user-management:user-group-path;
        ntos-extensions:nc-cli-no-name;
      }
    }

    leaf time-range {
      description
        "Name of a time range.";
      type ntos-types:ntos-obj-name-type;
      default "any";
    }

    leaf session-number {
      type uint32;
      default 0;
      description "The policy total session limit number.";
    }

    leaf per-ip-number {
      type uint32;
      default 0;
      description "The policy's session limit number per IP.";
    }

    leaf action {
      description
        "The action when the policy is hit.";
      type enumeration {
        enum alert;
        enum deny;
      }
      default "deny";
    }
  }

  grouping total-session-limit {
    description
      "Configure total session limit functionality.";

    leaf enabled {
      type boolean;
      default false;
      description "Enable or disable total session limit module.";
    }
  }

  rpc show-session-limit-pps {
    description
      "Show packets per second limit configuration.";
    input {
      uses vrf;
    }

    output {
      uses cmd-output-buffer;
    }
  }

  rpc show-session-limit-sps {
    description
      "Show session per second limit configuration.";
    input {
      uses vrf;
    }

    output {
      uses cmd-output-buffer;
    }
  }

  rpc show-session-limit-sps-suspect-list {
    description
      "Show suspect session per second limit host list.";
    input {
      uses vrf;

      leaf filter {
        description
          "The content of search.";
        type string;
      }

      leaf start {
        description
          "The index of page start.";
        type uint32;
      }

      leaf end {
        description
          "The index of page end.";
        type uint32;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-extensions:nc-cli-show "session-limit sps-suspect-list";
    ntos-extensions:nc-cli-hidden;
  }

  rpc show-session-limit-sps-suspect {
    description
      "Show suspect session per second limit host.";
    input {
      uses vrf;

      leaf filter {
        description
          "The content of search.";
        type string;
      }

      leaf start {
        description
          "The index of page start.";
        type uint32;
      }

      leaf end {
        description
          "The index of page end.";
        type uint32;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-extensions:nc-cli-show "session-limit sps-suspect";
  }

  rpc show-session-limit-policy {
    description
      "Show total session limit configuration.";
    input {
      uses vrf;
    }

    output {
      uses cmd-output-buffer;
    }
  }

  rpc show-session-limit-pps-list {
    description
      "Show packets per second limit address list.";

    input {
      uses vrf;

      leaf address {
        type ntos-inet:ip-address;
        description "IP address.";
      }

      leaf filter {
        description
          "The content of search.";
        type string;
      }

      leaf start {
        description
          "The index of page start.";
        type uint32;
      }

      leaf end {
        description
          "The index of page end.";
        type uint32;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-extensions:nc-cli-show "session-limit pps list";
    ntos-extensions:nc-cli-hidden;
  }

  rpc show-session-limit-pps-config {
    description
      "Show packets per second limit configuration.";

    input {
      uses vrf;

      leaf address {
        type ntos-inet:ip-address;
        description "IP address.";
      }

      leaf filter {
        description
          "The content of search.";
        type string;
      }

      leaf start {
        description
          "The index of page start.";
        type uint32;
      }

      leaf end {
        description
          "The index of page end.";
        type uint32;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-extensions:nc-cli-show "session-limit pps";
  }

  rpc show-session-limit-sps-list {
    description
      "Show session per second limit address list.";

    input {
      uses vrf;

      leaf address {
        type ntos-inet:ip-address;
        description "IP address.";
      }

      leaf filter {
        description
          "The content of search.";
        type string;
      }

      leaf start {
        description
          "The index of page start.";
        type uint32;
      }

      leaf end {
        description
          "The index of page end.";
        type uint32;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-extensions:nc-cli-show "session-limit sps list";
    ntos-extensions:nc-cli-hidden;
  }

  rpc show-session-limit-sps-config {
    description
      "Show session per second limit configuration.";

    input {
      uses vrf;

      leaf address {
        type ntos-inet:ip-address;
        description "IP address.";
      }

      leaf filter {
        description
          "The content of search.";
        type string;
      }

      leaf start {
        description
          "The index of page start.";
        type uint32;
      }

      leaf end {
        description
          "The index of page end.";
        type uint32;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-extensions:nc-cli-show "session-limit sps";
  }

  rpc show-session-limit-policy-list {
    description
      "Show total session limit policy list.";

    input {
      uses vrf;

      leaf filter {
        description
          "The content of search.";
        type ntos-types:ntos-obj-description-type;
      }

      leaf name {
        description
          "The name of session limit policy.";
        type ntos-types:ntos-obj-description-type;
      }

      leaf start {
        description
          "The index of page start.";
        type uint32;
      }

      leaf end {
        description
          "The index of page end.";
        type uint32;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-extensions:nc-cli-show "session-limit policy list";
    ntos-extensions:nc-cli-hidden;
  }

  rpc show-session-limit-policy-config {
    description
      "Show session limit policy configuration.";

    input {
      uses vrf;

      leaf filter {
        description
          "The content of search.";
        type ntos-types:ntos-obj-description-type;
      }

      leaf name {
        description
          "The name of session limit policy.";
        type ntos-types:ntos-obj-description-type;
      }

      leaf start {
        description
          "The index of page start.";
        type uint32;
      }

      leaf end {
        description
          "The index of page end.";
        type uint32;
        must "current() >= ../start" {
          error-message "The End value must be larger than the start value.";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-extensions:nc-cli-show "session-limit policy";
  }

  rpc show-session-limit-policy-brief {
    input {
      uses vrf;

      leaf start {
        description
          "The index of page start.";

        type uint32;
      }

      leaf end {
        description
          "The index of page end.";

        type uint32;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-extensions:nc-cli-show "session-limit policy brief";
    ntos-extensions:nc-cli-hidden;
  }

  rpc clear-session-limit-policy-stat {
    description
      "Clear session limit policy hit counter.";

    input {
      uses vrf;
      list policy {
        description
          "The name of this session limit policy.";
        key "name";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          ntos-extensions:nc-cli-no-name;
          description
            "The name of this session limit policy.";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-extensions:nc-cli-cmd "session-limit clear-stat";
  }

  rpc show-session-limit-capacity {
    description
      "Show total session limit capacity.";
    input {
      uses vrf;
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-extensions:nc-cli-show "session-limit capacity";
    ntos-extensions:nc-cli-hidden;
  }

  rpc sl-get-policy-name-by-id {
    description
      "Get policy name by ID.";

    input {
      uses vrf;
      leaf policy-id {
        type string;
        description
          "Policy ID list";
      }
    }

    output {
      leaf buffer {
        description
          "The command output buffer since last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
      }
    }
    ntos-extensions:nc-cli-show "session-limit get-policy-name-by-id";
    ntos-extensions:nc-cli-hidden;
  }

  augment "/ntos:config/ntos:vrf" {
    container session-limit {
      container pps-limit {
        uses packets-per-second-limit;
        list ip-addr {
          description
            "IP address pps configuration.";
          key "address";
          ordered-by user;

          uses ip-address-pps;
        }
      }

      container sps-limit {
        uses session-per-second-limit;
        list ip-addr {
          description
            "IP address sps configuration.";
          key "address";
          ordered-by user;

          uses ip-address-sps;
        }
      }

      container total-session {
        uses total-session-limit;
        list policy {
          description
            "Total session policy configuration.";
          key "name";
          ordered-by user;

          uses session-config-for-policy;
        }
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    container session-limit {
      container pps-limit {
        uses packets-per-second-limit;
        list ip-addr {
          description
            "IP address pps configuration.";
          key "address";

          uses ip-address-pps;
        }
      }

      container sps-limit {
        uses session-per-second-limit;
        list ip-addr {
          description
            "IP address sps configuration.";
          key "address";

          uses ip-address-sps;
        }
      }

      container total-session {
        uses total-session-limit;
        list policy {
          description
            "Total session policy configuration.";
          key "name";

          uses session-config-for-policy;
        }
      }
    }
  }
}
