module ntos-arp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:arp";
  prefix ntos-arp;

  import ntos {
    prefix ntos;
  }

  import ntos-inet-types {
    prefix ntos-inet;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co., Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS ARP module.";

  revision 2025-01-09 {
    description
      "Add disable/enable of nud probe.";
  }

  revision 2022-02-21 {
    description
      "Create initial version.";
    reference
      "";
  }

  identity arp {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Arp service.";
  }

  typedef ipv4-address {
    description
      "An IPv4 address or addresses range.";

    type union {
      type ntos-inet:ipv4-address {
        ntos-ext:nc-cli-shortdesc "<ipv4-address>";
      }
      type ntos-inet:ipv4-range {
        ntos-ext:nc-cli-shortdesc "<ipv4-range>";
      }
    }
  }

  grouping cmd-output-buffer {
    leaf buffer {
      description
        "Command output buffer since the last request.";

      type string;
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
      ntos-ext:nc-cli-completion-xpath
        "/ntos:state/ntos:vrf/ntos:name";
    }
  }

  grouping user-filter-criteria {
    description
      "Option to select one or several specific users.";

    leaf user-address {
      description
        "Filter user IP address.";

      type string {
        pattern '[0-9.]*';
        length "0..15";
        ntos-ext:nc-cli-shortdesc "<ip-address>";
      }
    }

    leaf mac-address {
      description
        "Filter MAC address.";

      type string {
        pattern '[A-Za-z0-9:]*';
        length "0..17";
        ntos-ext:nc-cli-shortdesc "<mac-address>";
      }
    }
  }

  grouping gratuitous-send {
    container gratuitous-send {
      description
        "Gratuitous ARP configuration.";

      leaf enabled {
        description
          "Enable or disable gratuitous ARP.";

        type boolean;
        default "false";
      }

      leaf interval {
        description
          "Gratuitous ARP delivery interval.";

        type uint32 {
          range "1..1800";
        }
        default "30";
      }
    }
  }

  rpc arp-get-mac {
    description
      "Get user's mac-address by given user's IP.";

    input {
      leaf user-address {
        description
          "User IP address.";

        type ntos-inet:ipv4-address {
          ntos-ext:nc-cli-shortdesc "<ip-address>";
        }
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "arp get-mac";
  }

  rpc arp-get-interface {
    description
      "Get interface by given IP and MAC.";

    input {
      leaf user-address {
        description
          "User IP address.";

        type ntos-inet:ipv4-address {
          ntos-ext:nc-cli-shortdesc "<ip-address>";
        }
        ntos-ext:nc-cli-no-name;
      }

      leaf mac-address {
        description
          "User MAC address.";

        type string {
          pattern '([0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){5})|([0-9a-fA-F]{4}(:[0-9a-fA-F]{4}){2})';
          ntos-ext:nc-cli-shortdesc "<mac-address>";
        }
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "arp-get-interface";
  }

  rpc show-arp-statistics {
    description
      "Statistics ARP list.";

    input {
      uses vrf;
      uses user-filter-criteria;
      leaf interface {
        description
          "Filter interface name.";

        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
        default "all";
      }

      leaf format {
        description
          "Format of output buffer.";

        type enumeration {
          enum text;
          enum json;
        }
        default "text";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "arp";
  }

  rpc show-arp-all {
    description
      "Show the ARP table.";

    input {
      leaf format {
        description
          "Format of output buffer.";

        type enumeration {
          enum text;
          enum json;
        }
        default "text";
        ntos-ext:nc-cli-no-name;
      }

      leaf filter {
        description
          "The content of search by Address and HWaddress.";
        type string;
      }

      leaf start {
        type uint32;
        description
          "Start offset of result.";
      }

      leaf end {
        type uint32;
        description
          "End offset of result.";
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "arp all";
  }

  rpc arp-delete {
    description
      "Remove an ARP entry from ARP table.";

    input {
      leaf address {
        description
          "The IP address required.";

        type ntos-inet:ipv4-address;
        ntos-ext:nc-cli-no-name;
      }

      leaf interface {
        description
          "The interface corresponding to ARP table entry.";

        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
      }
    }

    ntos-ext:nc-cli-cmd "arp delete-arp";
  }

  rpc ip-neigh-flush {
    description
      "Flush ip neigh for interface.";

    input {
      leaf interface {
        description
          "The interface corresponding to neighbor table entry.";

        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
      }
    }

    ntos-ext:nc-cli-cmd "arp ip-neigh-flush";
  }

  rpc show-arp-proxy-status {
    description
      "Show the ARP proxy status.";

    input {
      uses vrf;
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-show "arp proxy-status";
  }

  rpc show-arp-proxy {
    description
      "Show the ARP proxy list.";

    input {
      uses vrf;

      leaf address {
        description
          "Filter the address string.";

        type string;
      }

      leaf interface {
        description
          "Filter the interface name.";

        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
        default "all";
      }

      leaf format {
        description
          "Format of output buffer.";

        type enumeration {
          enum text;
          enum json;
        }
        default "text";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-command-no-pager;
    ntos-ext:nc-cli-show "arp proxy";
  }

  rpc show-arp-gratuitous {
    description
      "Show gratuitous ARP configuration.";

    input {
      uses vrf;
    }

    output {
      uses gratuitous-send;
    }

    ntos-ext:nc-cli-show "arp gratuitous";
  }

  rpc show-kernel-gc-thresh {
    description
      "Show kernel v4 gc-thresh config.";

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "arp-kernel-gc-thresh";
  }

  rpc refresh-kernel-gc-thresh {
    description
      "Refresh kernel v4 gc-thresh config.";

    input {
      leaf gc-thresh1 {
        type uint32;
      }
      leaf gc-thresh2 {
        type uint32;
      }
      leaf gc-thresh3 {
        type uint32;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-cmd "arp-refresh-kernel-gc-thresh";
  }

  rpc arp-mgmt-subcommand {
    input {
      leaf command {
        description
          "Subcommand.";

        type enumeration {
          enum arp-proxy;
          enum gra-arp;
        }
        ntos-ext:nc-cli-hidden;
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "arp-mgmt-subcommand";
  }

  rpc show-arp-trusted-config {
    description
      "Show trusted arp config.";
    input {
      uses vrf;
    }
    output {
      uses cmd-output-buffer;
    }

    ntos-ext:nc-cli-hidden;
    ntos-ext:nc-cli-show "arp-trusted-config";
  }

  augment "/ntos:config/ntos:vrf" {
    container arp {
      description
        "ARP configuration.";

      list static {
        ordered-by user;
        key "ip-address";

        leaf ip-address {
          ntos-ext:nc-cli-no-name;
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<A.B.C.D>";
          }
        }

        leaf mac-address {
          description
            "Assign a MAC address to the Ethernet interface. (00:00:00:00:00:00 or 0000:0000:0000)";

          type string {
            pattern '([0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){5})|([0-9a-fA-F]{4}(:[0-9a-fA-F]{4}){2})';
            ntos-ext:nc-cli-shortdesc "<mac-address>";
          }

          ntos-ext:nc-cli-no-name;
        }

        leaf interface {
          description
            "The interface corresponding to ARP table entry.";

          type ntos-types:ifname;
          ntos-ext:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
        }

        leaf description {
          type ntos-types:ntos-obj-description-type;
          default "";
        }
      }

      list proxy {
        ordered-by user;
        key "address";

        leaf address {
          description
            "The proxy IP address required.";

          type ipv4-address;
        }

        leaf interface {
          description
            "The interface corresponding to ARP table entry.";

          type ntos-types:ifname;
          ntos-ext:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
        }
      }

      leaf proxy-enabled {
        description
          "Enable or disable ARP proxy.";

        type boolean;
        default "false";
      }

      container trusted {
        container nud-probe {
          leaf enabled {
            description
              "Enable/disable nud probe for arp.";

            type boolean;
            default "false";
          }
        }
      }

      uses gratuitous-send;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    container arp {
      description
        "ARP configuration.";

      list static {
        ordered-by user;
        key "ip-address";

        leaf ip-address {
          ntos-ext:nc-cli-no-name;
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<A.B.C.D>";
          }
        }

        leaf mac-address {
          description
            "Assign a MAC address to the Ethernet interface. (00:00:00:00:00:00 or 0000:0000:0000)";

          type string {
            pattern '([0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){5})|([0-9a-fA-F]{4}(:[0-9a-fA-F]{4}){2})';
            ntos-ext:nc-cli-shortdesc "<mac-address>";
          }

          ntos-ext:nc-cli-no-name;
        }

        leaf interface {
          description
            "The interface corresponding to ARP table entry.";

          type ntos-types:ifname;
          ntos-ext:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
        }

        leaf description {
          type ntos-types:ntos-obj-description-type;
          default "";
        }
      }

      list proxy {
        ordered-by user;

        key "address";

        leaf address {
          description
            "The proxy IP address required.";

          type ipv4-address;
        }

        leaf interface {
          description
            "The interface corresponding to ARP table entry.";

          type ntos-types:ifname;
          ntos-ext:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
        }
      }

      leaf proxy-enabled {
        description
          "Enable or disable ARP proxy.";

        type boolean;
        default "false";
      }

      container trusted {
        container nud-probe {
          leaf enabled {
            description
              "Enable/disable nud probe for arp.";

            type boolean;
            default "false";
          }
        }
      }

      uses gratuitous-send;
    }
  }
}
