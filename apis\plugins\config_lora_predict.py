import os
import warnings
warnings.filterwarnings("ignore")
import torch 
import torch.nn as nn
import pandas as pd 
import numpy as np  
import datasets 
from tqdm import tqdm
import transformers
from peft import PeftModel
from torch.nn import functional as F
from typing import List, Tuple
import re  # 添加正则表达式模块
from lxml import etree

def make_history():
    '''
    构建历史记录
    输出: history 输出构建的历史记录
    '''
    history = []
    
    # 第一轮对话
    query1 = (
        "防火墙配置转换任务：将飞塔（Fortinet）防火墙配置转为锐捷（Ruijie）防火墙配置格式。\n下面是一些范例:\n飞塔配置:\n"
        "config firewall address\n"
        "edit \"***********-*************\"\n"
        "     set uuid 987da852-89c0-51ee-db79-7f4778c644b7\n"
        "     set type iprange\n"
        "     set start-ip ***********\n"
        "     set end-ip *************\n"
        "next\n"
        "edit \"***********\"\n"
        "     set uuid 98896f70-89c0-51ee-8e3c-5be630957f68\n"
        "     set subnet *********** *************\n"
        "next\n"
        "转换为锐捷配置:\n"
        "<network-obj xmlns=\"urn:ruijie:ntos:params:xml:ns:yang:network-obj\">\n"
        "      <address-set>\n"
        "        <name>***********</name>\n"
        "        <ip-set>\n"
        "          <ip-address>***********/*************</ip-address>\n"
        "        </ip-set>\n"
        "      </address-set>\n"
        "      <address-set>\n"
        "        <name>***********-*************</name>\n"
        "        <ip-set>\n"
        "          <ip-address>***********-*************</ip-address>\n"
        "        </ip-set>\n"
        "      </address-set>\n"
        "    </network-obj>\n"
        "【严格要求】将下述飞塔防火墙配置转换为锐捷格式。必须仅输出XML格式内容，不要有任何解释、说明或前后缀文字。\n"
        "  config firewall service group\n"
        "  edit \"Exchange Server\"\n"
        "        set member \"DCE-RPC\" \"DNS\" \"HTTPS\"\n"
        "  next\n"
        "->\n"
    )
    
    response1 = (
        "<service-obj xmlns=\"urn:ruijie:ntos:params:xml:ns:yang:service-obj\">\n"
        "      <service-group>\n"
        "        <name>Exchange-Server</name>\n"
        "        <service-set>\n"
        "          <name>DCE-RPC</name>\n"
        "        </service-set>\n"
        "        <service-set>\n"
        "          <name>DNS</name>\n"
        "        </service-set>\n"
        "        <service-set>\n"
        "          <name>HTTPS</name>\n"
        "        </service-set>\n"
        "      </service-group>\n"
        "</service-obj>"
    )
    
    history.append((query1, response1))
    
    # 第二轮对话
    history.append((
        "【严格要求】将下述飞塔防火墙配置转换为锐捷格式。必须仅输出XML格式内容，不要有任何解释、说明或前后缀文字。\n"
        "config firewall service group\n"
        "edit \"test\"\n"
        "        set member \"***********-*************\" \"***********\" \"***********\"\n"
        "next\n"
        "->\n",
        "<network-obj xmlns=\"urn:ruijie:ntos:params:xml:ns:yang:network-obj\">\n"
        "      <address-group>\n"
        "        <name>test</name>\n"
        "        <address-set>\n"
        "          <name>***********-*************</name>\n"
        "        </address-set>\n"
        "        <address-set>\n"
        "          <name>***********</name>\n"
        "        </address-set>\n"
        "        <address-set>\n"
        "          <name>***********</name>\n"
        "        </address-set>\n"
        "      </address-group>\n"
        "</network-obj>"
    ))
    
    # 第三轮对话
    history.append((
        "【严格要求】将下述飞塔防火墙配置转换为锐捷格式。必须仅输出XML格式内容，不要有任何解释、说明或前后缀文字。\n"
        "config firewall service custom\n"
        "edit \"TCP-1521\"\n"
        "        set tcp-portrange 1521\n"
        "next\n"
        "edit \"KERBEROS\"\n"
        "        set category \"Authentication\"\n"
        "        set tcp-portrange 88 464\n"
        "        set udp-portrange 88 464\n"
        "next\n"
        "edit \"UDP67-68\"\n"
        "        set udp-portrange 67 68\n"
        "next\n"
        "->\n",
        "<service-obj xmlns=\"urn:ruijie:ntos:params:xml:ns:yang:service-obj\">\n"
        "      <service-set>\n"
        "        <name>TCP-1521</name>\n"
        "        <tcp>\n"
        "          <dest-port>1521</dest-port>\n"
        "        </tcp>\n"
        "      </service-set>\n"
        "      <service-set>\n"
        "        <name>KERBEROS</name>\n"
        "        <tcp>\n"
        "          <dest-port>88-464</dest-port>\n"
        "        </tcp>\n"
        "      </service-set>\n"
        "      <service-set>\n"
        "        <name>UDP67-68</name>\n"
        "        <udp>\n"
        "          <dest-port>67,68</dest-port>\n"
        "        </udp>\n"
        "      </service-set>\n"
        "</service-obj>"
    ))
    
    # 第四轮对话
    history.append((
        "【严格要求】将下述飞塔防火墙配置转换为锐捷格式。必须仅输出XML格式内容，不要有任何解释、说明或前后缀文字。\n"
        "config firewall service group\n"
        "edit \"Exchange Server\"\n"
        "        set member \"DCE-RPC\" \"DNS\" \"HTTPS\"\n"
        "next\n"
        "->\n",
        "<service-obj xmlns=\"urn:ruijie:ntos:params:xml:ns:yang:service-obj\">\n"
        "      <service-group>\n"
        "        <name>Exchange-Server</name>\n"
        "        <service-set>\n"
        "          <name>DCE-RPC</name>\n"
        "        </service-set>\n"
        "        <service-set>\n"
        "          <name>DNS</name>\n"
        "        </service-set>\n"
        "        <service-set>\n"
        "          <name>HTTPS</name>\n"
        "        </service-set>\n"
        "      </service-group>\n"
        "</service-obj>"
    ))
    
    return history

def build_inputs(query, history):
    '''
    包装为多轮会话
    输入: query, history
    输出: prompt 多轮会话
    '''
    prompt = ""
    for i, (old_query, response) in enumerate(history):
        prompt += "[Round {}]\n\n问：{}\n\n答：{}\n\n".format(i + 1, old_query, response)
    
    # 确保查询格式正确
    if not query.strip().endswith("->"):
        query = query.strip() + " -> "
    else:
        query = query.strip()
        
    prompt += "[Round {}]\n\n问：{}\n\n答：".format(len(history) + 1, query)
    return prompt

def predict(model, tokenizer, text, history, temperature=0.01):
    '''
    预测单个问题
    输入: model, tokenizer, text, history
    输出: response
    '''
    # 检查 `history` 是否是 tuple 形式，如果是则转换
    if isinstance(history, list) and all(isinstance(h, tuple) for h in history):
        formatted_history = []
        for h in history:
            formatted_history.append({"role": "user", "content": h[0]})
            formatted_history.append({"role": "assistant", "content": h[1]})
        history = formatted_history

    # 确保查询以 "->" 结尾
    if not text.strip().endswith("->"):
        text = text.strip() + " ->"
        
    response, _ = model.chat(tokenizer, text, history=history, temperature=temperature)
    
    # 使用增强的XML提取函数进行后处理
    response = extract_xml(response)
    
    # 如果响应为空，生成一个基本的错误响应
    if not response or response.strip() == "":
        response = "<error>无法生成有效的XML响应</error>"
        
    return response

def validate_and_fix_xml(xml_text):
    """
    验证XML是否有效，并尝试修复常见问题
    输入: xml_text XML文本
    输出: 修复后的XML文本
    """
    # 如果文本为空，直接返回
    if not xml_text or xml_text.strip() == "":
        return ""
        
    # 1. 检查并修复缺失的结束标签
    if '<' in xml_text and '>' in xml_text:
        # 创建堆栈跟踪开放标签
        open_tags = []
        i = 0
        
        while i < len(xml_text):
            # 查找开始标签
            start_pos = xml_text.find('<', i)
            if start_pos == -1:
                break
                
            # 查找标签结束
            end_pos = xml_text.find('>', start_pos)
            if end_pos == -1:
                # 修复缺少的>
                xml_text += '>'
                end_pos = len(xml_text) - 1
                
            tag_content = xml_text[start_pos:end_pos+1]
            
            # 忽略自闭合标签、注释和处理指令
            if tag_content.endswith('/>') or tag_content.startswith('<!--') or tag_content.startswith('<?'):
                i = end_pos + 1
                continue
                
            # 检查是否是结束标签
            if tag_content.startswith('</'):
                tag_name = tag_content[2:-1].strip()
                if open_tags and open_tags[-1] == tag_name:
                    open_tags.pop()
                else:
                    # 结束标签不匹配
                    pass
            else:
                # 开始标签
                tag_name = tag_content[1:-1].split()[0].strip()
                open_tags.append(tag_name)
                
            i = end_pos + 1
            
        # 添加缺失的结束标签
        for tag in reversed(open_tags):
            xml_text += f"</{tag}>"
    
    # 2. 尝试通过lxml解析验证
    try:
        root = etree.fromstring(xml_text.encode())
        return etree.tostring(root, encoding='unicode', pretty_print=True)
    except Exception as e:
        # 如果仍然无法解析，返回原文本
        return xml_text

def extract_xml(text):
    """
    提取文本中的XML内容
    输入: text 包含XML的文本
    输出: 提取出的XML内容
    """
    # 如果文本为空，直接返回
    if not text or text.strip() == "":
        return ""
    
    # 尝试匹配完整的XML结构（从第一个<到最后一个>）
    if '<' in text and '>' in text:
        start_idx = text.find('<')
        end_idx = text.rfind('>')
        if start_idx < end_idx:
            xml_content = text[start_idx:end_idx+1]
            return validate_and_fix_xml(xml_content)
    
    # 如果无法提取有效XML，返回原文本
    return text

def predict_one(query, history, tokenizer, model, max_length=2048, top_p=0.7, temperature=0.01):
    """
    预测单个问题，并将结果添加到历史记录
    输入：query, history, tokenizer, model
    输出：response, history
    """
    # 添加严格要求标记，确保只输出XML
    if "【严格要求】" not in query:
        query = "【严格要求】将下述飞塔防火墙配置转换为锐捷格式。必须仅输出XML格式内容，不要有任何解释、说明或前后缀文字。\n" + query
    
    # 使用更新后的predict函数进行预测
    response = predict(model, tokenizer, query, history, temperature=temperature)
    
    # 更新历史记录
    updated_history = history + [(query, response)]
    
    return response, updated_history

def init_model(model_path="../chatglm3-6b", adapter_path="firewall_config_chatglm3-6b"):
    """初始化模型和分词器"""
    print("加载模型中...")
    tokenizer = transformers.AutoTokenizer.from_pretrained(
        model_path, trust_remote_code=True
    )
    model = transformers.AutoModel.from_pretrained(
        model_path, trust_remote_code=True, device_map="auto"
    ).half()
    
    # 加载adapter权重
    print(f"加载Adapter: {adapter_path}")
    model = PeftModel.from_pretrained(model, adapter_path)
    model = model.merge_and_unload() # 合并lora权重
    model = model.eval()
    
    return tokenizer, model

def data_split(data_path):
    '''
    数据集拆分
    输入: data_path 数据集的路径
    输出: dftrain, dftest
    '''
    df = pd.read_csv(data_path)
    
    # 划分训练集和测试集
    ds_dic = datasets.Dataset.from_pandas(df).train_test_split(
        test_size=0.2, shuffle=True, seed=43)
    dftrain = ds_dic['train'].to_pandas()
    dftest = ds_dic['test'].to_pandas()
    dftrain = dftrain[['source', 'target']]
    dftest = dftest[['source', 'target']]
    return dftrain, dftest

def evaluate_predictions(test_df):
    '''
    评估模型预测结果
    输入: test_df 包含预测结果的测试集DataFrame
    输出: accuracy 准确率和详细评估指标
    '''
    correct = 0
    xml_valid = 0
    name_count_correct = 0
    structure_correct = 0
    total = len(test_df)
    results = []
    
    for idx, row in test_df.iterrows():
        pred = row['pred']
        target = row['target']
        row_result = {'valid_xml': False, 'name_match': False, 'structure_match': False, 'overall_correct': False}
        
        # 1. 检查是否是有效的XML
        try:
            pred_xml = etree.fromstring(pred.encode())
            target_xml = etree.fromstring(target.encode())
            row_result['valid_xml'] = True
            xml_valid += 1
            
            # 2. 检查name标签数量是否匹配
            pred_names = pred_xml.xpath('//name')
            target_names = target_xml.xpath('//name')
            if len(pred_names) == len(target_names):
                row_result['name_match'] = True
                name_count_correct += 1
                
            # 3. 简单结构匹配检查（根标签和主要子标签）
            pred_root = pred_xml.tag
            target_root = target_xml.tag
            if pred_root == target_root:
                pred_children = [child.tag for child in pred_xml]
                target_children = [child.tag for child in target_xml]
                if set(pred_children) == set(target_children):
                    row_result['structure_match'] = True
                    structure_correct += 1
                    
            # 4. 综合评估
            if row_result['valid_xml'] and row_result['name_match'] and row_result['structure_match']:
                row_result['overall_correct'] = True
                correct += 1
                
        except Exception as e:
            # 如果XML解析失败，尝试基本文本匹配
            if '<name>' in pred and '<name>' in target:
                if pred.count('<name>') == target.count('<name>'):
                    row_result['name_match'] = True
                    name_count_correct += 1
        
        results.append(row_result)
    
    # 计算各项指标
    accuracy = correct / total if total > 0 else 0
    xml_valid_rate = xml_valid / total if total > 0 else 0
    name_match_rate = name_count_correct / total if total > 0 else 0
    structure_match_rate = structure_correct / total if total > 0 else 0
    
    print(f"XML有效率: {xml_valid_rate:.4f}")
    print(f"名称匹配率: {name_match_rate:.4f}")
    print(f"结构匹配率: {structure_match_rate:.4f}")
    print(f"整体准确率: {accuracy:.4f}")
    
    return accuracy

if __name__ == "__main__":
    # 0.定义变量
    model_name = "../chatglm3-6b"
    adapter_path = "firewall_config_chatglm3-6b"
    data_path = "./datasets/config_data.csv"
    result_path = "result/firewall_result.csv"
    test_mode = True  # 是否进行批量测试评估，False则进入交互模式
    
    # 1.模型加载
    tokenizer, model = init_model(model_path=model_name, adapter_path=adapter_path)
    
    # 加载历史记录
    history = make_history()
    
    if test_mode:
        # 2.数据处理
        _, dftest = data_split(data_path)
        print(f"测试集大小: {len(dftest)}")
        
        # 3.批量预测
        preds = ['' for _ in range(len(dftest))]
        for i in tqdm(range(len(dftest))):
            text = dftest['source'].iloc[i]
            # 直接使用predict函数，更简洁的代码风格
            response = predict(model, tokenizer, text, history.copy(), temperature=0.01)
            preds[i] = response
        
        # 4.保存结果并计算准确率
        dftest['pred'] = preds
        
        # 计算准确率
        acc = evaluate_predictions(dftest)
        
        # 保存预测结果
        os.makedirs(os.path.dirname(result_path), exist_ok=True)
        dftest.to_csv(result_path)
        print(f'准确率: {acc:.4f}')
        
        # 5.输出一些示例结果
        print("\n预测示例:")
        for i in range(min(5, len(dftest))):
            print(f"输入: {dftest['source'].iloc[i]}")
            print(f"预测: {dftest['pred'].iloc[i]}")
            print(f"参考: {dftest['target'].iloc[i]}")
            print("="*50)
    else:
        # 交互式对话
        print("防火墙配置转换系统已初始化完成，输入q退出对话")
        print("请输入飞塔防火墙配置:")
        
        while True:
            query = input(">>> ")
            if query.lower() == 'q':
                print("对话结束")
                break
            
            # 使用新的predict函数
            response = predict(model, tokenizer, query, history)
            print(response) 