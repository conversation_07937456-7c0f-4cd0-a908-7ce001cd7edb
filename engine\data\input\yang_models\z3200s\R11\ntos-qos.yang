module ntos-qos {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:qos";
  prefix ntos-qos;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS qos.";

  revision 2021-02-11 {
    description
      "Classes can match critical control plane traffic.";
    reference "";
  }
  revision 2019-07-01 {
    description
      "Add shapers and schedulers.";
    reference "";
  }
  revision 2019-03-21 {
    description
      "Add shared policers.";
    reference "";
  }
  revision 2018-11-29 {
    description
      "Initial version.";
    reference "";
  }

  feature advanced {
    description
      "Advanced QoS capabilities.";
  }

  identity qos {
    base ntos-types:SERVICE_LOG_ID;
    description
      "QoS service.";
  }

  typedef pb-dwrr-prio-t {
    type enumeration {
      enum low {
        description
          "Low priority.";
      }
      enum high {
        description
          "High priority.";
      }
    }
    description
      "PB-DWRR queue priority.";
  }

  typedef rate-t {
    type uint64 {
      ntos-extensions:nc-cli-int-multiplier;
    }
    units "bps";
    description
      "Rate in bits per second. K/M/G/T multipliers are supported.
       Example: 1G stands for 1000000000 bps.";
  }

  typedef burst-t {
    type uint32 {
      ntos-extensions:nc-cli-int-multiplier;
    }
    units "bytes";
    description
      "Burst size in bytes. K/M/G/T multipliers are supported.
       Example: 2K stands for 2000 bytes.";
  }

  grouping policer-grouping {
    description
      "QoS policer template (Using a Two-Rate, Three-Color Marker, RFC4115).";

    leaf bandwidth {
      type rate-t;
      mandatory true;
      description
        "Maximum bandwidth of regular traffic, a.k.a. CIR (Committed Information
         Rate), in bps. 0 allows no regular traffic.";
    }

    leaf burst {
      type burst-t;
      default "1500";
      description
        "Maximum burst size of regular traffic, a.k.a. CBS (Committed Burst
         Size), in bytes. 0 allows no regular traffic.";
    }

    leaf excess-bandwidth {
      type rate-t;
      default "0";
      description
        "Maximum bandwidth of excess traffic, a.k.a. EIR (Excess Information
         Rate), in bps. 0 allows no excess traffic.";
    }

    leaf excess-burst {
      type burst-t;
      default "1500";
      description
        "Maximum burst size of excess traffic, a.k.a. EBS (Excess Burst Size),
         in bytes. 0 allows no excess traffic.";
    }
  }

  grouping policer-stats-grouping {
    description
      "QoS policer statistics.";

    leaf pass-packets {
      type uint64;
      description
        "Number of packets passed (regular traffic that conforms to (bandwidth,
         burst) specification.";
    }

    leaf pass-bytes {
      type uint64;
      description
        "Number of bytes passed (regular traffic that conforms to (bandwidth,
         burst) specification.";
    }

    leaf pass-excess-packets {
      type uint64;
      description
        "Number of excess packets passed (excess traffic that conforms to
         (excess-bandwidth, excess-burst) specification.";
    }

    leaf pass-excess-bytes {
      type uint64;
      description
        "Number of excess bytes passed (excess traffic that conforms to
         (excess-bandwidth, excess-burst) specification.";
    }

    leaf drop-packets {
      type uint64;
      description
        "Number of packets dropped (traffic that does not conform to bandwidth or
         excess-bandwidth).";
    }

    leaf drop-bytes {
      type uint64;
      description
        "Number of bytes dropped (traffic that does not conform to bandwidth or
         excess-bandwidth).";
    }
  }

  grouping shaper-grouping {
    description
      "QoS shaper template.";

    leaf bandwidth {
      type rate-t;
      mandatory true;
      description
        "Maximum bandwidth of shaped traffic.";
    }

    leaf burst {
      type burst-t;
      default "48000";
      description
        "Maximum burst size of shaped traffic.";
    }

    leaf layer1-overhead {
      type uint32;
      default "0";
      description
        "Number of bytes added by the underlying protocol on each packet.";
    }

    leaf queue-size {
      type uint32;
      default "256";
      description
        "Number of packets that can be saved in the delay queue. If a scheduler
         is also configured on the interface, this value is not used, the queues
         of the scheduler are used as delay queues. The value is rounded up to
         the nearest power of 2.";
    }
  }

  grouping shaper-stats-grouping {
    description
      "QoS shaper statistics.";

    leaf pass-packets {
      type uint32;
      description
        "Number of packets sent.";
    }

    leaf drop-packets {
      type uint32;
      description
        "Number of packets dropped.";
    }
  }

  grouping rate-limit-grouping {
    description
      "Full QoS configuration: basic rate limit (policer), shaper and scheduler.";

    container rate-limit {
      must 'count(*) <= 1' {
        error-message "Only one rate-limit method must be specified.";
      }
      presence "Rate limit configuration";
      description
        "Rate limit configuration.";

      leaf policer {
        type leafref {
          path
            "/ntos:config/ntos-qos:qos/ntos-qos:policer/ntos-qos:name";
        }
        description
          "Traffic policer defined in the QoS context.";
      }

      leaf shared-policer {
        type leafref {
          path
            "/ntos:config/ntos-qos:qos/ntos-qos:shared-policer/ntos-qos:name";
        }
        description
          "Traffic shared policer defined in the QoS context.";
      }
    }
  }

  grouping physical-if-qos-config {
    description
      "QoS configuration for a physical interface.";

    container qos {
      presence "QoS configuration";
      description
        "QoS configuration.";
      ntos-extensions:feature "product";

      container ingress {
        presence "Ingress QoS configuration";
        description
          "Ingress QoS configuration.";
        uses rate-limit-grouping;
      }

      container egress {
        presence "Egress QoS configuration";
        description
          "Egress QoS configuration.";

        uses rate-limit-grouping {
          augment rate-limit {
            description
              "Add shaper rate-limiting.";

            leaf shaper {
              if-feature advanced;
              type leafref {
                path
                  "/ntos:config/ntos-qos:qos/ntos-qos:shaper/ntos-qos:name";
              }
              description
                "Traffic shaper defined in the QoS context.";
            }
          }
        }

        leaf scheduler {
          if-feature advanced;
          type leafref {
            path
              "/ntos:config/ntos-qos:qos/ntos-qos:scheduler/ntos-qos:name";
          }
          description
            "Scheduler defined in the QoS context.";
        }
      }
    }
  }

  grouping logical-if-qos-config {
    description
      "QoS configuration for a logical interface.";

    container qos {
      presence "QoS configuration";
      description
        "QoS configuration.";
      ntos-extensions:feature "product";

      container ingress {
        presence "Ingress QoS configuration";
        description
          "Ingress QoS configuration.";
        uses rate-limit-grouping;
      }

      container egress {
        presence "Egress QoS configuration";
        description
          "Egress QoS configuration.";
        uses rate-limit-grouping;
      }
    }
  }

  grouping rate-limit-state-grouping {
    description
      "Rate limit basic state.";

    container rate-limit {
      presence "Ingress/Egress rate limit configuration";
      description
        "Ingress/Egress rate limit configuration.";

      container policer {
        description
          "Traffic policer.";
        uses policer-grouping;

        leaf shared-policer {
          type string;
          description
            "Shared policer name.";
        }

        container stats {
          description
            "Traffic policer statistics.";
          uses policer-stats-grouping;
        }
      }
    }
  }

  grouping scheduler-queue-state-grouping {
    description
      "Common scheduler queue state.";

    leaf id {
      type uint32;
      description
        "Id of the queue.";
    }

    leaf size {
      type uint32;
      default "256";
      description
        "Size of the queue in packets. The value is rounded up to the nearest
         power of 2.";
    }

    container policer {
      description
        "Queue's input policer.";
      uses policer-grouping;

      container stats {
        description
          "Queue's input policer statistics.";
        uses policer-stats-grouping;
      }
    }

    container shaper {
      description
        "Queue's output shaper.";
      uses shaper-grouping;

      container stats {
        description
          "Queue's output shaper statistics.";
        uses shaper-stats-grouping;
      }
    }

    list class {
      key "mark";
      description
        "Classes assigned to the queue.";

      leaf mark {
        type string;
        description
          "Mark of the class.";
      }

      container stats {
        description
          "Class statistics.";

        leaf match-packets {
          type uint32;
          description
            "Number of packets matched.";
        }
      }
    }

    container stats {
      description
        "Queue statistics.";

      leaf enqueue-packets {
        type uint32;
        description
          "Number of packets enqueued.";
      }

      leaf xmit-packets {
        type uint32;
        description
          "Number of packets sent.";
      }

      leaf drop-queue-full {
        type uint32;
        description
          "Number of packets dropped.";
      }
    }
  }

  grouping scheduler-state-grouping {
    description
      "Scheduler state.";

    container scheduler {
      if-feature advanced;
      presence "Scheduler state.";
      description
        "Scheduler state.";

      leaf core {
        type uint32;
        description
          "Core used by the scheduler.";
      }

      container pq {
        presence "Priority Queueing state.";
        description
          "Priority Queueing state.";

        leaf nb-queue {
          type uint32;
          mandatory true;
          description
            "Number of Priority Queueing queues available in the scheduler.";
        }

        list queue {
          key "id";
          description
            "List of Priority Queueing queues.";
          uses scheduler-queue-state-grouping;
        }
      }

      container pb-dwrr {
        presence "Priority-Based Deficit Weighted Round Robin description.";
        description
          "Priority-Based Deficit Weighted Round Robin description.";

        leaf nb-queue {
          type uint32;
          mandatory true;
          description
            "Number of PB-DWRR queues available in the scheduler.";
        }

        list queue {
          key "id";
          description
            "List of PB-DWRR queues.";
          uses scheduler-queue-state-grouping;

          leaf quantum {
            type uint32;
            default "1500";
            description
              "Quantum of the queue in bytes. Relevant only if priority is low.";
          }

          leaf priority {
            type pb-dwrr-prio-t;
            default "low";
            description
              "Priority of the queue (low or high).";
          }
        }
      }
    }
  }

  grouping physical-if-qos-state {
    description
      "QoS state for a physical interface.";

    container qos {
      presence "QoS state";
      description
        "QoS state.";
      ntos-extensions:feature "product";

      container ingress {
        presence "Ingress QoS state.";
        description
          "Ingress QoS state.";
        uses rate-limit-state-grouping;
      }

      container egress {
        presence "Egress QoS state.";
        description
          "Egress QoS state.";
        uses rate-limit-state-grouping {
          augment rate-limit {
            description
              "Add shaper rate-limiting.";

            container shaper {
              if-feature advanced;
              description
                "Traffic shaper.";
              uses shaper-grouping;

              container stats {
                description
                  "Traffic shaper statistics.";
                uses shaper-stats-grouping;
              }
            }
          }
        }
        uses scheduler-state-grouping;
      }
    }
  }

  grouping logical-if-qos-state {
    description
      "QoS state for a logical interface.";

    container qos {
      presence "QoS state";
      description
        "QoS state.";

      container ingress {
        presence "Ingress QoS state.";
        description
          "Ingress QoS state.";
        uses rate-limit-state-grouping;
      }

      container egress {
        presence "Egress QoS state.";
        description
          "Egress QoS state.";
        uses rate-limit-state-grouping;
      }
    }
  }

  grouping scheduler-queue-grouping {
    description
      "Common scheduler queue options.";

    leaf id {
      type uint32;
      must 'number(.) <= number(../../nb-queue) and number(.) != 0' {
        error-message "Queue id must be less than or equal to the number of queues.";
      }
      description
        "Id of the queue.";
    }

    leaf size {
      type uint32;
      default "256";
      description
        "Size of the queue in packets.";
    }

    leaf policer {
      type leafref {
        path
          "/ntos:config/ntos-qos:qos/ntos-qos:policer/ntos-qos:name";
      }
      description
        "Traffic policer defined in the QoS context applied to incoming traffic.";
    }

    leaf shaper {
      type leafref {
        path
          "/ntos:config/ntos-qos:qos/ntos-qos:shaper/ntos-qos:name";
      }
      description
        "Traffic shaper defined in the QoS context applied to outgoing traffic.";
    }

    list class {
      key "name";
      description
        "List of traffic classes bound to this queue.";
      ntos-extensions:nc-cli-one-liner;

      leaf name {
        type leafref {
          path
            "/ntos:config/ntos-qos:qos/ntos-qos:class/ntos-qos:name";
        }
        description
          "Class name.";
      }
    }
  }

  augment "/ntos:config" {
    description
      "Top-level container for qos configuration.";

    container qos {
      presence "QoS configuration";
      description
        "QoS configuration.";
      ntos-extensions:feature "product";

      list policer {
        key "name";
        description
          "List of policer templates.";

        leaf name {
          type string;
          description
            "Policer template name.";
        }

        leaf description {
          type string;
          description
            "A comment to describe the policer template.";
        }
        uses policer-grouping;
      }

      list shared-policer {
        key "name";
        description
          "List of shared policers.";

        leaf name {
          type string;
          description
            "Shared policer name.";
        }

        leaf description {
          type string;
          description
            "A comment to describe the shared policer.";
        }

        leaf policer {
          type leafref {
            path
              "/ntos:config/ntos-qos:qos/ntos-qos:policer/ntos-qos:name";
          }
          description
            "Traffic policer template defined in the QoS context.";
        }
      }

      list shaper {
        if-feature advanced;
        key "name";
        description
          "List of shapers.";

        leaf name {
          type string;
          description
            "Shaper name.";
        }

        leaf description {
          type string;
          description
            "A comment to describe the shaper.";
        }
        uses shaper-grouping;
      }

      list scheduler {
        if-feature advanced;
        must 'number(boolean(pq)) + number(boolean(pb-dwrr)) = 1' {
          error-message "A queueing algorithm must be configured.";
        }
        key "name";
        description
          "List of schedulers.";

        leaf name {
          type string;
          description
            "Scheduler name.";
        }

        leaf description {
          type string;
          description
            "A comment to describe the scheduler.";
        }

        leaf core {
          type uint32;
          description
            "Core assigned to manage the scheduler. If unset, cpu is
             automaticaly selected.";
        }

        container pq {
          presence "Makes priority queueing available.";
          description
            "Priority Queueing description.";

          leaf nb-queue {
            type uint32;
            mandatory true;
            description
              "Number of Priority Queueing queues available in the scheduler.";
          }

          list queue {
            key "id";
            description
              "List of Priority Queueing queues.";
            uses scheduler-queue-grouping;
          }
        }

        container pb-dwrr {
          presence "Makes Priority-Based Deficit Weighted Round Robin available.";
          description
            "Priority-Based Deficit Weighted Round Robin description.";

          leaf nb-queue {
            type uint32;
            mandatory true;
            description
              "Number of PB-DWRR queues available in the scheduler.";
          }

          list queue {
            key "id";
            description
              "List of PB-DWRR queues.";
            uses scheduler-queue-grouping;

            leaf quantum {
              type uint32;
              default "1500";
              description
                "Quantum of the queue. Relevant only if priority is low.";
            }

            leaf priority {
              type pb-dwrr-prio-t;
              must "count(../../queue/priority[text() = 'high']) <= 1" {
                error-message "Only one queue can have the priority high.";
              }
              default "low";
              description
                "Priority of the queue (low or high).";
            }
          }
        }
      }

      leaf class-mask {
        if-feature advanced;
        type string {
          pattern '0x[0-9a-fA-F]{1,8}';
          ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
        }
        default "0xFFFFFFFF";
        description
          "Mask applied to marks.";
      }

      list class {
        if-feature "advanced";
        must "cp = 'true' or mark" {
          error-message
            "Classes that do not relate to critical control plane
             traffic must specify a mark.";
        }
        key "name";
        description
          "List of supported classes.";
        ntos-api:must-added "cp = 'true' or mark";

        leaf name {
          type string;
          description
            "Class name.";
        }

        leaf description {
          type string;
          description
            "A comment to describe the class.";
        }

        leaf mark {
          type string {
            pattern '0x[0-9a-fA-F]{1,8}';
            ntos-extensions:nc-cli-shortdesc "<0x0-0xffffffff>";
          }
          description
            "Class mark. Optional if cp is true.";
        }

        leaf cp {
          type boolean;
          description
            "Whether this class relates to critical control plane traffic.
             If unset, match any traffic.
             If true, only match critical control plane traffic.
             If false, do not match critical control plane traffic.";
        }
      }
    }
  }

  augment "/ntos:state" {
    description
      "Top-level container for qos state.";

    container qos {
      presence "QoS state";
      description
        "QoS state.";
      ntos-extensions:feature "product";

      list shared-policer {
        key "name";
        description
          "List of shared policers.";

        leaf name {
          type string;
          description
            "Shared policer name.";
        }
        uses policer-grouping;

        container stats {
          description
            "Traffic policer statistics.";
          uses policer-stats-grouping;
        }
      }
    }
  }
}
