package user

import (
	"encoding/xml"
	"time"

	"github.com/imroc/req/v3"
)

var SidWebClient *req.Client

func init() {
	SidWebClient = req.C().
		SetCommonRetryCount(5).
		// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
		SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
		AddCommonRetryCondition(func(resp *req.Response, err error) bool {
			return err != nil
		})
}

// 根结构体
type ServiceResponse struct {
	XMLName               xml.Name               `xml:"serviceResponse"`
	AuthenticationSuccess *AuthenticationSuccess `xml:"authenticationSuccess"`
	AuthenticationFailure *AuthenticationFailure `xml:"authenticationFailure"`
}

// 认证成功结构体
type AuthenticationSuccess struct {
	User       string      `xml:"user"`
	Attributes *Attributes `xml:"attributes"`
}

// 认证成功结构体
type AuthenticationFailure struct {
	Code    string `xml:"code,attr"`
	Message string `xml:",chardata"`
}

// 属性结构体
type Attributes struct {
	GH                               string `xml:"GH"`
	IsFromNewLogin                   bool   `xml:"isFromNewLogin"`
	AuthenticationDate               string `xml:"authenticationDate"`
	STAFFTYPE                        string `xml:"STAFFTYPE"`
	SuccessfulAuthenticationHandlers string `xml:"successfulAuthenticationHandlers"`
	XB                               string `xml:"XB"`
	RJXM                             string `xml:"RJXM"`
	POSITIONCLASSIFY                 string `xml:"POSITIONCLASSIFY"`
	HEADCOUNTTYPE                    string `xml:"HEADCOUNTTYPE"`
	SamlAuthMethod                   string `xml:"samlAuthenticationStatementAuthMethod"`
	CredentialType                   string `xml:"credentialType"`
	RJEMAIL                          string `xml:"RJEMAIL"`
	POSITION                         string `xml:"POSITION"`
	CompletedLoginMethod             string `xml:"completedLoginMethod"`
	SFLBDM                           string `xml:"SFLBDM"`
	XM                               string `xml:"XM"`
	AuthenticationMethod             string `xml:"authenticationMethod"`
	LongTermAuthTokenUsed            bool   `xml:"longTermAuthenticationRequestTokenUsed"`
	SZDW                             string `xml:"SZDW"`
	RJGH                             string `xml:"RJGH"`
	UserAgent                        string `xml:"user-agent"`
}

// func LoginBySid(ctx iris.Context) {
// 	ticket := ctx.FormValue("ticket")
// 	_url := fmt.Sprintf("https://sid.ruijie.com.cn/p3/serviceValidate?ticket=%s&service=http://csbu.ruijie.com.cn/", ticket)
// 	var loginResp ServiceResponse

// 	resp, err := SidWebClient.R().Get(_url)
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("login by sid get err", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}

// 	err = xml.Unmarshal([]byte(resp.String()), &loginResp)
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("login by sid unmarshal resp get err", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}

// 	if loginResp.AuthenticationFailure == nil && loginResp.AuthenticationSuccess == nil {
// 		logging.ErrorLogger.Errorf("login by sid get resp failure and success is nil")
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}

// 	if loginResp.AuthenticationFailure != nil {
// 		logging.ErrorLogger.Errorf("login by sid failure", loginResp.AuthenticationFailure.Code, loginResp.AuthenticationFailure.Message)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}

// 	attr := loginResp.AuthenticationSuccess.Attributes

// 	if attr.GH == "" {
// 		logging.ErrorLogger.Errorf("login by sid user is empty")
// 		ctx.JSON(response.NewResponse(response.AuthNameOrPassErr.Code, nil, response.AuthNameOrPassErr.Msg))
// 		return
// 	}

// 	user, err := CreateLocalUserFromSid(attr)
// 	if err != nil {
// 		logging.ErrorLogger.Errorf("login by sid create local user get err", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}

// 	var token string
// 	token, err = duser.Login(uint64(user.ID))
// 	if err != nil {
// 		ctx.JSON(response.NewResponse(response.AuthErr.Code, nil, err.Error()))
// 		return
// 	}

// 	logging.DebugLogger.Debugf("user token %s", token)
// 	// 自动获取gitlabtoken
// 	_user := User{
// 		Id:       user.ID,
// 		Username: user.Username,
// 		Password: "",
// 		Enable:   user.Enable,
// 	}
// 	go GetGitlabTokenjob(&_user)
// 	ctx.JSON(response.NewResponse(response.NoErr.Code, &Token{AccessToken: token}, response.NoErr.Msg))
// 	return
// }

// func CreateLocalUserFromSid(attr *Attributes) (duser.User, error) {
// 	user := duser.User{}
// 	username := attr.GH
// 	buMember := dpmsbumember.PmsBuMember{}
// 	err := buMember.FindEx("user_en", username)
// 	if err != nil {
// 		return user, err
// 	}

// 	if buMember.ID == 0 {
// 		return user, fmt.Errorf("%s不属于BU用户", username)
// 	}

// 	// 同步创建用户关系
// 	var group, link string
// 	var parentDepartmentID uint

// 	departments := []string{buMember.OneDepartmentName, buMember.TwoDepartmentName, buMember.ThreeDepartmentName, buMember.FourDepartmentName}

// 	// 同步组织架构

// 	for _, department := range departments {

// 		departmentInst := ddepartment.Department{}
// 		department = strings.ReplaceAll(department, "本部", "")

// 		if department != "" {
// 			group = department
// 			if link == "" {
// 				link = department
// 			} else {
// 				link = fmt.Sprintf("%s/%s", link, group)
// 				err := departmentInst.FindEx("name", department)
// 				if err != nil {
// 					return user, err
// 				}

// 				if departmentInst.ID == 0 {
// 					err := departmentInst.Create(map[string]interface{}{
// 						"name":       department,
// 						"parent_id":  parentDepartmentID,
// 						"link":       link,
// 						"created_at": time.Now(),
// 						"updated_at": time.Now(),
// 					})
// 					if err != nil {
// 						return user, err
// 					}
// 					err = departmentInst.FindEx("name", department)
// 					if err != nil {
// 						return user, err
// 					}
// 				}
// 				parentDepartmentID = departmentInst.ID
// 			}
// 		} else {
// 			break
// 		}
// 	}

// 	err = user.FindByUserName(username)
// 	if err != nil {
// 		return user, err
// 	}

// 	if user.ID == 0 {
// 		userData := map[string]interface{}{
// 			"Name":         attr.XM,
// 			"Username":     attr.GH,
// 			"Password":     "",
// 			"Intro":        "",
// 			"Avatar":       "",
// 			"UpdatedAt":    time.Now(),
// 			"CreatedAt":    time.Now(),
// 			"Enable":       true,
// 			"Roles":        []string{"组员"},
// 			"DepartmentID": parentDepartmentID,
// 		}
// 		err := user.Create(userData)
// 		if err != nil {
// 			return user, err
// 		}
// 	}
// 	return user, err
// }
