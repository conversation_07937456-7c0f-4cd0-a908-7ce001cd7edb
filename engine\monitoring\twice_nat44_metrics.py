#!/usr/bin/env python3
"""
twice-nat44性能监控和度量模块

本模块提供twice-nat44功能的性能监控和统计功能，包括：
1. 使用率统计
2. 评估效果监控
3. 转换成功率跟踪
4. 性能对比分析

作者: FortiGate转换系统
版本: 1.0
日期: 2025-08-08
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class TwiceNat44Metrics:
    """twice-nat44度量数据结构"""
    timestamp: str
    total_policies: int
    evaluated_policies: int
    recommended_policies: int
    generated_rules: int
    ippool_policies: int
    ippool_recommended: int
    average_score: float
    threshold_used: int
    conversion_duration: float
    success_rate: float

@dataclass
class PolicyEvaluationResult:
    """策略评估结果"""
    policy_name: str
    total_score: int
    recommended: bool
    confidence: float
    has_ippool: bool
    pool_format: str  # "ip_address", "traditional", "mixed", "none"
    vip_count: int
    service_count: int
    evaluation_time: float

class TwiceNat44MetricsCollector:
    """twice-nat44度量收集器"""
    
    def __init__(self, metrics_file: str = "output/metrics/twice_nat44_metrics.json"):
        self.metrics_file = Path(metrics_file)
        self.metrics_file.parent.mkdir(parents=True, exist_ok=True)
        
        self.current_session = {
            "start_time": time.time(),
            "policies": [],
            "evaluations": [],
            "generated_rules": 0,
            "threshold": 65
        }
    
    def start_conversion_session(self, threshold: int = 65):
        """开始转换会话"""
        self.current_session = {
            "start_time": time.time(),
            "policies": [],
            "evaluations": [],
            "generated_rules": 0,
            "threshold": threshold
        }
    
    def record_policy_evaluation(self, result: PolicyEvaluationResult):
        """记录策略评估结果"""
        self.current_session["evaluations"].append(asdict(result))
    
    def record_generated_rule(self, rule_type: str, policy_name: str):
        """记录生成的规则"""
        if rule_type == "twice-nat44":
            self.current_session["generated_rules"] += 1
    
    def end_conversion_session(self) -> TwiceNat44Metrics:
        """结束转换会话并生成度量"""
        end_time = time.time()
        duration = end_time - self.current_session["start_time"]
        
        evaluations = self.current_session["evaluations"]
        total_policies = len(evaluations)
        
        if total_policies == 0:
            return TwiceNat44Metrics(
                timestamp=datetime.now().isoformat(),
                total_policies=0,
                evaluated_policies=0,
                recommended_policies=0,
                generated_rules=0,
                ippool_policies=0,
                ippool_recommended=0,
                average_score=0.0,
                threshold_used=self.current_session["threshold"],
                conversion_duration=duration,
                success_rate=0.0
            )
        
        recommended_policies = sum(1 for e in evaluations if e["recommended"])
        ippool_policies = sum(1 for e in evaluations if e["has_ippool"])
        ippool_recommended = sum(1 for e in evaluations if e["has_ippool"] and e["recommended"])
        average_score = sum(e["total_score"] for e in evaluations) / total_policies
        success_rate = recommended_policies / total_policies if total_policies > 0 else 0.0
        
        metrics = TwiceNat44Metrics(
            timestamp=datetime.now().isoformat(),
            total_policies=total_policies,
            evaluated_policies=total_policies,
            recommended_policies=recommended_policies,
            generated_rules=self.current_session["generated_rules"],
            ippool_policies=ippool_policies,
            ippool_recommended=ippool_recommended,
            average_score=round(average_score, 2),
            threshold_used=self.current_session["threshold"],
            conversion_duration=round(duration, 2),
            success_rate=round(success_rate, 4)
        )
        
        self._save_metrics(metrics)
        return metrics
    
    def _save_metrics(self, metrics: TwiceNat44Metrics):
        """保存度量数据"""
        try:
            # 读取现有数据
            if self.metrics_file.exists():
                with open(self.metrics_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = {"sessions": []}
            
            # 添加新会话
            data["sessions"].append(asdict(metrics))
            
            # 保存数据
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存度量数据失败: {e}")
    
    def get_historical_metrics(self, days: int = 30) -> List[TwiceNat44Metrics]:
        """获取历史度量数据"""
        if not self.metrics_file.exists():
            return []
        
        try:
            with open(self.metrics_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_sessions = []
            
            for session_data in data.get("sessions", []):
                session_time = datetime.fromisoformat(session_data["timestamp"])
                if session_time >= cutoff_date:
                    recent_sessions.append(TwiceNat44Metrics(**session_data))
            
            return recent_sessions
            
        except Exception as e:
            print(f"读取历史度量数据失败: {e}")
            return []

class TwiceNat44PerformanceAnalyzer:
    """twice-nat44性能分析器"""
    
    def __init__(self, metrics_collector: TwiceNat44MetricsCollector):
        self.collector = metrics_collector
    
    def generate_performance_report(self, days: int = 30) -> Dict[str, Any]:
        """生成性能报告"""
        metrics = self.collector.get_historical_metrics(days)
        
        if not metrics:
            return {
                "period": f"最近{days}天",
                "total_sessions": 0,
                "message": "没有可用的度量数据"
            }
        
        # 基础统计
        total_sessions = len(metrics)
        total_policies = sum(m.total_policies for m in metrics)
        total_recommended = sum(m.recommended_policies for m in metrics)
        total_ippool_policies = sum(m.ippool_policies for m in metrics)
        total_ippool_recommended = sum(m.ippool_recommended for m in metrics)
        total_generated_rules = sum(m.generated_rules for m in metrics)
        
        # 计算平均值
        avg_score = sum(m.average_score for m in metrics) / total_sessions
        avg_success_rate = sum(m.success_rate for m in metrics) / total_sessions
        avg_conversion_time = sum(m.conversion_duration for m in metrics) / total_sessions
        
        # IP池相关统计
        ippool_coverage_rate = total_ippool_recommended / total_ippool_policies if total_ippool_policies > 0 else 0
        overall_success_rate = total_recommended / total_policies if total_policies > 0 else 0
        
        # 趋势分析
        recent_metrics = metrics[-5:] if len(metrics) >= 5 else metrics
        recent_avg_score = sum(m.average_score for m in recent_metrics) / len(recent_metrics)
        recent_success_rate = sum(m.success_rate for m in recent_metrics) / len(recent_metrics)
        
        # 阈值使用统计
        threshold_usage = {}
        for m in metrics:
            threshold = m.threshold_used
            if threshold not in threshold_usage:
                threshold_usage[threshold] = {"count": 0, "success_rate": 0}
            threshold_usage[threshold]["count"] += 1
            threshold_usage[threshold]["success_rate"] += m.success_rate
        
        for threshold in threshold_usage:
            threshold_usage[threshold]["success_rate"] /= threshold_usage[threshold]["count"]
            threshold_usage[threshold]["success_rate"] = round(threshold_usage[threshold]["success_rate"], 4)
        
        return {
            "period": f"最近{days}天",
            "summary": {
                "total_sessions": total_sessions,
                "total_policies": total_policies,
                "total_recommended": total_recommended,
                "total_generated_rules": total_generated_rules,
                "overall_success_rate": round(overall_success_rate, 4),
                "average_score": round(avg_score, 2),
                "average_conversion_time": round(avg_conversion_time, 2)
            },
            "ippool_analysis": {
                "total_ippool_policies": total_ippool_policies,
                "ippool_recommended": total_ippool_recommended,
                "ippool_coverage_rate": round(ippool_coverage_rate, 4),
                "improvement": "IP池支持改进使覆盖率从0%提升到{:.1%}".format(ippool_coverage_rate)
            },
            "performance_trends": {
                "recent_average_score": round(recent_avg_score, 2),
                "recent_success_rate": round(recent_success_rate, 4),
                "score_trend": "上升" if recent_avg_score > avg_score else "下降" if recent_avg_score < avg_score else "稳定",
                "success_rate_trend": "上升" if recent_success_rate > avg_success_rate else "下降" if recent_success_rate < avg_success_rate else "稳定"
            },
            "threshold_analysis": threshold_usage,
            "recommendations": self._generate_recommendations(metrics)
        }
    
    def _generate_recommendations(self, metrics: List[TwiceNat44Metrics]) -> List[str]:
        """生成优化建议"""
        if not metrics:
            return ["需要更多数据来生成建议"]
        
        recommendations = []
        
        # 分析平均分数
        avg_score = sum(m.average_score for m in metrics) / len(metrics)
        if avg_score < 70:
            recommendations.append("平均评分较低，建议检查评估逻辑或降低阈值")
        elif avg_score > 85:
            recommendations.append("平均评分较高，可以考虑适当提高阈值以提高质量")
        
        # 分析成功率
        avg_success_rate = sum(m.success_rate for m in metrics) / len(metrics)
        if avg_success_rate < 0.3:
            recommendations.append("成功率较低，建议优化评估算法或调整权重分配")
        elif avg_success_rate > 0.8:
            recommendations.append("成功率很高，twice-nat44功能运行良好")
        
        # 分析IP池覆盖率
        total_ippool = sum(m.ippool_policies for m in metrics)
        total_ippool_recommended = sum(m.ippool_recommended for m in metrics)
        if total_ippool > 0:
            ippool_rate = total_ippool_recommended / total_ippool
            if ippool_rate < 0.5:
                recommendations.append("IP池策略覆盖率较低，建议进一步优化IP池支持")
            else:
                recommendations.append("IP池策略覆盖率良好，改进效果显著")
        
        # 分析转换时间
        avg_time = sum(m.conversion_duration for m in metrics) / len(metrics)
        if avg_time > 60:
            recommendations.append("转换时间较长，建议优化性能")
        
        return recommendations

# 全局度量收集器实例
metrics_collector = TwiceNat44MetricsCollector()
performance_analyzer = TwiceNat44PerformanceAnalyzer(metrics_collector)
