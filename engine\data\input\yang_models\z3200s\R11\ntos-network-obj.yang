module ntos-network-obj {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:network-obj";
  prefix ntos-network-obj;

  import extra-conditions {
    prefix ext-cond;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-api {
    prefix ntos-api;
  }

  import ntos-if-types {
    prefix ntos-if;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS network object module.";

  revision 2024-07-24 {
    description
      "Add capacity query command.";
    reference "";
  }

  revision 2022-08-02 {
    description
      "Address type support IPv6.";
    reference "";
  }

  revision 2021-09-30 {
    description
      "Initial version.";
    reference "";
  }

  revision 2024-07-10 {
    description
      "support wildcard ip.";
    reference "";
  }

  typedef wildcard-ipv4-range {
    type string {
      pattern '(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.\*\.\*\.' +
              '([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]))-' +
              '(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.\*\.\*\.' +
              '([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]))' {
        error-message "Invalid wildcard IPv4 range.";
      }
      ntos-ext:nc-cli-shortdesc "<A.*.*.A-B.*.*.B>";
    }
    description
      "An wildcard IPv4 address range.";
    ntos-api:pattern-stable;
  }

  typedef wildcard-ipv4-address {
    type string {
      pattern '(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\.\*\.\*\.' +
              '([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]))' {
        error-message "Invalid IPv4 wildcard address.";
      }
      ntos-ext:nc-cli-shortdesc "<A.*.*.A>";
    }
    description
      "An wildcard IPv4 address.";
    ntos-api:pattern-stable;
  }

  typedef ipv4-address-type {
    type union {
      type ntos-inet:ipv4-address;
      type ntos-inet:ipv4-prefix;
      type ntos-inet:ipv4-range;
      type ntos-inet:ipv4-mask;
    }
  }

  typedef ip-address-type {
    type union {
      type ntos-inet:ipv4-address;
      type ntos-inet:ipv4-prefix;
      type ntos-inet:ipv4-range;
      type ntos-inet:ipv4-mask;
      type wildcard-ipv4-range;
      type wildcard-ipv4-address;

      /* add IPv6 Address Type */
      type ntos-inet:ipv6-address;
      type ntos-inet:ipv6-prefix;
      type ntos-inet:ipv6-range;
    }
  }

  grouping address-obj-comm-config {
    leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Address object name.";
    }

    leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "The descrption of the address object.";
    }
  }

  grouping show-domain-object-input-arguments {
    description
      "The domain group show input arguments";

    leaf vrf {
      type ntos:vrf-name;
      description
        "The specific vrf.";
    }

    leaf name {
      type string;
      description
        "The name string of the object.";
      ntos-ext:nc-cli-group "subcommand1";
    }

    leaf filter {
      type string;
      description
        "The filter string of the object.";
      ntos-ext:nc-cli-group "subcommand1";
    }

    leaf start {
      type uint32;
      description
        "The index of page start.";
    }

    leaf end {
      type uint32;
      must "current() >= ../start" {
          error-message "The end value must be larger than the start value.";
      }
      description "The index of page end.";
    }
  }

  grouping domain-object-refer-info {
    description
      "The domain group refer info.";

    list refer-info {
      description
        "The refer info.";

      leaf refer {
        type string;
        description "The refer of the object.";
      }

      leaf-list src-refer-id {
        type uint64;
        description "The src refer id";
      }

      leaf-list dst-refer-id {
        type uint64;
        description "The dst refer id";
      }
    }
  }

  grouping domain-obj-comm-config {
    description
      "The grouping of domain object.";

    list domain-set {
      key "domain";
        description
          "The list of IP address.";
        ntos-ext:nc-cli-one-liner;
      leaf domain {
        type ntos-inet:wildcard-domain-name;
        ntos-ext:nc-cli-shortdesc "<host-name>";
      }
    }
  }

  grouping show-domain-object-output {
    description
      "The domain object show output info";

    leaf domain-object-num {
      type uint32;
      description
        "The total number of the domain object.";
    }

    list domain-object {
      description
        "Indicate the domain group.";
      uses address-obj-comm-config;
      uses domain-obj-comm-config;
      uses domain-object-refer-info;
      leaf ref-cnt {
        type uint64;
        description "ref count";
      }
    }
  }

  grouping address-obj-config {
    description
      "Configuration of address object or address-group.";

    list domain-object-set {
      key "name";
      ordered-by user;
      must 'count(./domain-set) > 0' {
        error-message "domain-set can not be zero.";
      }
      description
        "The list of domian object.";
      uses address-obj-comm-config;

      uses domain-obj-comm-config;
    }

    list address-set {
      key "name";
      ordered-by user;
      description
        "The list of address object.";

      must ' count(./ip-set) > 0' {
        error-message "Ip-set can not be zero.";
      }

      uses address-obj-comm-config;

      list ip-set {
        key "ip-address";
        description
          "The list of IP address.";
        ntos-ext:nc-cli-one-liner;

        leaf ip-address {
          type ip-address-type;
          description
            "IPv4/IPv6 address.";
        }
      }
    }

    list address-group {
      key "name";
      ordered-by user;
      description
        "The list of address group.";

      must 'count(./address-set) > 0' {
        error-message "The numeration of address-set can not be zero.";
      }

      uses address-obj-comm-config;

      list address-set {
        description
          "The list of address-set.";
        key name;
        ntos-ext:nc-cli-one-liner;

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The address-set name.";
        }
      }
    }

    list mac-address-set {
      key "name";
      ordered-by user;
      description
        "The list of mac address object.";

      must 'count(./mac-set) > 0' {
        error-message "mac-set can not be zero.";
      }

      uses address-obj-comm-config;

      list mac-set {
        key "mac-address";
        description
          "The list of mac address.";
        ntos-ext:nc-cli-one-liner;

        leaf mac-address {
          type ntos-if:mac-address;
          description
            "mac address.";
        }
      }
    }

    list mac-address-group {
      key "name";
      ordered-by user;
      description
        "The list of mac address group.";

      must 'count(./mac-address-set) > 0' {
        error-message "The numeration of mac-address-set can not be zero.";
      }

      uses address-obj-comm-config;

      list mac-address-set {
        description
          "The list of mac-address-set.";
        key name;
        ntos-ext:nc-cli-one-liner;

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The mac-address-set name.";
        }
      }
    }
  }
  grouping output-ntk-group-detail {
    leaf name {
      type string;
      description
        "The name of the ntk group obj.";
    }
    leaf description {
      type string;
      description
        "The description of ntk group obj.";
    }
    leaf obj-type {
      type string;
      description
        "The type of the ntk obj.";
    }
    leaf-list address-set {
      description "The ip obj name.";
      type string;
    }
    leaf-list mac-address-set {
      description "The mac obj name.";
      type string;
    }
    leaf ref-ply-count {
      description
      "Total number of ref group.";
      type uint32;
    }
    list ref-ply-type {
      description
        "The policy type referenced by the ntk obj.";
      key "ply-type";
      leaf ply-type {
        type string;
        description
          "The name of custom policy.";
      }

      leaf-list src-ply-id {
        type uint32;
        description
          "The id of policy.";
      }

      leaf-list dst-ply-id {
        type uint32;
        description
          "The id of policy.";
      }
    }
  }

  grouping output-ntk-detail {
    leaf name {
      type string;
      description
        "The name of the ntk obj.";
    }
    leaf description {
      type string;
      description
        "The description of ntk obj.";
    }
    leaf ref-group-count {
      description
      "Total number of ref group.";
      type uint32;
    }
    leaf-list ref-group {
        type string;
        description "The group name.";
    }
    leaf obj-type {
      type string;
      description
        "The type of the ntk obj.";
    }
    list ip-set {
      description
        "The IP address bound to the ntk obj.";
      key "ip-address";
      leaf ip-address {
        type string;
      }
    }
    list mac-set {
      description
        "The mac address bound to the ntk obj.";
      key "mac-address";
      leaf mac-address {
        type string;
      }
    }

    leaf ref-ply-count {
      description
      "Total number of ref group.";
      type uint32;
    }
    list ref-ply-type {
      description
        "The policy type referenced by the ntk obj.";
      key "ply-type";
      leaf ply-type {
        type string;
        description
          "The name of custom policy.";
      }

      leaf-list src-ply-id {
        type uint32;
        description
          "The id of policy.";
      }

      leaf-list dst-ply-id {
        type uint32;
        description
          "The id of policy.";
      }
    }
  }

  rpc show-network-obj {
    description
      "Show network object.";

    input {
      leaf vrf {
        type string;
        description
          "VRF.";
      }

      container content {
        description
          "The content of network object";
        ntos-ext:nc-cli-group "subcommand";
        leaf type {
          type enumeration {
            enum address;
            enum address-group;
          }
          description
            "Address-set or address-group.";
        }

        leaf obj-type {
          type enumeration {
            enum ipv4;
            enum ipv6;
            enum mac;
          }
          description
            "The net object type is ipv4, ipv6 or mac.";
        }

        leaf filter {
          type string;
          description
            "Filter.";
        }

        leaf name {
          type string;
          description
            "Address-set name, address-group name, mac-address-set name or mac-address-group name.";
        }

        leaf start {
          type uint32;
          description
            "Start offset.";
        }

        leaf end {
          type uint32;
          description
            "End offset.";
        }
        leaf with-hidden-ref {
          type empty;
          description
            "With hidden reference policy";
        }
        leaf format {
          type empty;
          description
            "Formatting display";
        }
      }

      container reference {
        description
          "The reference infomation of network object by policy";
        ntos-ext:nc-cli-group "subcommand";
        leaf name {
          type string;
          description
            "Name of network object.";
        }
        leaf with-hidden-ref {
          type empty;
          description
            "With hidden reference policy";
        }
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
          ntos-ext:nc-cli-stdout;
          ntos-ext:nc-cli-hidden;
      }
      leaf ip-total {
        description
        "Total number of ntk obj.";
        type uint32;
      }
      leaf mac-total {
        description
        "Total number of ntk obj.";
        type uint32;
      }
      leaf ip-group-total {
        description
        "Total number of ip group obj.";
        type uint32;
      }
      leaf mac-group-total {
        description
        "Total number of mac group obj.";
        type uint32;
      }
      list address-obj {
        key "name";
        description
          "The detail of ntk obj.";
        uses output-ntk-detail;
      }
      list group-obj {
        key "name";
        description
          "The detail of ntk group obj.";
        uses output-ntk-group-detail;
      }
    }

    ntos-ext:nc-cli-show "network-obj";
    ntos-api:internal;
  }


  rpc show-all-network-obj {
    description
      "Show all network object and network group.";

    input {
      leaf vrf {
        type string;
        description
          "VRF.";
      }

      leaf filter {
        type string;
        description
          "Filter.";
      }
    }

    output {
      list ipv4 {
        key name;
        leaf name {
          type string;
        }
      }

      list ipv6 {
        key name;
        leaf name {
          type string;
        }
      }

      list ipv4-group {
        key name;
        leaf name {
          type string;
        }
      }

      list ipv6-group {
        key name;
        leaf name {
          type string;
        }
      }

      list mac {
        key name;
        leaf name {
          type string;
        }
      }

      list mac-group {
        key name;
        leaf name {
          type string;
        }
      }
    }

    ntos-ext:nc-cli-show "all-network-obj";
    ntos-api:internal;
  }

  rpc show-network-domain-object {
    description
      "Show domain group.";

    input {
      uses show-domain-object-input-arguments;
    }

    output {
      uses show-domain-object-output;
    }

    ntos-ext:nc-cli-show "network-domain-object";
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Network object configuration.";

    container network-obj {
      presence "Network object configuration.";
      description
        "Network object configuration.";
      ext-cond:unique-values "*/*[local-name()='name']" {
        error-message "The address-set and address-group name must be unique in a VRF.";
      }

      uses address-obj-config;
    }
  }

  grouping capacity-detail {
    description
      "The detail of capacity.";
    leaf total {
      description
        "The total capacity.";
      type uint32;
    }
    leaf remain {
      description
        "The remaining capacity.";
      type uint32;
    }
  }

  grouping item-capacity {
    container item {
      description
        "The capacity of item.";
      uses capacity-detail;
    }
    container subitem {
      description
        "The capacity of the subitems for all items.";
      uses capacity-detail;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Network object state data.";
    container network-obj {
      container capacity {
        description
          "The capacity of network object.";
        container ip-obj {
          description
            "The capacity of ip object.";
          uses item-capacity;
        }
        container ip-group {
          description
            "The capacity of ip group.";
          uses item-capacity;
        }
        container mac-obj {
          description
            "The capacity of mac object.";
          uses item-capacity;
        }
        container mac-group {
          description
            "The capacity of mac group.";
          uses item-capacity;
        }
      }
    }
  }
}
