package libs

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	"crypto/md5"
	"crypto/tls"
	"encoding/gob"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"irisAdminApi/application/logging"
	"math/rand"
	"os/exec"

	"unsafe"

	"net"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"time"

	"golang.org/x/crypto/ssh"
	"golang.org/x/text/encoding/simplifiedchinese"
	"gopkg.in/ini.v1"
)

// md5
func MD5(str string) string {
	encoder := md5.New()
	encoder.Write([]byte(str))
	return hex.EncodeToString(encoder.Sum(nil))
}

// 当前目录
func CWD() string {
	path, err := os.Executable()
	if err != nil {
		return ""
	}
	return filepath.Dir(path)
}

func EnsureDir(dir string) (err error) {
	if _, err = os.Stat(dir); os.IsNotExist(err) {
		err = os.MkdirAll(dir, 0755)
		if err != nil {
			return
		}
	}
	return
}

func IsPortInUse(port int64) bool {
	if conn, err := net.DialTimeout("tcp", net.JoinHostPort("", fmt.Sprintf("%d", port)), 3*time.Second); err == nil {
		conn.Close()
		return true
	}
	return false
}

func init() {
	gob.Register(map[string]interface{}{})
	ini.PrettyFormat = false
}

func CompressStr(str string) string {
	if str == "" {
		return ""
	}
	//匹配一个或多个空白符的正则表达式
	reg := regexp.MustCompile("\\s+")
	return reg.ReplaceAllString(str, "")
}

func SSHConnect(user, password, host string, port int) (*ssh.Session, error) {
	var (
		auth         []ssh.AuthMethod
		addr         string
		clientConfig *ssh.ClientConfig
		client       *ssh.Client
		session      *ssh.Session
		err          error
	)
	auth = make([]ssh.AuthMethod, 0)

	if len(Config.Rsa.Privatekey) > 0 {
		key, err := ioutil.ReadFile(Config.Rsa.Privatekey)
		if err != nil {
			return nil, err
		}
		// Create the Signer for this private key.
		signer, err := ssh.ParsePrivateKey(key)
		if err != nil {
			return nil, err
		}
		auth = append(auth, ssh.PublicKeys(signer))
	}

	// get auth method
	if len(password) > 0 {
		auth = append(auth, ssh.Password(password))
	}

	// hostKeyCallbk := func(hostname string, remote net.Addr, key ssh.PublicKey) error {
	// 	return nil
	// }

	clientConfig = &ssh.ClientConfig{
		User: user,
		Auth: auth,
		// Timeout:             30 * time.Second,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// connet to ssh
	addr = fmt.Sprintf("%s:%d", host, port)

	if client, err = ssh.Dial("tcp", addr, clientConfig); err != nil {
		return nil, err
	}

	// create session
	if session, err = client.NewSession(); err != nil {
		return nil, err
	}

	return session, nil
}

func SwitchContentEncoding(resp *http.Response) (bodyReader io.Reader, err error) {
	switch resp.Header.Get("Content-Encoding") {
	case "gzip":
		bodyReader, err = gzip.NewReader(resp.Body)
	case "deflate":
		bodyReader = flate.NewReader(resp.Body)
	default:
		bodyReader = resp.Body
	}
	return
}

var HttpClient = &http.Client{
	Transport: &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	},
}

func HandlerRequest(req *http.Request) (map[string]interface{}, error) {

	resp, err := HttpClient.Do(req)
	if err != nil {
		logging.ErrorLogger.Errorf("get response err ", err)
		return nil, err
	}
	defer resp.Body.Close()
	body, err := SwitchContentEncoding(resp)
	if err != nil {
		logging.ErrorLogger.Errorf("decode response err ", err)
		return nil, err
	}
	out, err := ioutil.ReadAll(body)
	if err != nil {
		logging.ErrorLogger.Errorf("read response err ", err)
		return nil, err
	}
	result := []map[string]interface{}{}
	err = json.Unmarshal(out, &result)
	if err != nil {

		result := map[string]interface{}{}
		err = json.Unmarshal(out, &result)
		if err != nil {
			result := map[string]interface{}{
				"data": string(out),
			}
			return result, nil
		}
		return result, nil
	} else {
		// X-Total:[2] X-Total-Pages:[1]

		total, _ := strconv.Atoi(resp.Header.Get("X-Total"))
		limit, _ := strconv.Atoi(resp.Header.Get("X-Per-Page"))
		list := map[string]interface{}{"items": result, "total": total, "limit": limit}
		return list, nil
	}

}

func HandlerRequestCommon(req *http.Request) ([]byte, error) {

	resp, err := HttpClient.Do(req)
	if err != nil {
		logging.ErrorLogger.Errorf("get response err ", err)
		return nil, err
	}
	defer resp.Body.Close()
	body, err := SwitchContentEncoding(resp)
	if err != nil {
		logging.ErrorLogger.Errorf("decode response err ", err)
		return nil, err
	}
	out, err := ioutil.ReadAll(body)
	if err != nil {
		logging.ErrorLogger.Errorf("read response err ", err)
		return nil, err
	}
	return out, nil
}

func str2bytes(s string) []byte {
	x := (*[2]uintptr)(unsafe.Pointer(&s))
	h := [3]uintptr{x[0], x[1], x[1]}
	return *(*[]byte)(unsafe.Pointer(&h))
}

func bytes2str(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

func GetUUID() string {
	rand.Seed(time.Now().UnixNano())
	randNum := fmt.Sprintf("%d", rand.Intn(9999)+1000)
	hashName := md5.Sum([]byte(time.Now().Format("2006_01_02_15_04_05_") + randNum))
	fileName := fmt.Sprintf("%x", hashName)
	return fileName
}

func GetUniqueID() string {
	now := time.Now()
	nanoSecond := now.Nanosecond() / 1e3
	if nanoSecond < 1000000 {
		nanoSecond += 1000000
	}
	return fmt.Sprintf("%s%d", now.Format("20060102150405"), nanoSecond)
}

func GetFileMd5(filepath string) (string, error) {
	// 文件全路径名
	pFile, err := os.Open(filepath)
	if err != nil {
		// fmt.Errorf("打开文件失败，filename=%v, err=%v", filepath, err)
		return "", err
	}
	defer pFile.Close()
	md5h := md5.New()
	io.Copy(md5h, pFile)

	return hex.EncodeToString(md5h.Sum(nil)), nil
}

// FileMd5 函数用于获取文件的MD5哈希值
// 作为GetFileMd5的别名，保持向后兼容
func FileMd5(filepath string) (string, error) {
	return GetFileMd5(filepath)
}

func Str2Uint(str string) uint {
	i, e := strconv.Atoi(str)
	if e != nil {
		return 0
	}
	return uint(i)
}

func Copy(src, dst string) (int64, error) {
	sourceFileStat, err := os.Stat(src)
	if err != nil {
		return 0, err
	}

	if !sourceFileStat.Mode().IsRegular() {
		return 0, fmt.Errorf("%s is not a regular file", src)
	}

	source, err := os.Open(src)
	if err != nil {
		return 0, err
	}
	defer source.Close()

	destination, err := os.Create(dst)
	if err != nil {
		return 0, err
	}
	defer destination.Close()
	nBytes, err := io.Copy(destination, source)
	return nBytes, err
}

// CopyFileWithBuffer 复制源文件到目标文件，使用缓冲区以减少内存使用
func CopyFileWithBuffer(src, dst string) (int64, error) {
	sourceFileStat, err := os.Stat(src)
	if err != nil {
		return 0, err
	}

	if !sourceFileStat.Mode().IsRegular() {
		return 0, fmt.Errorf("%s is not a regular file", src)
	}

	sourceFile, err := os.Open(src)
	if err != nil {
		return 0, err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return 0, err
	}
	defer destFile.Close()

	buf := make([]byte, 1024*1024) // 1MB 缓冲区
	var totalBytesCopied int64
	for {
		n, err := sourceFile.Read(buf)
		if err != nil && err != io.EOF {
			return totalBytesCopied, err
		}
		if n == 0 {
			break
		}

		nWritten, err := destFile.Write(buf[:n])
		if err != nil {
			return totalBytesCopied, err
		}
		totalBytesCopied += int64(nWritten)
	}

	return totalBytesCopied, nil
}

func HasPrefix(s, prefix string) bool {
	return len(s) >= len(prefix) && s[0:len(prefix)] == prefix
}

func HasSuffix(s, suffix string) bool {
	return len(s) >= len(suffix) && s[len(s)-len(suffix):] == suffix
}

func ConvertStr2GBK(str string) string {
	//将utf-8编码的字符串转换为GBK编码
	ret, _ := simplifiedchinese.GBK.NewEncoder().String(str)
	return ret //如果转换失败返回空字符串
}

func ConvertGBK2Str(gbkStr string) string {
	//将GBK编码的字符串转换为utf-8编码
	ret, _ := simplifiedchinese.GBK.NewDecoder().String(gbkStr)
	return ret //如果转换失败返回空字符串
}

func FloatRound(f float64, n int) float64 {
	format := "%." + strconv.Itoa(n) + "f"
	res, _ := strconv.ParseFloat(fmt.Sprintf(format, f), 64)
	return res
}

func GetAllFile(pathname string, s []string) ([]string, error) {
	fromSlash := filepath.FromSlash(pathname)
	//fmt.Println(fromSlash)
	rd, err := ioutil.ReadDir(fromSlash)
	if err != nil {
		//log.LOGGER("Error").Error("read dir fail %v\n", err)
		return s, err
	}
	for _, fi := range rd {
		if fi.IsDir() {
			fullDir := filepath.Join(fromSlash, fi.Name())
			s, err = GetAllFile(fullDir, s)
			if err != nil {
				fmt.Println("read dir fail:", err)
				continue
			}
		} else {
			fullName := filepath.Join(fromSlash, fi.Name())
			s = append(s, fullName)
		}
	}
	return s, nil
}

func GetAllDir(pathname string, s []string) ([]string, error) {
	fromSlash := filepath.FromSlash(pathname)
	//fmt.Println(fromSlash)
	rd, err := ioutil.ReadDir(fromSlash)
	if err != nil {
		//log.LOGGER("Error").Error("read dir fail %v\n", err)
		fmt.Println("read dir fail:", err)
		return s, err
	}
	for _, fi := range rd {
		if fi.IsDir() {
			fullDir := filepath.Join(fromSlash, fi.Name())
			s = append(s, fullDir)
			s, err = GetAllDir(fullDir, s)
			if err != nil {
				fmt.Println("read dir fail:", err)
				//log.LOGGER("Error").Error("read dir fail: %v\n", err)
				continue
			}
		}
	}
	return s, nil
}

func BytesToHexString(src []byte) string {
	res := bytes.Buffer{}
	if src == nil || len(src) <= 0 {
		return ""
	}
	temp := make([]byte, 0)
	for _, v := range src {
		sub := v & 0xFF
		hv := hex.EncodeToString(append(temp, sub))
		if len(hv) < 2 {
			res.WriteString(strconv.FormatInt(int64(0), 10))
		}
		res.WriteString(hv)
	}
	return res.String()
}

func GetFileHeader(fp string) (string, error) {
	file, _ := os.Open(fp)
	defer file.Close()

	// 缓冲区
	buf := make([]byte, 16)
	// 存放文件所有内容
	var bytes []byte

	// 每次读取一行放入缓冲区
	count, err := file.Read(buf)
	bytes = append(bytes, buf[:count]...)
	return BytesToHexString(bytes), err
}

func GetTimeRange(maxModifyDate ...string) []string {
	modifyDateArray := []string{}
	var now, last time.Time
	var err error
	now = time.Now()
	if len(maxModifyDate) == 0 {
		last = now.AddDate(0, 0, -7)
	} else {
		last, err = time.Parse("2006-01-02 15:04:05", maxModifyDate[0])
		logging.ErrorLogger.Errorf("parser last time err", err.Error())
		return nil
	}

	startTimeStr := fmt.Sprintf("%s %s", last.Format("2006-01-02"), "00:00:00")

	for startTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr); startTime.Before(now); startTime = startTime.Local().Add(time.Duration(1 * time.Hour)) {
		if err != nil {
			logging.ErrorLogger.Errorf("parser start time err", err.Error())
			return nil
		}
		modifyDateArray = append(modifyDateArray, startTime.Format("2006-01-02 15:04:05"))
	}
	return modifyDateArray
}

func GetDateRange(start int, maxModifyDate ...string) []string {
	modifyDateArray := []string{}
	var end, last time.Time
	var err error
	end = time.Now().AddDate(0, 0, 1)
	if len(maxModifyDate) == 0 {
		last = end.AddDate(0, 0, start)
	} else {
		last, err = time.Parse("2006-01-02", maxModifyDate[0])
		if err != nil {
			logging.ErrorLogger.Errorf("parser last time err", err.Error())
			return nil
		}
		last = last.AddDate(0, 0, -1)
	}
	for startTime := last; startTime.Sub(end) <= 0; startTime = startTime.AddDate(0, 0, 1) {
		modifyDateArray = append(modifyDateArray, startTime.Format("2006-01-02"))
	}
	return modifyDateArray
}

func ExecCommand(command string) (string, error) {
	if command == "" {
		return "command is nil", errors.New("command is nil")
	}

	cmd := exec.Command("bash", "-c", command)
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr
	err := cmd.Run()
	if err != nil {
		logging.ErrorLogger.Errorf("run script err, ", command, stdout.String(), stderr.String())
		return stdout.String() + "\n" + stderr.String(), err
	}
	return stdout.String(), nil
}

func RandString(n int) (ret string) {
	rand.Seed(time.Now().UnixNano())
	allString := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	ret = ""
	for i := 0; i < n; i++ {
		r := rand.Intn(len(allString))
		ret = ret + allString[r:r+1]
	}
	return
}

// getFileSize get file size by path(B)
func DirSize(path string) (int64, error) {
	var size int64
	err := filepath.Walk(path, func(_ string, info os.FileInfo, err error) error {
		if !info.IsDir() {
			size += info.Size()
		}
		return err
	})
	return size, err
}

// getFileSize get file size by path(B)
func getFileSize(path string) int64 {
	if !exists(path) {
		return 0
	}
	fileInfo, err := os.Stat(path)
	if err != nil {
		return 0
	}
	return fileInfo.Size()
}

// exists Whether the path exists
func exists(path string) bool {
	_, err := os.Stat(path)
	return err == nil || os.IsExist(err)
}

var SourceMap = map[uint]string{
	1: "普通编译",
	2: "每日编译",
	3: "生测编译",
}

func RandUint(n int32) uint {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	return uint(r.Int31n(n))
}

func PickProjectFromBuildInfo(line string) string {
	re := regexp.MustCompile(`ssh://.*?/(.*?\.git:)`)

	// 查找匹配项
	matches := re.FindStringSubmatch(line)

	// 检查是否匹配成功
	if len(matches) > 1 {
		// matches[1] 是提取到的部分
		return matches[1][:len(matches[1])-5]
	}
	return ""
}

func PickProjectFromFeeds(line string) string {
	re := regexp.MustCompile(`ssh://.*?/(.*?\.git;)`)

	// 查找匹配项
	matches := re.FindStringSubmatch(line)

	// 检查是否匹配成功
	if len(matches) > 1 {
		// matches[1] 是提取到的部分
		return matches[1][:len(matches[1])-5]
	}
	return ""
}
