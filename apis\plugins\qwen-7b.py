import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, Trainer, TrainingArguments
from datasets import load_dataset

# 自定义数据整理器
class CustomDataCollator:
    def __init__(self, tokenizer, pad_token_id=2):
        self.tokenizer = tokenizer
        self.pad_token_id = pad_token_id  # 手动指定 # 的 ID 为 2

    def __call__(self, features):
        # 提取 input_ids, attention_mask 和 labels
        input_ids = [f["input_ids"] for f in features]
        attention_mask = [f["attention_mask"] for f in features]
        labels = [f["labels"] for f in features]

        # 找到批次中 input_ids 和 labels 的最大长度
        max_input_length = max(len(ids) for ids in input_ids)
        max_labels_length = max(len(l) for l in labels)
        max_length = max(max_input_length, max_labels_length)

        # 手动填充
        padded_input_ids = []
        padded_attention_mask = []
        padded_labels = []
        for i in range(len(features)):
            # 调试：打印原始长度
            print(f"Sample {i}: input_ids len={len(input_ids[i])}, labels len={len(labels[i])}")
            
            # 填充 input_ids
            input_padding_length = max_length - len(input_ids[i])
            padded_input_ids.append(input_ids[i] + [self.pad_token_id] * input_padding_length)
            # 填充 attention_mask
            padded_attention_mask.append(attention_mask[i] + [0] * input_padding_length)
            # 填充 labels（用 -100 忽略填充部分的损失计算）
            labels_padding_length = max_length - len(labels[i])
            padded_labels.append(labels[i] + [-100] * labels_padding_length)

            # 调试：打印填充后长度
            print(f"Sample {i} after padding: input_ids len={len(padded_input_ids[-1])}, labels len={len(padded_labels[-1])}")

        # 转换为张量
        batch = {
            "input_ids": torch.tensor(padded_input_ids),
            "attention_mask": torch.tensor(padded_attention_mask),
            "labels": torch.tensor(padded_labels),
        }
        return batch

# **1. 加载 tokenizer 和 Qwen-7B 模型**
model_path = "/app/Qwen-7B"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)

# 检查词汇表（仅用于调试）
vocab = tokenizer.get_vocab()
print("Vocabulary sample:", list(vocab.keys())[:20])

model = AutoModelForCausalLM.from_pretrained(model_path, torch_dtype=torch.bfloat16, device_map="auto")

# **2. 加载数据集**
dataset = load_dataset("csv", data_files="/app/config_data.csv")
train_test_split = dataset["train"].train_test_split(test_size=0.1)

# **3. 数据预处理（移除 padding）**
def preprocess_function(examples):
    prompts = ["Convert Firewall Config:\n" + source for source in examples["source"]]
    targets = examples["target"]
    
    # Tokenize 输入和输出，不使用 padding
    input_enc = tokenizer(prompts, max_length=512, truncation=True)
    target_enc = tokenizer(targets, max_length=512, truncation=True)

    # 准备 labels，使用 # 的 ID（2）作为 EOS
    eos_id = 2  # 直接使用 # 的 ID
    labels = [ids + [eos_id] for ids in target_enc["input_ids"]]

    return {
        "input_ids": input_enc["input_ids"],
        "attention_mask": input_enc["attention_mask"],
        "labels": labels
    }

# **4. Tokenize 数据集**
tokenized_datasets = {
    "train": train_test_split["train"].map(preprocess_function, batched=True, remove_columns=["source", "target"]),
    "validation": train_test_split["test"].map(preprocess_function, batched=True, remove_columns=["source", "target"])
}

# **5. 训练参数**
training_args = TrainingArguments(
    output_dir="./qwen_model",
    num_train_epochs=5,
    per_device_train_batch_size=2,
    per_device_eval_batch_size=2,
    save_steps=500,
    save_total_limit=2,
    logging_dir="./logs",
    evaluation_strategy="epoch",
    save_strategy="epoch",
    logging_steps=5,
    learning_rate=2e-5,
    bf16=True,
    gradient_checkpointing=True,
    gradient_accumulation_steps=4,
    optim="adamw_bnb_8bit",
    report_to="none",
    dataloader_drop_last=True
)

# **6. 使用自定义 DataCollator 并训练**
data_collator = CustomDataCollator(tokenizer, pad_token_id=2)
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=tokenized_datasets["train"],
    eval_dataset=tokenized_datasets["validation"],
    data_collator=data_collator
)

trainer.train()
trainer.save_model("./qwen_model")
tokenizer.save_pretrained("./qwen_model")