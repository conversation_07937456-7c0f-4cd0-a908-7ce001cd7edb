module ntos-l2tp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:l2tp";
  prefix ntos-l2tp;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-ppp {
    prefix ntos-ppp;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS l2tp module.";

  revision 2025-02-19 {
    description
      "Initial version.";
    reference
      "";
  }

  grouping vrf {
    leaf vrf {
      description
        "VRF name.";

      type string;
      default "main";
    }
  }

  typedef l2tp-mode {
    type enumeration {
      enum lns {
        description
          "LNS.";
      }
      enum lac {
        description
          "LAC.";
      }
    }
  }

  typedef tunnel-mode {
    type enumeration {
      enum up {
        description
          "UP.";
      }
      enum down {
        description
          "DOWN.";
      }
    }
  }

  typedef access-mode {
    type enumeration {
      enum nat {
        description
          "NAT.";
      }
      enum route {
        description
          "Route.";
      }
    }
  }

  grouping user-unit {
    leaf user-name {
      type string {
        length "1..127";
      }
      description
        "User name.";
    }
    leaf user-ip {
      type ntos-inet:ip-address;
      description
        "User ip.";
    }
    leaf insert-ip {
      type ntos-inet:ip-address;
      description
        "Insert ip.";
    }
    leaf access-ip {
      type ntos-inet:ip-address;
      description
        "Access ip.";
    }
    leaf host-name {
      type string {
        length "1..127";
      }
      description
        "Host name.";
    }
    leaf insert-time {
      type uint64;
      description
        "insert time.";
    }
    leaf online-time {
      type uint64;
      description
        "Online time.";
    }
    leaf interface-name {
      type string {
        length "1..127";
      }
      description
        "Interface name.";
    }
  }

  grouping l2tp-status-unit {
    leaf index {
      type uint32;
      description
        "L2TP index.";
    }
    leaf tunnel-id {
      type uint32;
      description
        "L2TP tunnel id.";
    }
    leaf session-id {
      type uint32;
      description
        "L2TP session id.";
    }
    leaf tunnel-status {
      type uint32;
      description
        "Status of tunnel.";
    }
    leaf session-status {
      type uint32;
      description
        "Status of session.";
    }
    leaf flow-statistics {
      type uint32;
      description
        "Statistics of flow.";
    }
    uses user-unit;
  }

  grouping l2tp-auth-unit {
    leaf tunnel-auth {
      type boolean;
      default "false";
      description
        "L2TP tunnel authentication enabled status.";
    }
    leaf tunnel-auth-password {
      type string {
        length "1..256";
      }
      description
        "L2TP tunnel auth password.";
    }
  }

  grouping l2tp-protocol-cfg-unit {
    leaf protocol-ver {
      type string{
        length "1..127";
      }
      description
        "L2TP protocol version, default support V2";
    }
    leaf hidden-avp {
      type boolean;
      description
        "L2TP Hidden avp or not.";
    }
    leaf hello-time-interval {
      type uint32;
      description
        "L2TP hello time interval.";
    }
    leaf retry-time-interval {
      type uint32{
        range "1..5";
      }
      default "1";
      description
        "L2TP retry time interval.";
    }
    leaf negotiation-timeout {
      type uint32;
      default 60;
      description
        "L2TP negotiation maximum timeout in seconds.";
    }
    leaf recv-window-size {
      type uint32;
      default 4;
      description
        "L2TP receive window size.";
    }
    leaf l2tp-echo-interval {
      type uint32;
      description
        "L2TP echo interval.";
    }
    leaf hostname {
      type string{
        length "1..127";
      }
      description
        "L2TP hostname";
    }
  }

  grouping l2tp-base-unit {
    leaf max-conn {
      type uint32;
      default 15;
      description
        "L2TP max new connect count.";
    }
    leaf access-work-mode {
      type access-mode;
      description
        "L2TP access work mode.";
    }
    leaf ppp-reference {
      type string {
        length "1..127";
      }
      description
        "Name of ppp reference.";
    }

    uses l2tp-auth-unit;
    uses l2tp-protocol-cfg-unit;
    uses l2tp-ipsec-unit;
  }

  grouping l2tp-ipsec-unit {
    leaf over-ipsec {
      type boolean;
      default "false";
      description
        "L2TP over-ipsec.";
    }
    leaf ipsec-reference {
      type string {
        length "1..127";
      }
      description
        "L2TP ipsec reference name.";
    }
    leaf ipsec-vti {
      type string {
        length "1..127";
      }
      description
        "L2TP ipsec vti name.";
    }
    choice local-address {
        description
            "Select either an interface or an IP address as the local address.";

        case ipsec-ip-address {
            leaf ipsec-ip-address {
                type ntos-inet:ip-address;
                description
                    "L2TP ipsec ip address to be used as the local address.";
            }
        }

        case ipsec-interface {
            leaf ipsec-interface-name {
                description
                    "L2TP ipsec interface name to be used as the  local address.";

                type ntos-types:ifname;
                ntos-ext:nc-cli-completion-xpath "/ntos:config/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
            }
        }
    }
  }

  grouping l2tp-lns-unit {
    leaf force-lcp-renegotiation {
      type boolean;
      default "false";
      description "Indicates whether to force LCP renegotiation.";
    }

    leaf force-chap-auth {
      type boolean;
      default "false";
      description "Indicates whether to force this segment's CHAP authentication.";
    }

    leaf allow-access {
      type boolean;
      default "false";
      description "L2TP allows headquarters to access the branch intranet.";
    }

    list access-ip-prefix {
      key "id";
      description
        "L2TP lns access ip prefix.";
      leaf id {
        type uint32;
        description
          "Access ip id.";
      }
      leaf branch-ip {
        type ntos-inet:ip-address;
        description
          "Branch ip.";
      }
      leaf ip-prefix {
        type ntos-inet:ipv4-prefix;
        description
          "L2TP Accessible IP address prefix in CIDR notation.
        Example: ***********/24.";
      }
    }
  }

  grouping l2tp-lac-unit {
    leaf ppp-unit-id {
      type uint32;
      description
        "PPP interface name id.";
    }
    leaf remote-server-address {
      type union {
        type ntos-inet:ip-address;
        type ntos-inet:domain-name;
      }
      description
        "L2TP LAC mode remote server address for LNS.";
    }

    leaf-list access-ip-prefix {
      type ntos-inet:ipv4-prefix;
      description
        "L2TP Accessible IP address prefix in CIDR notation.
        Example: ***********/24.";
    }
  }

  grouping l2tp-unit {
    leaf name {
      type string {
        length "1..127";
      }
      description
        "L2TP name.";
    }
    leaf enabled {
      type boolean;
      description
        "Enable of L2TP.";
    }

    choice l2tp-type {
      description
        "L2TP type.";
      case lac {
        container lac {
          uses l2tp-lac-unit;
          description
            "L2TP work mode for LAC (L2TP Access Concentrator).";
        }
      }
      case lns {
        container lns {
          uses l2tp-lns-unit;
          description
            "L2TP work mode for LNS (L2TP Network Server).";
        }
      }
    }
    uses l2tp-base-unit;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "The configuration of l2tp.";
    container l2tp {
        list l2tp-list {
            key "name";
            uses l2tp-unit;
        }
    }
  }

  rpc l2tp-security-policy {
    description
      "Set securiy policy for l2tp in firewall series product";
    input {
      uses vrf;
      leaf operate {
        type enumeration {
          enum check {
            description
              "Check depends security policy exists";
          }
          enum recover {
            description
              "Recover depends security policy";
          }
          enum create {
            description
              "Create depends security policy";
          }
          enum delete {
            description
              "Delete depends security policy";
          }
          enum update {
            description
              "update depends security policy";
          }
        }
      }
      leaf l2tp-name {
        type string {
          length "1..127";
        }
      }
    }

    output {
      leaf result {
        type int32;
      }
      leaf message {
        type string {
          length "0..254";
        }
      }
    }
    ntos-ext:nc-cli-cmd "l2tp security-policy";
  }

  rpc l2tp-config-show {
    description
      "Get L2TP configuration";
    input {
      uses vrf;
    }
    output {
      container l2tp {
        leaf total {
          type uint32;
        }
        list l2tp-list {
            key "name";
            uses l2tp-unit;
            list security-policy {
              key "name";
              leaf name {
                type string {
                  length "1..127";
                }
                description
                  "Security policy name.";
              }
              leaf status {
                type boolean;
                description
                  "Security policy status.";
              }
            }
            container ppp {
              uses ntos-ppp:ppp-unit;
            }
        }
      }
    }
    ntos-ext:nc-cli-show "l2tp";
  }

  rpc get-l2tp-status {
    description
      "Get L2TP status";
    input {
      uses vrf;
      leaf start {
        type uint32;
      }
      leaf end {
        type uint32;
      }
      leaf filter {
        type string;
      }
    }
    output {
      container l2tp {
        leaf total {
          type uint32;
        }
        list session-list {
          key "index";
          uses l2tp-status-unit;
        }
      }
    }
    ntos-ext:nc-cli-cmd "l2tp status";
  }

  rpc l2tp-up-down-mutil {
    description
      "Up or down L2TP tunnel mutil";
    input {
      uses vrf;
      list session-list {
        key "index";
        leaf index {
          type uint32;
          description
            "L2TP index.";
        }
        leaf tunnel-id {
          type uint32;
          description
            "L2TP session id.";
        }
        leaf session-id {
          type uint32;
          description
            "L2TP session id.";
        }
      }
      leaf status {
        type tunnel-mode;
        description
          "Up or down to control tunnel.";
      }
    }
    ntos-ext:nc-cli-cmd "l2tp up-down-multi";
  }

  rpc l2tp-up-down-all {
    description
      "Up or down L2TP tunnel all";
    input {
      uses vrf;
      leaf status {
        type tunnel-mode;
        description
          "Up or down to control tunnel.";
      }
    }
    ntos-ext:nc-cli-cmd "l2tp up-down-all";
  }

  rpc l2tp-set-log {
    description
      "L2TP set log level";
    input {
      uses vrf;
      leaf submodule {
        type string {
          length "1..127";
        }
      }
      leaf level {
        type ntos-types:log-level;
      }
    }
    ntos-ext:nc-cli-cmd "l2tp set";
  }

  identity vpdn {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Vpdn service.";
  }

  identity l2tp {
    base ntos-types:SERVICE_LOG_ID;
    description
      "L2TP service.";
  }
}