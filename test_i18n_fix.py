#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试国际化修复效果的脚本
"""

import sys
import os

# 添加engine目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'engine'))

def test_language_parameter_fix():
    """测试语言参数修复效果"""
    print("🔧 测试语言参数修复效果")
    print("=" * 60)
    
    try:
        # 导入相关模块
        from engine.utils.i18n import init_i18n, _
        from engine.utils.logger import log, setup_logging
        
        # 设置日志系统
        setup_logging(log_to_stdout=True)
        
        # 初始化国际化
        init_i18n('zh-CN')
        
        print("✅ 模块导入成功")
        
        # 测试1: 正常的翻译调用（应该不产生警告）
        print("\n📋 测试1: 正常翻译调用")
        try:
            result = _("info.tool_started", mode="test", vendor="fortigate", language="zh-CN")
            print(f"   ✅ 翻译结果: {result}")
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
        
        # 测试2: 缺少language参数的调用（应该使用默认值）
        print("\n📋 测试2: 缺少language参数")
        try:
            result = _("info.tool_started", mode="test", vendor="fortigate")
            print(f"   ✅ 翻译结果: {result}")
        except Exception as e:
            print(f"   ❌ 翻译失败: {e}")
        
        # 测试3: 使用log函数（模拟main.py中的调用）
        print("\n📋 测试3: 使用log函数")
        try:
            log(_("info.tool_started"), "info", mode="test", vendor="fortigate", language="zh-CN")
            print("   ✅ log函数调用成功")
        except Exception as e:
            print(f"   ❌ log函数调用失败: {e}")
        
        # 测试4: 缺少参数的log调用
        print("\n📋 测试4: 缺少参数的log调用")
        try:
            log(_("info.tool_started"), "info", mode="test", vendor="fortigate")
            print("   ✅ 缺少参数的log调用成功（应该使用默认值）")
        except Exception as e:
            print(f"   ❌ 缺少参数的log调用失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_xml_namespace_translation():
    """测试XML命名空间相关翻译"""
    print("\n🔧 测试XML命名空间翻译")
    print("=" * 60)
    
    try:
        from engine.utils.i18n import _
        
        # 测试命名空间相关翻译
        test_cases = [
            ("format.namespace_urn", {"urn": "urn:ruijie:ntos"}),
            ("format.xml_namespace", {"namespace": "urn:ruijie:ntos"}),
        ]
        
        for key, params in test_cases:
            try:
                result = _(key, **params)
                print(f"   ✅ {key}: {result}")
            except Exception as e:
                print(f"   ❌ {key}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ XML命名空间翻译测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 国际化修复效果测试")
    print("=" * 80)
    
    # 运行测试
    test1_result = test_language_parameter_fix()
    test2_result = test_xml_namespace_translation()
    
    # 总结结果
    print("\n📊 测试结果总结")
    print("=" * 60)
    print(f"语言参数修复测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"XML命名空间翻译测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！修复效果良好。")
        return 0
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
