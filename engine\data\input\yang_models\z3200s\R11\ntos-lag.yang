module ntos-lag {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:lag";
  prefix ntos-lag;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-qos {
    prefix ntos-qos;
  }
  import ntos-routing {
    prefix ntos-routing;
  }
  import ntos-dhcp-snooping {
    prefix ntos-dhcp-snp;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS Link AGgregation.";

  revision 2022-11-08 {
    description
      "Add new features for NTOS.";
    reference "";
  }

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity bond {
    base ntos-types:INTERFACE_TYPE;
    description
      "LAG interface.";
  }

  identity bond-slave {
    base ntos-types:INTERFACE_TYPE;
    description
      "LAG slave interface.";
  }

  typedef lag-mode-config {
    type enumeration {
      enum round-robin {
        description
          "Outgoing traffic is distributed sequentially on each slave.";
      }
      enum xor {
        description
          "Outgoing traffic is distributed according to a configurable policy
           (see policy for details).";
      }
      enum active-backup {
        description
          "Only one link in the link aggregation will be used at a time.";
      }
      enum lacp {
        description
          "Full LACP support.";
      }
    }
    description
      "LAG mode for outgoing traffic.";
  }

  typedef lag-mode-state-only {
    type enumeration {
      enum broadcast {
        description
          "Outgoing traffic is broadcast on all links.";
      }
      enum tlb {
        description
          "Adaptive transmit load balancing. Outgoing traffic is distributed
           according to the current load on each slave.";
      }
      enum alb {
        description
          "Adaptive load balancing. Outgoing traffic is distributed according
           to the current load on each slave, incoming traffic is balancing by
           ARP resolution.";
      }
      enum unknown {
        description
          "Unknown mode.";
      }
    }
    description
      "Additional LAG mode for outgoing traffic.";
  }

  typedef lag-policy {
    type enumeration {
      enum layer2 {
        description
          "Hash L2 headers.";
      }
      enum layer2+3 {
        description
          "Hash L2 and L3 headers.";
      }
      enum layer3+4 {
        description
          "Hash L3 and L4 headers.";
      }
      enum encap2+3 {
        description
          "Hash most inner L2 and L3 headers.";
      }
      enum encap3+4 {
        description
          "Hash most inner L3 and L4 headers.";
      }
    }
    description
      "LAG xmit hash policy.";
  }

  typedef lag-policy-state-only {
    type enumeration {
      enum unknown {
        description
          "Unknown policy.";
      }
    }
    description
      "Additional LAG xmit hash policy.";
  }

  typedef rate {
    type enumeration {
      enum slow {
        description
          "In lacp mode, transmit LACPDU packets every 30 seconds.";
      }
      enum fast {
        description
          "In lacp mode, transmit LACPDU packets every seconds.";
      }
    }
    description
      "LACP rate transmission.";
  }

  typedef rate-state-only {
    type enumeration {
      enum unknown {
        description
          "Unknown rate.";
      }
    }
    description
      "Additional LAG rate transmission.";
  }

  grouping lag-config {
    description
      "Configuration for LAG.";

    leaf mode {
      type lag-mode-config;
      must "(. != 'lacp' and . != 'xor') or ../xmit-hash-policy" {
        error-message "xmit-hash-policy must be defined in 'lacp' or 'xor' mode";
      }
      must ". != 'lacp' or ../lacp-rate" {
        error-message "lacp-rate must be defined in 'lacp' mode";
      }
      mandatory true;
      description
        "LAG mode.";
    }

    leaf xmit-hash-policy {
      type lag-policy;
      must "../mode = 'xor' or ../mode = 'lacp'" {
        error-message "xmit-hash-policy has meanings only for 'lacp' or 'xor' mode";
      }
      description
        "LAG xmit hash policy to use for slave selection in xor or lacp modes.";
    }

    leaf lacp-rate {
      type rate;
      must "../mode = 'lacp'" {
        error-message "lacp-rate has meanings only for 'lacp' mode";
      }
      description
        "LACP rate transmission.";
    }

    leaf actor-sys-prio {
      type uint16;
      must "../mode = 'lacp'" {
        error-message "actor-sys-prio has meanings only for 'lacp' mode";
      }
      description
        "LACP system priority.";
    }

    leaf up-link-monitoring {
      type uint8;
      default "0";
      must '. <= count(../link-interface/*)' {
        error-message "Cannot set the number over than link-interface";
      }

      description
        "Define the link monitoring for up links the lag keep up is needed.";
    }

    leaf mii-link-monitoring {
      type uint32;
      default "100";
      description
        "Define the MII link monitoring frequency in milliseconds.";
    }

    list link-interface {
      key "slave";
      description
        "Set this interface as slave of this LAG.";
      ntos-extensions:nc-cli-one-liner;

      leaf slave {
        type ntos-types:ifname;
        must '. != ../../name' {
          error-message "Cannot bind our own interface";
        }
        must "count(../../../*[local-name()='bridge']/*[local-name()='link-interface']/*[local-name()='slave'][text()=current()]) = 0" {
          error-message "Cannot bind an interface already bound to a bridge";
        }
        must 'count(../../../lag/link-interface/slave[text()=current()]) = 1' {
          error-message "Cannot bind an interface already bound to another LAG";
        }
        description
          "Set this interface as slave of this LAG.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:config/vrf[ntos:name=string(current()/../../*[local-name()='name'])]/
             ntos-interface:interface/*[.!=current()]/*[local-name()='ethernet']/../*[local-name()='name'] |
           /ntos:state/vrf[ntos:name=string(current()/../../*[local-name()='name'])]/
             ntos-interface:interface/*[.!=current()]/*[local-name()='ethernet']/../*[local-name()='name']";
      }
    }

    container primary {
      when "../mode = 'active-backup'" {
        description
          "This option is available only for the active-backup mode.";
      }
      presence "Enable configuring primary interface.";
      description
        "Configure primary interface for the active-backup mode.";
      ntos-extensions:nc-cli-one-liner;

      leaf interface {
        type ntos-types:ifname;
        mandatory true;
        description
          "Lag primary interface. After recovery, this interface become the
           active interface according to the reselect policy.";
        ntos-extensions:nc-cli-completion-xpath
          "../../slave";
      }

      leaf reselect-policy {
        type enumeration {
          enum always {
            description
              "The primary interface becomes active whenever it comes back up.";
          }
          enum better {
            description
              "The primary interface becomes active when it comes back up, if
               its speed and duplex is better than the speed and duplex of the
               current active slave.";
          }
          enum failure {
            description
              "The primary interface becomes active only if it is up and the
               current active interface fails.";
          }
        }
        default "always";
        description
          "Specifies the reselection policy for the primary interface. This
           affects how the primary interface is chosen to become active when
           failure of the current active interface or recovery of the primary
           interface occurs.";
      }
    }
  }

  grouping lag-state {
    description
      "State for LAG.";

    leaf mode {
      type union {
        type lag-mode-config;
        type lag-mode-state-only;
      }
      mandatory true;
      description
        "LAG mode.";
    }

    leaf xmit-hash-policy {
      type union {
        type lag-policy;
        type lag-policy-state-only;
      }
      description
        "LAG xmit hash policy to use for slave selection in xor or lacp modes.";
    }

    leaf lacp-rate {
      type union {
        type rate;
        type rate-state-only;
      }
      description
        "LACP rate transmission.";
    }

    leaf actor-sys-prio {
      type uint16;
      description
        "LACP system priority.";
    }

    leaf mii-link-monitoring {
      type uint32;
      description
        "Define the MII link monitoring frequency in milliseconds.";
    }

    list link-interface {
      key "slave";
      description
        "Slave interface of this LAG.";
      ntos-extensions:nc-cli-one-liner;

      leaf slave {
        type ntos-types:ifname;
        description
          "Slave interface of this LAG.";
      }

      leaf state {
        type enumeration {
          enum active {
            description
              "This slave is active.";
          }
          enum backup {
            description
              "This slave is backup.";
          }
        }
        description
          "Slave state.";
      }

      leaf link {
        type enumeration {
          enum up {
            description
              "This slave is up.";
          }
          enum going-down {
            description
              "This slave is going down.";
          }
          enum down {
            description
              "This slave is down.";
          }
          enum going-up {
            description
              "This slave is going up.";
          }
          enum unknown {
            description
              "Unknown status.";
          }
        }
        description
          "Slave MII link monitoring status.";
      }

      leaf failure-count {
        type uint32;
        description
          "Slave failure count.";
      }
    }

    container primary {
      presence "State of the primary interface.";
      description
        "State of the primary interface for the active-backup mode.";
      ntos-extensions:nc-cli-one-liner;

      leaf interface {
        type ntos-types:ifname;
        description
          "Lag primary interface name. After recovery, this interface become the
           active interface according to the reselect policy.";
      }

      leaf reselect-policy {
        type string;
        description
          "The reselection policy for the primary interface. This affects how
           the primary interface is chosen to become active when failure of the
           current active interface or recovery of the primary interface
           occurs.";
      }
    }
  }

  rpc get-bond-slaves {
    input {
      leaf vrf {
        type string;
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf ifname {
        type string;
        description
          "Interface name.";
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-lag:lag/*[local-name()='name']";
        ntos-extensions:nc-cli-no-name;
      }
    }

    output {
      list bond-slaves-info {
        leaf master {
          type string;
        }

        list slaves {
          leaf slave {
            type string;
            description
              "Slave interface of this interface.";
          }
        }
      }
    }
    ntos-extensions:nc-cli-show "lag slaves";
  }

  rpc show-bond-info {
    input {
      leaf vrf {
        type string;
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
        default 'main';
      }

      leaf ifname {
        type string;
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-lag:lag/*[local-name()='name']";
        default '';
      }

      leaf detail {
        type empty;
        description
          "Show detailed information of bonding from procfs.";
      }
    }

    output {
      leaf buffer {
        description
          "Command output buffer since the last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-show "lag info";
  }

  rpc dump-bond-info {
    input {
      leaf vrf {
        type string;
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf ifname {
        type string;
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos-interface:interface/ntos-lag:lag/*[local-name()='name']";
        ntos-extensions:nc-cli-no-name;
      }
    }

    output {
      leaf buffer {
        description
          "Command output buffer since the last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-show "lag dump-info";
  }

  rpc interface-validate-check {
    input {
      leaf vrf {
        type string;
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      list interface {
        key "name";
        leaf name {
          type string;
        }
      }
    }

    output {
      leaf buffer {
        description
          "Command output buffer since the last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-show "lag interface-validate-check";
  }

  rpc lag-delete-validate-check {
    input {
      leaf vrf {
        type string;
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      list interface {
        key "name";
        leaf name {
          type string;
        }
      }
    }

    output {
      leaf buffer {
        description
          "Command output buffer since the last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-show "lag delete-validate-check";
  }

  rpc show-lag-interface-state {
    ntos-extensions:nc-cli-show "lag port state";
    ntos-api:internal;
    description
      "Show interface state.";
    input {
      leaf vrf {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos:name";
        type string;
        default "main";
        description
          "VRF to look into.";
      }
      leaf name {
        ntos-extensions:nc-cli-completion-xpath "/ntos:state/ntos:vrf/ntos-interface:interface/ntos:lag:lag/*[local-name()='name']";
        type ntos-types:ifname;
        description
          "Show interface by this name.";
      }
      leaf start {
        type uint16 {
          range "1..65535";
        }
        description
          "Start interface number.";
      }
      leaf end {
        type uint16 {
          range "1..65535";
        }
        description
          "End interface number.";
      }
      leaf search-name {
        type string {
          length "1..15";
        }
        description
          "Search device with name.";
      }
    }

    output {
      leaf interface-total {
        type uint16 {
          range "0..65535";
        }
        description
          "Interface total number.";
      }
      list interface {
        description
          "Output for interface list";
        uses ntos-interface:interface-state;
        uses ntos-interface:physical-state;
        uses ntos-if:interface-common-state;
        uses ntos-ip:ntos-ipv4-state;
        uses ntos-ip:ntos-ipv6-state;
        uses ntos-interface:interface-bandwidth;
        uses ntos-interface:eth-state {
          augment "ethernet" {
            description
              "Physical ethernet parameters.";
            uses ntos-if:ethernet-transmission-config;
          }
        }
        uses ntos-interface:physical-port-attr;
        uses lag-state;
        leaf host-br {
          type ntos-types:ifname;
          ntos-extensions:nc-cli-one-liner;
          description
            "The bridge interface which the lag interface belongs to.";
        }
        leaf ip-mac-enabled {
          type boolean;
          description
            "The interface IP-MAC binding state.";
        }
      }
    }
  }

  rpc show-lag-mac {
    input {
      leaf vrf {
        type string;
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf interface {
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/ntos:lag:lag/*[local-name()='name']";
        type ntos-types:ifname;
      }
    }

    output {
      leaf buffer {
        description
          "Command output buffer since the last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-show "lag mac";
  }

  rpc delete-lag-mac {
    input {
      leaf vrf {
        type string;
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf interface {
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos-interface:interface/ntos:lag:lag/*[local-name()='name']";
        type ntos-types:ifname;
      }
    }

    output {
      leaf buffer {
        description
          "Command output buffer since the last request.";

        type string;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-command-no-pager;
    ntos-extensions:nc-cli-cmd "lag delete-mac";
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface" {
    description
      "Network LAG configuration.";

    list lag {
      key "name";
      description
        "The list of LAG interfaces on the device.";
      uses ntos-interface:nonphy-interface-config {
        refine mtu {
          must 'current() >= 1280 and current() <= 1600' {
            error-message "MTU must be >= 1280 and <= 1600.";
          }
          description
            "Set the max transmission unit size in octets, range: 1280..1600.";
        }
      }
      uses ntos-interface:eth-config;
      uses ntos-interface:physical-config;
      uses ntos-interface:interface-bandwidth;
      uses ntos-ip:ntos-ipv4-config;
      uses ntos-ip:ntos-ipv6-config;
      uses ntos-ip:ntos-network-stack-parameters;
      uses ntos-routing:reverse-path;
      uses ntos-interface:network-access-attr;
      uses lag-config;
      uses ntos-qos:logical-if-qos-config;
      uses ntos-dhcp-snp:dhcp-snp-parameters;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network LAG operational state data.";

    list lag {
      key "name";
      description
        "The list of LAG interfaces on the device.";
      uses ntos-interface:interface-state;
      uses ntos-interface:physical-state;
      uses ntos-interface:interface-bandwidth;
      uses ntos-interface:eth-state {
        augment "ethernet" {
          description
            "Physical ethernet parameters.";
          uses ntos-if:ethernet-transmission-config;
        }
      }
      uses ntos-ip:ntos-ipv4-state;
      uses ntos-ip:ntos-ipv6-state;
      uses ntos-ip:ntos-network-stack-parameters;
      uses lag-state;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
      uses ntos-qos:logical-if-qos-state;
      uses ntos-dhcp-snp:dhcp-snp-parameters;
    }
  }
}
