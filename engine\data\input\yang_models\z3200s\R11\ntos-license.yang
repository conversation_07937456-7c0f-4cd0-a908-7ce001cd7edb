module ntos-license {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:license";
  prefix ntos-license;

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-api {
    prefix ntos-api;
  }

  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS license management module.";

  revision 2023-05-09 {
    description
      "Add show license unbind rpc.";
    reference
      "";
  }

  revision 2021-12-10 {
    description
      "Initial version.";
    reference
      "";
  }

  identity license {
    base ntos-types:SERVICE_LOG_ID;
    description
      "License service.";
  }

  rpc license-activate {
    description
      "Activate the license by a local license file.";
    input {
      leaf file {
        type union {
          type enumeration {
            enum flash: {
              description
                "Device name";
            }
          }
          type string;
        }
        ntos-ext:nc-cli-no-name;
        mandatory true;
        description
          "The path of license file";
      }
    }
    output {
      list service {
        key file;
        leaf file {
          type string;
          description
            "The file name of license.";
        }
        leaf result {
          type string;
          description
            "The result of activating the license.";
        }
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "license activate";
  }

  rpc license-remote-activate {
    description
      "Activate the license by a remote license file.";
    input {
      leaf url {
        type string;
        ntos-ext:nc-cli-no-name;
        mandatory true;
        description
          "The URL of license file.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "license remote activate";
  }

  rpc show-license-information {
    description
      "Show the detail license information.";
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "license information";
    ntos-api:internal;
  }

  rpc show-license-sn {
    description
      "Show the active license's serial number of device.";
    output {
      list license-sn {
        leaf sn {
          type string;
          description
            "The serial number of license.";
        }
        leaf error {
          type string;
          description
            "The command output error buffer.";
        }
      }
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
    ntos-ext:nc-cli-show "license sn";
    ntos-api:internal;
  }

  rpc show-license-capacity {
    description
      "Show the capacity information of each license.";
    output {
      list service {
        leaf id {
          type string;
          description
            "The service id of license.";
        }
        leaf name {
          type string;
          description
            "The service name of license.";
        }
        leaf desc {
          type string;
          description
            "The service description of license.";
        }
        leaf basic-bandwidth {
          type uint32;
          description
            "The basic bandwidth of device.";
        }
        leaf maximum-bandwidth {
          type uint32;
          description
            "The maximum bandwidth of device.";
        }
        leaf unit-bandwidth {
          type uint32;
          description
            "The unit bandwidth of device.";
        }
        leaf error {
          type string;
          description
            "The command output error buffer.";
        }
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "license capacity";
    ntos-api:internal;
  }

  rpc show-license {
    description
      "Show the information of license.";
    input {
      leaf lic-key {
        type string;
        default "all";
        ntos-ext:nc-cli-no-name;
        mandatory false;
        description
          "The service name or id of license. By default, show all active license information.";
      }
    }
    output {
      list service {
        leaf id {
          type string;
          description
            "The feature id of license.";
        }
        leaf name {
          type string;
          description
            "The feature name of license.";
        }
        leaf status {
          type string;
          description
            "The feature status of license.";
        }
        leaf type {
          type string;
          description
            "The feature type of license.";
        }
        leaf active-num {
          type uint32;
          description
            "The number of active licenses.";
        }
        leaf time {
          type string;
          description
            "The feature expiration time of license.";
        }
        leaf region {
          type uint32;
          description
            "The region of license.";
        }
        leaf error {
          type string;
          description
            "The command output error buffer.";
        }
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "license";
    ntos-api:internal;
  }

  rpc show-license-unbind {
    description
      "Show the information of license.";
    input {
      leaf lic-key {
        type string;
        default "all";
        ntos-ext:nc-cli-no-name;
        mandatory false;
        description
          "The service name or id of license. By default, show all active license information.";
      }
    }
    output {
      list unbind-info {
        leaf sn {
          type string;
          description
            "The serial number of license.";
        }
        leaf unbind-code {
          type string;
          description
            "The unbind code of license.";
        }
        leaf error {
          type string;
          description
            "The command output error buffer.";
        }
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-show "license unbind";
    ntos-api:internal;
  }

  rpc license-uninstall {
    description
      "Uninstall a license by service id.";
    input {
      leaf service {
        type string;
        ntos-ext:nc-cli-no-name;
        mandatory true;
        description
          "The service id of license.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "license uninstall";
  }

  rpc license-unbind {
    description
      "Unbind a license by SN.";
    input {
      leaf sn {
        type string;
        ntos-ext:nc-cli-no-name;
        mandatory true;
        description
          "The sn id of license.";
      }
    }
    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
    ntos-ext:nc-cli-cmd "license unbind";
  }
}
