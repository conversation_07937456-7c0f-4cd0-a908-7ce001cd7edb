module ntos-time-range {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:time-range";
  prefix ntos-time-range;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-extensions;
  }

  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS time range module.";

  revision 2021-12-20 {
    description
      "Initial version.";
    reference "";
  }

  typedef list-type {
    type enumeration {
      enum all {
        value 0;
        description
          "Contains elements of all.";
      }
      enum period {
        value 1;
        description
          "Contains elements of periodic type.";
      }
      enum once {
        value 2;
        description
          "Contains elements of one time type.";
      }
    }
  }

  typedef day-of-week {
    type enumeration {
      enum sun {
        value 0;
        description
          "Sunday.";
      }
      enum mon {
        value 1;
        description
          "Monday.";
      }
      enum tue {
        value 2;
        description
          "Tuesday.";
      }
      enum wed {
        value 3;
        description
          "Wednesday.";
      }
      enum thu {
        value 4;
        description
          "Thursday.";
      }
      enum fri {
        value 5;
        description
          "Friday.";
      }
      enum sat {
        value 6;
        description
          "Saturday.";
      }
    }
  }

  typedef time-str {
    type string {
      pattern '([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]' {
        error-message "Incorrect time format, expecting: hh:mm:ss.";
      }
      length 8;
    }
  }

  typedef time-date-str {
    type string {
      pattern '(([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]/' +
              '((2[0-1][0-9])[0-9]-((0[1-9]|1[0-2])-' +
              '(0[1-9]|1[0-9]|2[0-8])|(0[13-9]|1[0-2])-' +
              '(29|30)|(0[13578]|1[02])-31)|' +
              '([0-9]{2}(0[48]|[2468][048]|[13579][26])|' +
              '(0[48]|[2468][048]|[13579][26])00)-02-29))|' {
        error-message "Incorrect time/date format, expecting: hh:mm:ss/YYYY-MM-DD and it must be after 2000-01-01.";
      }
      length "0|19";
    }
  }

  grouping policy-id {
    leaf id {
      config false;
      type uint64;
      description
        "Policy id.";
    }
  }

  grouping time-range-config {
    description
      "Configuration data for time range configuration.";

    leaf name {
      type ntos-types:ntos-obj-name-type;
      description
        "The name of time range object.";
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description
        "The description of time range object.";
    }

    leaf start {
      type time-date-str;
      description
        "Specify the start time. Example:'hh:mm:ss/YYYY-MM-DD'.";
    }

    leaf end {
      type time-date-str;
      description
        "Specify the end time. Example:'hh:mm:ss/YYYY-MM-DD'.";
    }

    leaf be-ref {
      config false;
      type boolean;
      default "false";
      description
        "Whether the time range is referenced.";
    }

    leaf active {
      config false;
      type boolean;
      description
        "Whether the time range is activated.";
    }

    leaf policy-total {
      config false;
      type uint32;
      description
        "Total number of policies.";
    }

    list scy-policy {
      key id;
      config false;
      uses policy-id;
    }

    list sl-scy-policy {
      key id;
      config false;
      uses policy-id;
    }

    list flow-ctrl-policy {
      key id;
      config false;
      uses policy-id;
    }

    list snat-policy {
      key id;
      config false;
      uses policy-id;
    }

    list dnat-policy {
      key id;
      config false;
      uses policy-id;
    }

    list anti-ddos-policy {
      key id;
      config false;
      uses policy-id;
    }

    list local-defend-policy {
      key id;
      config false;
      uses policy-id;
    }

    list pbr-policy {
      key id;
      config false;
      uses policy-id;
    }

    list flow-control-policy {
      key id;
      config false;
      uses policy-id;
    }

    list content-audit-policy {
      key id;
      config false;
      uses policy-id;
    }

    list session-limit-policy {
      key id;
      config false;
      uses policy-id;
    }
    
    list port-mapping-policy {
      key id;
      config false;
      uses policy-id;
    }

    list period {
      key "start end";
      description
        "Set the type of the time-block to periodic.";

      leaf start {
        type time-str;
        description
          "Specify the start time. Example:'hh:mm:ss'.";
      }
      leaf end {
        type time-str;
        description
          "Specify the end time. Example:'hh:mm:ss'.";
      }
      list weekday {
        key "key";
        description
          "Day of the week(sun|mon|tue|wed|thu|fri|sat).";
        ntos-extensions:nc-cli-one-liner;

        leaf key {
          type day-of-week;
        }

        min-elements 1;
        max-elements 7;
      }
      max-elements 32;
    }

    list once {
      key "start end";
      description
        "Set the type of the time-block to once-time.";
      ntos-extensions:nc-cli-one-liner;

      leaf start {
        type time-date-str;
        description
          "Specify the start time. Example:'hh:mm:ss/YYYY-MM-DD'.";
      }
      leaf end {
        type time-date-str;
        description
          "Specify the end time. Example:'hh:mm:ss/YYYY-MM-DD'.";
      }
      max-elements 32;
    }
  }

  rpc time-range {
    description
      "Show time range by offset.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        default "main";
        description
          "VRF.";
      }

      leaf type {
        type list-type;
        default all;
        description
          "Time range type.";
      }

      container content {
        presence "Show time range content.";
        leaf start {
          type uint32;
          description
            "Start offset.";
        }
        leaf end {
          type uint32;
          description
            "End offset.";
        }

        leaf filter {
          type ntos-types:ntos-obj-name-type;
        }
      }
    }

    output {
      leaf total {
        type uint32;
        description
          "Total of a type.";
      }
      list range {
        key "name";
        description
          "Time range list.";
        uses time-range-config;
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }

    ntos-extensions:nc-cli-show "time-range";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Time range configuration.";

    container time-range {
      description
        "Configuration of time range.";
      list range {
        key "name";
        description
          "Configuration of time range.";
        uses time-range-config;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Time range state.";
    container time-range {
      description
        "State of time range.";
      list range {
        key "name";
        description
         "State of time range.";
        uses time-range-config;
      }
    }
  }
}
