{"test_scenarios": [{"name": "大型配置测试", "description": "11527行FortiGate配置", "metrics": ["处理时间", "内存使用", "成功率", "错误类型"]}, {"name": "DNS配置测试", "description": "包含DNS配置的转换", "metrics": ["DNS检测准确率", "配置生成正确性"]}, {"name": "服务对象测试", "description": "87个服务对象处理", "metrics": ["处理成功率", "映射准确率", "性能对比"]}, {"name": "接口映射测试", "description": "20个接口的映射处理", "metrics": ["映射准确率", "处理速度", "错误处理"]}], "comparison_framework": {"architectures": ["legacy", "new", "mixed"], "metrics": ["execution_time", "memory_usage", "success_rate", "error_count"], "output_format": "json_report"}}