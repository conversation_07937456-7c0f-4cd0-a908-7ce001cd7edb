module ntos-routing-types {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ntos-routing-types";
  prefix ntos-rt-types;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-routing {
    prefix ntos-rt;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS routing BGP.";

  revision 2019-10-16 {
    description
      "Ipv4-access-list/ipv6-access-list rule with priority.";
    reference "";
  }
  revision 2018-12-19 {
    description
      "Introduce v4/v6 access/prefix lists.";
    reference "";
  }
  revision 2018-10-24 {
    description
      "Initial version.";
    reference "";
  }

  typedef v4-access-list-name {
    type leafref {
      path
        "/ntos:config/ntos-rt:routing/ntos-rt:ipv4-access-list/ntos-rt:name";
    }
    description
      "Access list name.";
    ntos-extensions:nc-cli-shortdesc "<access-list>";
  }

  typedef v6-access-list-name {
    type leafref {
      path
        "/ntos:config/ntos-rt:routing/ntos-rt:ipv6-access-list/ntos-rt:name";
    }
    description
      "Access list name.";
    ntos-extensions:nc-cli-shortdesc "<access-list>";
  }

  typedef route-map-name {
    type leafref {
      path
        "/ntos:config/ntos-rt:routing/ntos-rt:route-map/ntos-rt:name";
    }
    description
      "Route map name.";
    ntos-extensions:nc-cli-shortdesc "<route-map>";
  }

  typedef v4-prefix-list-name {
    type leafref {
      path
        "/ntos:config/ntos-rt:routing/ntos-rt:ipv4-prefix-list/ntos-rt:name";
    }
    description
      "Prefix list name.";
    ntos-extensions:nc-cli-shortdesc "<prefix-list>";
  }

  typedef v6-prefix-list-name {
    type leafref {
      path
        "/ntos:config/ntos-rt:routing/ntos-rt:ipv6-prefix-list/ntos-rt:name";
    }
    description
      "Prefix list name.";
    ntos-extensions:nc-cli-shortdesc "<prefix-list>";
  }
}
