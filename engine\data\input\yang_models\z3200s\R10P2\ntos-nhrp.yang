module ntos-nhrp {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:nhrp";
  prefix ntos-nhrp;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-interface {
    prefix ntos-iface;
  }
  import ntos-gre {
    prefix ntos-gre;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-routing {
    prefix ntos-rt;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE NTOS NHRP.";

  revision 2020-09-25 {
    description
      "Change the index of nhrp4-cache-state and nhrp6-cache-state.";
    reference "";
  }
  revision 2020-08-11 {
    description
      "Replace dmvpn list by connection list in state.
       Rename protection-ipsec-profile to ipsec-profile in config and state.";
    reference "";
  }
  revision 2020-07-16 {
    description
      "Add enabled leaf to state model.";
    reference "";
  }
  revision 2020-05-27 {
    description
      "Map State model Change.";
    reference "";
  }
  revision 2020-03-17 {
    description
      "Initial version.";
    reference "";
  }

  identity nhrp {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Routing NHRP protocol.";
    ntos-extensions:nc-cli-identity-name "routing nhrp";
  }

  identity route-nhrp {
    base ntos-types:ROUTE4_FRR_ID;
    base ntos-types:ROUTE6_FRR_ID;
    description
      "NHRP routes.";
    ntos-extensions:nc-cli-identity-name "nhrp";
  }

  grouping nhrp-common-config {
    description
      "NHRP objects common to configuration and state.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable/disable NHRP logging configuration.";
    }

    leaf hub-mode {
      type boolean;
      default "false";
      description
        "Enable hub-mode by setting up redirect interception.";
    }
  }

  grouping nhrp-router-config {
    description
      "NHRP configuration objects.";
    uses nhrp-common-config;
  }

  grouping nhs-info-state {
    description
      "Common attributes defining NHS entries.";

    leaf interface {
      type string;
      description
        "Interface Name.";
    }

    leaf fqdn {
      type string;
      description
        "Fully Qualified Domain Name of Server.";
    }

    leaf nbma {
      type union {
        type ntos-inet:ipv4-address;
        type enumeration {
          enum local {
            description
              "Local protocol address.";
          }
        }
      }
      description
        "NBMA IPv4 Address.";
    }
  }

  grouping cache-info-state {
    description
      "Common attributes defining cache entries.";

    leaf interface {
      type string;
      description
        "Interface Name.";
    }

    leaf type {
      type enumeration {
        enum local {
          description
            "Cache type is local.";
        }
        enum static {
          description
            "Cache type is statically defined with maps.";
        }
        enum dynamic {
          description
            "Cache type is dynamically created with remote NBMA.";
        }
        enum nhs {
          description
            "Cache type is the one of the nexthop cache server.";
        }
        enum invalid {
          description
            "Cache type is invalid.";
        }
        enum incomplete {
          description
            "Cache type is incompete.";
        }
        enum negative {
          description
            "Cache type is negative.";
        }
        enum cached {
          description
            "Cache type is cached.";
        }
      }
      description
        "NHRP cache type.";
    }

    leaf nbma {
      type union {
        type ntos-inet:ipv4-address;
        type enumeration {
          enum local {
            description
              "Local protocol address.";
          }
        }
      }
      description
        "NBMA IP address.";
    }

    leaf timeout {
      type boolean;
      description
        "Tell if cache entry has timeout.";
    }

    leaf auth {
      type boolean;
      description
        "Tell if cache entry has authentication procedure.";
    }

    leaf used {
      type boolean;
      description
        "Tell if cache entry is used.";
    }

    leaf identity {
      type string;
      description
        "Identity of the connection of the cache entry.";
    }
  }

  grouping nhrp4-cache-state {
    description
      "NHRP cache objects.";

    list nhrp4-cache {
      key "interface protocol";
      description
        "Nexthop Cache Entry.";
      ntos-api:key-removed "nbma";
      ntos-api:key-added "protocol";
      uses cache-info-state;

      leaf protocol {
        type ntos-inet:ipv4-address;
        description
          "Protocol IPv4 Address.";
      }
    }
  }

  grouping nhrp6-cache-state {
    description
      "NHRP cache objects.";

    list nhrp6-cache {
      key "interface protocol";
      description
        "Nexthop Cache Entry.";
      ntos-api:key-removed "nbma";
      ntos-api:key-added "protocol";
      uses cache-info-state;

      leaf protocol {
        type ntos-inet:ipv6-address;
        description
          "Protocol IPv6 Address.";
      }
    }
  }

  grouping nhrp4-nhs-state {
    description
      "NHRP NextHop Server objects.";

    list nhrp4-nhs {
      key "interface nbma";
      description
        "Nexthop Cache Entry.";
      uses nhs-info-state;

      leaf protocol {
        type ntos-inet:ipv4-address;
        description
          "Protocol IPv4 Address.";
      }
    }
  }

  grouping nhrp6-nhs-state {
    description
      "NHRP NextHop Server objects.";

    list nhrp6-nhs {
      key "interface nbma";
      description
        "Nexthop Cache Entry.";
      uses nhs-info-state;

      leaf protocol {
        type ntos-inet:ipv6-address;
        description
          "Protocol IPv6 Address.";
      }
    }
  }

  grouping nhrp-connection-state {
    description
      "NHRP Connections.";

    list connection {
      key "src dst";
      description
        "NHRP Connection.";

      leaf src {
        type string;
        description
          "Source IP Address.";
      }

      leaf dst {
        type string;
        description
          "Destination IP Address.";
      }

      leaf notifier-active {
        type boolean;
        description
          "Whether the connection is active.";
      }

      leaf sa-count {
        type uint16;
        description
          "Number of child SAs for this connection.";
      }

      leaf identity {
        type string;
        description
          "IKE remote identity of the connection.";
      }
    }
  }

  grouping nhrp-iface-common-config {
    description
      "Common IPv4/IPv6 Configuration for NHRP interfaces.";

    leaf registration-no-unique {
      type boolean;
      default "false";
      description
        "Registration configuration with unique flag not set.";
    }

    leaf shortcut {
      type boolean;
      default "false";
      description
        "Allow shortcut establishment.";
    }

    leaf redirect {
      type boolean;
      default "false";
      description
        "Send redirect notifications.";
    }

    leaf shortcut-keep-sa {
      type boolean;
      default "false";
      description
        "Do not flush IPsec SAs after shortcut expires.";
    }

    leaf network-id {
      type uint32 {
        range "1..4294967295";
      }
      description
        "Specify network-id to specify interface group.";
    }

    leaf holdtime {
      type uint16 {
        range "1..65000";
      }
      default "7200";
      description
        "Time in seconds that NBMA addresses are advertised valid.";
    }
  }

  grouping nhrp4-interface-config {
    description
      "Top-level configuration for NHRP IPv4 interfaces.";
    uses nhrp-iface-common-config;

    list nhrp-map {
      key "ip";
      description
        "Nexthop Server mapping configuration.";

      leaf ip {
        type ntos-inet:ipv4-address;
        description
          "IPv4 protocol address.";
      }

      leaf nbma {
        type union {
          type ntos-inet:ipv4-address;
          type enumeration {
            enum local {
              description
                "Handle protocol address locally.";
            }
          }
        }
        description
          "IPv4 NBMA address.";
      }
    }

    list nhrp-nhs {
      key "ip";
      description
        "Nexthop Server mapping configuration.";

      leaf ip {
        type union {
          type ntos-inet:ipv4-address;
          type enumeration {
            enum dynamic {
              description
                "Automatic detection of protocol address.";
            }
          }
        }
        description
          "IPv4 protocol address configuration.";
      }

      leaf nbma {
        type union {
          type ntos-inet:ipv4-address;
          type string;
        }
        mandatory true;
        description
          "NBMA address configuration.";
      }
    }

    leaf ip-nhrp-mtu {
      type union {
        type uint32 {
          range "576..1500";
        }
        type enumeration {
          enum opennhrp {
            description
              "Advertise bound interface MTU similar to OpenNHRP.";
          }
          enum default {
            description
              "MTU is not configured.";
          }
        }
      }
      default "default";
      description
        "MTU configuration.";
    }
  }

  grouping nhrp6-interface-config {
    description
      "Top-level configuration for NHRP IPv6 interfaces.";
    uses nhrp-iface-common-config;

    list nhrp-map {
      key "ipv6";
      description
        "Nexthop Server mapping configuration.";

      leaf ipv6 {
        type ntos-inet:ipv6-address;
        description
          "IPv6 protocol address.";
      }

      leaf nbma {
        type union {
          type ntos-inet:ipv4-address;
          type enumeration {
            enum local {
              description
                "Handle protocol address locally.";
            }
          }
        }
        mandatory true;
        description
          "IPv4 NBMA address.";
      }
    }

    list nhrp-nhs {
      key "ipv6";
      description
        "Nexthop Server mapping configuration.";

      leaf ipv6 {
        type union {
          type ntos-inet:ipv6-address;
          type enumeration {
            enum dynamic {
              description
                "Automatic detection of protocol address.";
            }
          }
        }
        description
          "IPv6 protocol address configuration.";
      }

      leaf nbma {
        type union {
          type ntos-inet:ipv4-address;
          type string;
        }
        mandatory true;
        description
          "NBMA address configuration.";
      }
    }
  }

  grouping logging-common-config {
    description
      "NHRP logging configuration.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable/disable NHRP logging configuration.";
    }

    leaf common {
      type boolean;
      default "false";
      description
        "Log NHRP common information.";
    }

    leaf event {
      type boolean;
      default "false";
      description
        "Log NHRP event messages.";
    }

    leaf interface {
      type boolean;
      default "false";
      description
        "Log NHRP interface messages.";
    }

    leaf kernel {
      type boolean;
      default "false";
      description
        "Log NHRP kernel messages.";
    }

    leaf route {
      type boolean;
      default "false";
      description
        "Log NHRP route messages.";
    }

    leaf vici {
      type boolean;
      default "false";
      description
        "Log NHRP VICI messages.";
    }
  }

  grouping nhrp-common-flush {
    description
      "NHRP common flush information.";

    leaf cache {
      type empty;
      description
        "NHRP dynamic cache entries.";
    }

    leaf shortcut {
      type empty;
      description
        "NHRP shortcut entries.";
    }
  }

  grouping nhrp-common-show {
    description
      "NHRP common show information.";

    leaf cache {
      type empty;
      description
        "NHRP forwarding cache information.";
      ntos-extensions:nc-cli-group "nhrp-common-show";
    }

    leaf nhs {
      type empty;
      description
        "NHRP Next hop server information.";
      ntos-extensions:nc-cli-group "nhrp-common-show";
    }

    leaf opennhrp {
      type empty;
      description
        "NHRP opennhrpctl style cache dump.";
      ntos-extensions:nc-cli-group "nhrp-common-show";
    }

    leaf shortcut {
      type empty;
      description
        "NHRP shortcut information.";
      ntos-extensions:nc-cli-group "nhrp-common-show";
    }

    leaf default {
      type empty;
      description
        "NHRP default information.";
      ntos-extensions:nc-cli-group "nhrp-common-show";
    }
  }

  rpc flush-nhrp {
    description
      "Flush NHRP information.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      uses nhrp-common-flush;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-flush "nhrp";
    ntos-api:internal;
  }

  rpc flush-nhrp6 {
    description
      "Flush NHRP information.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      uses nhrp-common-flush;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-flush "nhrp6";
    ntos-api:internal;
  }

  rpc show-nhrp {
    description
      "Show NHRP IPv4 information.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      uses nhrp-common-show;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "nhrp";
    ntos-api:internal;
  }

  rpc show-nhrp6 {
    description
      "Show NHRP IPv6 information.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
      uses nhrp-common-show;
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "nhrp6";
    ntos-api:internal;
  }

  rpc show-nhrp-connection {
    description
      "Show NHRP connection information.";
    input {

      leaf vrf {
        type string;
        default "main";
        description
          "Specify the VRF.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
    }
    output {
      uses ntos-cmd:long-cmd-status;
      uses ntos-cmd:long-cmd-output;
    }
    ntos-extensions:feature "product";
    ntos-extensions:nc-cli-show "nhrp-connection";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing" {
    description
      "NHRP configuration.";

    container nhrp {
      presence "Makes NHRP available";
      description
        "NHRP configuration.";
      ntos-extensions:feature "product";
      uses nhrp-router-config;
    }
  }

  augment "/ntos:config/ntos-rt:routing/ntos-rt:logging" {
    description
      "Common NHRP logging configuration.";

    container nhrp {
      presence "Make NHRP common logging configuration available.";
      description
        "Common NHRP routers logging configuration.";
      ntos-extensions:feature "product";
      uses logging-common-config;
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ipv6" {
    description
      "NHRP IPv6 interface configuration.";

    container nhrp {
      must '../../../../ntos-iface:interface/ntos-gre:gre/ntos-gre:name = ../../ntos-rt:name' {
        error-message "interface must be a gre interface.";
      }
      presence "Makes NHRP IPv6 interface available";
      description
        "Interface NHRP configuration.";
      ntos-extensions:feature "product";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable NHRP IPv6 on this interface.";
      }

      uses nhrp6-interface-config;
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ip" {
    description
      "NHRP IPv4 interface configuration.";

    container nhrp {
      must '../../../../ntos-iface:interface/ntos-gre:gre/ntos-gre:name = ../../ntos-rt:name' {
        error-message "interface must be a gre interface.";
      }
      presence "Makes NHRP IP interface available";
      description
        "Interface NHRP configuration.";
      ntos-extensions:feature "product";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable NHRP IPv4 on this interface.";
      }

      uses nhrp4-interface-config;
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-rt:routing/ntos-rt:interface" {
    description
      "NHRP connection configuration.";

    container nhrp-connection {
      must '../../../ntos-iface:interface/ntos-gre:gre/ntos-gre:name = ../ntos-rt:name' {
        error-message "interface must be a gre interface.";
      }
      presence "Configure NHRP connection.";
      description
        "Configure NHRP connection(protection).";
      ntos-api:child-removed "protection-ipsec-profile";

      leaf ipsec-profile {
        type ntos-types:ike-object-name;
        description
          "IPsec profile name configured.";
        ntos-extensions:nc-cli-completion-xpath
          "/ntos:config/vrf[ntos:name=string(current()/../../../ntos-interface:interface
             /ntos-gre:gre[ntos-gre:name=current()/../ntos-routing:name]/ntos-gre:link-vrf) or
             (not(current()/../../../ntos-interface:interface/ntos-gre:gre[ntos-gre:name=current()
             /../ntos-routing:name]/ntos-gre:link-vrf) and ntos:name=string(current()/../../../ntos:name)
             )]/ntos-ike:ike/ntos-ike:vpn/ntos-ike:security-policy/ntos-ike:name";
      }
      ntos-extensions:feature "product";
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing" {
    description
      "NHRP state.";

    container nhrp {
      presence "Makes NHRP available";
      description
        "NHRP operational state data.";
      ntos-api:child-removed "dmvpn";
      ntos-api:child-removed "dmvpn/dst";
      ntos-api:child-removed "dmvpn/identity";
      ntos-api:child-removed "dmvpn/notifier-active";
      ntos-api:child-removed "dmvpn/sas";
      ntos-api:child-removed "dmvpn/src";
      ntos-extensions:feature "product";
      uses nhrp-router-config;
      uses nhrp4-cache-state;
      uses nhrp6-cache-state;
      uses nhrp4-nhs-state;
      uses nhrp6-nhs-state;
      uses nhrp-connection-state;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ipv6" {
    description
      "NHRP IPv6 interface configuration.";

    container nhrp {
      presence "Makes NHRP IPv6 interface available";
      description
        "Interface NHRP configuration.";
      ntos-extensions:feature "product";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable NHRP IPv6 on this interface.";
      }
      uses nhrp6-interface-config;
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-rt:interface" {
    description
      "NHRP connection configuration.";

    container nhrp-connection {
      description
        "Configure NHRP connection(protection).";
      ntos-api:child-removed "protection-ipsec-profile";

      leaf ipsec-profile {
        type string;
        description
          "IPsec profile name configured.";
      }
      ntos-extensions:feature "product";
    }
  }

  augment "/ntos:state/ntos:vrf/ntos-rt:routing/ntos-rt:interface/ntos-rt:ip" {
    description
      "NHRP IPv4 interface configuration.";

    container nhrp {
      presence "Makes NHRP IP interface available";
      description
        "Interface NHRP configuration.";
      ntos-extensions:feature "product";

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable NHRP IPv4 on this interface.";
      }
      uses nhrp4-interface-config;
    }
  }
}
