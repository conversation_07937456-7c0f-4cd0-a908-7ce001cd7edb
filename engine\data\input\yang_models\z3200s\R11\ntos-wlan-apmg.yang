module ntos-wlan-apmg {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:wlan-apmg";
  prefix ntos-wlan-apmg;

  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-if-types {
    prefix ntos-if;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS WLAN apmg module.";

  revision 2024-07-23 {
    description
      "Initial version.";
    reference "";
  }

  grouping enabled-leaf {
    leaf enabled {
      type boolean;
      default "false";
    }
  }

  grouping apmg-ac-config {
    leaf ac-name {
      description
        "Set AC's name.";

      type ntos-types:ntos-obj-name-type;
      default "Ruijie_Ac_V0001";
    }

    container acctrl-trap {
      description
        "Set the trap on/off keyword.";

      container acap-microap-ctrl {
        description
          "Set the MicroApCtrl's value.";
        uses enabled-leaf;
      }
      container acap-updown-ctrl {
        description
          "Set the AcApUpDownCtrl's value.";
        uses enabled-leaf;
      }
      container acap-joinfail-ctrl {
        description
          "Set the AcApJoinFailCtrl's value.";
        uses enabled-leaf;
      }
      container acap-decryeroreport-ctrl {
        description
          "Set the AcApDecryEroReportCtrl's value.";
        uses enabled-leaf;
      }
      container acap-imageupdt-ctrl {
        description
          "Set the AcApImageUpdtCtrl's value.";
        uses enabled-leaf;
      }
      container acap-timestamp-ctrl {
        description
          "Set the AcApTimestampCtrl's value.";
        uses enabled-leaf;
      }
      container acsta-oper-ctrl {
        description
          "Set the AcStaOperCtrl key.";
        uses enabled-leaf;
      }
    }

    container ap-auth {
      description
        "Ap identity authentication.";

      container serial {
        uses enabled-leaf;
      }
      container password {
        uses enabled-leaf;
      }
      container certificate {
        uses enabled-leaf;
      }
    }

    container ap-backup {
      list group {
        key "name";
        description
          "Create ap backup group.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
        }
      }
    }

    container ap-priority {
      description
        "Set the ap priority function.";
      uses enabled-leaf;
    }

    leaf wtp-limit {
      description
        "Set the limit WTP attached to this AC.";
      type uint32;
    }

    container bind-ap-mac {
      description
        "Only AP with MAC config can connect AC.";
      uses enabled-leaf;
    }

    leaf nas-id {
      description
        "Set AC's nas id.";
      type string;
    }
  }

  grouping apmg-ap-config {
    container ap-backup-group {
      description
        "Config backup group.";

      leaf group {
        type ntos-types:ntos-obj-name-type;
        ntos-ext:nc-cli-no-name;
      }

      leaf master {
        description
          "As master in the backup group.";
        type empty;
        when "../group != ''";
      }
    }

    leaf rename {
      type ntos-types:ntos-obj-name-type;
    }

    leaf ap-group {
      description
        "Set ap-group name which the Ap belongs to.";
      type ntos-types:ntos-obj-name-type;
    }

    leaf ap-mac {
      type ntos-if:mac-address;
    }

    container credential {
      description
        "Set ap's credential.";
      leaf user-name {
        type string;
        ntos-ext:nc-cli-no-name;
      }

      leaf password {
        type string;
        when "../user-name != ''";
        ntos-ext:nc-cli-no-name;
      }
      ntos-ext:nc-cli-one-liner;
    }

    leaf priority {
      type uint8 {
        range "1..4";
      }
      default "1";
    }

    leaf statistics-timer {
      type uint16 {
        range "1..65535";
      }
      default "120";
    }

    container reload {
      description
        "Set the ap to reload.";
      leaf at {
        type string {
          pattern '([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]' {
            error-message "Incorrect time format, expecting: hh:mm:ss.";
          }
        }
      }
    }
  }

  grouping apmg-apg-config {
    container credential {
      description
        "Set ap group's ap credential.";
      leaf user-name {
        type string;
        ntos-ext:nc-cli-no-name;
      }

      leaf password {
        type string;
        when "../user-name != ''";
        ntos-ext:nc-cli-no-name;
      }
      ntos-ext:nc-cli-one-liner;
    }

    leaf statistics-timer {
      type uint16 {
        range "1..65535";
      }
      default "120";
    }

    list interface-mapping {
      key "wlan-id";
      description
        "Set the interface map, ssid -- vid peer.";

      leaf wlan-id {
        description
          "The wlan id want to be mapped.";
        type uint16 {
          range "1..4094";
        }
        ntos-ext:nc-cli-no-name;
      }

      leaf vlan-id {
        description
          "The vlan id want to be mapped.";
        type uint16 {
          range "1..4094";
        }
        when "../wlan-id";
        ntos-ext:nc-cli-no-name;
      }

      choice radio-type {
        case radio {
          leaf radio {
            description
              "Set radio ID.";
            type union {
              type uint8;
              type enumeration {
                enum all;
                enum 802.11a;
                enum 802.11b;
                enum 6GHz;
              }
            }
            when "../vlan-id";
          }
        }
        case mlo {
          leaf mlo {
            description
              "Set mlo group ID.";
            type union {
              type uint8;
            }
            when "../vlan-id";
          }
        }
      }

      leaf ap-wlan-id {
        description
          "Set ap wlan ID.";
        type uint32;
        when "../vlan-id";
      }

      ntos-ext:nc-cli-one-liner;
    }
  }

  grouping apmg-wlan-config {
    container enable-broad-ssid {
      description
        "IEEE 802.11 Add WLAN.Suppress SSID";
      leaf enabled {
        type boolean;
        default "true";
      }
    }

    leaf ssid {
      description
        "Modify the wlan's ssid.";
      type ntos-types:ntos-obj-name-type;
    }

    leaf description {
      description
        "The description of the this wlan.";
      type string;
    }

    leaf ssid-code {
      description
        "Set the wlan's ssid character code.";
      type enumeration {
        enum utf-8;
        enum gbk;
      }
    }

    leaf nas-id {
      description
        "Set AC's nas id.";
      type string;
    }
  }

  grouping gipc-log-type {
    choice switch-type {
      case on {
        leaf on {
          type enumeration {
            enum all;
            enum event;
            enum info;
            enum packet;
            enum thread;
            enum err;
            enum debug;
            enum warning;
            enum skb;
          }
        }
      }
      case off {
        leaf off {
          type enumeration {
            enum all;
            enum event;
            enum info;
            enum packet;
            enum thread;
            enum err;
            enum debug;
            enum warning;
            enum skb;
          }
        }
      }
    }
  }

  grouping cmd-apmg {
    container apmg {
      container gipc {
        uses gipc-log-type;

        leaf client-name {
          type string;
          ntos-ext:nc-cli-no-name;
        }
      }

      container apc {
        container gipc {
          choice operate {
            case reconnect {
              leaf reconnect {
                description
                  "Set the gipc reconnect start.";
                type empty;
              }
            }
            case on-off {
              uses gipc-log-type;
            }
          }
        }
      }
    }
  }

  grouping gipc-info {
    leaf ctrl {
      type empty;
    }

    leaf mem-stats {
      type empty;
    }

    leaf pkt-stats {
      type string;
    }

    leaf recv-skb {
      type string;
    }

    leaf send-skb {
      type string;
    }
  }

  grouping show-apmg {
    container apmg {
      container debug {
        description
          "Show apmg debug command.";
        leaf redis {
          type enumeration {
            enum ctrl;
          }
        }
        leaf ctrl {
          type enumeration {
            enum info;
          }
        }
      }

      container db {
        description
          "Show the apmg db information.";
        leaf ctrl {
          description
            "Show apmg ctrl.";
          type enumeration {
            enum info;
          }
        }

        list shm {
          key "name";
          description
            "Show apmg db shm info.";

          leaf name {
            type enumeration {
              enum ap-group;
              enum ap-index;
              enum ap-name;
              enum ap-new-index;
              enum ap-new-name;
            }
          }

          choice content {
            case choice-ctrl {
              leaf ctrl {
                type empty;
              }
            }
            case choice-data {
              leaf data {
                type empty;
              }
            }
          }
          max-elements 1;
        }

        container table {
          description
            "Show the specified table information.";
          choice content {
            case choice-attr {
              container attr {
                choice ap-type {
                  case ac {
                    leaf ac {
                      type empty;
                    }
                  }
                  case ap-cfg {
                    list ap-cfg {
                      key "name";
                      leaf name {
                        type string;
                      }

                      leaf radio {
                        type uint8;
                      }
                      max-elements 1;
                    }
                  }
                  case ap-group {
                    leaf ap-group {
                      type string;
                    }
                  }
                  case ap-state {
                    list ap-state {
                      key "name";

                      leaf name {
                        type ntos-if:mac-address;
                      }
                      leaf radio {
                        type uint8;
                      }
                      leaf wlan {
                        type uint16;
                      }
                      max-elements 1;
                    }
                  }
                  case wlan {
                    leaf wlan {
                      type uint16;
                    }
                  }
                  case mlo {
                    leaf mlo {
                      type uint8;
                    }
                    leaf radio {
                      type uint8;
                    }
                  }
                }

                leaf attr {
                  type string;
                }

                leaf attr-type {
                  description
                    "1-uint8_t,2-uint16_t,3-uint32_t,4-string,5-struct.";
                  type uint8 {
                    range '1..5';
                  }
                  when "../attr";
                  ntos-ext:nc-cli-no-name;
                }
              }
            }
            case choice-entry {
              container entry {
                choice ap-type {
                  case ap-cfg {
                    list ap-cfg {
                      key "name";

                      leaf name {
                        type string;
                      }

                      leaf radio {
                        type empty;
                      }
                      max-elements 1;
                    }
                  }
                  case ap-group {
                    list ap-group {
                      key "name";

                      leaf name {
                        type string;
                      }

                      leaf radio {
                        type empty;
                      }
                      max-elements 1;
                    }
                  }
                  case ap-state {
                    list ap-state {
                      key "name";

                      leaf name {
                        type ntos-if:mac-address;
                      }

                      leaf radio {
                        type uint8;
                      }

                      leaf wlan {
                        type empty;
                      }
                      max-elements 1;
                    }
                  }
                  case mlo {
                    leaf mlo {
                      type uint8;
                    }
                    leaf radio {
                      type empty;
                    }
                  }
                  case wlan {
                    leaf wlan {
                      type uint16;
                    }
                  }
                }
              }
            }
          }
        }

        leaf ap-config {
          description
            "Show ap-config information.";
          type enumeration {
            enum mac-index;
            enum name-index;
          }
        }

        leaf ap-state {
          description
            "Show ap-state information.";
          type enumeration {
            enum index-mac;
            enum name-mac;
          }
        }

        list ap-group {
          key "name";
          description
            "Show apmg db ap-group info.";
          leaf name {
            type string;
          }

          leaf aps {
            type empty;
          }
          max-elements 1;
        }

        leaf listen-table {
          description
            "Show apmg db listen table info.";
          type empty;
        }

        leaf wlan {
          description
            "Show wlan information.";
          type enumeration {
            enum apw-acw;
          }
        }

        leaf attr-func {
          description
            "Show apmg db attr modify notify function.";
          type empty;
        }

        leaf debug {
          description
            "Show apmg db debug.";
          type empty;
        }

        container wdb {
          description
            "Show apmg db wdb information.";
          choice content {
            case client {
              leaf client {
                type empty;
              }
            }
            case ctrl {
              leaf ctrl {
                type empty;
              }
            }
            case memory {
              leaf memory {
                type empty;
              }
            }
            case sync-info {
              leaf sync-info {
                type empty;
              }
            }
            case table-name {
              list table-name {
                key "name";

                leaf name {
                  type union {
                    type string;
                    type enumeration {
                      enum all;
                    }
                  }
                }

                leaf entry-list {
                  description
                    "1-uint8_t,2-uint16_t,3-uint32_t,4-string,5-struct.";
                  type uint8 {
                    range '1..5';
                  }
                }

                list entry-id {
                  key "name";
                  description
                    "Show rg_wdb entry-id info";

                  leaf name {
                    type uint32;
                  }

                  leaf attr-name {
                    type string;
                  }
                  max-elements 1;
                }

                leaf attr-list {
                  description
                    "Show rg_wdb attr info.";
                  type empty;
                }

                leaf attr-reg-client {
                  description
                    "Show rg_wdb attr reg clients.";
                  type string;
                }

                leaf detail {
                  description
                    "Show rg_wdb detail info.";
                  type empty;
                }

                leaf reg-add-attr  {
                  description
                    "Show rg_wdb attr reg and add info.";
                  type union {
                    type string;
                    type empty;
                  }
                }
                max-elements 1;
              }
            }
          }
        }
      }
      container apc {
        description
          "Show the apmg apc information.";
        choice content {
          case cmsg {
            leaf cmsg {
              description
                "Show cmsg decode info";
              type empty;
            }
          }
          case controller {
            leaf controller {
              description
                "Show controller info.";
              type empty;
            }
          }
          case debugging {
            leaf debugging {
              description
                "Show debugging.";
              type empty;
            }
          }
          case decode-elem {
            leaf decode-elem {
              description
                "Show elem decode reg info.";
              type empty;
            }
          }
          case elapsed-time {
            leaf elapsed-time {
              description
                "Show elapsed time info.";
              type empty;
            }
          }
          case encode-elem {
            leaf encode-elem {
              description
                "Show elem encode reg info.";
              type empty;
            }
          }
          case gipc {
            container gipc {
              description
                "Show apc gipc info.";
              uses gipc-info;
            }
          }
          case pkt-dump {
            leaf pkt-dump {
              description
                "Show pkt first message.";
              type union {
                type uint8 {
                  range 0..1;
                }
                type ntos-if:mac-address;
              }
            }
          }
          case pkt-hash {
            leaf pkt-hash {
              description
                "Show pkt hash info.";
              type empty;
            }
          }
          case vsp {
            leaf vsp {
              description
                "Show vsp reg info.";
              type empty;
            }
          }
        }
      }

      container gipc {
        description
          "Show the apmg gipc information.";
        uses gipc-info;

        leaf client {
          type empty;
        }

        leaf log-switch {
          type string;
        }
      }
    }
  }

  grouping show-ac-config {
    container AC-Configuration-info {
      leaf max-wtp {
      type uint16;
      }
      leaf sta-limit {
      type uint16;
      }
      leaf license-wtp-max {
      type uint16;
      }
      leaf license-sta-max {
      type uint16;
      }
      leaf serial-auth {
      type string;
      }
      leaf password-auth {
      type string;
      }
      leaf certificate-auth {
      type string;
      }
      leaf Bind-AP-MAC {
      type string;
      }
      leaf AP-Priority {
      type string;
      }
      leaf supp-psk-cer {
      type string;
      }
      leaf ac-name {
      type string;
      }
      leaf ac-location {
      type string;
      }
    }
    container AC-State-info {
      leaf sta-num {
      type uint16;
      }
      leaf act-wtp {
      type uint16;
      }
      leaf localIpAddr {
      type string;
      }
      leaf localIpAddr6 {
      type string;
      }
      leaf used-wtp {
      type string;
      }
      leaf remain-wtp {
      type string;
      }
      leaf HW-Ver {
      type string;
      }
      leaf SW-Ver {
      type string;
      }
      leaf Mac-address {
      type string;
      }
      leaf System-SN {
      type string;
      }
      leaf Product-ID {
      type string;
      }
      leaf NET-ID {
      type string;
      }
      leaf NAS-ID {
      type string;
      }
    }
  }

  grouping show-ap-detail {
    list wlan-list {
      key "wlan-id";
      leaf wlan-id {
        type uint32;
      }
      leaf ssid {
        type string;
      }
      leaf mode {
        type string;
      }
      leaf broad-cast {
        type string;
      }
      leaf sta-limit {
        type uint32;
      }
      leaf sta-num {
        type uint32;
      }
      leaf session {
        type uint32;
      }
      leaf enable-5g {
        type string;
      }
      leaf ssid-code {
        type string;
      }
      leaf master-group {
        type string;
      }
      leaf user-arr {
        type string;
      }
    }

    list ap-list {
      key "ap-name";
      leaf ap-name {
        type string;
      }
      leaf ap-group {
        type string;
      }
      leaf master-group {
        type string;
      }
      leaf cpu-percent {
        type uint16;
      }
      leaf freememory-percent {
        type uint16;
      }
      leaf flow-kbps {
        type uint16;
      }
      leaf stanum {
        type uint16;
      }
      leaf stalimit {
        type uint16;
      }
      leaf ip {
        type string;
      }
      leaf mac {
        type string;
      }
      leaf location {
        type string;
      }
      leaf state {
        type string;
      }
      leaf upflow-kbps {
        type uint32;
      }
      leaf downflow-kbps {
        type uint32;
      }
      leaf sub-ap-name {
        type string;
      }
      leaf sub-ap-mac {
        type string;
      }
      leaf master-ap-name {
        type string;
      }
      leaf sub-rd {
        type uint16;
      }
      leaf vapsupport {
        type string;
      }
      leaf capwapbackup {
        type string;
      }
    }
  }
}

