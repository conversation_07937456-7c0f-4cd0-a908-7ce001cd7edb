package main

import (
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/imroc/req/v3"
	"github.com/pkg/errors"
)

var GitlabWebClient = req.C().
	SetCommonRetryCount(3).
	// Set the retry sleep interval with a commonly used algorithm: capped exponential backoff with jitter (https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/).
	SetCommonRetryBackoffInterval(1*time.Second, 5*time.Second).
	AddCommonRetryCondition(func(resp *req.Response, err error) bool {
		if err == nil && libs.InArrayInt([]int{200, 201, 409}, resp.StatusCode) {
			return false
		}
		return err != nil
	})

type ProjectResponse struct {
	ID                int              `json:"id"`
	SshUrlToRepo      string           `json:"ssh_url_to_repo"`
	ForkedFromProject *ProjectResponse `json:"forked_from_project"`
}

func GetForkedFromProject(repoPathID string) (string, error) {
	project := ProjectResponse{}
	url := fmt.Sprintf("%s/api/%s/projects/%s?private_token=%s", "http://10.51.135.102:8080", "v4", repoPathID, "WmQqS7_KXPRk-7XfvLhy")
	resp, err := GitlabWebClient.R().SetSuccessResult(&project).Get(url)
	if err != nil {
		return "", errors.Wrap(err, "")
	}

	if resp.IsSuccessState() {
		if project.ForkedFromProject != nil {
			return project.ForkedFromProject.SshUrlToRepo, nil
		}
		return "", nil
	}
	return "", fmt.Errorf("unkown err: %s", resp.String())
}

func GetGitlabProjectID(repo string) string {
	_repo := strings.TrimPrefix(repo, "ssh://")
	_repo = strings.TrimSuffix(_repo, ".git")
	_array := strings.Split(_repo, "/")
	return strings.Join(_array[1:], "/")
}

func CheckPersonalGit(dir string) error {
	logFile, err := os.OpenFile(filepath.Join("/tmp", "build-product"+".log"), os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logging.ErrorLogger.Errorf("create log file err ", err, "build-product")
		return err
	}

	logger := logging.NewLogger(&logging.Options{
		TimesFormat: logging.TIMESECOND,
	})

	logger.SetOutput(logFile)
	command := fmt.Sprintf("cd %s && find ./ -name .git|sed 's@/.git@@g'", dir)
	logger.Debugf("查找编译作业下所有仓库:", command)
	ret, err := libs.ExecCommand(command)
	if err != nil {
		logger.Errorf("run command error", err.Error(), command, ret)
		return err
	}
	logger.Debugf("所有仓库:", ret)

	for _, subDir := range strings.Split(ret, "\n") {
		if len(subDir) == 0 {
			continue
		}

		command := fmt.Sprintf("cd %s && cd %s && git remote -v |grep origin|grep push|grep -E 'aqyfzx.ruijie.net|10.51.135.102'|awk '{print $2}'", dir, subDir)
		logger.Debugf("查找为NTOS仓库:", command)
		subDirRet, err := libs.ExecCommand(command)
		if err != nil {
			logger.Errorf("run command error", err.Error(), command, subDirRet)
			continue
		}

		if len(strings.TrimSpace(subDirRet)) == 0 {
			continue
		}
		logger.Debugf("NTOS仓库:", subDirRet)

		repo := strings.TrimSpace(subDirRet)
		gitlabProjectPathID := GetGitlabProjectID(repo)

		// projects, err := dproject.FindByRepoPathID(gitlabProjectPathID)
		// if err != nil {
		// 	logging.ErrorLogger.Errorf("find project by repo path id err", err.Error())
		// 	continue
		// }
		// if len(projects) > 0 {
		// 	continue
		// }

		// 结果为空，git仓库非生产仓库，查找关联主仓
		forkedSshUrlToRepo, err := GetForkedFromProject(url.PathEscape(gitlabProjectPathID))
		if err != nil {
			logger.Errorf("get forked ssh url to repo error", err.Error())
			continue
		}

		if forkedSshUrlToRepo == "" {
			logger.Errorf("无关联主仓", repo)
			continue
		}

		logger.Debugf("主仓地址:", forkedSshUrlToRepo)
		command = fmt.Sprintf("cd %s && cd %s && (git remote -v |grep upstream || git remote add upstream %s) && git fetch upstream", dir, subDir, forkedSshUrlToRepo)
		logger.Debugf("增加upstream:", command)
		subDirRet, err = libs.ExecCommand(command)
		if err != nil {
			logger.Errorf("run command error", err.Error(), command, subDirRet)
			continue
		}
		logger.Debugf("增加upstream结果:", subDirRet)

		command = fmt.Sprintf("cd %s && cd %s && for i in `git log --graph --pretty=format:'%%h' --abbrev-commit|grep '^\\*'|awk '{print $NF}'|head -n 1000`;do limit=`git branch -a --contains $i|wc -l`; if [ $limit -gt 3 ]; then git branch -a --contains $i|grep upstream|tail -n 1 && break; fi; done", dir, subDir)
		logger.Debugf("查找个人仓主仓基线分支：", command)
		upstreamBranch, err := libs.ExecCommand(command)
		if err != nil {
			logger.Errorf("run command error", err.Error(), command, upstreamBranch)
			continue
		}

		upstreamBranchStr := strings.TrimSpace(upstreamBranch)
		logger.Debugf("个人仓的主仓基线分支：", upstreamBranchStr)
		command = fmt.Sprintf("cd %s && cd %s && git diff %s...`git branch|awk '{print $NF}`", dir, subDir, upstreamBranchStr)
		logger.Debugf("获取个人仓与主仓差异:", command)
		diff, err := libs.ExecCommand(command)
		if err != nil {
			logging.ErrorLogger.Errorf("run command error", err.Error(), command, diff)
			continue
		}
		logger.Debugf(diff)
	}
	return nil
}

func main() {
	CheckPersonalGit("/mnt/sata0/build-product")
}
