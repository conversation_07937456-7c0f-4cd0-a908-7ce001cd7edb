module ntos-misn {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:misn";
  prefix ntos-misn;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-interface {
    prefix ntos-interface;
  }

  organization
    "Ruijie Networks Co., Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS local defend module.";

  revision 2023-05-05 {
    description
      "Create initial version.";
  }

  identity misn {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Multi-Interface-Same-Network service.";
  }

  grouping cmd-output-buffer {
    leaf buffer {
      description
        "Command output buffer since last request.";

      type string;
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  grouping misn-config {
    container misn {
      leaf enabled {
        description
          "Support multi interface have same network.";

        type boolean;
        default "true";
      }
    }
  }

  rpc show-interface-status {
    description
      "Show the details.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf interface {
        description
          "The name of interface.";

        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";

        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      list interface {
        key name;

        leaf name {
          type string;
        }

        leaf flag {
          type string;
        }

        container ipv4 {
          leaf address {
            type string;
          }

          leaf net {
            type string;
          }
        }

        container ipv6 {
          leaf address {
            type string;
          }

          leaf net {
            type string;
          }
        }

        leaf table {
          type uint32;
        }
      }
    }
    ntos-ext:nc-cli-show "misn interface";
  }

  rpc show-network-status {
    description
      "Show the details.";

    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf type {
        type enumeration {
          enum ipv4;
          enum ipv6;
        }

        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      list network {
        key address;

        leaf address {
          type string;
        }

        list interface {
          key name;

          leaf name {
            type string;
          }

          leaf address {
            type string;
          }
        }
      }
    }
    ntos-ext:nc-cli-show "misn network";
  }

  rpc show-route-table {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf interface {
        description
          "The name of interface.";

        type ntos-types:ifname;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-interface:interface/*/*[local-name()='name']";
      }

      leaf table-id {
        description
          "The id of table.";

        type uint16 {
          range "1..254";
        }
      }

      leaf type {
        type enumeration {
          enum ipv4;
          enum ipv6;
        }

        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-show "misn table";
  }

  rpc show-route-iptables {
    input {
      leaf type {
        type enumeration {
          enum ipv4;
          enum ipv6;
        }

        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-show "misn iptables";
  }

  rpc show-status {
    input {
      leaf vrf {
        description
          "Specify the VRF.";

        type ntos:vrf-name;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }
    }

    output {
      uses cmd-output-buffer;
    }
    ntos-ext:nc-cli-show "misn-status";
    ntos-ext:nc-cli-hidden;
  }

  augment "/ntos:config/ntos:vrf" {
    uses misn-config;
  }
}