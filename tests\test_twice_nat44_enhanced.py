#!/usr/bin/env python3
"""
twice-nat44增强版本测试

本测试脚本验证重构后的twice-nat44功能，包括：
1. 新的评估器架构
2. 性能优化功能
3. 增强的日志记录
4. 错误处理机制

作者: FortiGate转换系统
版本: 1.0
日期: 2025-08-08
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine.business.models.twice_nat44_enhanced import (
    TwiceNat44Evaluator, EvaluationContext, EvaluationWeight,
    IPPoolValidator, PoolNameFormat, TwiceNat44PerformanceOptimizer,
    create_twice_nat44_evaluator, create_performance_optimizer
)
from engine.utils.twice_nat44_logger import (
    twice_nat44_logger, create_performance_timer, ErrorCategory
)


def test_ip_pool_validator():
    """测试IP池验证器"""
    print("🧪 测试IP池验证器...")
    
    validator = IPPoolValidator()
    
    # 测试IP地址验证
    test_ips = [
        ("**************", True),
        ("************", True),
        ("256.1.1.1", False),
        ("192.168.1", False),
        ("not_an_ip", False)
    ]
    
    for ip, expected in test_ips:
        result = validator.is_valid_ipv4(ip)
        assert result == expected, f"IP验证失败: {ip}, 期望: {expected}, 实际: {result}"
        print(f"  ✅ IP验证: {ip} -> {result}")
    
    # 测试池格式检测
    test_pools = [
        ("**************", PoolNameFormat.IP_ADDRESS),
        ("EXTERNAL_POOL", PoolNameFormat.TRADITIONAL),
        ("", PoolNameFormat.INVALID),
        ("invalid@pool", PoolNameFormat.INVALID)
    ]
    
    for pool, expected in test_pools:
        result = validator.detect_pool_format(pool)
        assert result == expected, f"池格式检测失败: {pool}, 期望: {expected}, 实际: {result}"
        print(f"  ✅ 池格式检测: {pool} -> {result.value}")
    
    # 测试池列表验证
    pool_names = ["**************", "EXTERNAL_POOL", "invalid_pool"]
    available_pools = ["EXTERNAL_POOL", "INTERNAL_POOL"]
    
    valid_pools, format_results = validator.validate_pool_list(pool_names, available_pools)
    
    expected_valid = ["**************", "EXTERNAL_POOL"]
    assert valid_pools == expected_valid, f"池列表验证失败: 期望: {expected_valid}, 实际: {valid_pools}"
    print(f"  ✅ 池列表验证: {valid_pools}")
    
    print("  ✅ IP池验证器测试通过")


def test_evaluator_architecture():
    """测试评估器架构"""
    print("\n🧪 测试评估器架构...")
    
    # 创建自定义权重
    custom_weights = EvaluationWeight(
        vip_count=0.35,
        vip_completeness=0.25,
        ippool_usage=0.15,
        service_complexity=0.15,
        interface_config=0.10
    )
    
    # 创建评估上下文
    context = EvaluationContext(
        ntos_version="R11",
        threshold=65,
        available_pools=["EXTERNAL_POOL"],
        weights=custom_weights
    )
    
    # 验证权重
    assert context.weights.validate(), "权重配置无效"
    print(f"  ✅ 权重配置验证通过")
    
    # 创建评估器
    evaluator = TwiceNat44Evaluator(context)
    
    # 测试策略
    test_policy = {
        "name": "TEST_POLICY",
        "srcintf": ["wan1"],
        "dstintf": ["dmz"],
        "srcaddr": ["all"],
        "dstaddr": ["WEB_SERVER_VIP"],
        "service": ["HTTP", "HTTPS"],
        "nat": "enable",
        "ippool": "enable",
        "poolname": ["**************"]
    }
    
    # VIP配置
    vip_configs = {
        "WEB_SERVER_VIP": {
            "name": "WEB_SERVER_VIP",
            "extip": "**************",
            "mappedip": "**************",
            "extport": "80-443",
            "mappedport": "80-443"
        }
    }
    
    # 执行评估
    with create_performance_timer("policy_evaluation", {"policy": test_policy["name"]}):
        result = evaluator.evaluate_policy(test_policy, vip_configs)
    
    # 验证结果
    assert hasattr(result, 'total_score'), "评估结果缺少总分"
    assert hasattr(result, 'should_use'), "评估结果缺少推荐标志"
    assert hasattr(result, 'confidence_score'), "评估结果缺少置信度"
    
    print(f"  ✅ 评估结果: 总分={result.total_score}, 推荐={result.should_use}, 置信度={result.confidence_score:.2f}")
    print(f"  ✅ 评估器架构测试通过")


def test_performance_optimizer():
    """测试性能优化器"""
    print("\n🧪 测试性能优化器...")
    
    # 创建性能优化器
    optimizer = create_performance_optimizer(batch_size=2, enable_cache=True)
    
    # 创建测试策略列表
    test_policies = []
    for i in range(5):
        policy = {
            "name": f"TEST_POLICY_{i}",
            "srcintf": ["wan1"],
            "dstintf": ["dmz"],
            "srcaddr": ["all"],
            "dstaddr": ["WEB_SERVER_VIP"],
            "service": ["HTTP", "HTTPS"],
            "nat": "enable",
            "ippool": "enable" if i % 2 == 0 else "disable",
            "poolname": ["**************"] if i % 2 == 0 else []
        }
        test_policies.append(policy)
    
    # VIP配置
    vip_configs = {
        "WEB_SERVER_VIP": {
            "name": "WEB_SERVER_VIP",
            "extip": "**************",
            "mappedip": "**************"
        }
    }
    
    # 创建评估上下文
    context = EvaluationContext(ntos_version="R11", threshold=65)
    
    # 执行批量评估
    start_time = time.time()
    results = optimizer.batch_evaluate_policies(test_policies, vip_configs, context)
    end_time = time.time()
    
    # 验证结果
    assert len(results) == len(test_policies), f"结果数量不匹配: 期望{len(test_policies)}, 实际{len(results)}"
    
    batch_time = end_time - start_time
    print(f"  ✅ 批量评估完成: {len(results)}个策略, 耗时{batch_time:.3f}秒")
    
    # 测试缓存清除
    optimizer.clear_cache()
    print(f"  ✅ 缓存清除完成")
    
    print(f"  ✅ 性能优化器测试通过")


def test_enhanced_logging():
    """测试增强的日志记录"""
    print("\n🧪 测试增强的日志记录...")
    
    # 记录评估开始
    policy_name = "TEST_LOGGING_POLICY"
    policy_details = {"type": "test", "complexity": "simple"}
    
    twice_nat44_logger.log_evaluation_start(policy_name, policy_details)
    
    # 记录维度评分
    twice_nat44_logger.log_dimension_score(
        policy_name, "vip_count", 85.0, "单个VIP配置", {"vip_count": 1}
    )
    
    # 记录IP池验证
    twice_nat44_logger.log_pool_validation(
        policy_name, ["**************"], {"valid": True, "format": "ip_address"}
    )
    
    # 记录警告
    twice_nat44_logger.log_warning(
        policy_name, "测试警告消息", {"warning_type": "test"}
    )
    
    # 记录错误
    try:
        raise ValueError("测试错误")
    except Exception as e:
        twice_nat44_logger.log_error(
            policy_name, ErrorCategory.EVALUATION_ERROR, "测试错误处理", e
        )
    
    # 记录评估结果
    result_details = {
        "total_score": 85.0,
        "should_use": True,
        "confidence": 0.85
    }
    twice_nat44_logger.log_evaluation_result(policy_name, result_details, 0.15)
    
    # 获取错误摘要
    error_summary = twice_nat44_logger.get_error_summary()
    assert error_summary["total_errors"] >= 1, "错误摘要中应该有至少1个错误"
    assert error_summary["total_warnings"] >= 1, "错误摘要中应该有至少1个警告"
    
    print(f"  ✅ 错误摘要: {error_summary['total_errors']}个错误, {error_summary['total_warnings']}个警告")
    
    # 获取性能摘要
    performance_summary = twice_nat44_logger.get_performance_summary()
    print(f"  ✅ 性能摘要: {performance_summary.get('total_operations', 0)}个操作")
    
    print(f"  ✅ 增强日志记录测试通过")


def test_factory_functions():
    """测试工厂函数"""
    print("\n🧪 测试工厂函数...")
    
    # 测试评估器工厂函数
    evaluator = create_twice_nat44_evaluator(
        threshold=70,
        ntos_version="R11",
        available_pools=["TEST_POOL"]
    )
    
    assert evaluator.context.threshold == 70, "阈值设置错误"
    assert evaluator.context.ntos_version == "R11", "NTOS版本设置错误"
    assert "TEST_POOL" in evaluator.context.available_pools, "可用池设置错误"
    
    print(f"  ✅ 评估器工厂函数测试通过")
    
    # 测试性能优化器工厂函数
    optimizer = create_performance_optimizer(batch_size=50, enable_cache=False)
    
    assert optimizer.batch_size == 50, "批处理大小设置错误"
    assert optimizer.enable_cache == False, "缓存设置错误"
    
    print(f"  ✅ 性能优化器工厂函数测试通过")
    
    print(f"  ✅ 工厂函数测试通过")


def main():
    """主测试函数"""
    print("🚀 开始twice-nat44增强版本测试\n")
    
    try:
        # 执行各项测试
        test_ip_pool_validator()
        test_evaluator_architecture()
        test_performance_optimizer()
        test_enhanced_logging()
        test_factory_functions()
        
        # 保存详细日志
        twice_nat44_logger.save_detailed_log()
        
        print("\n🎉 所有测试完成！")
        print("\n📊 测试总结:")
        print("  ✅ IP池验证器重构成功")
        print("  ✅ 评估器架构优化完成")
        print("  ✅ 性能优化功能正常")
        print("  ✅ 增强日志记录工作正常")
        print("  ✅ 工厂函数设计合理")
        
        # 显示性能摘要
        performance_summary = twice_nat44_logger.get_performance_summary()
        print(f"\n⏱️  性能统计:")
        print(f"  • 总操作数: {performance_summary.get('total_operations', 0)}")
        print(f"  • 会话时长: {performance_summary.get('session_duration', 0):.3f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
