package i18n

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"github.com/BurntSushi/toml"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
)

var (
	bundle             *i18n.Bundle
	defaultLanguage    = language.MustParse("zh-CN")
	supportedLanguages = []language.Tag{
		language.MustParse("zh-CN"),
		language.MustParse("en-US"),
	}
	once              sync.Once
	bundleInitialized bool
)

// 尝试从多个位置加载语言文件
func loadMessageFile(bundle *i18n.Bundle, filename string) error {
	// 获取可执行文件所在目录
	execPath, err := os.Executable()
	if err == nil {
		execDir := filepath.Dir(execPath)
		// 尝试可执行文件目录下的路径
		execLocalePath := filepath.Join(execDir, "locales", filename)
		if _, err := os.Stat(execLocalePath); err == nil {
			if _, loadErr := bundle.LoadMessageFile(execLocalePath); loadErr == nil {
				return nil
			}
		}

		// 直接在可执行文件目录下查找
		execFilePath := filepath.Join(execDir, filename)
		if _, err := os.Stat(execFilePath); err == nil {
			if _, loadErr := bundle.LoadMessageFile(execFilePath); loadErr == nil {
				return nil
			}
		}
	}

	// 尝试的路径列表
	paths := []string{
		filename,                                 // 直接路径
		filepath.Join("locales", filename),       // locales/filename
		filepath.Join("apis/locales", filename),  // apis/locales/filename
		filepath.Join("../locales", filename),    // ../locales/filename
		filepath.Join("../../locales", filename), // ../../locales/filename
		"/app/locales/" + filename,               // Docker容器中的路径
	}

	// 尝试每个路径
	var lastErr error
	for _, path := range paths {
		fmt.Println("尝试加载语言文件: ", path)
		if _, err := os.Stat(path); err == nil {
			if _, loadErr := bundle.LoadMessageFile(path); loadErr == nil {
				return nil
			} else {
				lastErr = loadErr
			}
		} else {
			lastErr = err
		}
	}

	return fmt.Errorf("无法找到语言文件 %s: %v", filename, lastErr)
}

// 初始化国际化束
func Init() {
	once.Do(func() {
		bundle = i18n.NewBundle(defaultLanguage)
		bundle.RegisterUnmarshalFunc("toml", toml.Unmarshal)

		// 加载语言文件
		err1 := loadMessageFile(bundle, "active.zh-CN.toml")
		err2 := loadMessageFile(bundle, "active.en-US.toml")

		if err1 != nil || err2 != nil {
			fmt.Printf("警告: 加载语言文件时出错: %v, %v\n", err1, err2)
		}

		bundleInitialized = true
	})
}

// 获取翻译器
func NewLocalizer(lang string) *i18n.Localizer {
	if !bundleInitialized {
		Init()
	}

	return i18n.NewLocalizer(bundle, lang)
}

// 翻译文本
func Translate(lang, messageID string, templateData map[string]interface{}) string {
	localizer := NewLocalizer(lang)
	msg, err := localizer.Localize(&i18n.LocalizeConfig{
		MessageID:    messageID,
		TemplateData: templateData,
	})

	if err != nil {
		return messageID // 如果没找到翻译，返回原始ID
	}

	return msg
}

// GetDefaultLang 获取默认语言
func GetDefaultLang() string {
	return "zh-CN"
}

// IsSupportedLang 检查语言是否支持
func IsSupportedLang(lang string) bool {
	tag, err := language.Parse(lang)
	if err != nil {
		return false
	}

	for _, supported := range supportedLanguages {
		if tag == supported {
			return true
		}
	}

	return false
}

// GetMatchingLang 根据Accept-Language获取最匹配的支持语言
func GetMatchingLang(acceptLang string) string {
	matcher := language.NewMatcher(supportedLanguages)
	tags, _, _ := language.ParseAcceptLanguage(acceptLang)

	if len(tags) == 0 {
		return "zh-CN"
	}

	tag, _, _ := matcher.Match(tags...)
	return tag.String()
}
