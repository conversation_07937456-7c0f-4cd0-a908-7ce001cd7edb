{"architecture": {"version": "2.0", "default_mode": "new", "fallback_enabled": true, "force_new_architecture": false, "performance_monitoring": true}, "conversion": {"prefer_new_pipeline": true, "enable_caching": true, "enable_performance_metrics": true, "yang_validation": true}, "logging": {"architecture_selection": true, "performance_metrics": true, "pipeline_stages": true}, "features": {"fortigate_enhanced_pipeline": true, "xml_template_integration": true, "yang_validation_stage": true, "error_recovery": true}}