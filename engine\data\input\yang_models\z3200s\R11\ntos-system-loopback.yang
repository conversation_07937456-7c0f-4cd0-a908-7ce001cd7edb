module ntos-system-loopback {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system-loopback";
  prefix ntos-system-loopback;

  import ntos {
    prefix ntos;
  }
  import ntos-if-types {
    prefix ntos-if;
  }
  import ntos-ip {
    prefix ntos-ip;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS system loopback interfaces.";

  revision 2018-11-07 {
    description
      "Initial version.";
    reference "";
  }

  identity system-loopback {
    base ntos-types:INTERFACE_TYPE;
    description
      "System loopback interface.";
  }

  augment "/ntos:state/ntos:vrf/ntos-interface:interface" {
    description
      "Network system-loopback operational state data.";

    list system-loopback {
      key "name";
      description
        "The list of system-loopback interfaces on the device.";

      leaf name {
        type ntos-types:ifname;
        description
          "The name of the interface.";
      }

      leaf mtu {
        type uint32;
        description
          "Set the max transmission unit size in octets.";
      }

      leaf promiscuous {
        type boolean;
        description
          "Set promiscuous mode.";
      }
      uses ntos-if:interface-common-config;
      uses ntos-ip:ntos-ipv4-state;
      uses ntos-ip:ntos-ipv6-state;
      uses ntos-if:interface-common-state;
      uses ntos-if:interface-counters-state;
    }
  }
}
