module ntos-url-filter {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:url-filter";
  prefix ntos-url-filter;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";

  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";

  description
    "Ruijie NTOS URL filter module.";

  revision 2023-04-10 {
    description "Added signature and internationalization.";
    reference "";
  }

  revision 2022-12-16 {
    description "Initial revision.";
    reference "";
  }

  identity url-filter {
    base ntos-types:SERVICE_LOG_ID;
    description "The URL filter service.";
  }

  typedef url-filter-action {
    type enumeration {
      enum alert {
        description "Indicate the type of alert.";
      }
      enum allow {
        description "Indicate the type of allow.";
      }
      enum block {
        description "Indicate the type of block.";
      }
    }
    description "The action type of the URL filter profile.";
  }

  typedef url-refer-type {
    type enumeration {
      enum security-policy {
        description "Indicate the type of security-policy.";
      }
      enum sim-security-policy {
        description "Indicate the type of sim-security-policy.";
      }
      enum url-filter {
        description "Indicate the type of the URL filter.";
      }
      enum app-route {
        description "Indicate the type of app route.";
      }
      enum content-audit-whitelist {
        description "Indicate the type of content audit whitelist.";
      }
      enum flow-control {
        description "Indicate the type of flow control policy.";
      }
      enum url-blacklist {
        description "Indicate the type of url filter blacklist.";
      }
    }
    description "The reference type of the URL.";
  }

  typedef url-category-type {
    type enumeration {
      enum user-defined {
        description "Indicate user-defined type.";
      }
      enum pre-defined {
        description "Indicate pre-defined type.";
      }
    }
    description "The category type of the URL.";
  }

  grouping category-list {
    description "The URL category list.";
    container url-category {
      list user-defined {
        description "The list of the user-defined URL category.";
        key "name";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of the URL category.";
        }
        leaf name-i18n {
          type ntos-types:ntos-obj-name-type;
          config false;
          description "The showed category name.";
        }
      }
      list pre-defined {
        description "The list of the pre-defined URL category.";
        key "name";
        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of the URL category.";
        }
        leaf name-i18n {
          type ntos-types:ntos-obj-name-type;
          config false;
          description "The showed category name.";
        }
      }
    }
  }

  grouping grp-refer {
    description "The grouping of the URL reference.";
    list refer {
      key "refer-type";
      config false;
      description "The reference list.";

      leaf refer-type {
        type url-refer-type;
        description "Indicate the type of reference.";
      }

      leaf-list id {
        type uint32;
        description "The objection id.";
      }
    }
  }

  grouping grp-category-detail {
    description "The grouping of the URL subcategory.";

    list subcategory {
      key "name";
      description "The URL subcategory.";

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description "The name of the URL subcategory.";
      }

      leaf name-i18n {
        type ntos-types:ntos-obj-name-type;
        config false;
        description "The showed subcategory name.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description "The description of the URL subcategory.";
      }

      leaf url-num {
        type uint32;
        config false;
        description "Total number of URLs in category.";
      }

      leaf-list url {
        type string {
          pattern "[^`!@$(){};,'\\\\<>|#]*" {
            error-message "Can not include character: `^$!@(){};,'<>\\|#";
          }
        }
        must 'not(starts-with(string(.),"https://")) and not(starts-with(string(.),"http://"))' {
          error-message "Can not start with http(s)://.";
        }
        description "The specific URL.";
      }

      uses grp-refer;
    }
  }

  grouping grp-input-leaf-rpc {
    description "The grouping of input leaf node.";

    leaf vrf {
      type ntos:vrf-name;
      description "The specific vrf.";
    }

    leaf start {
      type uint32;
      description "The index of page start.";
    }

    leaf end {
      type uint32;
      must "current() >= ../start" {
          error-message "The end value must be larger than the start value.";
      }
      description "The index of page end.";
    }
  }

  grouping grp-filter-subcategory {
    description "The grouping of the subcategory action in the URL filter profile.";

    leaf all-action {
      type url-filter-action;
      config false;
      description "Indicate the action of all category.";
    }

    list subcategory {
      key "name";
      description "The name of subcategory.";
      ntos-ext:nc-cli-one-liner;

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description "The subcategory name.";
      }

      leaf name-i18n {
        type ntos-types:ntos-obj-name-type;
        config false;
        description "The showed subcategory name.";
      }

      leaf action {
        type url-filter-action;
        mandatory true;
        description "The action of subcategory.";
      }
    }
  }

  grouping grp-filter {
    description "The grouping of the URL filter.";

    leaf name {
      type ntos-types:ntos-obj-name-type;
      description "The name of the URL filter profile.";
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      description "The description of the URL filter profile.";
    }

    leaf unknown-category-action {
      type url-filter-action;
      default allow;
      description "The action of the unknown category.";
    }

    container whitelist {
      description "Indicate the whitelist of the URL filter profile.";
      leaf-list url {
        type string {
          pattern "[^`!@$(){};,'\\\\<>|#]*" {
            error-message "Can not include character: `^$!@(){};,'<>\\|#";
          }
        }
        must 'not(starts-with(string(.),"https://")) and not(starts-with(string(.),"http://"))' {
          error-message "Can not start with http(s)://.";
        }
        description "The specific URL of whitelist.";
      }
    }

    container blacklist {
      description "Indicate the blacklist of the URL filter profile.";
      leaf-list url {
        type string {
          pattern "[^`!@$(){};,'\\\\<>|#]*" {
            error-message "Can not include character: `^$!@(){};,'<>\\|#";
          }
        }
        must 'not(starts-with(string(.),"https://")) and not(starts-with(string(.),"http://"))' {
          error-message "Can not start with http(s)://.";
        }
        description "The specific URL of blacklist.";
      }
    }

    container category {
      description "Indicate the category configuration of the URL filter profile.";

      container user-defined {
        description "Indicate the action of user-defined category.";
        uses grp-filter-subcategory;
      }

      container pre-defined {
        description "Indicate the action of pre-defined category.";
        uses grp-filter-subcategory;
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description "The configuration of the URL filter.";

    container url-category {
      description "The configuration of the URL category.";
      container user-defined {
        description "The configuration of the user-defined URL category.";
        uses grp-category-detail;
      }

      leaf cloud-search-enabled {
        type boolean;
        description "The configuration of cloud-search-enabled";
      }
    }

    list url-filter {
      key "name";
      description "Indicate the URL filter profile.";
      uses grp-filter;
    }

    container url-blacklist {
      description "The blacklist of URL category.";
      uses category-list;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description "The state of the URL filter.";

    container url-category {
      config false;
      description "The state of the URL category.";
      container user-defined {
        description "The state of the user-defined URL category.";
        uses grp-category-detail;
      }

      leaf cloud-search-enabled {
        config false;
        type boolean;
        description "The state of cloud-search-enabled";
      }
    }

    list url-filter {
      config false;
      description "The state of the URL filter profile.";
      uses grp-filter;
    }

    container url-blacklist {
      description "The blacklist state of URL category.";
      uses category-list;
    }
  }

  rpc url-category-show {
    description "Show the URL category.";

    input {
      leaf name {
        type string;
        description "The searched subcategory name.";
        ntos-ext:nc-cli-group "subcommand1";
      }

      leaf filter {
        type string;
        description "The searched subcategory filter name.";
        ntos-ext:nc-cli-group "subcommand1";
      }

      leaf primary-name {
        type string;
        description "The primary category name.";
      }

      leaf type {
        type url-category-type;
        default user-defined;
        description "The searched category type.";
      }

      uses grp-input-leaf-rpc;
    }

    output {
      leaf category-num {
        type uint32;
        description "The total number of the URL category.";
      }

      list category {
        description "Indicate the URL category.";

        leaf primary-name {
          type ntos-types:ntos-obj-name-type;
          description "The primary category name.";
        }

        leaf primary-name-i18n {
          type ntos-types:ntos-obj-name-type;
          description "The showed primary name.";
        }

        uses grp-category-detail;
      }
    }
    ntos-ext:nc-cli-show "url-category";
  }

  rpc url-category-search {
    description "Search the URL category information.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }

      leaf url {
        type string {
          pattern "[^`!@$(){};,'\\\\<>|]*" {
            error-message "Can not include character: `^$!@(){};,'<>\\|";
          }
        }
        mandatory true;
        description "The specific URL.";
      }
    }

    output {
      list category {
        description "Indicate the URL category information.";

        leaf type {
          type url-category-type;
          description "The category type.";
        }

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of subcategory.";
        }
      }
    }
    ntos-ext:nc-cli-show "url-category search";
  }

  rpc url-category-show-url {
    description "Show the specific URL of the catetory.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }

      leaf name {
        type ntos-types:ntos-obj-name-type;
        description "The searched subcategory name.";
      }
    }

    output {
      leaf num {
        type uint32;
        description "The total number of the URL.";
      }

      leaf-list url {
        type string;
        description "The specific URL.";
      }
    }
    ntos-ext:nc-cli-show "url-category url";
  }

  rpc url-filter-show {
    description "Show the URL filter profile.";

    input {
      leaf profile-type {
        type url-category-type;
        default user-defined;
        description "The type of url-filter profile.";
      }

      leaf name {
        type string;
        description "The searched name of url-filter.";
        ntos-ext:nc-cli-group "subcommand1";
      }

      leaf filter {
        type string;
        description "The searched filter name of url-filter.";
        ntos-ext:nc-cli-group "subcommand1";
      }

      uses grp-input-leaf-rpc;
    }

    output {
      leaf num {
        type uint32;
        description "The total number of the URL filter profile.";
      }

      list profile {
        key "name";
        description "Indicate the URL filter profile.";

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of the URL filter profile.";
        }

        leaf id {
          type uint32;
          description "The id of the URL filter profile.";
        }

        leaf description {
          type ntos-types:ntos-obj-description-type;
          description "The description of the URL filter profile.";
        }

        leaf unknown-category-action {
          type url-filter-action;
          description "The action of the unknown category.";
        }

        container whitelist {
          description "Indicate the whitelist of the URL filter profile.";
          leaf url-num {
            type uint32;
            description "Total number of URLs in whitelist.";
          }
          leaf-list url {
            type string;
            description "The specific URL of whitelist.";
          }
        }

        container blacklist {
          description "Indicate the blacklist of the URL filter profile.";
          leaf url-num {
            type uint32;
            description "Total number of URLs in blacklist.";
          }
          leaf-list url {
            type string;
            description "The specific URL of blacklist.";
          }
        }

        container category {
          description "Indicate the category configuration of the URL filter profile.";

          container user-defined {
            description "Indicate the action of user-defined category.";
            uses grp-filter-subcategory;
          }

          list pre-defined {
            key "category";
            description "Indicate the action of pre-defined category.";

            leaf category {
              type ntos-types:ntos-obj-name-type;
              description "Indicate the primary name of the URL category.";
            }
            uses grp-filter-subcategory;
          }
        }
        uses grp-refer;
      }
    }
    ntos-ext:nc-cli-show "url-filter";
  }

  rpc url-filter-show-url {
    description "Show the specific URL of the whitelist or blacklist catetory.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }

      leaf name {
        type string;
        description "The searched profile name.";
      }

      leaf type {
        type enumeration {
          enum whitelist {
            description "Indicate whitelist type.";
          }
          enum blacklist {
            description "Indicate blacklist type.";
          }
        }
        mandatory true;
        description "Blacklist or whitelist.";
      }
    }

    output {
      leaf num {
        type uint32;
        description "The total number of the URL.";
      }

      leaf-list url {
        type string;
        description "The specific URL.";
      }
    }
    ntos-ext:nc-cli-show "url-filter url";
  }

  rpc url-filter-show-all-category {
    description "Show all category for url-filter.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }
    }

    output {
      container user-defined {
        description "Indicate the action of user-defined category.";
        uses grp-filter-subcategory;
      }

      list pre-defined {
        key "category";
        description "Indicate the action of pre-defined category.";

        leaf category {
          type ntos-types:ntos-obj-name-type;
          description "Indicate the primary name of the URL category.";
        }
        uses grp-filter-subcategory;
      }
    }
    ntos-ext:nc-cli-show "url-filter all-category";
  }

  rpc url-filter-name {
    description "Show the name of the URL filter.";
    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }

      leaf id-list {
        type string;
        description "Indicate the ID list of policy.";
      }
    }

    output {
      list filter-name {
        description "The URL filter name list.";

        leaf id {
          type uint32;
          description "The searched id.";
        }

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description "The name of the URL profile.";
        }
      }
    }
    ntos-ext:nc-cli-show "url-filter get-name";
  }

  grouping grp-signature-state {
    description "The grouping of URL signature state.";
      leaf err-code {
        type uint32;
        description "Error code.";
      }

      leaf buf {
        type string;
        description "Error code information.";
      }

  }

  rpc url-signature-update {
    description "Update URL category signature database.";

    input {
      leaf filepath {
        type string;
        description "Path of the update file.";
      }
    }

    output {
      uses grp-signature-state;
    }
    ntos-ext:nc-cli-cmd "url signature-update";
  }

  rpc url-signature-state {
    description "Show URL signature state.";

    output {
      uses grp-signature-state;
    }
    ntos-ext:nc-cli-show "url signature-state";
  }


  rpc url-signature-version {
    description "Show URL signature version.";

    output {
      leaf version {
        type string;
        description "Signature version.";
      }
    }

    ntos-ext:nc-cli-show "url signature-version";
  }

  rpc url-cloud-search-state {
    description "Show status of cloud-search";

    output {
      leaf cloud-search-enabled {
        type boolean;
        description "Status of cloud-search-enabled.";
      }

      leaf cloud-search-support {
        type boolean;
        description "Status of cloud-search-support.";
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description "Description of cloud-search notification.";
      }

      leaf description-i18n {
        type ntos-types:ntos-obj-description-type;
        description "Description of cloud-search notification.";
      }
    }

    ntos-ext:nc-cli-show "url cloud-search-state";
  }

  rpc url-category-blacklist-show {
    description "Show blacklist URL category.";
    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }

      leaf name {
        type string;
        description "The searched subcategory name.";
        ntos-ext:nc-cli-group "subcommand1";
      }

      leaf filter {
        type string;
        description "The searched subcategory filter name.";
        ntos-ext:nc-cli-group "subcommand1";
      }

      leaf type {
        type url-category-type;
        description "The searched category type.";
      }
    }
    output {
      leaf enabled {
        description "The status of enabled.";
        type boolean;
      }
      uses category-list;
    }
    ntos-ext:nc-cli-show "url-blacklist";
  }
}
