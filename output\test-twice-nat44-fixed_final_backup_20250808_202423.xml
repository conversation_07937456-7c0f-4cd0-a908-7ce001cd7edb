<config xmlns="urn:ruijie:ntos">
  <vrf>
    <name>main</name>
    <aaa xmlns="urn:ruijie:ntos:params:xml:ns:yang:aaad">
      <enabled>true</enabled>
      <domain-enabled>true</domain-enabled>
      <domain>
        <name>default</name>
        <authentication>
          <sslvpn>
            <method>default</method>
            <enabled>true</enabled>
          </sslvpn>
          <webauth>
            <method>default</method>
            <enabled>true</enabled>
          </webauth>
        </authentication>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
        <auto-create-group>true</auto-create-group>
      </domain>
      <domain>
        <name>vpn</name>
        <authentication>
          <sslvpn>
            <method>vpn</method>
            <enabled>true</enabled>
          </sslvpn>
          <vpn>
            <method>vpn</method>
            <enabled>true</enabled>
          </vpn>
        </authentication>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
        <auto-create-group>true</auto-create-group>
      </domain>
      <domain>
        <name>pppoe</name>
        <authentication>
          <pppoe>
            <method>pppoe</method>
            <enabled>true</enabled>
          </pppoe>
        </authentication>
        <enabled>true</enabled>
        <username-format>
          <without-domain/>
        </username-format>
        <auto-create-group>true</auto-create-group>
      </domain>
      <authentication>
        <sslvpn>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </sslvpn>
        <sslvpn>
          <name>vpn</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </sslvpn>
        <webauth>
          <name>default</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </webauth>
        <vpn>
          <name>vpn</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </vpn>
        <pppoe>
          <name>pppoe</name>
          <subs/>
          <auth-order>only-subs</auth-order>
        </pppoe>
      </authentication>
      <accounting>
        <update>
          <periodic>5</periodic>
          <enabled>false</enabled>
        </update>
      </accounting>
    </aaa>
    <app-behavior-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:app-behavior-control">
      <qq-control>
        <control-mode>blacklist</control-mode>
      </qq-control>
    </app-behavior-control>
    <app-parse-mgmt xmlns="urn:ruijie:ntos:params:xml:ns:yang:app-parse-mgmt">
      <overload-protection>
        <enabled>false</enabled>
        <action>bypass</action>
      </overload-protection>
      <http>
        <decompress-length>2048</decompress-length>
      </http>
    </app-parse-mgmt>
    <appid xmlns="urn:ruijie:ntos:params:xml:ns:yang:appid">
      <mode>dynamic-identify</mode>
    </appid>
    <arp xmlns="urn:ruijie:ntos:params:xml:ns:yang:arp">
      <proxy-enabled>false</proxy-enabled>
      <trusted>
        <nud-probe>
          <enabled>false</enabled>
        </nud-probe>
      </trusted>
      <gratuitous-send>
        <enabled>false</enabled>
        <interval>30</interval>
      </gratuitous-send>
    </arp>
    <bridge xmlns="urn:ruijie:ntos:params:xml:ns:yang:bridge">
      <fdb>
        <aging>300</aging>
      </fdb>
    </bridge>
    <collab-disposal xmlns="urn:ruijie:ntos:params:xml:ns:yang:collab-disposal">
      <enabled>false</enabled>
      <identity-system>none</identity-system>
    </collab-disposal>
    <dhcp xmlns="urn:ruijie:ntos:params:xml:ns:yang:dhcp">
      <server>
        <enabled>true</enabled>
        <default-lease-time>43200</default-lease-time>
        <max-lease-time>86400</max-lease-time>
        <subnet>
          <prefix>***********/24</prefix>
          <interface>Ge0/0</interface>
          <default-gateway>*************</default-gateway>
          <range>
            <start-ip>***********</start-ip>
            <end-ip>*************</end-ip>
          </range>
          <ping-check>true</ping-check>
          <default-lease-time>20</default-lease-time>
          <max-lease-time>20</max-lease-time>
          <lease-id-format>hex</lease-id-format>
          <warning-high-threshold>90</warning-high-threshold>
          <warning-low-threshold>80</warning-low-threshold>
        </subnet>
      </server>
    </dhcp>
    <dns-client xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-client">
      <ip-domain-lookup>
        <enabled>true</enabled>
      </ip-domain-lookup>
    </dns-client>
    <dns-security xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-security">
      <dns-filter>
        <dns-status>false</dns-status>
      </dns-filter>
      <dns-attack-defense>
        <attack-defense>
          <protocol>true</protocol>
          <security-vul>true</security-vul>
        </attack-defense>
        <flood-defense>
          <enable>true</enable>
        </flood-defense>
      </dns-attack-defense>
      <dns-security-cloud>
        <pdns>true</pdns>
        <online-protect>true</online-protect>
      </dns-security-cloud>
    </dns-security>
    <dns-transparent-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns-transparent-proxy">
      <enabled>false</enabled>
      <mode>mllb</mode>
    </dns-transparent-proxy>
    <file-filter xmlns="urn:ruijie:ntos:params:xml:ns:yang:file-filter">
      <global-config>
        <feature-identify-enabled>false</feature-identify-enabled>
      </global-config>
    </file-filter>
    <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
      <enabled>false</enabled>
    </flow-control>
    <ha xmlns="urn:ruijie:ntos:params:xml:ns:yang:ha">
      <mode>A-P</mode>
      <heart-interval>1000</heart-interval>
      <heart-alert-count>3</heart-alert-count>
      <gra-arp-count>5</gra-arp-count>
      <vmac-prefix>00:00:5e</vmac-prefix>
      <preempt>false</preempt>
      <preempt-delay>60</preempt-delay>
      <session-sync>true</session-sync>
      <neigh-sync>true</neigh-sync>
      <switch-link-time>1</switch-link-time>
      <auth-type>none</auth-type>
      <enabled>false</enabled>
      <log-level>
        <group>on</group>
        <adv>off</adv>
        <dbus>on</dbus>
        <monitor>off</monitor>
      </log-level>
    </ha>
    <ike xmlns="urn:ruijie:ntos:params:xml:ns:yang:ike">
      <proposal>
        <name>default</name>
        <prf>sha-256</prf>
        <life-seconds>86400</life-seconds>
        <encrypt-alg>des des3 aes-128 aes-192 aes-256</encrypt-alg>
        <hash-alg>md5 sha</hash-alg>
        <dh-group>group1 group2 group5</dh-group>
        <auth-mode>preshared-key</auth-mode>
      </proposal>
      <profile>
        <name>default</name>
      </profile>
      <profile>
        <name>temporary</name>
      </profile>
      <dpd>
        <interval>30</interval>
        <retry-interval>5</retry-interval>
      </dpd>
      <nat-traversal>
        <enabled>true</enabled>
      </nat-traversal>
      <nat>
        <keepalive>20</keepalive>
      </nat>
    </ike>
    <interface xmlns="urn:ruijie:ntos:params:xml:ns:yang:interface">
      <snmp>
        <if-usage-compute-interval>30</if-usage-compute-interval>
        <if-global-notify-enable>true</if-global-notify-enable>
      </snmp>
      <physical>
        <name>Ge0/0</name>
        <ipv4>
          <address>
            <ip>*************/24</ip>
          </address>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/1</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/2</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/3</name>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*************/32</ip>
          </address>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>wan</wanlan>
        <description>test-静态IP测试</description>
        <upload-bandwidth>
          <upload-bandwidth-value>500000</upload-bandwidth-value>
          <upload-bandwidth-unit>kbps</upload-bandwidth-unit>
        </upload-bandwidth>
        <download-bandwidth>
          <download-bandwidth-value>600000</download-bandwidth-value>
          <download-bandwidth-unit>kbps</download-bandwidth-unit>
        </download-bandwidth>
      </physical>
      <physical>
        <name>Ge0/4</name>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>true</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>3600</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>wan</wanlan>
        <description>test-dhcp</description>
        <upload-bandwidth>
          <upload-bandwidth-value>700000</upload-bandwidth-value>
          <upload-bandwidth-unit>kbps</upload-bandwidth-unit>
        </upload-bandwidth>
        <download-bandwidth>
          <download-bandwidth-value>800000</download-bandwidth-value>
          <download-bandwidth-unit>kbps</download-bandwidth-unit>
        </download-bandwidth>
      </physical>
      <physical>
        <name>Ge0/5</name>
        <ipv4>
          <enabled>true</enabled>
          <pppoe>
            <connection>
              <tunnel>1</tunnel>
              <enabled>true</enabled>
              <user>test</user>
              <password>******</password>
              <gateway>true</gateway>
              <timeout>5</timeout>
              <retries>3</retries>
              <ppp>
                <negotiation-timeout>3</negotiation-timeout>
                <lcp-echo-interval>10</lcp-echo-interval>
                <lcp-echo-retries>10</lcp-echo-retries>
              </ppp>
              <ppp-mtu>1492</ppp-mtu>
              <reverse-path>true</reverse-path>
              <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
                <enabled>false</enabled>
              </flow-control>
              <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
                <https>false</https>
                <ping>false</ping>
                <ssh>false</ssh>
              </access-control>
            </connection>
          </pppoe>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description>test-pppoe拨号</description>
      </physical>
      <physical>
        <name>Ge0/6</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/7</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/8</name>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>true</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>3600</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>Ge0/9</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/0</name>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <wanlan>lan</wanlan>
        <description/>
      </physical>
      <physical>
        <name>TenGe0/1</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/2</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <physical>
        <name>TenGe0/3</name>
        <enabled>true</enabled>
        <working-mode>route</working-mode>
        <ipv4>
          <enabled>true</enabled>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <flow-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-control">
          <enabled>false</enabled>
        </flow-control>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
          <ipv4>
            <enabled>false</enabled>
          </ipv4>
          <ipv6>
            <enabled>false</enabled>
          </ipv6>
        </ip-mac-bind>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>false</https>
          <ping>false</ping>
          <ssh>false</ssh>
        </access-control>
      </physical>
      <bridge xmlns="urn:ruijie:ntos:params:xml:ns:yang:bridge">
        <name>br0</name>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>true</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>true</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <session-source-check>dont-check</session-source-check>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </bridge>
      <vswitch xmlns="urn:ruijie:ntos:params:xml:ns:yang:vswitch">
        <name>vswitch0</name>
        <enabled>true</enabled>
        <wanlan>lan</wanlan>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>false</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>7200</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <network-stack>
          <ipv4>
            <arp-ignore>check-interface-and-subnet</arp-ignore>
          </ipv4>
        </network-stack>
        <reverse-path>true</reverse-path>
        <snooping>
          <trust>false</trust>
          <suppress>false</suppress>
        </snooping>
        <session-source-check>transparent-forward</session-source-check>
        <monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:if-mon">
          <notify-up-drop-rate-threshold>1000</notify-up-drop-rate-threshold>
          <notify-down-drop-rate-threshold>1000</notify-down-drop-rate-threshold>
          <if-notify-enable>false</if-notify-enable>
          <notify-up-usage-threshold>100</notify-up-usage-threshold>
          <notify-down-usage-threshold>100</notify-down-usage-threshold>
          <notify-up-speed-threshold>100000000</notify-up-speed-threshold>
          <notify-down-speed-threshold>100000000</notify-down-speed-threshold>
        </monitor>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vswitch>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/5.103</name>
        <description>vlan103，开启https/ping，dhcp</description>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <dhcp>
            <enabled>true</enabled>
            <timeout>60</timeout>
            <retry>30</retry>
            <select-timeout>0</select-timeout>
            <reboot>10</reboot>
            <initial-interval>10</initial-interval>
            <dhcp-lease-time>3600</dhcp-lease-time>
            <request>subnet-mask</request>
            <request>broadcast-address</request>
            <request>time-offset</request>
            <request>routers</request>
            <request>domain-name</request>
            <request>domain-search</request>
            <request>domain-name-servers</request>
            <request>host-name</request>
            <request>nis-domain</request>
            <request>nis-servers</request>
            <request>ntp-servers</request>
            <request>interface-mtu</request>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>103</vlan-id>
        <link-interface>Ge0/5</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>false</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/4.20</name>
        <description>子接口-静态</description>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <address>
            <ip>*************/24</ip>
          </address>
          <dhcp>
            <enabled>false</enabled>
          </dhcp>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>20</vlan-id>
        <link-interface>Ge0/4</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
      <vlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:vlan">
        <name>Ge0/3.50</name>
        <description>pppoe-test</description>
        <enabled>true</enabled>
        <ipv4>
          <enabled>true</enabled>
          <pppoe>
            <connection>
              <tunnel>0</tunnel>
              <enabled>true</enabled>
              <user>test2</user>
              <password>=*-#!$_puO5f8l705jnMw1g==</password>
              <gateway>true</gateway>
              <timeout>5</timeout>
              <retries>3</retries>
              <ppp>
                <negotiation-timeout>3</negotiation-timeout>
                <lcp-echo-interval>10</lcp-echo-interval>
                <lcp-echo-retries>10</lcp-echo-retries>
              </ppp>
              <ppp-mtu>1492</ppp-mtu>
            </connection>
          </pppoe>
        </ipv4>
        <ipv6>
          <enabled>false</enabled>
        </ipv6>
        <reverse-path>true</reverse-path>
        <vlan-id>50</vlan-id>
        <link-interface>Ge0/3</link-interface>
        <protocol>802.1q</protocol>
        <access-control xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
          <https>true</https>
          <ping>true</ping>
          <ssh>true</ssh>
        </access-control>
      </vlan>
    </interface>
    <ip-mac-bind xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-mac">
      <ipv4>
        <enabled>false</enabled>
        <no-match-action-drop>false</no-match-action-drop>
      </ipv4>
      <ipv6>
        <enabled>false</enabled>
        <no-match-action-drop>false</no-match-action-drop>
      </ipv6>
    </ip-mac-bind>
    <track xmlns="urn:ruijie:ntos:params:xml:ns:yang:ip-track">
      <enabled>false</enabled>
    </track>
    <ipsec xmlns="urn:ruijie:ntos:params:xml:ns:yang:ipsec">
      <anti-replay>
        <check>true</check>
        <window-size>64</window-size>
      </anti-replay>
      <df-bit>clear</df-bit>
      <prefrag>true</prefrag>
      <inbound-sp>
        <check>true</check>
      </inbound-sp>
      <spd-hash-bits>
        <src-bits>16</src-bits>
        <dst-bits>16</dst-bits>
      </spd-hash-bits>
      <hardware-crypto-offload>true</hardware-crypto-offload>
    </ipsec>
    <isp xmlns="urn:ruijie:ntos:params:xml:ns:yang:isp">
      <distance>10</distance>
    </isp>
    <lldp xmlns="urn:ruijie:ntos:params:xml:ns:yang:lldp">
      <enabled>true</enabled>
      <hello-timer>30</hello-timer>
      <tx-hold>4</tx-hold>
      <system-name>Z5100-S</system-name>
      <interface>
        <name>Ge0/0</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/1</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/2</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/3</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/4</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/5</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/6</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/7</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/8</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>Ge0/9</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/0</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/1</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/2</name>
        <enabled>true</enabled>
      </interface>
      <interface>
        <name>TenGe0/3</name>
        <enabled>true</enabled>
      </interface>
    </lldp>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <policy>
        <name>deny_all</name>
        <enabled>true</enabled>
        <action>deny</action>
        <limit>false</limit>
        <pps>600</pps>
        <description/>
      </policy>
      <policy>
        <name>limit_local</name>
        <enabled>true</enabled>
        <action>permit</action>
        <limit>true</limit>
        <pps>1500</pps>
        <description/>
      </policy>
    </local-defend>
    <misn xmlns="urn:ruijie:ntos:params:xml:ns:yang:misn">
      <enabled>true</enabled>
    </misn>
    <mllb xmlns="urn:ruijie:ntos:params:xml:ns:yang:mllb">
      <advanced-options>
        <refresh-session>false</refresh-session>
        <cache-timeout>300</cache-timeout>
        <cache-once>256</cache-once>
        <cache-disable>false</cache-disable>
        <alarm-threshold>90</alarm-threshold>
      </advanced-options>
      <all-if-switch>false</all-if-switch>
    </mllb>
    <n2n-config xmlns="urn:ruijie:ntos:params:xml:ns:yang:n2n">
      <crypto>CC20</crypto>
      <vpn-port-v4>0</vpn-port-v4>
      <vpn-port-v6>0</vpn-port-v6>
      <hang-side>false</hang-side>
      <flow-control>
        <enabled>true</enabled>
        <rate>100</rate>
        <rate-unit>kbps</rate-unit>
      </flow-control>
      <detect-policy>
        <enabled>true</enabled>
        <interval>5000</interval>
        <timeout>3000</timeout>
      </detect-policy>
    </n2n-config>
    <port-mapping xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <enabled>true</enabled>
    </port-mapping>
    <nat xmlns="urn:ruijie:ntos:params:xml:ns:yang:nat">
      <enabled>true</enabled>
      <alg>ftp sip-tcp sip-udp tftp dns-udp</alg>
      <sip-port-check>
        <enabled>true</enabled>
      </sip-port-check>
      <rule>
        <name>PORT7_to_LAN_P1</name>
        <rule_en>true</rule_en>
        <static-snat44>
          <match>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <output-address/>
            <no-pat>false</no-pat>
            <try-no-pat>true</try-no-pat>
          </translate-to>
        </static-snat44>
      </rule>
      <rule>
        <name>PORT7_to_LAN_P2</name>
        <rule_en>true</rule_en>
        <static-snat44>
          <match>
            <source-network>
              <name>none</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <output-address/>
            <no-pat>false</no-pat>
            <try-no-pat>true</try-no-pat>
          </translate-to>
        </static-snat44>
      </rule>
      <rule>
        <name>WAN_to_PORT6_P5</name>
        <rule_en>true</rule_en>
        <static-snat44>
          <match>
            <source-network>
              <name>tct-addr-net-140</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <output-address/>
            <no-pat>false</no-pat>
            <try-no-pat>true</try-no-pat>
          </translate-to>
        </static-snat44>
      </rule>
      <rule>
        <name>PORT7_to_OPT_HTTPS_P7_tct-vip-addr1</name>
        <rule_en>true</rule_en>
        <static-dnat44>
          <match>
            <dest-network>
              <name>tct-vip-addr1</name>
            </dest-network>
            <service>
              <name>https</name>
            </service>
            <time-range>
              <value>always</value>
            </time-range>
          </match>
          <translate-to>
            <ipv4-address>*********</ipv4-address>
          </translate-to>
        </static-dnat44>
      </rule>
      <rule>
        <name>TCT-ZONE_to_OPT_P8</name>
        <rule_en>true</rule_en>
        <static-snat44>
          <match>
            <source-network>
              <name>tct-addr-net-160</name>
            </source-network>
            <service>
              <name>any</name>
            </service>
            <time-range>
              <value>worker-day</value>
            </time-range>
          </match>
          <translate-to>
            <output-address/>
            <no-pat>false</no-pat>
            <try-no-pat>true</try-no-pat>
          </translate-to>
        </static-snat44>
      </rule>
    </nat>
    <netconf-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:netconf-server">
      <enabled>false</enabled>
      <idle-timeout>3600</idle-timeout>
    </netconf-server>
    <network-measure xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-measure">
      <enabled>true</enabled>
      <message-send-enabled>false</message-send-enabled>
      <service>
        <dhcp>
          <enabled>true</enabled>
        </dhcp>
        <dns>
          <enabled>true</enabled>
          <monitor-threshold>1000</monitor-threshold>
        </dns>
        <nat>
          <enabled>true</enabled>
        </nat>
        <ipsec>
          <enabled>true</enabled>
        </ipsec>
        <sslvpn>
          <enabled>true</enabled>
        </sslvpn>
      </service>
      <warning-config>
        <link>
          <high-load>
            <enabled>true</enabled>
            <duration>120</duration>
            <threshold>95</threshold>
          </high-load>
          <suspect-disconnected>
            <enabled>true</enabled>
            <duration>300</duration>
            <threshold>10000</threshold>
          </suspect-disconnected>
          <change-to-down>
            <enabled>true</enabled>
          </change-to-down>
        </link>
        <app>
          <change-to-poor>
            <enabled>true</enabled>
          </change-to-poor>
        </app>
        <user>
          <wan-detect-failed>
            <enabled>true</enabled>
          </wan-detect-failed>
          <lan-detect-failed>
            <enabled>true</enabled>
          </lan-detect-failed>
          <change-to-poor>
            <enabled>true</enabled>
          </change-to-poor>
        </user>
        <dhcp-server>
          <ip-conflict>
            <enabled>true</enabled>
          </ip-conflict>
          <high-load>
            <enabled>true</enabled>
            <threshold>95</threshold>
          </high-load>
        </dhcp-server>
        <dns>
          <unuseable>
            <enabled>true</enabled>
          </unuseable>
        </dns>
        <nat>
          <hit-fail>
            <enabled>false</enabled>
          </hit-fail>
          <hit-miss>
            <enabled>false</enabled>
          </hit-miss>
        </nat>
        <ipsec>
          <disconnected>
            <enabled>true</enabled>
            <duration>120</duration>
          </disconnected>
        </ipsec>
        <sslvpn>
          <lost>
            <enabled>true</enabled>
            <threshold>10</threshold>
          </lost>
          <license>
            <enabled>true</enabled>
            <threshold>20</threshold>
          </license>
        </sslvpn>
      </warning-config>
      <flow-limit>
        <enabled>true</enabled>
        <up>80</up>
        <down>80</down>
      </flow-limit>
    </network-measure>
    <nfp xmlns="urn:ruijie:ntos:params:xml:ns:yang:nfp">
      <session>
        <state-inspection>
          <tcp>true</tcp>
          <tcp-mode>standard</tcp-mode>
          <icmp>true</icmp>
        </state-inspection>
      </session>
      <tunnel-inspection>
        <bridge>
          <VLAN>true</VLAN>
          <PPPoE>false</PPPoE>
          <GRE>false</GRE>
          <L2TPv2>false</L2TPv2>
        </bridge>
      </tunnel-inspection>
    </nfp>
    <ntp xmlns="urn:ruijie:ntos:params:xml:ns:yang:ntp">
      <enabled>true</enabled>
      <server>
        <address>ntp.ntsc.ac.cn</address>
        <version>4</version>
        <association-type>SERVER</association-type>
        <iburst>false</iburst>
        <prefer>false</prefer>
      </server>
      <server>
        <address>ntp1.aliyun.com</address>
        <version>4</version>
        <association-type>SERVER</association-type>
        <iburst>false</iburst>
        <prefer>false</prefer>
      </server>
    </ntp>
    <wba-portal xmlns="urn:ruijie:ntos:params:xml:ns:yang:portal">
      <enabled>false</enabled>
      <port>8081</port>
      <ssl-enabled>false</ssl-enabled>
      <redirection-mode>no-redirection</redirection-mode>
    </wba-portal>
    <portal-languages xmlns="urn:ruijie:ntos:params:xml:ns:yang:portal">
      <language>
        <name>zh_CN</name>
        <display-name>简体中文</display-name>
        <package-name>zh_CN.json</package-name>
      </language>
      <language>
        <name>en_US</name>
        <display-name>English</display-name>
        <package-name>en_US.json</package-name>
      </language>
      <language>
        <name>tr_TR</name>
        <display-name>Türkçe</display-name>
        <package-name>tr_TR.json</package-name>
      </language>
    </portal-languages>
    <pppoe xmlns="urn:ruijie:ntos:params:xml:ns:yang:pppoe">
      <multi-dial>
        <enabled>false</enabled>
      </multi-dial>
    </pppoe>
    <pppoe-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:pppoe-server">
      <enabled>false</enabled>
    </pppoe-server>
    <replacement-messages xmlns="urn:ruijie:ntos:params:xml:ns:yang:replacement-messages">
      <management>
        <cache-enable-status>true</cache-enable-status>
      </management>
    </replacement-messages>
    <reputation-center xmlns="urn:ruijie:ntos:params:xml:ns:yang:reputation-center">
      <enabled>false</enabled>
    </reputation-center>
    <security-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-defend">
      <basic-protocol-control-enabled>false</basic-protocol-control-enabled>
      <enabled>false</enabled>
    </security-defend>
    <security-zone xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-zone">
      <zone>
        <name>trust</name>
        <description>Trusted Zone</description>
        <priority>85</priority>
      </zone>
      <zone>
        <name>untrust</name>
        <description>Untrusted Zone</description>
        <priority>5</priority>
      </zone>
      <zone>
        <name>DMZ</name>
        <description>DMZ</description>
        <priority>50</priority>
      </zone>
      <zone>
        <name>tct-zone-1</name>
        <description>这个区域包含了port5</description>
        <priority>49</priority>
        <interface>
          <name>Ge0/7</name>
        </interface>
      </zone>
    </security-zone>
    <session-limit xmlns="urn:ruijie:ntos:params:xml:ns:yang:session-limit">
      <pps-limit>
        <enabled>false</enabled>
        <global-pps>0</global-pps>
      </pps-limit>
      <sps-limit>
        <enabled>false</enabled>
        <global-sps>0</global-sps>
      </sps-limit>
      <total-session>
        <enabled>false</enabled>
      </total-session>
    </session-limit>
    <ssh-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssh-server">
      <enabled>true</enabled>
      <port>22</port>
      <deny-count>3</deny-count>
      <unlock-time>60</unlock-time>
    </ssh-server>
    <ssl-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:ssl-proxy">
      <profile>
        <name>default</name>
        <description>Default Template, Traffic proxy for Internet access of users.</description>
        <outbound/>
      </profile>
      <ca-cert>
        <trust-cert>default_ca</trust-cert>
      </ca-cert>
    </ssl-proxy>
    <network-stack xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
      <ipv4>
        <arp-ignore>check-interface-and-subnet</arp-ignore>
      </ipv4>
    </network-stack>
    <threat-intelligence xmlns="urn:ruijie:ntos:params:xml:ns:yang:threat-intelligence">
      <management>
        <enable-status>false</enable-status>
        <enable-ai>false</enable-ai>
      </management>
    </threat-intelligence>
    <time-range xmlns="urn:ruijie:ntos:params:xml:ns:yang:time-range">
      <range>
        <name>any</name>
        <description>Time range of all the time.</description>
        <period>
          <start>00:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
        </period>
      </range>
      <range>
        <name>onetime-test001</name>
        <description>一次性时间范围 onetime-test001</description>
        <once>
          <start>11:05:00/2025-07-01</start>
          <end>12:05:00/2025-07-10</end>
        </once>
      </range>
      <range>
        <name>onetime-test002</name>
        <description>一次性时间范围 onetime-test002</description>
        <once>
          <start>11:06:00/2025-07-01</start>
          <end>12:06:00/2025-07-25</end>
        </once>
      </range>
      <range>
        <name>always</name>
        <description>always</description>
        <period>
          <start>00:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
        </period>
      </range>
      <range>
        <name>none</name>
        <description>none</description>
      </range>
      <range>
        <name>default-darrp-optimize</name>
        <description>default-darrp-optimize</description>
        <period>
          <start>01:00:00</start>
          <end>01:30:00</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
          <weekday>
            <key>sat</key>
          </weekday>
        </period>
      </range>
      <range>
        <name>worker-day</name>
        <description>worker-day</description>
        <period>
          <start>00:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
        </period>
      </range>
      <range>
        <name>recurring-test01</name>
        <description>recurring-test01</description>
        <period>
          <start>09:00:00</start>
          <end>23:59:00</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
        </period>
      </range>
      <range>
        <name>recurring-test02</name>
        <description>recurring-test02</description>
        <period>
          <start>23:00:00</start>
          <end>23:59:59</end>
          <weekday>
            <key>sun</key>
          </weekday>
          <weekday>
            <key>tue</key>
          </weekday>
          <weekday>
            <key>thu</key>
          </weekday>
        </period>
        <period>
          <start>00:00:00</start>
          <end>02:00:00</end>
          <weekday>
            <key>mon</key>
          </weekday>
          <weekday>
            <key>wed</key>
          </weekday>
          <weekday>
            <key>fri</key>
          </weekday>
        </period>
      </range>
    </time-range>
    <traffic-analy xmlns="urn:ruijie:ntos:params:xml:ns:yang:traffic-analy">
      <enabled>false</enabled>
    </traffic-analy>
    <upnp-proxy xmlns="urn:ruijie:ntos:params:xml:ns:yang:upnp-proxy">
      <enabled>false</enabled>
      <bind-rule>ip</bind-rule>
      <advance>
        <automatic-enrollment>
          <enabled>false</enabled>
          <registration-time>1440</registration-time>
          <logout-check-period>3</logout-check-period>
        </automatic-enrollment>
        <terminal-authorization>
          <enabled>false</enabled>
        </terminal-authorization>
        <linkage-service>
          <enabled>false</enabled>
        </linkage-service>
        <offline-detect>
          <flow-rate>0</flow-rate>
        </offline-detect>
        <scheduled-offline>
          <enabled>false</enabled>
          <time>00:00</time>
        </scheduled-offline>
        <quick-response-code-valid-time>
          <time>480</time>
        </quick-response-code-valid-time>
      </advance>
      <reserve>
        <single-ip-process>
          <interval-time>5</interval-time>
          <max-package>40</max-package>
        </single-ip-process>
        <unicast>
          <enabled>false</enabled>
        </unicast>
        <web-url-compatible>
          <enabled>false</enabled>
        </web-url-compatible>
        <map-cover-mode>
          <enabled>false</enabled>
        </map-cover-mode>
        <server-capacity>1</server-capacity>
      </reserve>
    </upnp-proxy>
    <webauth xmlns="urn:ruijie:ntos:params:xml:ns:yang:webauth">
      <authentication-options>
        <portal-authentication>
          <portal-group>
            <name>cportal</name>
            <protocol>portal</protocol>
          </portal-group>
        </portal-authentication>
      </authentication-options>
      <single-sign-on>
        <ad>
          <method>plugin</method>
        </ad>
      </single-sign-on>
    </webauth>
    <wlan xmlns="urn:ruijie:ntos:params:xml:ns:yang:wlan">
      <web-ac>
        <topology>ac-connect</topology>
      </web-ac>
      <diag>
        <enabled>false</enabled>
      </diag>
      <ac-controller>
        <ac-name>Ruijie_Ac_V0001</ac-name>
        <acctrl-trap>
          <acap-microap-ctrl>
            <enabled>false</enabled>
          </acap-microap-ctrl>
          <acap-updown-ctrl>
            <enabled>false</enabled>
          </acap-updown-ctrl>
          <acap-joinfail-ctrl>
            <enabled>false</enabled>
          </acap-joinfail-ctrl>
          <acap-decryeroreport-ctrl>
            <enabled>false</enabled>
          </acap-decryeroreport-ctrl>
          <acap-imageupdt-ctrl>
            <enabled>false</enabled>
          </acap-imageupdt-ctrl>
          <acap-timestamp-ctrl>
            <enabled>false</enabled>
          </acap-timestamp-ctrl>
          <acsta-oper-ctrl>
            <enabled>false</enabled>
          </acsta-oper-ctrl>
        </acctrl-trap>
        <ap-auth>
          <serial>
            <enabled>false</enabled>
          </serial>
          <password>
            <enabled>false</enabled>
          </password>
          <certificate>
            <enabled>false</enabled>
          </certificate>
        </ap-auth>
        <ap-priority>
          <enabled>false</enabled>
        </ap-priority>
        <bind-ap-mac>
          <enabled>false</enabled>
        </bind-ap-mac>
        <balance-group>
          <flow-balance-group>
            <base>10</base>
          </flow-balance-group>
        </balance-group>
        <sta-balance>
          <num-limit>
            <enabled>false</enabled>
          </num-limit>
        </sta-balance>
        <sta-blacklist>
          <enabled>false</enabled>
        </sta-blacklist>
        <black-white-list>
          <blacklist>
            <enabled>true</enabled>
          </blacklist>
          <whitelist>
            <enabled>false</enabled>
          </whitelist>
        </black-white-list>
        <ac-control>
          <enabled>false</enabled>
        </ac-control>
        <ap-image>
          <auto-upgrade>
            <enabled>false</enabled>
          </auto-upgrade>
        </ap-image>
        <capwap>
          <dtls>
            <enabled>true</enabled>
          </dtls>
        </capwap>
      </ac-controller>
      <wids>
        <countermeasures>
          <enabled>false</enabled>
          <rssi-min>25</rssi-min>
          <channel-match>false</channel-match>
        </countermeasures>
      </wids>
    </wlan>
    <network-obj xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-obj">
      <address-set>
        <name>tct-vip-addr1</name>
        <ip-set>
          <ip-address>***************/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>SSLVPN_TUNNEL_ADDR1</name>
        <ip-set>
          <ip-address>**************-**************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>测试</name>
        <ip-set>
          <ip-address>*******/32</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>tct-addr-net-150</name>
        <description>150.0.0.0-24子网，any接口</description>
        <ip-set>
          <ip-address>150.0.0.0/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>tct-addr-net-160</name>
        <description>160.0.0.0-24子网，any接口</description>
        <ip-set>
          <ip-address>160.0.0.0/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>tct-addr-net-140</name>
        <description>140.0.0.0-24子网, any接口</description>
        <ip-set>
          <ip-address>140.0.0.0/24</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>tct-addr-net-170-1-10</name>
        <description>*********-*********0范围，any接口</description>
        <ip-set>
          <ip-address>*********-*********0</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>address-test01</name>
        <description>地址对象测试</description>
        <ip-set>
          <ip-address>************-**************</ip-address>
        </ip-set>
      </address-set>
      <address-set>
        <name>test002</name>
        <ip-set>
          <ip-address>*************/24</ip-address>
        </ip-set>
      </address-set>
      <address-group>
        <name>adg-test1</name>
        <address-set>
          <name>address-test01</name>
        </address-set>
        <address-set>
          <name>test002</name>
        </address-set>
      </address-group>
      <address-group>
        <name>adg-test2</name>
        <address-set>
          <name>tct-addr-net-150</name>
        </address-set>
      </address-group>
      <address-group>
        <name>adg-test3</name>
        <address-set>
          <name>测试</name>
        </address-set>
        <address-set>
          <name>tct-addr-net-150</name>
        </address-set>
        <address-set>
          <name>tct-addr-net-160</name>
        </address-set>
        <address-set>
          <name>tct-addr-net-140</name>
        </address-set>
        <address-set>
          <name>tct-addr-net-170-1-10</name>
        </address-set>
        <address-set>
          <name>address-test01</name>
        </address-set>
        <address-set>
          <name>test002</name>
        </address-set>
      </address-group>
    </network-obj>
    <service-obj xmlns="urn:ruijie:ntos:params:xml:ns:yang:service-obj">
      <service-set>
        <name>FTP_GET</name>
        <tcp>
          <dest-port>21</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>FTP_PUT</name>
        <tcp>
          <dest-port>21</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>ALL_TCP</name>
        <tcp>
          <dest-port>1-65535</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>ALL_UDP</name>
        <udp>
          <dest-port>1-65535</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>ALL_ICMP6</name>
        <protocol-id>1</protocol-id>
      </service-set>
      <service-set>
        <name>GRE</name>
        <protocol-id>47</protocol-id>
      </service-set>
      <service-set>
        <name>AH</name>
        <protocol-id>51</protocol-id>
      </service-set>
      <service-set>
        <name>ESP</name>
        <protocol-id>50</protocol-id>
      </service-set>
      <service-set>
        <name>AOL</name>
        <tcp>
          <dest-port>5190-5194</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>BGP</name>
        <tcp>
          <dest-port>179</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>FINGER</name>
        <tcp>
          <dest-port>79</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>GOPHER</name>
        <tcp>
          <dest-port>70</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>H323</name>
        <tcp>
          <dest-port>1720,1503</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>1719</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>IKE</name>
        <udp>
          <dest-port>500,4500</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>IMAPS</name>
        <tcp>
          <dest-port>993</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>Internet-Locator-Service</name>
        <tcp>
          <dest-port>389</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>IRC</name>
        <tcp>
          <dest-port>6660-6669</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>L2TP</name>
        <tcp>
          <dest-port>1701</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>1701</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>LDAP</name>
        <tcp>
          <dest-port>389</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NetMeeting</name>
        <tcp>
          <dest-port>1720</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NFS</name>
        <tcp>
          <dest-port>111,2049</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>111,2049</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>NNTP</name>
        <tcp>
          <dest-port>119</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>OSPF</name>
        <protocol-id>89</protocol-id>
      </service-set>
      <service-set>
        <name>PC-Anywhere</name>
        <tcp>
          <dest-port>5631</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>5632</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TIMESTAMP</name>
        <icmp>
          <type>13</type>
          <code>0</code>
        </icmp>
      </service-set>
      <service-set>
        <name>INFO_REQUEST</name>
        <icmp>
          <type>15</type>
          <code>0</code>
        </icmp>
      </service-set>
      <service-set>
        <name>INFO_ADDRESS</name>
        <icmp>
          <type>17</type>
          <code>0</code>
        </icmp>
      </service-set>
      <service-set>
        <name>ONC-RPC</name>
        <tcp>
          <dest-port>111</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>111</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>DCE-RPC</name>
        <tcp>
          <dest-port>135</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>135</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>POP3S</name>
        <tcp>
          <dest-port>995</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>PPTP</name>
        <tcp>
          <dest-port>1723</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>QUAKE</name>
        <udp>
          <dest-port>26000,27000,27910,27960</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>RAUDIO</name>
        <udp>
          <dest-port>7070</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>REXEC</name>
        <tcp>
          <dest-port>512</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RIP</name>
        <udp>
          <dest-port>520</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>RLOGIN</name>
        <tcp>
          <dest-port>513</dest-port>
          <source-port>512-1023</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RSH</name>
        <tcp>
          <dest-port>514</dest-port>
          <source-port>512-1023</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SCCP</name>
        <tcp>
          <dest-port>2000</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SIP</name>
        <tcp>
          <dest-port>5060</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>5060</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>SIP-MSNmessenger</name>
        <tcp>
          <dest-port>1863</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SAMBA</name>
        <tcp>
          <dest-port>139</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SMTPS</name>
        <tcp>
          <dest-port>465</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SYSLOG</name>
        <udp>
          <dest-port>514</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TALK</name>
        <udp>
          <dest-port>517-518</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TFTP</name>
        <udp>
          <dest-port>69</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>MGCP</name>
        <tcp>
          <dest-port>2428</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>2427,2727</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>UUCP</name>
        <tcp>
          <dest-port>540</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>VDOLIVE</name>
        <tcp>
          <dest-port>7000-7010</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>WAIS</name>
        <tcp>
          <dest-port>210</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>WINFRAME</name>
        <tcp>
          <dest-port>1494,2598</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>X-WINDOWS</name>
        <tcp>
          <dest-port>6000-6063</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>PING6</name>
        <icmp>
          <type>128</type>
          <code>0</code>
        </icmp>
      </service-set>
      <service-set>
        <name>MS-SQL</name>
        <tcp>
          <dest-port>1433,1434</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>MYSQL</name>
        <tcp>
          <dest-port>3306</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>RDP</name>
        <tcp>
          <dest-port>3389</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>VNC</name>
        <tcp>
          <dest-port>5900</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>DHCP6</name>
        <udp>
          <dest-port>546,547</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>SQUID</name>
        <tcp>
          <dest-port>3128</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>SOCKS</name>
        <tcp>
          <dest-port>1080</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>1080</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>WINS</name>
        <tcp>
          <dest-port>1512</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>1512</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>RADIUS</name>
        <udp>
          <dest-port>1812,1813</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>RADIUS-OLD</name>
        <udp>
          <dest-port>1645,1646</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>CVSPSERVER</name>
        <tcp>
          <dest-port>2401</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>2401</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>AFS3</name>
        <tcp>
          <dest-port>7000-7009</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>7000-7009</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>TRACEROUTE</name>
        <udp>
          <dest-port>33434-33535</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>RTSP</name>
        <tcp>
          <dest-port>554,7070,8554</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>554</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>MMS</name>
        <tcp>
          <dest-port>1755</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>1024-5000</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>KERBEROS</name>
        <tcp>
          <dest-port>88,464</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
        <udp>
          <dest-port>88,464</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>LDAP_UDP</name>
        <udp>
          <dest-port>389</dest-port>
          <source-port>0-65535</source-port>
        </udp>
      </service-set>
      <service-set>
        <name>SMB</name>
        <tcp>
          <dest-port>445</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>NONE</name>
        <tcp>
          <dest-port>0</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>webproxy</name>
        <tcp>
          <dest-port>0-65535</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>service-test01</name>
        <description>服务测试001</description>
        <tcp>
          <dest-port>9227-9228,1000-1024</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-set>
        <name>service-test02</name>
        <description>服务test02</description>
        <tcp>
          <dest-port>8080,80,443,8443</dest-port>
          <source-port>0-65535</source-port>
        </tcp>
      </service-set>
      <service-group>
        <name>Email_Access</name>
        <service-set>
          <name>dns</name>
        </service-set>
        <service-set>
          <name>imap</name>
        </service-set>
        <service-set>
          <name>IMAPS</name>
        </service-set>
        <service-set>
          <name>pop3</name>
        </service-set>
        <service-set>
          <name>POP3S</name>
        </service-set>
        <service-set>
          <name>smtp</name>
        </service-set>
        <service-set>
          <name>SMTPS</name>
        </service-set>
      </service-group>
      <service-group>
        <name>Web_Access</name>
        <service-set>
          <name>dns</name>
        </service-set>
        <service-set>
          <name>http</name>
        </service-set>
        <service-set>
          <name>https</name>
        </service-set>
      </service-group>
      <service-group>
        <name>Windows_AD</name>
        <service-set>
          <name>DCE-RPC</name>
        </service-set>
        <service-set>
          <name>dns</name>
        </service-set>
        <service-set>
          <name>KERBEROS</name>
        </service-set>
        <service-set>
          <name>LDAP</name>
        </service-set>
        <service-set>
          <name>LDAP_UDP</name>
        </service-set>
        <service-set>
          <name>SAMBA</name>
        </service-set>
        <service-set>
          <name>SMB</name>
        </service-set>
      </service-group>
      <service-group>
        <name>Exchange_Server</name>
        <service-set>
          <name>DCE-RPC</name>
        </service-set>
        <service-set>
          <name>dns</name>
        </service-set>
        <service-set>
          <name>https</name>
        </service-set>
      </service-group>
      <service-group>
        <name>serviceg_test01</name>
        <description>服务组测试</description>
        <service-set>
          <name>service-test01</name>
        </service-set>
        <service-set>
          <name>service-test02</name>
        </service-set>
      </service-group>
      <service-group>
        <name>serviceg_test02</name>
        <description>服务组测试02</description>
        <service-set>
          <name>http</name>
        </service-set>
        <service-set>
          <name>https</name>
        </service-set>
        <service-set>
          <name>SMB</name>
        </service-set>
      </service-group>
    </service-obj>
    <dns xmlns="urn:ruijie:ntos:params:xml:ns:yang:dns">
      <server>
        <address>***********</address>
      </server>
      <server>
        <address>***********</address>
      </server>
      <proxy>
        <enabled>false</enabled>
      </proxy>
    </dns>
    <routing xmlns="urn:ruijie:ntos:params:xml:ns:yang:routing">
      <static>
        <ipv4-route>
          <destination>0.0.0.0/0</destination>
          <next-hop>
            <next-hop>***********</next-hop>
            <distance>1</distance>
            <descr>静态路由 1</descr>
          </next-hop>
        </ipv4-route>
      </static>
    </routing>
    <security-policy xmlns="urn:ruijie:ntos:params:xml:ns:yang:security-policy">
      <policy>
        <name>TCT-VLAN_to_LAN_HTTP_HTTPS_P4</name>
        <enabled>true</enabled>
        <description>放行tct-vlan103到port1，tct-addr-net-140到tct-addr-net-160的HTTP-HTTPS流量，没有NAT规则，无安全检查</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-interface>
          <name>Ge0/5.103</name>
        </source-interface>
        <dest-interface>
          <name>Ge0/3</name>
        </dest-interface>
        <source-network>
          <name>tct-addr-net-140</name>
        </source-network>
        <dest-network>
          <name>tct-addr-net-160</name>
        </dest-network>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>https</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy>
        <name>WAN_to_PORT6_P5</name>
        <enabled>true</enabled>
        <description>port2到port6，来之tct-addr-net-140的做snat，同时开启AV，IPS；使用出接口，保持源端口</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-interface>
          <name>Ge0/4</name>
        </source-interface>
        <dest-interface>
          <name>Ge0/8</name>
        </dest-interface>
        <source-network>
          <name>tct-addr-net-140</name>
        </source-network>
        <time-range>always</time-range>
        <ips>default-use-signature-action</ips>
        <av>default-alert</av>
      </policy>
      <policy>
        <name>DMZ_to_OPT_FTP_P6</name>
        <enabled>false</enabled>
        <description>放行port3到port4，tct-addr-net-150到tct-addr-net170-1-10的ftp流量，不做nat，该规则暂时禁用</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-interface>
          <name>Ge0/5</name>
        </source-interface>
        <dest-interface>
          <name>Ge0/6</name>
        </dest-interface>
        <source-network>
          <name>tct-addr-net-150</name>
        </source-network>
        <dest-network>
          <name>tct-addr-net-170-1-10</name>
        </dest-network>
        <service>
          <name>ftp</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy>
        <name>TCT-ZONE_to_OPT_P8</name>
        <enabled>true</enabled>
        <description>放行从区域tct-zone-1到port4，tct-addr-net-160到tct-addr-net-150的流量，时间限制在工作日，同时开启snat</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-zone>
          <name>tct-zone-1</name>
        </source-zone>
        <dest-interface>
          <name>Ge0/6</name>
        </dest-interface>
        <source-network>
          <name>tct-addr-net-160</name>
        </source-network>
        <dest-network>
          <name>tct-addr-net-150</name>
        </dest-network>
        <time-range>worker-day</time-range>
      </policy>
      <policy>
        <name>LAN_to_WAN_SERVICE-_P9</name>
        <enabled>true</enabled>
        <description>测试包含iprange的服务对象在策略中的使用</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-interface>
          <name>Ge0/3</name>
        </source-interface>
        <dest-interface>
          <name>Ge0/4</name>
        </dest-interface>
        <source-network>
          <name>tct-addr-net-150</name>
        </source-network>
        <dest-network>
          <name>tct-addr-net-160</name>
        </dest-network>
        <service>
          <name>service-test01</name>
        </service>
        <time-range>always</time-range>
      </policy>
      <policy>
        <name>WAN_to_DMZ_SERVICE-_HTTP_P10</name>
        <enabled>true</enabled>
        <description>测试混合服务策略，包含多个iprange服务</description>
        <group-name>def-group</group-name>
        <config-source>manual</config-source>
        <session-timeout>0</session-timeout>
        <action>permit</action>
        <source-interface>
          <name>Ge0/4</name>
        </source-interface>
        <dest-interface>
          <name>Ge0/5</name>
        </dest-interface>
        <source-network>
          <name>tct-addr-net-160</name>
        </source-network>
        <dest-network>
          <name>tct-addr-net-150</name>
        </dest-network>
        <service>
          <name>service-test01</name>
        </service>
        <service>
          <name>http</name>
        </service>
        <service>
          <name>service-test02</name>
        </service>
        <time-range>always</time-range>
      </policy>
    </security-policy>
  </vrf>
  <system xmlns="urn:ruijie:ntos:params:xml:ns:yang:system">
    <cp-mask>default</cp-mask>
    <nfp>
      <autoperf>
        <enabled>true</enabled>
      </autoperf>
    </nfp>
    <network-stack>
      <bridge>
        <call-ipv4-filtering>false</call-ipv4-filtering>
        <call-ipv6-filtering>false</call-ipv6-filtering>
      </bridge>
      <icmp>
        <rate-limit-icmp>1000</rate-limit-icmp>
        <rate-mask-icmp>destination-unreachable source-quench time-exceeded parameter-problem</rate-mask-icmp>
      </icmp>
      <ipv4>
        <forwarding>true</forwarding>
        <send-redirects>true</send-redirects>
        <accept-redirects>false</accept-redirects>
        <accept-source-route>false</accept-source-route>
        <arp-announce>any</arp-announce>
        <arp-filter>false</arp-filter>
        <arp-ignore>any</arp-ignore>
        <log-invalid-addresses>false</log-invalid-addresses>
      </ipv4>
      <ipv6>
        <forwarding>true</forwarding>
        <autoconfiguration>true</autoconfiguration>
        <accept-router-advert>never</accept-router-advert>
        <accept-redirects>false</accept-redirects>
        <accept-source-route>false</accept-source-route>
        <router-solicitations>-1</router-solicitations>
        <use-temporary-addresses>never</use-temporary-addresses>
      </ipv6>
    </network-stack>
    <timezone>Asia/Shanghai</timezone>
    <scheduled-restart>
      <enabled>false</enabled>
      <hour>3</hour>
      <minute>0</minute>
      <once>false</once>
    </scheduled-restart>
    <anti-virus-file-exception xmlns="urn:ruijie:ntos:params:xml:ns:yang:anti-virus">
      <enabled>true</enabled>
    </anti-virus-file-exception>
    <auth xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:auth">
      <user>
        <name>admin</name>
        <role>admin</role>
        <password>$5$zHDpSFXXrdqxgOGh$uk/0SQ.WKEWWPXkHyuUnFA6Ym9sIEtnFX4bcHViEwZ1</password>
      </user>
      <user>
        <name>securityadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>useradmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
      <user>
        <name>auditadmin</name>
        <role>admin</role>
        <lock>true</lock>
      </user>
    </auth>
    <wis-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </wis-service>
    <macc-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </macc-service>
    <security-cloud-service xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <enabled>true</enabled>
    </security-cloud-service>
    <log2cloud xmlns="urn:ruijie:ntos:params:xml:ns:yang:cloud-service">
      <upload-interval>5</upload-interval>
    </log2cloud>
    <collect xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:collect">
      <alarm-threshold>
        <disk-db-threshold>90</disk-db-threshold>
      </alarm-threshold>
      <enabled>true</enabled>
      <max-records>3072</max-records>
      <record-interval>300</record-interval>
      <memory-storage-threshold>90</memory-storage-threshold>
      <statistics-enabled>false</statistics-enabled>
      <record-stats-enabled>true</record-stats-enabled>
      <flow-log-enabled>true</flow-log-enabled>
      <log-language>Chinese</log-language>
    </collect>
    <dataplane xmlns="urn:ruijie:ntos:params:xml:ns:yang:dataplane-dsa">
      <hash>
        <type>hash-default</type>
        <bind>none</bind>
      </hash>
    </dataplane>
    <flow-audit xmlns="urn:ruijie:ntos:params:xml:ns:yang:flow-audit">
      <hard-disk-quota>20</hard-disk-quota>
      <refresh-time>30</refresh-time>
    </flow-audit>
    <local-defend xmlns="urn:ruijie:ntos:params:xml:ns:yang:local-defend">
      <enabled>true</enabled>
      <host-hardening>
        <enabled>true</enabled>
        <injection-prevention>
          <enabled>false</enabled>
          <action>block</action>
        </injection-prevention>
      </host-hardening>
      <arp-monitor>
        <enabled>false</enabled>
        <scan-threshold>200</scan-threshold>
      </arp-monitor>
      <rate-limit>
        <arp>
          <req-token>5</req-token>
          <res-token>1</res-token>
          <req-threshold>100</req-threshold>
          <res-threshold>100</res-threshold>
        </arp>
      </rate-limit>
    </local-defend>
    <memory xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:mem">
      <warning-threshold>95</warning-threshold>
      <critical-threshold>95</critical-threshold>
    </memory>
    <network-monitor xmlns="urn:ruijie:ntos:params:xml:ns:yang:network-monitor">
      <enabled>false</enabled>
      <wireless-gather-type>snmp</wireless-gather-type>
    </network-monitor>
    <usr-exp-plan xmlns="urn:ruijie:ntos:params:xml:ns:yang:user-experience-plan">
      <no-prompt>false</no-prompt>
      <enabled>false</enabled>
    </usr-exp-plan>
    <web-server xmlns="urn:ruijie:ntos:params:xml:ns:yang:system:web-server">
      <enabled>true</enabled>
      <port>443</port>
      <http-enabled>true</http-enabled>
      <smart-http-enabled>true</smart-http-enabled>
    </web-server>
  </system>
</config>
