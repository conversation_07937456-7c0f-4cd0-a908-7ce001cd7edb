package firewallflextrans

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/libs/i18n"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/firewallflextrans"
)

// 处理验证配置响应
func handleVerificationResponse(rawData map[string]interface{}, language string) (map[string]interface{}, error) {
	// 如果未提供语言参数，默认使用中文
	if language == "" {
		language = "zh-CN"
	}

	// 创建符合预期格式的响应
	response := map[string]interface{}{
		"valid":   false,
		"message": i18n.Translate(language, "convert.verification_failed", nil),
	}

	// 解析成功状态
	if valid, ok := rawData["valid"].(bool); ok {
		response["valid"] = valid
	}

	// 解析消息
	if message, ok := rawData["message"].(string); ok && message != "" {
		response["message"] = message
	} else {
		// 尝试从其他字段获取消息
		if response["valid"].(bool) {
			response["message"] = i18n.Translate(language, "convert.verification_success", nil)
		} else if errMsg, ok := rawData["error"].(string); ok && errMsg != "" {
			response["message"] = errMsg
		}
	}

	// 如果原始结果中有版本信息，保留它
	if detectedVersion, ok := rawData["detected_version"].(string); ok && detectedVersion != "" {
		response["detected_version"] = detectedVersion
	}

	// 如果原始结果中有警告信息，保留它
	if warnings, ok := rawData["warnings"].([]interface{}); ok && len(warnings) > 0 {
		response["warnings"] = warnings
	}

	return response, nil
}

// 处理接口提取响应
func handleInterfaceExtractionResponse(rawData map[string]interface{}) (map[string]interface{}, error) {
	// 创建新的简化响应对象
	response := firewallflextrans.SimpleInterfaceExtractionResponse{
		Interfaces: []firewallflextrans.SimpleInterfaceInfo{},
		Count:      0,
	}

	// 解析接口列表
	interfaces := []firewallflextrans.SimpleInterfaceInfo{}

	// 首先尝试解析interfaces数组
	if ifaces, ok := rawData["interfaces"].([]interface{}); ok {
		for _, iface := range ifaces {
			if ifaceMap, ok := iface.(map[string]interface{}); ok {
				info := parseSimpleInterfaceInfo(ifaceMap)
				if info.Name != "" {
					interfaces = append(interfaces, info)
				}
			}
		}
	}

	// 然后尝试解析interfaces_map对象
	if ifacesMap, ok := rawData["interfaces"].(map[string]interface{}); ok {
		for name, iface := range ifacesMap {
			if ifaceMap, ok := iface.(map[string]interface{}); ok {
				info := parseSimpleInterfaceInfo(ifaceMap)
				// 如果名称为空，使用map的键作为名称
				if info.Name == "" {
					info.Name = name
				}
				interfaces = append(interfaces, info)
			}
		}
	}

	response.Interfaces = interfaces
	response.Count = len(interfaces)

	// 如果Python引擎已经提供了接口数量，使用它
	if count, ok := rawData["count"].(float64); ok {
		response.Count = int(count)
	}

	// 将响应转换为map
	result, err := convertResponseToMap(response)
	if err != nil {
		return nil, err
	}

	// 移除不需要的字段
	delete(result, "success")
	delete(result, "message")
	delete(result, "mapping_json")
	delete(result, "interfaces_count")

	return result, nil
}

// 处理配置转换响应
func handleConversionResponse(rawData map[string]interface{}, language string) (map[string]interface{}, error) {
	response := firewallflextrans.ConversionResponse{
		Success:      false,
		Message:      i18n.Translate(language, "convert.conversion_failed", nil),
		ErrorDetails: make(map[string]interface{}),
	}

	// 解析成功状态
	if success, ok := rawData["success"].(bool); ok {
		response.Success = success
	}

	// 解析消息
	if message, ok := rawData["message"].(string); ok && message != "" {
		response.Message = message
	} else {
		// 尝试从其他字段获取消息
		if response.Success {
			response.Message = i18n.Translate(language, "convert.conversion_success", nil)
		} else if errMsg, ok := rawData["error"].(string); ok && errMsg != "" {
			response.Message = errMsg
		}
	}

	// 解析输出文件路径
	if outputFile, ok := rawData["output_file"].(string); ok {
		response.OutputFile = outputFile
	}

	// 解析报告文件路径
	if reportPath, ok := rawData["report_path"].(string); ok {
		response.ReportPath = reportPath
	}

	// 解析详细日志
	if logsData, ok := rawData["logs"].([]interface{}); ok && len(logsData) > 0 {
		logs := make([]firewallflextrans.LogEntry, 0, len(logsData))

		for _, logItem := range logsData {
			if logMap, ok := logItem.(map[string]interface{}); ok {
				entry := firewallflextrans.LogEntry{}

				if time, ok := logMap["time"].(string); ok {
					entry.Time = time
				}

				if level, ok := logMap["level"].(string); ok {
					entry.Level = level
				}

				if message, ok := logMap["message"].(string); ok {
					entry.Message = message
				}

				if module, ok := logMap["module"].(string); ok {
					entry.Module = module
				}

				logs = append(logs, entry)
			}
		}

		response.Logs = logs
		logging.DebugLogger.Infof("解析到 %d 条日志记录", len(logs))
	}

	// 解析转换总结
	if summaryData, ok := rawData["summary"].(map[string]interface{}); ok {
		summary := &firewallflextrans.ConversionSummary{}

		// 解析基本计数
		if val, ok := summaryData["total_interfaces"].(float64); ok {
			summary.TotalInterfaces = int(val)
		}

		if val, ok := summaryData["mapped_interfaces"].(float64); ok {
			summary.MappedInterfaces = int(val)
		}

		if val, ok := summaryData["unmapped_interfaces"].(float64); ok {
			summary.UnmappedInterfaces = int(val)
		}

		if val, ok := summaryData["zones"].(float64); ok {
			summary.Zones = int(val)
		}

		if val, ok := summaryData["static_routes"].(float64); ok {
			summary.StaticRoutes = int(val)
		}

		if val, ok := summaryData["address_objects"].(float64); ok {
			summary.AddressObjects = int(val)
		}

		if val, ok := summaryData["service_objects"].(float64); ok {
			summary.ServiceObjects = int(val)
		}

		if val, ok := summaryData["policy_rules"].(float64); ok {
			summary.PolicyRules = int(val)
		}

		// 解析跳过的项目
		if skippedItems, ok := summaryData["skipped_items"].([]interface{}); ok {
			skipped := make([]firewallflextrans.SkippedItem, 0, len(skippedItems))

			for _, item := range skippedItems {
				if itemMap, ok := item.(map[string]interface{}); ok {
					skippedItem := firewallflextrans.SkippedItem{}

					if val, ok := itemMap["type"].(string); ok {
						skippedItem.Type = val
					}

					if val, ok := itemMap["name"].(string); ok {
						skippedItem.Name = val
					}

					if val, ok := itemMap["reason"].(string); ok {
						skippedItem.Reason = val
					}

					if val, ok := itemMap["detail"].(string); ok {
						skippedItem.Detail = val
					}

					skipped = append(skipped, skippedItem)
				}
			}

			summary.SkippedItems = skipped
		}

		// 解析需要手动配置的项目
		if manualItems, ok := summaryData["manual_config_required"].([]interface{}); ok {
			manual := make([]firewallflextrans.ManualConfigItem, 0, len(manualItems))

			for _, item := range manualItems {
				if itemMap, ok := item.(map[string]interface{}); ok {
					manualItem := firewallflextrans.ManualConfigItem{}

					if val, ok := itemMap["type"].(string); ok {
						manualItem.Type = val
					}

					if val, ok := itemMap["name"].(string); ok {
						manualItem.Name = val
					}

					if val, ok := itemMap["reason"].(string); ok {
						manualItem.Reason = val
					}

					if val, ok := itemMap["detail"].(string); ok {
						manualItem.Detail = val
					}

					manual = append(manual, manualItem)
				}
			}

			summary.ManualConfigRequired = manual
		}

		response.Summary = summary
		logging.DebugLogger.Infof("解析到转换总结信息")
	}

	// 收集错误详情
	for key, value := range rawData {
		switch key {
		case "success", "message", "output_file", "report_path", "logs", "summary":
			// 这些字段已经处理过了
		default:
			// 其他字段作为错误详情
			if !response.Success {
				response.ErrorDetails[key] = value
			}
		}
	}

	// 如果没有错误详情，删除该字段
	if len(response.ErrorDetails) == 0 {
		response.ErrorDetails = nil
	}

	// 将响应转换为map
	return convertResponseToMap(response)
}

// parseSimpleInterfaceInfo 解析简化的接口信息
func parseSimpleInterfaceInfo(data map[string]interface{}) firewallflextrans.SimpleInterfaceInfo {
	info := firewallflextrans.SimpleInterfaceInfo{
		Zone: "NA", // 默认区域为NA
		Mask: "NA", // 默认掩码为NA
	}

	// 提取基本字段
	if name, ok := data["name"].(string); ok {
		info.Name = name
	}

	// 提取区域信息
	if zone, ok := data["zone"].(string); ok && zone != "" {
		info.Zone = zone
	}

	// 提取IP地址
	if ip, ok := data["ip"].(string); ok {
		info.IP = ip
	} else if mode, ok := data["mode"].(string); ok {
		if mode == "dhcp" {
			info.IP = "DHCP"
		} else if mode == "pppoe" {
			info.IP = "PPPoE"
		}
	}

	// 提取子网掩码
	if mask, ok := data["mask"].(string); ok && mask != "" {
		info.Mask = mask
	} else if mode, ok := data["mode"].(string); ok {
		if mode == "dhcp" || mode == "pppoe" {
			info.Mask = "NA"
		}
	}

	// 提取是否为子接口
	if isSubinterface, ok := data["is_subinterface"].(bool); ok {
		info.IsSubinterface = isSubinterface
	}

	// 提取接口模式
	if mode, ok := data["mode"].(string); ok {
		info.Mode = mode
	}

	// 提取访问控制设置
	if allowaccess, ok := data["allowaccess"].(string); ok {
		info.Allowaccess = allowaccess
	}

	// 提取接口类型
	if ifType, ok := data["type"].(string); ok {
		info.Type = ifType
	}

	// 提取是否为必需接口
	if required, ok := data["required"].(bool); ok {
		info.Required = required
	}

	// 提取接口类型名称
	if typeName, ok := data["type_name"].(string); ok {
		info.TypeName = typeName
	}

	return info
}

// convertResponseToMap 将响应对象转换为map[string]interface{}
func convertResponseToMap(response interface{}) (map[string]interface{}, error) {
	// 使用JSON序列化和反序列化进行转换
	jsonData, err := json.Marshal(response)
	if err != nil {
		return nil, fmt.Errorf("序列化响应失败: %v", err)
	}

	var result map[string]interface{}
	if err := json.Unmarshal(jsonData, &result); err != nil {
		return nil, fmt.Errorf("反序列化响应失败: %v", err)
	}

	// 记录转换后的结果
	logging.DebugLogger.Infof("转换后的响应: %+v", result)

	return result, nil
}
