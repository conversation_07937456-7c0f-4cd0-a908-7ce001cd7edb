module ntos-user-management {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:user-management";
  prefix ntos-user-management;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-if-types {
    prefix ntos-if;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS user management module.";

  revision 2022-11-18 {
    description
      "Initial version.";
    reference "";
  }

  revision 2024-03-18 {
    description
      "add user black list.";
    reference "";
  }

  revision 2024-09-08 {
    description
      "add user block export/import.";
    reference "";
  }

  revision 2024-10-28 {
    description
      "add user otp-key.";
    reference "";
  }


  revision 2024-12-12 {
    description
      "add user label.";
    reference "";
  }

  revision 2024-12-13 {
    description
      "add user otp-key.";
    reference "";
  }

  identity user-manage {
    base ntos-types:SERVICE_LOG_ID;
    description
      "user-manage service.";
  }

  typedef ipv4-address-type {
    type union {
      type ntos-inet:ipv4-address;
      type ntos-inet:ipv4-prefix;
      type ntos-inet:ipv4-range;
      type ntos-inet:ipv4-mask;
    }
  }

  typedef user-group-path {
    description
      "This type represents a user group path.";
    type string {
      length "1..256";
      pattern "[^`~!#$%^&*+|;:\"',\\\\<>?]*" {
        error-message 'cannot include character: `~!#%^&*+|;:",\<>?';
      }
    }
  }

  typedef user-label-name {
    description "This type represents a user label name.";
    type string {
      length "1..64";
      pattern "[^`~!#$%^&*+|;:\"',\\\\<>?]*" {
        error-message 'cannot include character: `~!#%^&*+|;:",\<>?';
      }
    }
  }

  typedef ip-mac-binding-type {
    description
      "The user and IP/MAC address binding mode.";
    type enumeration {
      enum bidirectional {
        description
          "Only the designated user can log in with the specified IP/MAC address,
           but other users are also allowed to log in with the same IP/MAC address.";
      }
      enum unidirectional {
        description
          "Only the designated user can log in with the specified IP/MAC address,
           and other users are not allowed to log in with the same IP/MAC address.";
      }
      enum auto-bind-mac {
          description
          "The user's MAC address is automatically bound.";
      }
    }
  }

  typedef time-date-str {
    type string {
      pattern '(([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]/' +
              '((2[0-1][0-9])[0-9]-((0[1-9]|1[0-2])-' +
              '(0[1-9]|1[0-9]|2[0-8])|(0[13-9]|1[0-2])-' +
              '(29|30)|(0[13578]|1[02])-31)|' +
              '([0-9]{2}(0[48]|[2468][048]|[13579][26])|' +
              '(0[48]|[2468][048]|[13579][26])00)-02-29))|' {
        error-message "Incorrect time/date format, expecting: hh:mm:ss/YYYY-MM-DD and it must be after current time.";
      }
      length "0|19";
    }
  }

  grouping user-package-detail {
    leaf name {
       description
         "Configure the name of the user package.";
       type ntos-types:ntos-obj-name-type;
    }

    leaf access-limit {
      description "maximum number of access terminals.";
      type uint32;
    }
  }

  grouping user-block-detail {
    leaf name {
      description
        "Configure the name of the user.";
      type ntos-types:ntos-obj-name-type;
    }

    leaf description {
      type ntos-types:ntos-obj-description-type;
      default "";
    }

    leaf enabled {
      type boolean;
      default "true";
    }

    leaf notify-enabled {
      type boolean;
      default "false";
    }

    leaf source {
      type enumeration {
        enum manual-config;
        enum security-log-import;
        enum session-log-import;
        enum ca-log-import;
        enum session-monitor-import;
        enum csv-import;
      }
    }

    leaf block-type {
      type enumeration {
        enum permanent;
        enum temporary;
      }
    }
    leaf block-method {
      type enumeration {
        enum local-block;
        enum identity-system-block;
      }
    }
    choice blocktime {
      description
        "Configure the blocktime of the block user.";
      case blocktime-day {
        description
          "Configure the blocktime-day of the block user.";
        leaf blocktime-day {
          type uint32 {
            range '1..15';
          }
        }
      }

      case blocktime-hour {
        description
          "Configure the blocktime-hour of the block user.";
        leaf blocktime-hour {
          type uint32 {
            range '1..360';
          }
        }
      }
      case blocktime-minute {
        description
          "Configure the blocktime-minute of the block user.";
        leaf blocktime-minute {
          type uint32 {
            range '3..21600';
          }
        }
      }
    }
    leaf log-detail {
      description
        "Blocklist User Source Log.";
      type string {
          length "1..256";
      }
    }
  }

  grouping output-user-package-detail {
    leaf name {
       description
         "Configure the name of the user package.";
       type ntos-types:ntos-obj-name-type;
    }

    leaf access-limit {
      description "maximum number of access terminals.";
      type uint32;
    }

    leaf ref-count {
      description "the count of user packages referenced by user.";
      type uint32;
    }

    list ref-user-name {
      leaf name {
        type string;
        description
          "The name of user.";
      }
      leaf parent-user-group {
        description
          "Configure the name of the parent group.";
        type string;
      }
      leaf aaa-domain {
        description
          "Configure the name of the domain to which the user belong.";
        type string;
      }
    }
  }

  grouping output-user-label-detail {
    leaf name {
      type user-label-name;
      description
        "The name of user label.";
    }

    leaf aaa-domain {
      description
        "Configure the name of the domain to which the user belong.";
      type string;
    }

    leaf full-name {
      description
        "Configure the name of the domain to which the user belong.";
      type string;
    }

    leaf label-id {
      type uint32;
    }

    leaf description {
      type string;
      description
        "The description of user label.";
    }

    leaf source {
      type string;
      description
        "The source of user label.";
    }

    leaf level {
      type uint32;
    }

    list reference-obj {
      description
        "The childern object type referenced by the user label.";

      leaf object-type {
        type string;
        description
          "The name of obj type, eg: user-label or user.";
      }
      leaf object-count {
        type uint32;
      }
      leaf-list object-name {
        type string;
        description
            "The name of user label.";
      }
    }

    list ply-ref-type {
      description
        "The policy type referenced by the user label.";

      leaf ply-type {
        type string;
        description
          "The name of custom policy.";
      }
      leaf ply-count {
        type uint32;
      }

      leaf-list ply-id {
        type uint32;
        description
          "The id of policy.";
      }
    }

    list reference-module-type {
      description
        "The module type referenced by the user label.";

      leaf module-type {
        type string;
        description
          "The name of custom policy.";
      }
      leaf reference-count {
        type uint32;
      }
    }

  }

  grouping output-user-block-detail {
    leaf name {
      type ntos-types:ntos-obj-name-type;
      description
        "The name of block user.";
    }

    leaf description {
      type string;
      description
        "The description of block user.";
    }

    leaf enabled {
      type boolean;
      description
        "Enable or disable block user.";
    }

    leaf notify-enabled {
      type boolean;
      description
        "Enable or disable block user notify.";
    }

    leaf source {
      description
        "block user source.";
      type string;
    }

    leaf block-type {
      description
        "block user block type.";
      type string;
    }
    leaf block-method {
      description
        "block user block method.";
      type string;
    }
    leaf blocktime-day {
      type uint32 {
        range '1..15';
      }
    }

    leaf blocktime-hour {
      type uint32 {
        range '1..360';
      }
    }

    leaf blocktime-minute {
      type uint32 {
        range '3..21600';
      }
    }
    leaf block-time-remaining {
      description
        "block user blocktime time.";
      type string;
    }

    leaf aging-time {
      description
        "block user aging time.";
      type string;
    }

    leaf create-time {
      description
        "block user create time.";
      type string;
    }

    leaf log-detail {
      description
        "black User Source Log.";
      type string;
    }
  }

  grouping user-detail {
    description
      "Configuration of user object or user group.";

    list user-group {
      key "name";
      description
        "The list of user group.";

      leaf name {
        description
          "Configure the name of the user group.
           An user group name must carry the authentication domain name.
           For example, /default/group1 indicates group1 in the default authentication domain.";
        type user-group-path;
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "The descrption of the user group.";
      }

      leaf parent-user-group {
        description
          "Configure the name of the parent group.";
        type user-group-path;
      }

      leaf source {
        type enumeration {
          enum manual-config;
          enum ad-domain-import;
          enum csv-import;
        }
      }
    }

    list user-label {
      key "name aaa-domain";
      ntos-ext:nc-cli-show-key-name;
      description
        "The list of user label.";

      leaf name {
        description "Configure the name of the user label.";
        type user-label-name;
      }

      leaf aaa-domain {
        description
          "Configure the name of the domain to which the user belong.";
        type string {
            length "1..64";
        }
      }

      leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "The descrption of the user label.";
      }

      leaf-list parent-label {
        description "Configure the parent of the user label";
        type string;
      }

      leaf source {
        type enumeration {
          enum manual-config;
          enum ad-domain-import;
          enum csv-import;
        }
      }
    }

    list user {
      key "name aaa-domain";
      ntos-ext:nc-cli-show-key-name;

      description
        "The list of user.";

      leaf name {
        description
          "Configure the name of the user.";
        type ntos-types:ntos-obj-name-type;
      }

      leaf aaa-domain {
        description
          "Configure the name of the domain to which the user belong.";
        type string {
            length "1..64";
        }
      }

      leaf enabled {
        type boolean;
        default "true";
        description
          "Enable or disable user.";
      }

      leaf description {
        description
          "Configure the description of the user";
        type ntos-types:ntos-obj-description-type;
      }

      leaf alias {
        description
          "Configure the alias of the user.";
        type ntos-types:ntos-obj-name-type;
      }

      leaf password {
        description
          "Configure the authenticate password of the user.";
        type string;
      }

      leaf otp-key {
        description
          "Configure the authenticate otp-key of the user.";
        type string;
      }

      leaf parent-group-path {
        description
          "Configure the name of the parent group.";
        type user-group-path;
        mandatory true;
      }

      leaf-list parent-label {
        description "Configure the parent of the user label";
        type string;
      }

      leaf areacode {
        description
          "Configure the area code for phone number.";
        type string {
          pattern '\d{1,4}';
        }
      }

      leaf phone {
        description
          "Configure the phone number for the user.";
        type string;
      }

      leaf source {
        type enumeration {
          enum manual-config;
          enum ad-domain-import;
          enum csv-import;
        }
      }

      leaf package-name {
        description
          "Configure the package for the user.";
        type string;
      }

      container ip-mac-binding {
        description
          "Configure the IP/MAC bound to the user.";

        choice bind-state {
          description
            "Configure the bind state of the user.";

          case no-binding {
            description
              "Choice this to prevent user from binding to any IP address and MAC address.";
            leaf no-binding {
              description
                "Configure the user to binding no IP/MAC address.";
              type empty;
            }
          }
          case binding {
            description
              "Choice this to binding user to IP address or MAC address.";
            leaf bind-mode{
              description
                "Configuring the user and IP/MAC address binding mode.";
              type ip-mac-binding-type;
            }

            list ip-binding {
              key "ip";
              leaf ip {
                description
                  "Configure the IP address bound to the user.";
                type ipv4-address-type;
              }
              ntos-ext:nc-cli-one-liner;
            }

            list mac-binding {
              key "mac";
              leaf mac {
                description
                  "Configure the MAC address bound to the user.";
                type ntos-if:mac-address;
              }
            }
          }
        }
      }

      leaf multi-ip-online {
        description
          "Configure whether to allow users to log in multiple IP.
           A value of true means allow, while false is not allow.";
        type boolean;
      }

      container expiration-time {
        description
          "Configure the user expiration time.";

        choice expiration-type {
          description
            "Configure the expiration type.";

          case never-expire {
            description
              "Choice this to configure the user never expire.";
            leaf never-expire {
              description
                "Configure the user never expire.";
              type empty;
            }
          }

          case expire-after-this-time {
            description
              "Choice this to configure the user expired time.";
            leaf expiration-time {
              description
                "Configure the user expired time. Example:'hh:mm:ss/YYYY-MM-DD'.";
              type time-date-str;
            }
          }
        }
      }
    }

    list user-block {
      key "name";
      ntos-ext:nc-cli-show-key-name;
      description
        "User black list configuration.";
      uses user-block-detail;
    }

    list user-package {
      key "name";
      ntos-ext:nc-cli-show-key-name;
      description
        "Account package configuration.";
      uses user-package-detail;
    }
  }

  typedef user-password-level {
    description
      "user password level.";
    type enumeration {
      enum low {
        description
          "The user password level is low. A password is a string of 1 to 16 characters and
           can not contain '*' only.";
      }
      enum middle {
        description
          "The user password level is medium. A password cannot be the same as the user name
            and must be a string of 6 to 16 characters, containing any two types of the following
            characters: digits, uppercase letters, lowercase letters, and special characters. ";
      }
      enum high {
        description
          "Indicates a high level password. A password must be different from the user name and
           must be a string of 8 to 16 characters, containing any three types of the following
           characters: digits, uppercase letters, lowercase letters, and special characters.";
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "User management configuration.";

    container user-management {
      presence
        "User management configuration.";
      description
        "User management configuration.";

      uses user-detail;
    }

    container user-password-policy {
      description
        "Configure the policy of user password";

      leaf level {
        description
          "Configure the level of user password";
        type user-password-level;
      }

      leaf first-login-modify-password {
        description
          "Configure weather the user needs to change the password on first login.
           A value of true means need, while false is needn't.";
        type boolean;
      }
    }
    container user-offline-by-idle {
      leaf enabled {
        type boolean;
        description
          "Enable or disable offline user by idle.";
      }
      leaf timeout {
        type uint32{
          range "1..50000";
        }
        description
          "Configure timeout of offline user by idle.";
      }
    }
    container user-privacy-statement {
      description
        "Configuration of user privacy policy statement.";
      leaf enabled {
        type boolean;
        description
          "Enable or disable the configuration of the Chinese user privacy policy statement.";
      }
    }
  }

  rpc user-privacy-statement {
    description
      "Show configuration of user privacy policy statement.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
    }

    output {
      container user-privacy-statement {
        description
          "Configuration of user privacy policy statement.";
        leaf enabled {
          type boolean;
          description
            "Enable or disable the configuration of the Chinese user privacy policy statement.";
        }
      }
    }

    ntos-ext:nc-cli-show "user-privacy-statement";
    ntos-api:internal;
  }

  rpc user-privacy-statement-check {
    description
      "Verify the privacy policy statement document.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
    }

    output {
      leaf result {
        description
          "This is the result of verifying the privacy policy statement document.";
        type boolean;
      }
    }

    ntos-ext:nc-cli-cmd "user-privacy-statement check";
    ntos-api:internal;
  }

  rpc user-manage-debug {
    description
      "debug.";
    input {
      leaf cmd {
        type string;
        description
          "debug commend.";
      }
    }
    output {
      leaf result {
        type string;
        description
          "debug result.";
      }
    }
    ntos-ext:nc-cli-cmd "user-manage-debug";
    ntos-api:internal;
  }

  rpc user-management {
    description
      "Show state of user management.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        ntos-ext:nc-cli-group "subcommand";
        leaf type {
          type enumeration {
            enum user-list;
            enum group-list;
            enum organization;
            enum group-organization;
            enum online-user-list;
            enum block-user-list;
            enum user-package;
            enum label-list;
          }

          description
            "Show type of user managemnet.";
        }

        leaf begin-group {
          type string;
          description
            "Show children by begin user group";
        }

        leaf start {
          type uint32;
          description
            "Start offset of result.";
        }

        leaf end {
          type uint32;
          description
            "End offset of result.";
        }

        leaf format {
          type empty;
          description
            "Formatting display";
        }

        leaf children-level {
          type uint32;
          description
            "show children by level";
        }

        leaf filter {
          type string;
          description
            "show children by filter";
        }

        leaf name {
          type string;
          description
            "show children by name";
        }
        leaf access-type {
          type string;
          description
            "show online user by access type";
        }
        leaf aaa-domain {
          type string;
          description
            "Show user label belong to domain";
        }
      }

      container reference {
        ntos-ext:nc-cli-group "subcommand";
        leaf type {
          type enumeration {
            enum user-package;
          }

          description
            "Show reference of user managemnet.";
        }
        leaf name {
          type string;
          description
            "Name of network object.";
        }

        leaf start {
          type uint32;
          description
            "Start offset of result.";
        }

        leaf end {
          type uint32;
          description
            "End offset of result.";
        }
      }
    }

    output {
      leaf json {
        description
          "The json data of result.";
        type string;
      }

      leaf total {
        description
        "Total number of region.";
        type uint32;
      }

      list block-user {
        key "name";
        description
          "The detail of block user.";
        uses output-user-block-detail;
      }

      list user-package {
        key "name";
        description
          "The detail of user package.";
        uses output-user-package-detail;
      }

      list user-label {
        key "name";
        description
          "The detail of user label.";
        uses output-user-label-detail;
      }
    }

    ntos-ext:nc-cli-show "user-management";
    ntos-api:internal;
  }

  rpc online-user-management {
    description
      "online user management.";
    input {
      leaf offline-user-by-ip {
        type string;
        description
          "offline user by user ip.";
      }
      leaf access-type {
        type string;
        description
          "offline user by access method.";
      }
      leaf offline-user-by-name {
        type string;
        description
          "offline user by user name.";
      }
    }
    output {
      leaf result {
        type string;
        description
          "show result.";
      }
    }
    ntos-ext:nc-cli-cmd "online-user-management";
    ntos-api:internal;
  }

  rpc user-offline-by-idle {
    description
      "Show state of user offline by idle.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }
    }

    output {
      leaf json {
        description
          "The json data of result.";
        type string;
      }
    }

    ntos-ext:nc-cli-show "user-offline-by-idle";
    ntos-api:internal;
  }

  rpc user-export {
    description
      "User export instruct.";

    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      leaf filename {
        description
          "Name of export file,
          format: user-config-YYYYMMDDhhmmssSSS.csv
          or user-label-config-YYYYMMDDhhmmssSSS.csv
          or block-user-config-YYYYMMDDhhmmssSSS.csv.";

        type string;
      }

      leaf type {
        description
          "User label or user or block user.";

        type enumeration {
          enum user;
          enum block-user;
          enum label;
        }
      }

      container domain {
        ntos-ext:nc-cli-group "subcommand";
        description
          "Domain exported.";

        choice domain-name {
          case all {
            leaf all {
              type empty;
            }
          }

          case name {
            leaf name {
              type string;
            }
          }
        }
      }
    }

    output {
      leaf result {
        description
          "Export result";
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "user-export";
  }

  rpc user-import {
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      leaf result {
        type empty;
      }

      leaf filename {
        type string;
      }

      leaf type {
        description
          "User label or user or block user.";

        type enumeration {
          enum user;
          enum block-user;
          enum label;
        }
      }

      leaf auto-create {
        description
          "Auto create user group and user label.";
        type boolean;
        default true;
      }

      leaf overwrite {
        description
          "Overwrite user while conflict.";
        type boolean;
        default false;
      }

      leaf skip-fail {
        description
          "Skip check-failed user info.";
        type boolean;
        default false;
      }
    }

    output {
      leaf result {
        type string;
      }
    }
    ntos-ext:nc-cli-cmd "user-import";
  }

  rpc block-user-search {
    description "Search which block user an IP belongs to.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        description "The specific vrf.";
      }

      leaf ip {
        description
          "Configure the IP address ref to the block user.";
        type union {
          type ntos-inet:ipv4-address;
          type ntos-inet:ipv6-address;
        }
      }
    }

    output {
      list block-user {
        key "name";
        description
          "User black list configuration.";
        uses output-user-block-detail;
      }
    }
    ntos-ext:nc-cli-show "block-user-search";
  }
}
