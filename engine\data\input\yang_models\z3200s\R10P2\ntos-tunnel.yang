module ntos-tunnel {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:tunnel";
  prefix ntos-tunnel;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-inet-types {
    prefix ntos-inet-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS tunnel interfaces.";

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  grouping tunnel-common-config {
    description
      "Common data for configuration and state for all tunnels interfaces.";

    leaf ttl {
      type uint8 {
        range "1..255";
      }
      description
        "The time-to-live (or hop limit) that should be utilised
         for the IP packets used for the tunnel transport.";
    }

    leaf tos {
      type uint8 {
        range "1..255";
      }
      description
        "Set the DSCP bits in the Type of Service field.";
    }

    leaf link-interface {
      type ntos-types:ifname;
      must '. != ../name or string(../link-vrf) != string(../../../ntos:name)' {
        error-message "Cannot bind our own interface";
      }
      description
        "Route tunneled packets through this interface.";
      /* interfaces from config and state in the selected vrf */
      ntos-extensions:nc-cli-completion-xpath
        "/ntos:config/vrf[ntos:name=string(
           /ntos:config/vrf[ntos:name=string(current()/*[local-name()='link-vrf'])]/ntos:name |
           /ntos:config/vrf[ntos:name=string(
             current()/../../*[local-name()='name']
           )][count(current()/*[local-name()='link-vrf'])=0]/ntos:name
         )]/ntos-interface:interface/*[.!=current()]/*[local-name()='ethernet']/../*[local-name()='name'] |
         /ntos:state/vrf[ntos:name=string(
           /ntos:config/vrf[ntos:name=string(current()/*[local-name()='link-vrf'])]/ntos:name |
           /ntos:config/vrf[ntos:name=string(
             current()/../../*[local-name()='name']
           )][count(current()/*[local-name()='link-vrf'])=0]/ntos:name
         )]/ntos-interface:interface/*[.!=current()]/*[local-name()='ethernet']/../*[local-name()='name']";
    }

    leaf link-vrf {
      type leafref {
        path
          "/ntos:config/ntos:vrf/ntos:name";
      }
      must 'string(.) != string(../../../ntos:name)' {
        error-message "link-vrf must reference another vrf.";
      }
      description
        "The link vrf name.";
    }
  }

  grouping tunnel-config {
    description
      "Data for configuration and state for all tunnels interfaces but vxlan.";

    leaf local {
      type ntos-inet-types:ip-address;
      must 'contains(., ":") = contains(../remote, ":")' {
        error-message "Ip version of local and remote must be the same";
      }
      mandatory true;
      description
        "The source address that should be used for the
         tunnel.";
    }

    leaf remote {
      type ntos-inet-types:ip-address;
      mandatory true;
      description
        "The destination address that should be used for the
         tunnel.";
    }
    uses tunnel-common-config;
  }
}
