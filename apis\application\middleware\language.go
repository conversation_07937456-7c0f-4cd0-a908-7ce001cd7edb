package middleware

import (
	"time"

	"irisAdminApi/application/libs/i18n"
	"irisAdminApi/application/logging"

	"github.com/kataras/iris/v12"
)

// LanguageMiddleware 处理语言选择
func LanguageMiddleware(ctx iris.Context) {
	// 尝试从不同来源获取语言首选项
	// 1. 查询参数 ?lang=en
	// 2. Cookie
	// 3. Accept-Language 头

	langParam := ctx.URLParam("lang")
	acceptLang := ctx.GetHeader("Accept-Language")
	langCookie := ctx.GetCookie("language")

	// 记录调试信息
	logging.DebugLogger.Debugf("LanguageMiddleware - URL参数: %s, Accept-Language: %s, Cookie: %s",
		langParam, acceptLang, langCookie)

	lang := langParam

	// 如果没有明确的URL参数，尝试从Accept-Language头获取（优先于Cookie）
	if lang == "" {
		if acceptLang != "" {
			lang = i18n.GetMatchingLang(acceptLang)
			logging.DebugLogger.Debugf("LanguageMiddleware - 从Accept-Language解析出语言: %s", lang)
		}
	}

	// 如果还没有语言设置，尝试从Cookie获取
	if lang == "" {
		if langCookie != "" {
			lang = langCookie
			logging.DebugLogger.Debugf("LanguageMiddleware - 从Cookie获取语言: %s", lang)
		}
	}

	// 如果仍然没有语言设置，使用默认语言
	if lang == "" || !i18n.IsSupportedLang(lang) {
		oldLang := lang
		lang = i18n.GetDefaultLang()
		logging.DebugLogger.Debugf("LanguageMiddleware - 语言无效或为空 (%s)，使用默认语言: %s", oldLang, lang)
	}

	// 保存语言偏好到上下文
	ctx.Values().Set("language", lang)

	// 记录最终设置的语言
	logging.DebugLogger.Debugf("LanguageMiddleware - 最终设置语言: %s", lang)

	// 如果有明确的语言选择，设置Cookie保持会话
	if ctx.URLParam("lang") != "" {
		ctx.SetCookieKV("language", lang, iris.CookieExpires(time.Hour*24*30)) // 30天过期
	}

	ctx.Next()
}
