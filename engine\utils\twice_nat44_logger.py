#!/usr/bin/env python3
"""
twice-nat44专用日志记录和错误处理模块

本模块提供twice-nat44功能的专用日志记录和错误处理功能，包括：
1. 结构化日志记录
2. 性能监控日志
3. 错误分类和处理
4. 调试信息收集

作者: FortiGate转换系统
版本: 1.0
日期: 2025-08-08
"""

import time
import json
import traceback
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
from datetime import datetime

from engine.utils.logger import log


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


class ErrorCategory(Enum):
    """错误分类枚举"""
    VALIDATION_ERROR = "validation_error"
    EVALUATION_ERROR = "evaluation_error"
    CONFIGURATION_ERROR = "configuration_error"
    PERFORMANCE_ERROR = "performance_error"
    SYSTEM_ERROR = "system_error"


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: str
    level: LogLevel
    category: str
    message: str
    details: Dict[str, Any] = None
    policy_name: Optional[str] = None
    evaluation_time: Optional[float] = None
    error_category: Optional[ErrorCategory] = None


@dataclass
class PerformanceMetrics:
    """性能度量"""
    operation: str
    start_time: float
    end_time: float
    duration: float
    memory_usage: Optional[float] = None
    cpu_usage: Optional[float] = None
    details: Dict[str, Any] = None


class TwiceNat44Logger:
    """twice-nat44专用日志记录器"""
    
    def __init__(self, log_file: str = "output/logs/twice_nat44_detailed.log"):
        self.log_file = Path(log_file)
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        self.log_entries = []
        self.performance_metrics = []
        self._session_start_time = time.time()
    
    def log_evaluation_start(self, policy_name: str, policy_details: Dict[str, Any]):
        """记录评估开始"""
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=LogLevel.DEBUG,
            category="evaluation_start",
            message=f"开始评估策略: {policy_name}",
            policy_name=policy_name,
            details=policy_details
        )
        self._add_log_entry(entry)
    
    def log_evaluation_result(self, policy_name: str, result: Dict[str, Any], 
                            evaluation_time: float):
        """记录评估结果"""
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=LogLevel.INFO,
            category="evaluation_result",
            message=f"策略评估完成: {policy_name}",
            policy_name=policy_name,
            evaluation_time=evaluation_time,
            details=result
        )
        self._add_log_entry(entry)
    
    def log_dimension_score(self, policy_name: str, dimension: str, 
                           score: float, reason: str, details: Dict[str, Any] = None):
        """记录维度评分"""
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=LogLevel.DEBUG,
            category="dimension_score",
            message=f"维度评分 - {dimension}: {score}分",
            policy_name=policy_name,
            details={
                "dimension": dimension,
                "score": score,
                "reason": reason,
                "dimension_details": details or {}
            }
        )
        self._add_log_entry(entry)
    
    def log_pool_validation(self, policy_name: str, pool_names: List[str], 
                           validation_result: Dict[str, Any]):
        """记录IP池验证"""
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=LogLevel.DEBUG,
            category="pool_validation",
            message=f"IP池验证: {pool_names}",
            policy_name=policy_name,
            details={
                "pool_names": pool_names,
                "validation_result": validation_result
            }
        )
        self._add_log_entry(entry)
    
    def log_vip_analysis(self, policy_name: str, vip_analysis: Dict[str, Any]):
        """记录VIP分析"""
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=LogLevel.DEBUG,
            category="vip_analysis",
            message=f"VIP配置分析完成",
            policy_name=policy_name,
            details=vip_analysis
        )
        self._add_log_entry(entry)
    
    def log_error(self, policy_name: str, error_category: ErrorCategory, 
                  error_message: str, exception: Exception = None):
        """记录错误"""
        details = {
            "error_message": error_message,
            "error_category": error_category.value
        }
        
        if exception:
            details.update({
                "exception_type": type(exception).__name__,
                "exception_message": str(exception),
                "traceback": traceback.format_exc()
            })
        
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=LogLevel.ERROR,
            category="error",
            message=f"错误: {error_message}",
            policy_name=policy_name,
            error_category=error_category,
            details=details
        )
        self._add_log_entry(entry)
    
    def log_warning(self, policy_name: str, warning_message: str, 
                   details: Dict[str, Any] = None):
        """记录警告"""
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=LogLevel.WARNING,
            category="warning",
            message=warning_message,
            policy_name=policy_name,
            details=details or {}
        )
        self._add_log_entry(entry)
    
    def log_performance_metric(self, metric: PerformanceMetrics):
        """记录性能度量"""
        self.performance_metrics.append(metric)
        
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=LogLevel.DEBUG,
            category="performance",
            message=f"性能度量 - {metric.operation}: {metric.duration:.3f}s",
            details=asdict(metric)
        )
        self._add_log_entry(entry)
    
    def _add_log_entry(self, entry: LogEntry):
        """添加日志条目"""
        self.log_entries.append(entry)
        
        # 同时写入标准日志
        log_message = f"[{entry.category}] {entry.message}"
        if entry.policy_name:
            log_message = f"[{entry.policy_name}] {log_message}"
        
        log(log_message, entry.level.value)
    
    def save_detailed_log(self):
        """保存详细日志到文件"""
        try:
            log_data = {
                "session_info": {
                    "start_time": self._session_start_time,
                    "end_time": time.time(),
                    "duration": time.time() - self._session_start_time,
                    "total_entries": len(self.log_entries),
                    "total_metrics": len(self.performance_metrics)
                },
                "log_entries": [asdict(entry) for entry in self.log_entries],
                "performance_metrics": [asdict(metric) for metric in self.performance_metrics]
            }
            
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, indent=2, ensure_ascii=False, default=str)
            
            log(f"详细日志已保存到: {self.log_file}", "info")
            
        except Exception as e:
            log(f"保存详细日志失败: {e}", "error")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        error_entries = [entry for entry in self.log_entries if entry.level == LogLevel.ERROR]
        warning_entries = [entry for entry in self.log_entries if entry.level == LogLevel.WARNING]
        
        error_categories = {}
        for entry in error_entries:
            if entry.error_category:
                category = entry.error_category.value
                error_categories[category] = error_categories.get(category, 0) + 1
        
        return {
            "total_errors": len(error_entries),
            "total_warnings": len(warning_entries),
            "error_categories": error_categories,
            "recent_errors": [asdict(entry) for entry in error_entries[-5:]],
            "recent_warnings": [asdict(entry) for entry in warning_entries[-5:]]
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.performance_metrics:
            return {"message": "无性能数据"}
        
        operations = {}
        for metric in self.performance_metrics:
            op = metric.operation
            if op not in operations:
                operations[op] = {
                    "count": 0,
                    "total_duration": 0,
                    "min_duration": float('inf'),
                    "max_duration": 0
                }
            
            operations[op]["count"] += 1
            operations[op]["total_duration"] += metric.duration
            operations[op]["min_duration"] = min(operations[op]["min_duration"], metric.duration)
            operations[op]["max_duration"] = max(operations[op]["max_duration"], metric.duration)
        
        # 计算平均值
        for op_data in operations.values():
            op_data["avg_duration"] = op_data["total_duration"] / op_data["count"]
        
        return {
            "total_operations": len(self.performance_metrics),
            "operations": operations,
            "session_duration": time.time() - self._session_start_time
        }


class PerformanceTimer:
    """性能计时器上下文管理器"""
    
    def __init__(self, logger: TwiceNat44Logger, operation: str, 
                 details: Dict[str, Any] = None):
        self.logger = logger
        self.operation = operation
        self.details = details or {}
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = time.time()
        duration = end_time - self.start_time
        
        metric = PerformanceMetrics(
            operation=self.operation,
            start_time=self.start_time,
            end_time=end_time,
            duration=duration,
            details=self.details
        )
        
        self.logger.log_performance_metric(metric)


# 全局日志记录器实例
twice_nat44_logger = TwiceNat44Logger()


def get_logger() -> TwiceNat44Logger:
    """获取全局日志记录器实例"""
    return twice_nat44_logger


def create_performance_timer(operation: str, details: Dict[str, Any] = None) -> PerformanceTimer:
    """创建性能计时器"""
    return PerformanceTimer(twice_nat44_logger, operation, details)
