package user

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	usermodels "irisAdminApi/application/models/user"
	"irisAdminApi/service/dao"
	"irisAdminApi/service/dao/user/dpassword"
	"irisAdminApi/service/dao/user/duser"

	"github.com/jameskeane/bcrypt"
	"github.com/kataras/iris/v12"
)

func Profile(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	profile := &duser.User{}
	err = profile.Profile(id)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, profile, response.NoErr.Msg))
	return
}

type Avatar struct {
	Avatar string
}

type Password struct {
	OldPassword string `json:"old_password"`
	Password    string `json:"password"`
}

func ChangeAvatar(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	avatar := &Avatar{}
	if err := ctx.ReadJSON(avatar); err != nil {
		logging.ErrorLogger.Errorf("change avatar read json error ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*avatar)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	err = easygorm.GetEasyGormDb().Model(&usermodels.User{}).Where("id = ?", id).Update("avatar", avatar.Avatar).Error
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
}

func ChangePassword(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	Password := Password{}
	if err := ctx.ReadJSON(&Password); err != nil {
		logging.ErrorLogger.Errorf("change avatar read json error ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(&Password)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	OldPassword := dpassword.Response{}
	err = easygorm.GetEasyGormDb().Model(&usermodels.User{}).Where("id = ?", id).Find(&OldPassword).Error
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	if ok := bcrypt.Match(Password.OldPassword, OldPassword.Password); !ok {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "旧密码错误"))
		return
	}

	err = easygorm.GetEasyGormDb().Model(&usermodels.User{}).Where("id = ?", id).Update("password", Password.Password).Error
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
}

func UpdateProfile(ctx iris.Context) {
	id, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	userReq := &duser.UserReq{}
	if err := ctx.ReadJSON(userReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*userReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	data := map[string]interface{}{
		"Name":      userReq.Name,
		"Intro":     userReq.Intro,
		"UpdatedAt": time.Now(),
	}

	if userReq.Password != "" {
		password := dpassword.Response{}

		err = easygorm.GetEasyGormDb().Model(&usermodels.User{}).Where("id = ?", id).Find(&password).Error
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
			return
		}

		if ok := bcrypt.Match(userReq.OldPassword, password.Password); !ok {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "旧密码错误"))
			return
		}
		data["Password"] = libs.HashPassword(userReq.Password)
	}

	err = easygorm.GetEasyGormDb().Model(&usermodels.User{}).Where("id = ?", id).UpdateColumns(data).Error
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func DeleteUser(ctx iris.Context) {
	err := dao.Delete(&duser.User{}, ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

func GetUser(ctx iris.Context) {
	info := duser.User{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func CreateUser(ctx iris.Context) {
	userReq := &duser.UserReq{}
	if err := ctx.ReadJSON(userReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*userReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}

	err := dao.Create(&duser.User{}, ctx, map[string]interface{}{
		"Name":     userReq.Name,
		"Username": userReq.Username,
		"Password": libs.HashPassword(userReq.Password),
		"Intro":    userReq.Intro,
		// "Avatar":    userReq.Avatar,
		"Avatar":       fmt.Sprintf("http://%s:%s/upload/user_avatar.jpg", libs.Config.Host, strconv.FormatInt(libs.Config.Port, 10)),
		"CreatedAt":    time.Now(),
		"UpdatedAt":    time.Now(),
		"Roles":        userReq.Roles,
		"DepartmentID": userReq.DepartmentID,
	})
	if err != nil {
		logging.ErrorLogger.Errorf("create user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}

	ctx.JSON(response.NewResponse(response.NoErr.Code, userReq, response.NoErr.Msg))
	return
}

func UpdateUser(ctx iris.Context) {
	userReq := &duser.UserReq{}
	if err := ctx.ReadJSON(userReq); err != nil {
		logging.ErrorLogger.Errorf("create user read json err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}

	validErr := libs.Validate.Struct(*userReq)
	errs := libs.ValidRequest(validErr)
	if len(errs) > 0 {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
		return
	}
	data := map[string]interface{}{
		"Name":         userReq.Name,
		"Intro":        userReq.Intro,
		"UpdatedAt":    time.Now(),
		"DepartmentID": userReq.DepartmentID,
		"Enable":       userReq.Enable,
	}
	// update roles
	password := dpassword.Response{}
	dao.Find(&password, ctx)

	if password.Id != 1 && libs.InArrayS(userReq.Roles, "1") {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "禁止分配超级管理员角色"))
		return
	}

	roles := []string{}
	for _, role := range userReq.Roles {
		roles = append(roles, fmt.Sprintf("role::%v", role))
	}
	duser.UpdateRoleForUserById(password.Id, roles)
	// update group

	// update department
	// 		"Password":  libs.HashPassword(userReq.Password),
	if userReq.Password != "" {
		data["Password"] = libs.HashPassword(userReq.Password)
	}

	err := dao.Update(&duser.User{}, ctx, data)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
	return
}

// GetUsers
func GetUsers(ctx iris.Context) {
	token := ctx.GetHeader("sysid")
	if token != "cvd20220614" {
		uid, err := dao.GetAuthId(ctx)
		if err != nil || uid == 0 {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "无权限访问"))
			return
		}
	}
	name := ctx.FormValue("name")
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")

	list, err := dao.All(&duser.User{}, ctx, name, sort, orderBy, page, pageSize)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
	return
}

type AuditorResponse struct {
	Id   uint   `copier:"must" json:"id"`
	Name string `copier:"must" json:"name"`
}
type AuditorsResponse struct {
	AuditorResponse
}

func GetAuditors(ctx iris.Context) {

	uid, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	auditors, err := duser.FindAuditors(uid)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, auditors, response.NoErr.Msg))
	return
}

func GetAllAuditors(ctx iris.Context) {

	uid, err := dao.GetAuthId(ctx)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	auditors, err := duser.FindAllAuditors(uid)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, auditors, response.NoErr.Msg))
	return
}
