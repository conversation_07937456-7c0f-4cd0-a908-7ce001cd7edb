"""
FortiGate twice-nat44转换数据模型

本模块定义了FortiGate复合NAT转换为NTOS twice-nat44配置所需的数据结构。
基于深度分析报告和YANG模型规范，提供完整的、高质量的数据模型实现。

主要功能：
- 完整的twice-nat44规则数据结构
- FortiGate策略到twice-nat44的转换逻辑
- 智能判断机制支持
- 完整的验证和错误处理
- YANG模型合规性保证

版本: 2.0
作者: FortiGate转换系统
更新时间: 2025-08-06
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List, Tuple, Union
from enum import Enum
import re
import ipaddress
from engine.utils.logger import log
from engine.utils.i18n import _
# 避免循环导入，错误处理装饰器将在运行时动态导入


class TwiceNat44AddressType(Enum):
    """twice-nat44地址类型枚举"""
    INTERFACE = "interface"
    IP = "ip"
    POOL = "pool"


class TwiceNat44ConfigError(Exception):
    """twice-nat44配置错误"""
    pass


class TwiceNat44ValidationError(Exception):
    """twice-nat44验证错误"""
    pass


def _is_valid_ipv4(ip_str: str) -> bool:
    """
    验证字符串是否为有效的IPv4地址

    Args:
        ip_str: 待验证的IP地址字符串

    Returns:
        bool: 是否为有效的IPv4地址
    """
    try:
        ipaddress.IPv4Address(ip_str)
        return True
    except (ipaddress.AddressValueError, ValueError):
        return False


def _validate_fortigate_pools(poolnames: List[str], available_pools: List[str] = None) -> List[str]:
    """
    验证FortiGate IP池格式，支持IP地址格式的池名称

    Args:
        poolnames: 池名称列表
        available_pools: 可用的池对象名称列表

    Returns:
        List[str]: 有效的池名称列表
    """
    if not poolnames:
        return []

    available_pools = available_pools or []
    valid_pools = []

    for pool in poolnames:
        # 支持IP地址格式的池名称（FortiGate常用格式）
        if _is_valid_ipv4(pool):
            valid_pools.append(pool)
            log(_("twice_nat44.pool_ip_format_detected", pool=pool), "debug")
        # 支持传统池对象名称
        elif pool in available_pools:
            valid_pools.append(pool)
            log(_("twice_nat44.pool_object_found", pool=pool), "debug")
        else:
            log(_("twice_nat44.pool_validation_failed", pool=pool), "warning")

    return valid_pools


@dataclass
class TwiceNat44Recommendation:
    """
    twice-nat44使用建议数据结构

    基于智能判断机制生成的使用建议，包含评分、置信度和详细原因。
    """
    should_use: bool
    confidence_score: float
    total_score: int
    max_score: int
    reasons: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    fallback_reason: Optional[str] = None

    def __post_init__(self):
        """初始化后验证"""
        if not (0 <= self.confidence_score <= 1):
            raise TwiceNat44ValidationError(f"Invalid confidence score: {self.confidence_score}")

        if self.total_score < 0 or self.max_score <= 0:
            raise TwiceNat44ValidationError("Invalid score values")

    @property
    def confidence_percentage(self) -> float:
        """获取置信度百分比"""
        return self.confidence_score * 100

    @property
    def score_percentage(self) -> float:
        """获取评分百分比"""
        return (self.total_score / self.max_score) * 100 if self.max_score > 0 else 0

    def add_reason(self, reason: str, score: int = 0):
        """添加推荐原因"""
        if score > 0:
            reason = f"{reason} (+{score}分)"
        self.reasons.append(reason)

    def add_warning(self, warning: str):
        """添加警告信息"""
        self.warnings.append(warning)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "should_use": self.should_use,
            "confidence_score": self.confidence_score,
            "confidence_percentage": self.confidence_percentage,
            "total_score": self.total_score,
            "max_score": self.max_score,
            "score_percentage": self.score_percentage,
            "reasons": self.reasons,
            "warnings": self.warnings,
            "fallback_reason": self.fallback_reason
        }


@dataclass
class TwiceNat44MatchConditions:
    """
    twice-nat44匹配条件数据结构
    
    定义了twice-nat44规则的匹配条件，包括源网络、目标网络、服务和时间范围。
    """
    dest_network: str
    source_network: Optional[str] = None
    service: str = "any"
    time_range: str = "any"
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.dest_network:
            raise TwiceNat44ConfigError("dest_network cannot be empty")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式，用于XML生成
        
        Returns:
            Dict[str, Any]: 匹配条件字典
        """
        result = {
            "dest-network": {"name": self.dest_network},
            "service": {"name": self.service},
            "time-range": {"value": self.time_range}
        }
        
        if self.source_network:
            result["source-network"] = {"name": self.source_network}
        
        return result
    
    def validate_network_objects(self, available_objects: List[str]) -> bool:
        """
        验证网络对象是否存在
        
        Args:
            available_objects: 可用的网络对象列表
            
        Returns:
            bool: 验证是否通过
        """
        if self.dest_network not in available_objects:
            log(_("twice_nat44.dest_network_not_found", network=self.dest_network), "warning")
            return False
        
        if self.source_network and self.source_network not in available_objects:
            log(_("twice_nat44.source_network_not_found", network=self.source_network), "warning")
            return False
        
        return True


@dataclass
class TwiceNat44SnatConfig:
    """
    twice-nat44 SNAT配置数据结构
    
    定义了twice-nat44规则的源地址转换配置。
    """
    address_type: TwiceNat44AddressType
    address_value: Optional[str] = None
    no_pat: bool = False
    try_no_pat: bool = True
    
    def __post_init__(self):
        """初始化后验证"""
        if self.address_type in [TwiceNat44AddressType.IP, TwiceNat44AddressType.POOL]:
            if not self.address_value:
                raise TwiceNat44ConfigError(f"address_value is required for {self.address_type.value}")
        
        if self.address_type == TwiceNat44AddressType.IP:
            if not self._is_valid_ipv4(self.address_value):
                raise TwiceNat44ConfigError(f"Invalid IPv4 address: {self.address_value}")
    
    def _is_valid_ipv4(self, ip: str) -> bool:
        """
        验证IPv4地址格式（使用更严格的验证）

        Args:
            ip: IP地址字符串

        Returns:
            bool: 是否为有效的IPv4地址
        """
        if not ip:
            return False

        try:
            # 使用ipaddress模块进行更严格的验证
            addr = ipaddress.IPv4Address(ip)
            # 排除特殊地址
            if addr.is_multicast or addr.is_reserved:
                return False
            return True
        except (ipaddress.AddressValueError, ValueError):
            return False

    def validate_pool_name(self, available_pools: List[str]) -> bool:
        """
        验证IP池名称是否存在，支持FortiGate IP地址格式

        Args:
            available_pools: 可用的IP池列表

        Returns:
            bool: 验证是否通过
        """
        if self.address_type == TwiceNat44AddressType.POOL:
            if not self.address_value:
                return False

            # 检查是否在可用池列表中
            if self.address_value in available_pools:
                return True

            # 如果不在列表中，检查是否是有效的IP地址格式（FortiGate常用）
            if _is_valid_ipv4(self.address_value):
                log(_("twice_nat44.pool_ip_format_accepted", pool=self.address_value), "debug")
                return True

            # 都不符合，记录警告
            log(_("twice_nat44.pool_not_found", pool=self.address_value), "warning")
            return False
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式，用于XML生成
        
        Returns:
            Dict[str, Any]: SNAT配置字典
        """
        result = {
            "no-pat": self.no_pat,
            "try-no-pat": self.try_no_pat
        }
        
        if self.address_type == TwiceNat44AddressType.INTERFACE:
            result["output-address"] = {}
        elif self.address_type == TwiceNat44AddressType.IP:
            result["ipv4-address"] = self.address_value
        elif self.address_type == TwiceNat44AddressType.POOL:
            result["pool-name"] = self.address_value
        
        return result


@dataclass
class TwiceNat44DnatConfig:
    """
    twice-nat44 DNAT配置数据结构
    
    定义了twice-nat44规则的目标地址转换配置。
    """
    ipv4_address: str
    port: Optional[int] = None
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.ipv4_address:
            raise TwiceNat44ConfigError("ipv4_address cannot be empty")
        
        if not self._is_valid_ipv4(self.ipv4_address):
            raise TwiceNat44ConfigError(f"Invalid IPv4 address: {self.ipv4_address}")
        
        if self.port is not None:
            if not (1 <= self.port <= 65535):
                raise TwiceNat44ConfigError(f"Invalid port number: {self.port}")
    
    def _is_valid_ipv4(self, ip: str) -> bool:
        """验证IPv4地址格式"""
        if not ip:
            return False
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(pattern, ip):
            return False
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式，用于XML生成
        
        Returns:
            Dict[str, Any]: DNAT配置字典
        """
        result = {"ipv4-address": self.ipv4_address}
        if self.port:
            result["port"] = self.port
        return result


@dataclass
class TwiceNat44Rule:
    """
    twice-nat44规则完整数据结构
    
    这是twice-nat44规则的主要数据模型，包含了完整的规则配置信息。
    基于已重构的XML处理框架设计，确保与现有系统的完美兼容。
    """
    name: str
    enabled: bool
    description: str
    match_conditions: TwiceNat44MatchConditions
    snat_config: TwiceNat44SnatConfig
    dnat_config: TwiceNat44DnatConfig
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.name:
            raise TwiceNat44ConfigError("Rule name cannot be empty")
        
        if not self.description:
            self.description = f"twice-nat44 rule {self.name}"
    
    @classmethod
    def from_fortigate_policy(cls, policy: Dict[str, Any], vip_config: Dict[str, Any]) -> 'TwiceNat44Rule':
        """
        从FortiGate策略创建twice-nat44规则
        
        Args:
            policy: FortiGate策略配置
            vip_config: VIP对象配置
            
        Returns:
            TwiceNat44Rule: 创建的规则对象
            
        Raises:
            TwiceNat44ConfigError: 当配置无效时
            KeyError: 当必需的配置项缺失时
        """
        try:
            # 构建规则名称 - 优先使用策略智能名称
            policy_name = policy.get("intelligent_name") or policy.get("name", "unknown")
            vip_name = vip_config.get("name", "unknown")
            rule_name = f"{policy_name}_{vip_name}_twice_nat"
            
            # 构建匹配条件 - 包含服务映射
            service_name = policy.get("service", ["any"])[0] if isinstance(policy.get("service"), list) else policy.get("service", "any")

            # 应用FortiGate服务映射
            mapped_service = cls._map_fortigate_service(service_name)

            match_conditions = TwiceNat44MatchConditions(
                dest_network=vip_name,
                service=mapped_service,
                time_range=policy.get("schedule", "any")
            )
            
            # 构建SNAT配置
            snat_config = TwiceNat44SnatConfig(
                address_type=TwiceNat44AddressType.INTERFACE,
                no_pat=policy.get("fixedport", "disable") == "enable",
                try_no_pat=policy.get("fixedport", "disable") != "enable"
            )
            
            # 构建DNAT配置
            mapped_ip = vip_config.get("mappedip")
            if not mapped_ip:
                raise TwiceNat44ConfigError(f"VIP {vip_name} missing mappedip")
            
            mapped_port = None
            if "mappedport" in vip_config:
                try:
                    mapped_port = int(vip_config["mappedport"])
                except (ValueError, TypeError):
                    log(_("twice_nat44.invalid_mapped_port", port=vip_config["mappedport"]), "warning")
            
            dnat_config = TwiceNat44DnatConfig(
                ipv4_address=mapped_ip,
                port=mapped_port
            )
            
            # 创建规则
            rule = cls(
                name=rule_name,
                enabled=policy.get("status", "enable") == "enable",
                description=f"FortiGate复合NAT策略 {policy_name}",
                match_conditions=match_conditions,
                snat_config=snat_config,
                dnat_config=dnat_config
            )
            
            log(_("twice_nat44.rule_created", name=rule_name), "info")
            return rule
            
        except Exception as e:
            log(_("twice_nat44.rule_creation_failed", policy=policy.get("intelligent_name") or policy.get("name", "unknown"), error=str(e)), "error")
            raise TwiceNat44ConfigError(f"Failed to create twice-nat44 rule: {str(e)}")

    @staticmethod
    def _map_fortigate_service(service: str) -> str:
        """
        映射FortiGate服务名称到NTOS格式

        Args:
            service: FortiGate服务名称

        Returns:
            str: 映射后的NTOS服务名称
        """
        if not service:
            return "any"

        try:
            # 使用FortigateServiceMapper进行映射
            from engine.mappers.fortigate_service_mapper import FortigateServiceMapper
            mapper = FortigateServiceMapper()
            mapping_result = mapper.map_service(service)

            if mapping_result and "ntos_service" in mapping_result:
                return mapping_result["ntos_service"]

        except Exception:
            # 映射失败时使用回退映射
            pass

        # 回退映射
        simple_mappings = {
            "HTTP": "http",
            "HTTPS": "https",
            "FTP": "ftp",
            "SSH": "ssh",
            "TELNET": "telnet",
            "ALL": "any",
            "ALL_TCP": "tcp",
            "ALL_UDP": "udp",
            "ALL_ICMP": "icmp"
        }

        return simple_mappings.get(service.upper(), service.lower())
    
    def to_nat_rule_dict(self) -> Dict[str, Any]:
        """
        转换为NAT规则字典格式，用于XML生成
        
        Returns:
            Dict[str, Any]: NAT规则字典
        """
        return {
            "name": self.name,
            "type": "twice-nat44",
            "rule_en": self.enabled,
            "desc": self.description,
            "twice-nat44": {
                "match": self.match_conditions.to_dict(),
                "snat": self.snat_config.to_dict(),
                "dnat": self.dnat_config.to_dict()
            }
        }
    
    def validate(self) -> bool:
        """
        验证规则配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 验证基本字段
            if not self.name or not self.description:
                return False
            
            # 验证匹配条件
            if not self.match_conditions.dest_network:
                return False
            
            # 验证SNAT配置
            if self.snat_config.address_type in [TwiceNat44AddressType.IP, TwiceNat44AddressType.POOL]:
                if not self.snat_config.address_value:
                    return False
            
            # 验证DNAT配置
            if not self.dnat_config.ipv4_address:
                return False
            
            return True
            
        except Exception as e:
            log(_("twice_nat44.validation_failed", name=self.name, error=str(e)), "error")
            return False
    
    def get_xml_namespace(self) -> str:
        """
        获取XML命名空间

        Returns:
            str: XML命名空间URI
        """
        return "urn:ruijie:ntos:params:xml:ns:yang:nat"

    @staticmethod
    def evaluate_twice_nat44_suitability(
        policy: Dict[str, Any],
        vip_configs: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> TwiceNat44Recommendation:
        """
        评估策略是否适合使用twice-nat44

        基于深度分析报告中的判断标准，对FortiGate策略进行智能评估。

        Args:
            policy: FortiGate策略配置
            vip_configs: VIP配置字典
            context: 上下文信息（可选）

        Returns:
            TwiceNat44Recommendation: 评估结果和建议
        """
        context = context or {}
        recommendation = TwiceNat44Recommendation(
            should_use=False,
            confidence_score=0.0,
            total_score=0,
            max_score=100
        )

        try:
            # 基本条件检查
            has_vip = any(addr in vip_configs for addr in policy.get("dstaddr", []))
            nat_enabled = policy.get("nat", "disable") == "enable"

            if not (has_vip and nat_enabled):
                recommendation.fallback_reason = "策略不满足基本条件：需要同时有VIP和启用NAT"
                return recommendation

            # 评分标准（基于分析报告）
            score = 0

            # 1. VIP数量评分 (权重: 30%)
            vip_count = len([addr for addr in policy.get("dstaddr", []) if addr in vip_configs])
            if vip_count == 1:
                score += 30
                recommendation.add_reason("单一VIP配置，最适合twice-nat44", 30)
            elif vip_count <= 3:
                score += 15
                recommendation.add_reason(f"VIP数量适中({vip_count}个)，适合twice-nat44", 15)
            else:
                recommendation.add_warning(f"VIP数量较多({vip_count}个)，可能增加配置复杂度")

            # 2. IP池使用评分 (权重: 15% - 从25%降低，减少绝对影响)
            if policy.get("ippool", "disable") == "disable":
                score += 15
                recommendation.add_reason("不使用IP池，配置简单", 15)
            else:
                # 检查IP池配置有效性
                poolnames = policy.get("poolname", [])
                if not poolnames:
                    score += 5
                    recommendation.add_warning("启用IP池但未指定池名称")
                else:
                    # 验证FortiGate IP池格式（支持IP地址格式）
                    available_pools = context.get("available_pools", [])
                    valid_pools = _validate_fortigate_pools(poolnames, available_pools)

                    if valid_pools:
                        score += 12  # 给予较高分数，支持IP池场景
                        recommendation.add_reason(f"使用有效IP池: {valid_pools}", 12)
                        log(_("twice_nat44.ippool_supported", pools=valid_pools), "info")
                    else:
                        score += 8   # 给予中等分数，需要进一步验证
                        recommendation.add_warning(f"IP池需要验证: {poolnames}")
                        log(_("twice_nat44.ippool_needs_validation", pools=poolnames), "warning")

            # 3. VIP配置完整性评分 (权重: 25% - 从20%提高，强调配置质量)
            complete_vips = 0
            for addr in policy.get("dstaddr", []):
                if addr in vip_configs:
                    vip = vip_configs[addr]
                    if all(key in vip for key in ["extip", "mappedip"]):
                        complete_vips += 1

            if complete_vips == vip_count and vip_count > 0:
                score += 25
                recommendation.add_reason("所有VIP配置完整", 25)
            elif complete_vips > 0:
                score += 12
                recommendation.add_reason(f"部分VIP配置完整({complete_vips}/{vip_count})", 12)
                recommendation.add_warning("存在不完整的VIP配置")

            # 4. 设备版本支持评分 (权重: 15%)
            ntos_version = context.get("ntos_version", "R11")
            if ntos_version >= "R11":
                score += 15
                recommendation.add_reason(f"目标设备版本({ntos_version})支持twice-nat44", 15)
            else:
                recommendation.add_warning(f"目标设备版本({ntos_version})可能不支持twice-nat44")
                recommendation.fallback_reason = "目标设备版本不支持twice-nat44"

            # 5. 服务复杂度评分 (权重: 15% - 从10%提高，重视服务复杂度影响)
            service_count = len(policy.get("service", []))
            if service_count <= 2:
                score += 15
                recommendation.add_reason("服务配置简单，适合twice-nat44", 15)
            elif service_count <= 5:
                score += 8
                recommendation.add_reason("服务配置适中，可以使用twice-nat44", 8)
            else:
                score += 3
                recommendation.add_warning(f"服务配置复杂({service_count}个服务)，可能影响twice-nat44性能")

            # 计算最终结果
            recommendation.total_score = score
            recommendation.confidence_score = score / 100.0

            # 判断是否推荐使用（阈值：65分 - 从80分降低，支持更多适用场景）
            threshold = context.get("twice_nat44_threshold", 65)
            recommendation.should_use = score >= threshold and not recommendation.fallback_reason

            if recommendation.should_use:
                recommendation.add_reason(f"总评分{score}分，超过阈值{threshold}分，推荐使用twice-nat44")
            else:
                if not recommendation.fallback_reason:
                    recommendation.fallback_reason = f"总评分{score}分，未达到阈值{threshold}分"

            log(_("twice_nat44.evaluation_completed",
                 policy=policy.get("intelligent_name") or policy.get("name", "unknown"),
                 score=score,
                 should_use=recommendation.should_use), "info")

            return recommendation

        except Exception as e:
            log(_("twice_nat44.evaluation_failed",
                 policy=policy.get("intelligent_name") or policy.get("name", "unknown"),
                 error=str(e)), "error")
            recommendation.fallback_reason = f"评估过程出错: {str(e)}"
            return recommendation
