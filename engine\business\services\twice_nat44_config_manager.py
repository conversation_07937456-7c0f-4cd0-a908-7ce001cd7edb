"""
FortiGate twice-nat44配置管理服务

本模块提供twice-nat44功能的配置管理、监控和统计功能，
支持渐进式实施策略和运行时配置调整。

主要功能：
- 配置参数管理
- 使用统计监控
- 性能指标收集
- 渐进式实施支持
- 运行时配置调整

版本: 1.0
作者: FortiGate转换系统
创建时间: 2025-08-06
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import threading
from engine.utils.logger import log
from engine.utils.i18n import _


class ImplementationPhase(Enum):
    """实施阶段枚举"""
    DISABLED = "disabled"
    PILOT = "pilot"
    GRADUAL = "gradual"
    FULL = "full"


@dataclass
class TwiceNat44Config:
    """
    twice-nat44配置参数
    """
    # 基本开关
    enabled: bool = True
    
    # 智能判断参数
    evaluation_threshold: int = 65  # 从80降低到65，支持更多适用场景
    high_confidence_threshold: int = 80  # 从90降低到80
    low_confidence_threshold: int = 50   # 从60降低到50
    
    # 回退机制参数
    enable_fallback: bool = True
    fallback_on_error: bool = True
    max_fallback_attempts: int = 3
    
    # 性能参数
    max_vips_per_rule: int = 3
    enable_optimization: bool = True
    enable_caching: bool = True
    
    # 监控参数
    enable_monitoring: bool = True
    enable_detailed_logging: bool = False
    log_evaluation_decisions: bool = True
    
    # 实施策略参数
    implementation_phase: ImplementationPhase = ImplementationPhase.GRADUAL
    pilot_policy_patterns: List[str] = field(default_factory=lambda: ["test_*", "dev_*"])
    gradual_rollout_percentage: float = 50.0
    
    # YANG验证参数
    enable_yang_validation: bool = True
    strict_validation: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "enabled": self.enabled,
            "evaluation_threshold": self.evaluation_threshold,
            "high_confidence_threshold": self.high_confidence_threshold,
            "low_confidence_threshold": self.low_confidence_threshold,
            "enable_fallback": self.enable_fallback,
            "fallback_on_error": self.fallback_on_error,
            "max_fallback_attempts": self.max_fallback_attempts,
            "max_vips_per_rule": self.max_vips_per_rule,
            "enable_optimization": self.enable_optimization,
            "enable_caching": self.enable_caching,
            "enable_monitoring": self.enable_monitoring,
            "enable_detailed_logging": self.enable_detailed_logging,
            "log_evaluation_decisions": self.log_evaluation_decisions,
            "implementation_phase": self.implementation_phase.value,
            "pilot_policy_patterns": self.pilot_policy_patterns,
            "gradual_rollout_percentage": self.gradual_rollout_percentage,
            "enable_yang_validation": self.enable_yang_validation,
            "strict_validation": self.strict_validation
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TwiceNat44Config':
        """从字典创建配置对象"""
        config = cls()
        for key, value in data.items():
            if key == "implementation_phase":
                config.implementation_phase = ImplementationPhase(value)
            elif hasattr(config, key):
                setattr(config, key, value)
        return config


@dataclass
class TwiceNat44Statistics:
    """
    twice-nat44使用统计
    """
    # 基本统计
    total_evaluations: int = 0
    successful_generations: int = 0
    failed_generations: int = 0
    fallback_count: int = 0
    
    # 评分统计
    average_evaluation_score: float = 0.0
    high_confidence_count: int = 0
    low_confidence_count: int = 0
    
    # 性能统计
    total_processing_time_ms: float = 0.0
    average_processing_time_ms: float = 0.0
    
    # 时间统计
    start_time: datetime = field(default_factory=datetime.now)
    last_update_time: datetime = field(default_factory=datetime.now)
    
    def update_evaluation(self, score: int, processing_time_ms: float, success: bool):
        """更新评估统计"""
        self.total_evaluations += 1
        self.total_processing_time_ms += processing_time_ms
        self.average_processing_time_ms = self.total_processing_time_ms / self.total_evaluations
        
        # 更新评分统计
        self.average_evaluation_score = (
            (self.average_evaluation_score * (self.total_evaluations - 1) + score) / 
            self.total_evaluations
        )
        
        if score >= 90:
            self.high_confidence_count += 1
        elif score <= 60:
            self.low_confidence_count += 1
        
        if success:
            self.successful_generations += 1
        else:
            self.failed_generations += 1
        
        self.last_update_time = datetime.now()
    
    def update_fallback(self):
        """更新回退统计"""
        self.fallback_count += 1
        self.last_update_time = datetime.now()
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_evaluations == 0:
            return 0.0
        return (self.successful_generations / self.total_evaluations) * 100
    
    @property
    def fallback_rate(self) -> float:
        """计算回退率"""
        if self.total_evaluations == 0:
            return 0.0
        return (self.fallback_count / self.total_evaluations) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "total_evaluations": self.total_evaluations,
            "successful_generations": self.successful_generations,
            "failed_generations": self.failed_generations,
            "fallback_count": self.fallback_count,
            "average_evaluation_score": round(self.average_evaluation_score, 2),
            "high_confidence_count": self.high_confidence_count,
            "low_confidence_count": self.low_confidence_count,
            "total_processing_time_ms": round(self.total_processing_time_ms, 2),
            "average_processing_time_ms": round(self.average_processing_time_ms, 2),
            "success_rate": round(self.success_rate, 2),
            "fallback_rate": round(self.fallback_rate, 2),
            "start_time": self.start_time.isoformat(),
            "last_update_time": self.last_update_time.isoformat()
        }


class TwiceNat44ConfigManager:
    """
    twice-nat44配置管理器
    
    负责管理twice-nat44功能的配置参数、监控统计和实施策略。
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径（可选）
        """
        self.config_file = config_file
        self.config = TwiceNat44Config()
        self.statistics = TwiceNat44Statistics()
        self._lock = threading.Lock()
        
        # 加载配置
        if config_file:
            self.load_config(config_file)
        
        log(_("twice_nat44_config_manager.initialized"), "info")
    
    def get_config(self) -> TwiceNat44Config:
        """获取当前配置"""
        with self._lock:
            return self.config
    
    def update_config(self, **kwargs) -> bool:
        """
        更新配置参数
        
        Args:
            **kwargs: 要更新的配置参数
            
        Returns:
            bool: 更新是否成功
        """
        try:
            with self._lock:
                for key, value in kwargs.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
                        log(_("twice_nat44_config_manager.config_updated",
                             key=key, value=value), "info")
                    else:
                        log(_("twice_nat44_config_manager.unknown_config_key",
                             key=key), "warning")
                        return False
                
                # 保存配置（如果有配置文件）
                if self.config_file:
                    self.save_config(self.config_file)
                
                return True
                
        except Exception as e:
            log(_("twice_nat44_config_manager.config_update_failed",
                 error=str(e)), "error")
            return False
    
    def should_use_twice_nat44(self, policy_name: str) -> bool:
        """
        根据实施策略判断是否应该使用twice-nat44
        
        Args:
            policy_name: 策略名称
            
        Returns:
            bool: 是否应该使用twice-nat44
        """
        with self._lock:
            if not self.config.enabled:
                return False
            
            phase = self.config.implementation_phase
            
            if phase == ImplementationPhase.DISABLED:
                return False
            elif phase == ImplementationPhase.PILOT:
                # 试点阶段：只对匹配模式的策略使用
                return any(self._matches_pattern(policy_name, pattern) 
                          for pattern in self.config.pilot_policy_patterns)
            elif phase == ImplementationPhase.GRADUAL:
                # 渐进阶段：按百分比随机选择
                import hashlib
                hash_value = int(hashlib.md5(policy_name.encode()).hexdigest()[:8], 16)
                percentage = (hash_value % 100) + 1
                return percentage <= self.config.gradual_rollout_percentage
            elif phase == ImplementationPhase.FULL:
                # 全面阶段：所有策略都使用
                return True
            
            return False
    
    def record_evaluation(self, policy_name: str, score: int, processing_time_ms: float, 
                         success: bool, used_twice_nat44: bool):
        """
        记录评估结果
        
        Args:
            policy_name: 策略名称
            score: 评估分数
            processing_time_ms: 处理时间（毫秒）
            success: 是否成功
            used_twice_nat44: 是否使用了twice-nat44
        """
        try:
            with self._lock:
                if used_twice_nat44:
                    self.statistics.update_evaluation(score, processing_time_ms, success)
                
                if self.config.enable_detailed_logging:
                    log(_("twice_nat44_config_manager.evaluation_recorded",
                         policy=policy_name,
                         score=score,
                         time=processing_time_ms,
                         success=success,
                         used_twice_nat44=used_twice_nat44), "debug")
                
        except Exception as e:
            log(_("twice_nat44_config_manager.record_evaluation_failed",
                 error=str(e)), "error")
    
    def record_fallback(self, policy_name: str, reason: str):
        """
        记录回退事件
        
        Args:
            policy_name: 策略名称
            reason: 回退原因
        """
        try:
            with self._lock:
                self.statistics.update_fallback()
                
                log(_("twice_nat44_config_manager.fallback_recorded",
                     policy=policy_name,
                     reason=reason), "info")
                
        except Exception as e:
            log(_("twice_nat44_config_manager.record_fallback_failed",
                 error=str(e)), "error")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return self.statistics.to_dict()
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        with self._lock:
            stats = self.statistics
            
            # 计算健康指标
            health_score = 100
            issues = []
            
            # 成功率检查
            if stats.success_rate < 90:
                health_score -= 20
                issues.append(f"Low success rate: {stats.success_rate:.1f}%")
            
            # 回退率检查
            if stats.fallback_rate > 20:
                health_score -= 15
                issues.append(f"High fallback rate: {stats.fallback_rate:.1f}%")
            
            # 性能检查
            if stats.average_processing_time_ms > 100:
                health_score -= 10
                issues.append(f"High processing time: {stats.average_processing_time_ms:.1f}ms")
            
            # 确定健康状态
            if health_score >= 90:
                status = "healthy"
            elif health_score >= 70:
                status = "warning"
            else:
                status = "critical"
            
            return {
                "status": status,
                "health_score": max(0, health_score),
                "issues": issues,
                "statistics": stats.to_dict(),
                "config": self.config.to_dict()
            }
    
    def load_config(self, config_file: str) -> bool:
        """
        从文件加载配置
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            with self._lock:
                self.config = TwiceNat44Config.from_dict(config_data)
            
            log(_("twice_nat44_config_manager.config_loaded",
                 file=config_file), "info")
            return True
            
        except Exception as e:
            log(_("twice_nat44_config_manager.config_load_failed",
                 file=config_file, error=str(e)), "error")
            return False
    
    def save_config(self, config_file: str) -> bool:
        """
        保存配置到文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                config_data = self.config.to_dict()
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            log(_("twice_nat44_config_manager.config_saved",
                 file=config_file), "info")
            return True
            
        except Exception as e:
            log(_("twice_nat44_config_manager.config_save_failed",
                 file=config_file, error=str(e)), "error")
            return False
    
    def _matches_pattern(self, text: str, pattern: str) -> bool:
        """检查文本是否匹配模式"""
        import fnmatch
        return fnmatch.fnmatch(text, pattern)
