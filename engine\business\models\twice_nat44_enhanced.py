#!/usr/bin/env python3
"""
twice-nat44增强模型 - 重构版本

本模块提供重构后的twice-nat44功能，具有更好的可读性、可维护性和性能。

主要改进：
1. 模块化评估逻辑
2. 增强的错误处理
3. 详细的日志记录
4. 性能优化
5. 更清晰的代码结构

作者: FortiGate转换系统
版本: 2.0
日期: 2025-08-08
"""

import ipaddress
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from functools import lru_cache

try:
    from engine.utils.logger import log
except ImportError:
    def log(message, level="info"):
        print(f"[{level.upper()}] {message}")

def _(key, **kwargs):
    """简单的翻译函数替代"""
    return key.format(**kwargs) if kwargs else key

# 导入TwiceNat44Recommendation类
try:
    from engine.business.models.twice_nat44_models import TwiceNat44Recommendation
except ImportError:
    # 如果导入失败，创建一个简单的替代类
    @dataclass
    class TwiceNat44Recommendation:
        should_use: bool
        confidence_score: float
        total_score: int
        max_score: int
        reasons: List[str] = None
        warnings: List[str] = None
        fallback_reason: Optional[str] = None
        dimension_scores: Optional[List['DimensionScore']] = None
        evaluation_context: Optional['EvaluationContext'] = None
        evaluation_time: Optional[float] = None

        def __post_init__(self):
            if self.reasons is None:
                self.reasons = []
            if self.warnings is None:
                self.warnings = []


class PoolNameFormat(Enum):
    """IP池名称格式枚举"""
    IP_ADDRESS = "ip_address"
    TRADITIONAL = "traditional"
    MIXED = "mixed"
    INVALID = "invalid"


class EvaluationDimension(Enum):
    """评估维度枚举"""
    VIP_COUNT = "vip_count"
    VIP_COMPLETENESS = "vip_completeness"
    IPPOOL_USAGE = "ippool_usage"
    SERVICE_COMPLEXITY = "service_complexity"
    INTERFACE_CONFIG = "interface_config"


@dataclass
class EvaluationWeight:
    """评估权重配置"""
    vip_count: float = 0.30          # VIP配置数量权重
    vip_completeness: float = 0.25   # VIP配置完整性权重
    ippool_usage: float = 0.15       # IP池使用权重
    service_complexity: float = 0.15 # 服务复杂度权重
    interface_config: float = 0.15   # 接口配置权重
    
    def validate(self) -> bool:
        """验证权重总和是否为1.0"""
        total = (self.vip_count + self.vip_completeness + 
                self.ippool_usage + self.service_complexity + 
                self.interface_config)
        return abs(total - 1.0) < 0.01


@dataclass
class DimensionScore:
    """单个维度的评分结果"""
    dimension: EvaluationDimension
    score: float
    max_score: float
    reason: str
    details: Dict[str, Any] = None


@dataclass
class EvaluationContext:
    """评估上下文"""
    ntos_version: str
    threshold: int = 65
    high_confidence_threshold: int = 80
    low_confidence_threshold: int = 50
    available_pools: List[str] = None
    weights: EvaluationWeight = None
    
    def __post_init__(self):
        if self.available_pools is None:
            self.available_pools = []
        if self.weights is None:
            self.weights = EvaluationWeight()


class IPPoolValidator:
    """IP池验证器 - 重构版本"""
    
    @staticmethod
    @lru_cache(maxsize=1000)
    def is_valid_ipv4(ip_str: str) -> bool:
        """
        验证字符串是否为有效的IPv4地址（带缓存）
        
        Args:
            ip_str: 待验证的IP地址字符串
            
        Returns:
            bool: 是否为有效的IPv4地址
        """
        try:
            ipaddress.IPv4Address(ip_str)
            return True
        except (ipaddress.AddressValueError, ValueError):
            return False
    
    @classmethod
    def detect_pool_format(cls, pool_name: str) -> PoolNameFormat:
        """
        检测池名称格式
        
        Args:
            pool_name: 池名称
            
        Returns:
            PoolNameFormat: 池名称格式类型
        """
        if not pool_name:
            return PoolNameFormat.INVALID
        
        if cls.is_valid_ipv4(pool_name):
            return PoolNameFormat.IP_ADDRESS
        elif pool_name.isalnum() or '_' in pool_name or '-' in pool_name:
            return PoolNameFormat.TRADITIONAL
        else:
            return PoolNameFormat.INVALID
    
    @classmethod
    def validate_pool_list(cls, pool_names: List[str], 
                          available_pools: List[str] = None) -> Tuple[List[str], Dict[str, PoolNameFormat]]:
        """
        验证池名称列表
        
        Args:
            pool_names: 池名称列表
            available_pools: 可用的池对象名称列表
            
        Returns:
            Tuple[List[str], Dict[str, PoolNameFormat]]: (有效池名称列表, 格式检测结果)
        """
        if not pool_names:
            return [], {}
        
        available_pools = available_pools or []
        valid_pools = []
        format_results = {}
        
        for pool in pool_names:
            pool_format = cls.detect_pool_format(pool)
            format_results[pool] = pool_format
            
            if pool_format == PoolNameFormat.IP_ADDRESS:
                valid_pools.append(pool)
                log(_("twice_nat44.pool_ip_format_detected", pool=pool), "debug")
            elif pool_format == PoolNameFormat.TRADITIONAL and pool in available_pools:
                valid_pools.append(pool)
                log(_("twice_nat44.pool_object_found", pool=pool), "debug")
            elif pool_format == PoolNameFormat.TRADITIONAL:
                log(_("twice_nat44.pool_not_in_available", pool=pool), "warning")
            else:
                log(_("twice_nat44.pool_invalid_format", pool=pool), "warning")
        
        return valid_pools, format_results


class TwiceNat44Evaluator:
    """twice-nat44评估器 - 重构版本"""
    
    def __init__(self, context: EvaluationContext):
        self.context = context
        self.pool_validator = IPPoolValidator()
        self._evaluation_start_time = None
    
    def evaluate_policy(self, policy: Dict[str, Any], 
                       vip_configs: Dict[str, Dict[str, Any]]) -> 'TwiceNat44Recommendation':
        """
        评估策略是否适合使用twice-nat44
        
        Args:
            policy: FortiGate策略配置
            vip_configs: VIP配置字典
            
        Returns:
            TwiceNat44Recommendation: 评估推荐结果
        """
        self._evaluation_start_time = time.time()
        
        try:
            # 执行各维度评估
            dimension_scores = self._evaluate_all_dimensions(policy, vip_configs)
            
            # 计算总分
            total_score = self._calculate_total_score(dimension_scores)
            
            # 生成推荐结果
            recommendation = self._generate_recommendation(
                policy, dimension_scores, total_score
            )
            
            # 记录评估时间
            evaluation_time = time.time() - self._evaluation_start_time
            recommendation.evaluation_time = evaluation_time
            
            log(_("twice_nat44.evaluation_completed", 
                 policy=policy.get("name", "unknown"),
                 score=total_score,
                 time=f"{evaluation_time:.3f}s"), "debug")
            
            return recommendation
            
        except Exception as e:
            log(_("twice_nat44.evaluation_error", 
                 policy=policy.get("name", "unknown"),
                 error=str(e)), "error")
            return self._create_error_recommendation(policy, str(e))
    
    def _evaluate_all_dimensions(self, policy: Dict[str, Any], 
                                vip_configs: Dict[str, Dict[str, Any]]) -> List[DimensionScore]:
        """评估所有维度"""
        evaluators = {
            EvaluationDimension.VIP_COUNT: self._evaluate_vip_count,
            EvaluationDimension.VIP_COMPLETENESS: self._evaluate_vip_completeness,
            EvaluationDimension.IPPOOL_USAGE: self._evaluate_ippool_usage,
            EvaluationDimension.SERVICE_COMPLEXITY: self._evaluate_service_complexity,
            EvaluationDimension.INTERFACE_CONFIG: self._evaluate_interface_config
        }
        
        dimension_scores = []
        for dimension, evaluator in evaluators.items():
            try:
                score = evaluator(policy, vip_configs)
                dimension_scores.append(score)
            except Exception as e:
                log(_("twice_nat44.dimension_evaluation_error", 
                     dimension=dimension.value, error=str(e)), "warning")
                # 创建默认分数
                dimension_scores.append(DimensionScore(
                    dimension=dimension,
                    score=0,
                    max_score=100,
                    reason=f"评估失败: {str(e)}"
                ))
        
        return dimension_scores
    
    def _evaluate_vip_count(self, policy: Dict[str, Any], 
                           vip_configs: Dict[str, Dict[str, Any]]) -> DimensionScore:
        """评估VIP配置数量维度"""
        dstaddr = policy.get("dstaddr", [])
        vip_count = sum(1 for addr in dstaddr if addr in vip_configs)
        
        if vip_count == 0:
            return DimensionScore(
                dimension=EvaluationDimension.VIP_COUNT,
                score=0,
                max_score=100,
                reason="策略不包含VIP配置",
                details={"vip_count": 0, "dstaddr": dstaddr}
            )
        elif vip_count == 1:
            return DimensionScore(
                dimension=EvaluationDimension.VIP_COUNT,
                score=100,
                max_score=100,
                reason="单个VIP配置，最适合twice-nat44",
                details={"vip_count": 1}
            )
        elif vip_count <= 3:
            return DimensionScore(
                dimension=EvaluationDimension.VIP_COUNT,
                score=70,
                max_score=100,
                reason=f"多个VIP配置({vip_count}个)，适中",
                details={"vip_count": vip_count}
            )
        else:
            return DimensionScore(
                dimension=EvaluationDimension.VIP_COUNT,
                score=30,
                max_score=100,
                reason=f"VIP配置过多({vip_count}个)，复杂度高",
                details={"vip_count": vip_count}
            )
    
    def _evaluate_vip_completeness(self, policy: Dict[str, Any], 
                                  vip_configs: Dict[str, Dict[str, Any]]) -> DimensionScore:
        """评估VIP配置完整性维度"""
        dstaddr = policy.get("dstaddr", [])
        vip_addresses = [addr for addr in dstaddr if addr in vip_configs]
        
        if not vip_addresses:
            return DimensionScore(
                dimension=EvaluationDimension.VIP_COMPLETENESS,
                score=0,
                max_score=100,
                reason="无VIP配置",
                details={"complete_vips": 0, "total_vips": 0}
            )
        
        complete_vips = 0
        incomplete_details = []
        
        for addr in vip_addresses:
            vip = vip_configs[addr]
            required_fields = ["extip", "mappedip"]
            missing_fields = [field for field in required_fields if field not in vip]
            
            if not missing_fields:
                complete_vips += 1
            else:
                incomplete_details.append({
                    "vip": addr,
                    "missing_fields": missing_fields
                })
        
        total_vips = len(vip_addresses)
        completeness_ratio = complete_vips / total_vips
        score = completeness_ratio * 100
        
        if completeness_ratio == 1.0:
            reason = "所有VIP配置完整"
        else:
            reason = f"部分VIP配置完整({complete_vips}/{total_vips})"
        
        return DimensionScore(
            dimension=EvaluationDimension.VIP_COMPLETENESS,
            score=score,
            max_score=100,
            reason=reason,
            details={
                "complete_vips": complete_vips,
                "total_vips": total_vips,
                "incomplete_details": incomplete_details
            }
        )
    
    def _evaluate_ippool_usage(self, policy: Dict[str, Any], 
                              vip_configs: Dict[str, Dict[str, Any]]) -> DimensionScore:
        """评估IP池使用维度"""
        if policy.get("ippool", "disable") == "disable":
            return DimensionScore(
                dimension=EvaluationDimension.IPPOOL_USAGE,
                score=100,
                max_score=100,
                reason="不使用IP池，配置简单",
                details={"uses_ippool": False}
            )
        
        poolnames = policy.get("poolname", [])
        if not poolnames:
            return DimensionScore(
                dimension=EvaluationDimension.IPPOOL_USAGE,
                score=30,
                max_score=100,
                reason="启用IP池但未指定池名称",
                details={"uses_ippool": True, "pool_count": 0}
            )
        
        # 验证IP池格式
        valid_pools, format_results = self.pool_validator.validate_pool_list(
            poolnames, self.context.available_pools
        )
        
        if valid_pools:
            # 分析池格式分布
            format_counts = {}
            for pool_format in format_results.values():
                format_counts[pool_format] = format_counts.get(pool_format, 0) + 1
            
            score = 80  # 基础分数
            reason = f"使用有效IP池: {valid_pools}"
            
            # IP地址格式的池给予额外分数
            if PoolNameFormat.IP_ADDRESS in format_counts:
                score += 10
                reason += " (包含IP地址格式)"
            
            return DimensionScore(
                dimension=EvaluationDimension.IPPOOL_USAGE,
                score=min(score, 100),
                max_score=100,
                reason=reason,
                details={
                    "uses_ippool": True,
                    "valid_pools": valid_pools,
                    "format_results": {k: v.value for k, v in format_results.items()},
                    "format_counts": {k.value: v for k, v in format_counts.items()}
                }
            )
        else:
            return DimensionScore(
                dimension=EvaluationDimension.IPPOOL_USAGE,
                score=50,
                max_score=100,
                reason=f"IP池需要验证: {poolnames}",
                details={
                    "uses_ippool": True,
                    "invalid_pools": poolnames,
                    "format_results": {k: v.value for k, v in format_results.items()}
                }
            )
    
    def _evaluate_service_complexity(self, policy: Dict[str, Any], 
                                   vip_configs: Dict[str, Dict[str, Any]]) -> DimensionScore:
        """评估服务复杂度维度"""
        services = policy.get("service", [])
        service_count = len(services)
        
        if service_count <= 2:
            return DimensionScore(
                dimension=EvaluationDimension.SERVICE_COMPLEXITY,
                score=100,
                max_score=100,
                reason="服务配置简单，适合twice-nat44",
                details={"service_count": service_count, "services": services}
            )
        elif service_count <= 5:
            return DimensionScore(
                dimension=EvaluationDimension.SERVICE_COMPLEXITY,
                score=60,
                max_score=100,
                reason="服务配置适中，可以使用twice-nat44",
                details={"service_count": service_count, "services": services}
            )
        else:
            return DimensionScore(
                dimension=EvaluationDimension.SERVICE_COMPLEXITY,
                score=20,
                max_score=100,
                reason=f"服务配置复杂({service_count}个服务)，可能影响twice-nat44性能",
                details={"service_count": service_count, "services": services}
            )
    
    def _evaluate_interface_config(self, policy: Dict[str, Any], 
                                  vip_configs: Dict[str, Dict[str, Any]]) -> DimensionScore:
        """评估接口配置维度"""
        srcintf = policy.get("srcintf", [])
        dstintf = policy.get("dstintf", [])
        
        # 简单的接口配置检查
        if len(srcintf) == 1 and len(dstintf) == 1:
            return DimensionScore(
                dimension=EvaluationDimension.INTERFACE_CONFIG,
                score=100,
                max_score=100,
                reason="接口配置简单，适合twice-nat44",
                details={"srcintf": srcintf, "dstintf": dstintf}
            )
        elif len(srcintf) <= 2 and len(dstintf) <= 2:
            return DimensionScore(
                dimension=EvaluationDimension.INTERFACE_CONFIG,
                score=70,
                max_score=100,
                reason="接口配置适中",
                details={"srcintf": srcintf, "dstintf": dstintf}
            )
        else:
            return DimensionScore(
                dimension=EvaluationDimension.INTERFACE_CONFIG,
                score=40,
                max_score=100,
                reason="接口配置复杂",
                details={"srcintf": srcintf, "dstintf": dstintf}
            )
    
    def _calculate_total_score(self, dimension_scores: List[DimensionScore]) -> float:
        """计算总分"""
        weights = self.context.weights
        weight_map = {
            EvaluationDimension.VIP_COUNT: weights.vip_count,
            EvaluationDimension.VIP_COMPLETENESS: weights.vip_completeness,
            EvaluationDimension.IPPOOL_USAGE: weights.ippool_usage,
            EvaluationDimension.SERVICE_COMPLEXITY: weights.service_complexity,
            EvaluationDimension.INTERFACE_CONFIG: weights.interface_config
        }
        
        total_score = 0
        for score in dimension_scores:
            weight = weight_map.get(score.dimension, 0)
            weighted_score = (score.score / score.max_score) * 100 * weight
            total_score += weighted_score
        
        return round(total_score, 2)
    
    def _generate_recommendation(self, policy: Dict[str, Any], 
                               dimension_scores: List[DimensionScore], 
                               total_score: float) -> 'TwiceNat44Recommendation':
        """生成推荐结果"""
        from engine.business.models.twice_nat44_models import TwiceNat44Recommendation
        
        should_use = total_score >= self.context.threshold
        
        # 计算置信度
        if total_score >= self.context.high_confidence_threshold:
            confidence = 0.9 + (total_score - self.context.high_confidence_threshold) / 100 * 0.1
        elif total_score >= self.context.threshold:
            confidence = 0.7 + (total_score - self.context.threshold) / (self.context.high_confidence_threshold - self.context.threshold) * 0.2
        else:
            confidence = total_score / self.context.threshold * 0.7
        
        confidence = min(max(confidence, 0.0), 1.0)
        
        # 生成推荐原因
        reasons = []
        warnings = []
        
        for score in dimension_scores:
            if score.score >= 80:
                reasons.append(f"{score.reason} ({score.score:.0f}分)")
            elif score.score >= 50:
                reasons.append(f"{score.reason} ({score.score:.0f}分)")
            else:
                warnings.append(f"{score.reason} ({score.score:.0f}分)")
        
        recommendation = TwiceNat44Recommendation(
            should_use=should_use,
            confidence_score=confidence,
            total_score=int(total_score),
            max_score=100,
            reasons=reasons,
            warnings=warnings,
            fallback_reason=None if should_use else f"评分({total_score})低于阈值({self.context.threshold})"
        )
        
        # 添加详细信息
        recommendation.dimension_scores = dimension_scores
        recommendation.evaluation_context = self.context
        
        return recommendation
    
    def _create_error_recommendation(self, policy: Dict[str, Any], error: str) -> 'TwiceNat44Recommendation':
        """创建错误推荐结果"""
        from engine.business.models.twice_nat44_models import TwiceNat44Recommendation
        
        return TwiceNat44Recommendation(
            should_use=False,
            confidence_score=0.0,
            total_score=0,
            max_score=100,
            reasons=[],
            warnings=[f"评估过程出错: {error}"],
            fallback_reason=f"评估失败: {error}"
        )


class TwiceNat44PerformanceOptimizer:
    """twice-nat44性能优化器"""

    def __init__(self, batch_size: int = 100, enable_cache: bool = True):
        self.batch_size = batch_size
        self.enable_cache = enable_cache
        self._vip_cache = {} if enable_cache else None
        self._pool_cache = {} if enable_cache else None

    def batch_evaluate_policies(self, policies: List[Dict[str, Any]],
                               vip_configs: Dict[str, Dict[str, Any]],
                               context: EvaluationContext) -> List['TwiceNat44Recommendation']:
        """
        批量评估策略

        Args:
            policies: 策略列表
            vip_configs: VIP配置字典
            context: 评估上下文

        Returns:
            List[TwiceNat44Recommendation]: 评估结果列表
        """
        evaluator = TwiceNat44Evaluator(context)
        results = []

        # 预处理VIP配置缓存
        if self.enable_cache:
            self._preprocess_vip_cache(vip_configs)

        # 批量处理
        for i in range(0, len(policies), self.batch_size):
            batch = policies[i:i + self.batch_size]
            batch_results = []

            for policy in batch:
                try:
                    result = evaluator.evaluate_policy(policy, vip_configs)
                    batch_results.append(result)
                except Exception as e:
                    log(_("twice_nat44.batch_evaluation_error",
                         policy=policy.get("name", "unknown"),
                         error=str(e)), "error")
                    batch_results.append(evaluator._create_error_recommendation(policy, str(e)))

            results.extend(batch_results)

            # 记录批次进度
            log(_("twice_nat44.batch_progress",
                 completed=len(results),
                 total=len(policies)), "info")

        return results

    def _preprocess_vip_cache(self, vip_configs: Dict[str, Dict[str, Any]]):
        """预处理VIP配置缓存"""
        if not self.enable_cache:
            return

        for vip_name, vip_config in vip_configs.items():
            # 缓存VIP完整性检查结果
            required_fields = ["extip", "mappedip"]
            is_complete = all(field in vip_config for field in required_fields)
            self._vip_cache[vip_name] = {
                "is_complete": is_complete,
                "missing_fields": [field for field in required_fields if field not in vip_config]
            }

    def clear_cache(self):
        """清除缓存"""
        if self._vip_cache:
            self._vip_cache.clear()
        if self._pool_cache:
            self._pool_cache.clear()


# 工厂函数
def create_twice_nat44_evaluator(threshold: int = 65,
                                ntos_version: str = "R11",
                                available_pools: List[str] = None,
                                custom_weights: EvaluationWeight = None) -> TwiceNat44Evaluator:
    """
    创建twice-nat44评估器的工厂函数

    Args:
        threshold: 评估阈值
        ntos_version: NTOS版本
        available_pools: 可用IP池列表
        custom_weights: 自定义权重配置

    Returns:
        TwiceNat44Evaluator: 配置好的评估器实例
    """
    context = EvaluationContext(
        ntos_version=ntos_version,
        threshold=threshold,
        available_pools=available_pools or [],
        weights=custom_weights or EvaluationWeight()
    )

    return TwiceNat44Evaluator(context)


def create_performance_optimizer(batch_size: int = 100,
                               enable_cache: bool = True) -> TwiceNat44PerformanceOptimizer:
    """
    创建性能优化器的工厂函数

    Args:
        batch_size: 批处理大小
        enable_cache: 是否启用缓存

    Returns:
        TwiceNat44PerformanceOptimizer: 配置好的性能优化器实例
    """
    return TwiceNat44PerformanceOptimizer(batch_size=batch_size, enable_cache=enable_cache)
