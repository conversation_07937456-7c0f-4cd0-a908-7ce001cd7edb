module ntos-ip-mac {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:ip-mac";
  prefix ntos-ip-mac;

  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-interface {
    prefix ntos-interface;
  }
  import ntos-vlan {
    prefix ntos-vlan;
  }
  import ntos-lag {
    prefix ntos-lag;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS ip-mac module.";
  revision 2023-06-20 {
    description
      "Initial version.";
    reference "revision 2023-06-20.";
  }

  identity ip-mac {
    base ntos-types:SERVICE_LOG_ID;
    description
      "IP-MAC bind service.";
  }

  typedef all-info {
    type enumeration {
      enum all;
    }
  }

  typedef packet-action {
    type enumeration {
      enum drop-num;
      enum pass-num;
      enum all-num;
    }
  }

  grouping ipv4-policy-group {
    description
      "Configuration of ip mac policy.";

    list ip {
      ordered-by user;
      key "ipv4";

      leaf ipv4 {
        description
          "Enter a ipv4 address.";
        type ntos-inet:ipv4-address;
      }

      leaf mac {
        description
          "Enter MAC address of the IP.";
        type string {
          pattern '([0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){5})|([0-9a-fA-F]{4}(:[0-9a-fA-F]{4}){2})';
          ntos-extensions:nc-cli-shortdesc "<mac-address>";
        }
      }

      leaf vlan-id {
        description
          "Enter vlan id.";
        type uint32 {
          range "1..4063";
        }
      }

      leaf description {
        type ntos-types:ntos-obj-description-type {
          length "0..63";
        }
        description
          "The description of this IP-MAC bind policy.";
      }
    }
  }

  grouping ipv6-policy-group {
    description
      "Configuration of IP-MAC bind policy.";

    list ip {
      ordered-by user;
      key "ipv6";

      leaf ipv6 {
        description
          "Enter a ipv6 address.";
        type ntos-inet:ipv6-address;
      }

      leaf mac {
        description
          "Enter MAC address of the IP.";
        type string {
          pattern '([0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){5})|([0-9a-fA-F]{4}(:[0-9a-fA-F]{4}){2})';
          ntos-extensions:nc-cli-shortdesc "<mac-address>";
        }
      }

      leaf vlan-id {
        description
          "Enter vlan id.";
        type uint32 {
          range "1..4063";
        }
      }

      leaf description {
        type ntos-types:ntos-ipv6-obj-description-type {
          length "0..63";
        }
        description
          "The description of this IP-MAC bind policy.";
      }
    }
  }

  grouping intf-group {
    container ip-mac-bind {
      description
        "IP-MAC bind ip type.";

      container ipv4 {
        description
          "IP-MAC bind ip type.";
        leaf enabled {
          type boolean;
          default "false";
          description
            "Enable or disable IP-MAC bind.";
        }
      }
      container ipv6 {
        description
          "IP-MAC bind ip type.";
        leaf enabled {
          type boolean;
          default "false";
          description
            "Enable or disable IP-MAC bind.";
        }
      }
    }
  }

  rpc show-ipmac-config {
    description
      "Show IP-MAC bind config.";
    ntos-extensions:nc-cli-show "ip-mac-bind config json";
    ntos-extensions:nc-cli-hidden;

    output {
      leaf buffer {
        type string;
        description
          "Show IP-MAC bind config.";
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc show-ipmac-information {
    description
      "Show IP-MAC bind information.";
    ntos-extensions:nc-cli-show "ip-mac-bind information json";
    ntos-extensions:nc-cli-hidden;

    input {
      leaf filter {
        description
          "The content of search by IP or MAC.";
        type string;
      }

      leaf ipv4 {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "Show all ipv4 info.";
        type all-info;
      }
      leaf ipv6 {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "Show all ipv6 info.";
        type all-info;
      }

      leaf start {
        description
          "The index of page start.";
        type uint16;
      }
      leaf end {
        description
          "The index of page end.";
        type uint16;
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Show IP-MAC bind information.";
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc show-ipmac-statistics {
    description
      "Show IP-MAC bind statistics.";
    ntos-extensions:nc-cli-show "ip-mac-bind statistics";

    input {
      container ipv4 {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The IP-MAC bind ip type.";

        leaf ip {
          description
            "Enter a ipv4 address.";
          type ntos-inet:ipv4-address;
        }
        leaf action {
          type packet-action;
          default "all-num";
          description
            "The action of packet.";
        }
      }

      container ipv6 {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The IP-MAC bind ip type.";

        leaf ip {
          description
            "Enter a ipv6 address.";
          type ntos-inet:ipv6-address;
        }
        leaf action {
          type packet-action;
          default "all-num";
          description
            "The action of packet.";
        }
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "show IP-MAC bind statistics.";
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  rpc clear-ipmac-statistics {
    description
      "Clear statistics of IP-MAC bind.";
    ntos-extensions:nc-cli-cmd "clear ip-mac-bind statistics";

    input {
      container ipv4 {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The IP-MAC bind ip type.";

        leaf-list ip {
          type ntos-inet:ipv4-address;
          description
            "The name of ip addr.";
        }
        leaf action {
          type packet-action;
          description
            "The type of action.";
        }
      }

      container ipv6 {
        ntos-extensions:nc-cli-group "subcommand";
        description
          "The IP-MAC bind ip type.";

        leaf-list ip {
          type ntos-inet:ipv6-address;
          description
            "The name of ip addr.";
        }
        leaf action {
          type packet-action;
          description
            "The type of action.";
        }
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "Command output buffer.";
        ntos-extensions:nc-cli-stdout;
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "The IP-MAC bind config.";
    container ip-mac-bind {
      description
        "The IP-MAC bind configuration.";

      container ipv4 {
        description
          "The IP-MAC bind ip type.";
        leaf enabled {
          type boolean;
          default "false";
          description
            "Enable or disable ip-mac-bind.";
        }
        leaf no-match-action-drop {
          type boolean;
          default "false";
          description
            "Drop or pass the packet. (true:drop, false:pass)";
        }
        uses ipv4-policy-group;
      }

      container ipv6 {
        description
          "The IP-MAC bind ip type.";
        leaf enabled {
          type boolean;
          default "false";
          description
            "Enable or disable ip-mac-bind.";
        }
        leaf no-match-action-drop {
          type boolean;
          default "false";
          description
            "Drop or pass the packet. (true:drop, false:pass)";
        }
        uses ipv6-policy-group;
      }

    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "The IP-MAC bind state.";
    container ip-mac-bind {
      config false;
      leaf enabled {
        type boolean;
      }
    }
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-interface:physical" {
    uses intf-group;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-vlan:vlan" {
    uses intf-group;
  }

  augment "/ntos:config/ntos:vrf/ntos-interface:interface/ntos-lag:lag" {
    uses intf-group;
  }

}
