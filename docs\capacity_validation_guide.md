# FortiGate到NTOS转换器 - 设备容量限制校验功能指南

## 概述

设备容量限制校验功能是FortiGate到NTOS转换器的重要组成部分，用于在配置转换前验证FortiGate配置是否超出目标NTOS设备的容量限制。该功能可以帮助用户：

- **提前发现容量问题**：在转换前识别可能导致设备性能问题的配置超限
- **获得优化建议**：提供具体的配置优化建议和最佳实践
- **选择合适设备**：帮助用户选择满足需求的设备型号
- **风险评估**：评估配置超限的风险级别和影响

## 支持的设备型号

目前支持以下NTOS设备型号的容量校验：

### Z3200S系列防火墙
- **定位**：中端防火墙设备
- **主要限制**：
  - DNS服务器：最多3个
  - 安全策略：最多3000条
  - NAT策略：最多250条
  - 地址对象：最多1024个
  - 服务对象：最多1024个

### Z5100S系列防火墙
- **定位**：高端防火墙设备
- **主要限制**：
  - DNS服务器：最多3个
  - 安全策略：最多4000条
  - NAT策略：最多300条
  - 地址对象：最多1024个
  - 服务对象：最多1024个

## 使用方法

### 基本用法

```bash
# 验证FortiGate配置文件并进行容量校验
python engine/main.py --mode verify --cli config.conf --vendor fortigate --device-model z3200s

# 指定不同的设备型号
python engine/main.py --mode verify --cli config.conf --vendor fortigate --device-model z5100s
```

### 输出JSON格式结果

```bash
# 获取JSON格式的详细结果
python engine/main.py --mode verify --cli config.conf --vendor fortigate --device-model z3200s --json
```

### 不进行容量校验

```bash
# 只进行基本验证，不进行容量校验
python engine/main.py --mode verify --cli config.conf --vendor fortigate
```

## 输出格式

### 标准输出格式

当发现容量违规时，系统会输出警告信息：

```
验证结果: 通过
警告信息:
- 容量限制警告：DNS服务器数量(5)超出z3200s设备限制(3)。建议配置不超过3个DNS服务器
- 容量限制警告：安全策略数量(3500)超出z3200s设备限制(3000)。建议优化安全策略配置或升级到Z5100S型号
```

### JSON输出格式

```json
{
  "valid": true,
  "message": "",
  "success": true,
  "warnings": [
    "容量限制警告：DNS服务器数量(5)超出z3200s设备限制(3)。建议配置不超过3个DNS服务器",
    "容量限制警告：安全策略数量(3500)超出z3200s设备限制(3000)。建议优化安全策略配置或升级到Z5100S型号"
  ],
  "detected_version": "7.0.0",
  "capacity_violations": [
    {
      "resource_type": "dns_servers",
      "description": "DNS服务器最大数量",
      "current_count": 5,
      "max_limit": 3,
      "violation_count": 2,
      "usage_rate": "166.7%",
      "severity": "error",
      "risk_level": "critical",
      "category": "network",
      "device_model": "z3200s",
      "suggestion": "建议配置不超过3个DNS服务器",
      "analysis": {
        "usage_percentage": "166.7%",
        "excess_count": 2,
        "recommended_action": "minor_adjustment",
        "impact_assessment": "可能导致设备性能严重下降或功能异常",
        "optimization_tips": [
          "优化网络配置",
          "检查配置的必要性",
          "考虑使用更高效的配置方式"
        ]
      }
    }
  ]
}
```

## 检查的资源类型

容量校验功能会检查以下11种资源类型：

### 网络配置
- **DNS服务器** (`dns_servers`)：系统DNS配置中的服务器数量
- **子接口** (`sub_interfaces`)：VLAN子接口数量
- **PPPOE会话** (`pppoe_sessions`)：PPPOE拨号会话数量

### 路由配置
- **静态路由** (`static_routes_v4`)：IPv4静态路由条数

### 策略配置
- **安全策略** (`security_policies`)：防火墙安全策略数量
- **NAT策略** (`nat44_policies`)：启用NAT的策略数量

### 对象配置
- **地址对象** (`address_objects`)：防火墙地址对象数量
- **地址组** (`address_groups`)：地址对象组数量
- **服务对象** (`service_objects`)：自定义服务对象数量
- **服务组** (`service_groups`)：服务对象组数量
- **时间对象** (`time_objects`)：时间计划对象数量

## 风险级别说明

系统会根据资源使用率自动计算风险级别：

- **Critical（严重）**：使用率 ≥ 100%，已超出设备限制
- **High（高）**：使用率 ≥ 95%，接近设备限制
- **Medium（中等）**：使用率 ≥ 80%，需要关注
- **Low（低）**：使用率 < 80%，正常范围

## 优化建议

### 策略类资源优化
- 合并相似的策略规则
- 删除不再使用的策略
- 使用地址组和服务组简化策略

### 对象类资源优化
- 清理未使用的对象
- 合并重复的对象定义
- 使用组对象减少单个对象数量

### 接口类资源优化
- 检查是否有未使用的接口配置
- 考虑使用VLAN聚合
- 优化接口配置结构

### 网络类资源优化
- 优化网络配置
- 检查配置的必要性
- 考虑使用更高效的配置方式

## 配置文件

容量限制配置存储在 `engine/data/capacity_limits.json` 文件中，包含：

- 设备型号定义
- 各种资源的限制值
- 错误严重级别
- 优化建议文本
- 资源分类信息

### 配置文件结构示例

```json
{
  "version": "1.0.0",
  "description": "设备容量限制配置文件",
  "device_models": {
    "z3200s": {
      "name": "Z3200S",
      "description": "锐捷Z3200S系列防火墙",
      "limits": {
        "dns_servers": {
          "max": 3,
          "description": "DNS服务器最大数量",
          "severity": "error",
          "suggestion": "建议配置不超过3个DNS服务器",
          "category": "network"
        }
      }
    }
  }
}
```

## 故障排除

### 常见问题

1. **不支持的设备型号**
   ```
   警告: 不支持的设备型号: unknown_device
   ```
   **解决方案**：使用支持的设备型号（z3200s 或 z5100s）

2. **配置文件加载失败**
   ```
   错误: 加载容量配置失败: [Errno 2] No such file or directory
   ```
   **解决方案**：确保 `engine/data/capacity_limits.json` 文件存在

3. **资源统计错误**
   ```
   错误: 统计资源数量失败: invalid syntax
   ```
   **解决方案**：检查FortiGate配置文件格式是否正确

### 调试模式

使用 `--verbose` 参数获取详细的调试信息：

```bash
python engine/main.py --mode verify --cli config.conf --vendor fortigate --device-model z3200s --verbose
```

## API集成

### Python API使用

```python
from engine.verify import perform_capacity_validation, count_resources

# 执行容量校验
violations = perform_capacity_validation(config_content, "z3200s")

# 统计资源数量
resource_counts = count_resources(config_content)

# 处理违规结果
for violation in violations:
    print(f"违规: {violation.description}")
    print(f"当前/限制: {violation.current_count}/{violation.max_limit}")
    print(f"建议: {violation.suggestion}")
```

### 集成到现有工作流

容量校验功能已集成到现有的验证模式中，无需修改现有代码即可使用。只需在调用验证函数时添加 `device_model` 参数即可启用容量校验。

## 更新日志

- **v1.0.0**：初始版本，支持Z3200S和Z5100S设备型号
- 支持11种资源类型的容量校验
- 提供详细的分析报告和优化建议
- 集成到现有验证模式中
