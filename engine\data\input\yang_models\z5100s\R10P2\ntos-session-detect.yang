module ntos-session-detect {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:session-detect";
  prefix ntos-session-detect;

  import ntos {
    prefix ntos;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-api {
    prefix ntos-api;
  }

  organization
    "Ruijie Networks Co.,Ltd";

  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";

  description
    "Ruijie NTOS Session Detect Service.";

  revision 2024-08-21 {
    description
      "Create.";
  }

  identity session-detect {
    description
      "Session Detect service.";
    ntos-ext:nc-cli-identity-name "session-detect";
  }

  typedef detect-source {
    description
      "Source of detection.";
    type enumeration {
      enum session-detect {
        description
          "From session detect web.";
      }
      enum from-mllb {
        description
          "From mllb web.";
      }
    }
  }

  typedef detect-state {
    description
      "Type of detect state.";
    type enumeration {
      enum undetect {
        description
          "Not detected.";
      }

      enum detecting {
        description
          "On detecting.";
      }

      enum successful {
        description
          "Successful detection.";
      }

      enum failed {
        description
          "Failed detection.";
      }

      enum waiting {
        description
          "Detection is waiting.";
      }

      enum aborted {
        description
          "Detection is aborted.";
      }
    }
  }

  grouping session-detect-config {
    description
      "Global configuration of session detect.";

    container timed-detect {
      description
        "Configuration of timed detection.";

      list interface {
        key "name";
        ntos-ext:nc-cli-one-liner;
        description
          "Out interface of detect link.";
        leaf name {
          type ntos-types:ifname;
          description
            "Interface name.";
        }
      }

      leaf start-time {
        description
          "Start time of timed detection.";
        type string;
      }

      leaf enabled {
        description
          "Enabled/Disabled timed detection.";
        type boolean;
      }
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Configuration of session detect service.";
    container session-detect {
        uses session-detect-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "The state of session detect service.";
    container session-detect {
        uses session-detect-config;
    }
  }

  rpc show-timed-detect {
    description
      "Show links of timed detect.";
    input {
      leaf start {
        type uint32;
        description
          "Start offset of result.";
      }

      leaf end {
        type uint32;
        description
          "End offset of result.";
      }
    }

    output {
      container selection-interface {
        leaf interface-total {
          type uint32;
        }

        list interface {
          leaf name {
            type string;
          }
        }
      }

      container selected-interface {
        leaf interface-total {
          type uint32;
        }

        list interface {
          leaf name {
            type string;
          }
        }
      }

      leaf start-time {
        type string;
      }

      leaf enabled {
        type boolean;
      }
    }
    ntos-ext:nc-cli-cmd "session-detect show-timed";
    ntos-api:internal;
  }

  rpc show-detect-details {
    description
      "Show detect details of links.";
    input { 
      leaf all {
        type boolean;
        description
        "Show all interfaces or valid interfaces.";
      }

      leaf start {
        type uint32;
        description
          "Start offset of result.";
      }

      leaf name {
        description "Show interface with the specified name.";
        type string;
      }

      leaf name-list {
        description "Show interface with the specified name list X,Y,Z...";
        type string;
      }

      leaf end {
        type uint32;
        description
          "End offset of result.";
      }

      leaf with-timed {
        type boolean;
        description
          "Show result with timed detections.";
      }
    }

    output {
      leaf interface-total {
        type uint32;
        description
          "Total interface number.";
      }

      list interface {
        leaf name {
         type ntos-types:ifname;
         description
           "Interface name.";
        }

        leaf broadband-type {
          type string;
          description
            "The type of link.";
        }

        leaf detect-time {
          type string;
          description
            "Latest detect time.";
        }

        leaf last-detect-time {
          type string;
          description
            "Last detect time.";
        }

        leaf detect-result {
          type uint32;
          description
            "Latest detect result.";
        }

        leaf last-detect-result {
          type uint32;
          description
            "Last detect result.";
        }

        leaf max-conn {
          type uint32;
          description
            "Max connection, mllb configuration.";
        }

        leaf allow-detect {
            type boolean;
        }

        leaf forbid-detect-reason {
          type string;
          description
            "Reasons for not allow detection.";
        }

        leaf progress {
            type uint8;
        }

        leaf state {
          type detect-state;
          description
            "Detection state.";
        }

        leaf failure-reason {
          type string;
          description
            "Reasons for detection failure.";
        }

        leaf estimated-duration {
          type uint32;
          description
            "Estimated detection duration seconds.";
        }

        leaf config-source {
          type string;
          description
            "Detection source.";
        }
      }

      container timed-detection {
        leaf interface-total {
          type uint32;
          description
            "Total interface number.";
        }

        list interface {
          leaf name {
          type ntos-types:ifname;
            description
              "Interface name.";
          }
        }

        leaf start-time {
          description
            "Start time of timed detection.";
          type string;
        }

        leaf enabled {
          type boolean;
        }
      }
    }
    ntos-ext:nc-cli-show "session-detect";
    ntos-api:internal;
  }

  rpc get-detect-duration {
    description
      "Get estimated detect duration.";

    input {
      list interface {
        key "name";
        description
          "Out interface of detect link.";
        leaf name {
          type ntos-types:ifname;
          description
            "Interface name.";
        }
      }
    }

    output {
      leaf interface-number {
        type uint32;
        description
          "The number of links detected.";
      }

      leaf duration {
        type uint32;
        description
          "Detection duration.";
      }
    }

    ntos-ext:nc-cli-cmd "session-detect get-duration";
    ntos-api:internal;
  }

  rpc set-detect-task {
    input {
	  leaf interfaces {
		type string;
		description
		  "Out interface of detect link, format ifname1,ifname2,ifname3 .";
	  }

      leaf config-source {
        type detect-source;
        description
          "Detection source.";
      }

      leaf enabled {
        type boolean;
        description
          "Enable/disable all detections of selected interfaces.";
      }
    }

    ntos-ext:nc-cli-cmd "session-detect set-task";
    ntos-api:internal;
  }
}

