module ntos {
  yang-version 1.1;
  namespace "urn:ruijie:ntos";
  prefix ntos;

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS data model.";

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  feature product-license {
    description
      "This feature requires a valid Turbo Router product license.";
  }

  feature cgnat-license {
    description
      "This feature requires a valid Turbo CG-NAT application license.";
  }

  feature ipsec-license {
    description
      "This feature requires a valid Turbo IPsec application license.";
  }

  typedef vrf-name {
    type union {
      type enumeration {
        enum main {
          description
            "The main vrf.";
        }
      }
      type string {
        pattern '[-_a-zA-Z0-9]+' {
                 error-message
                   "vrf name should only contain alphanumerical
                    characters, underscores and dashes.";
               }
      }
    }
    description
      "The vrf name.";
  }

  container config {
    description
      "Ruijie NTOS configuration.";

    list vrf {
      key "name";
      description
        "Vrf list.";

      leaf name {
        type ntos:vrf-name;
        description
          "The vrf name.";
      }
    }
  }

  container state {
    config false;
    description
      "Ruijie NTOS operational state data.";

    list vrf {
      key "name";
      description
        "Vrf list.";

      leaf name {
        type union {
          type enumeration {
            enum main {
              description
                "The main vrf.";
            }
          }
          type string;
        }
        description
          "The vrf name.";
      }
    }
  }
}
