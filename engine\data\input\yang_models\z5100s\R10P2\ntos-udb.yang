module ntos-udb {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:system:udb";
  prefix ntos-udb;

  import ietf-yang-types {
    prefix ietf-yang;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS UDB management.";

  revision 2024-08-05 {
    description
      "Initial version.";
    reference "";
  }
  
  rpc udb-get-data{
    description
      "View data of XPath paths for specified logical library through UDB.";

    input {
      leaf db {
        type int16 {
          range '0..63';
        }
        description
          "The specified logical library where the data of XPath paths is located.";
      }
      leaf xpath {
        type string;
        description
          "The XPath path where the desired data is located.";
      }
      leaf max-depth {
        type uint32;
        default 0;
        description
          "The maximum depth of the selected subtree. 0 means unlimited depth, while 1 will not return any descendant nodes.";
      }

      leaf timeout-ms {
        type uint32;
        default 1000;
        description
          "The timeout duration for command execution.";
      }
    }

    output {
      leaf data {
        type string;
        description
          "Viewing the results of data through UDB.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-show "udb-get-data";
  }
  
  rpc show-udb-info {
    description
      "Show UDB Info.";
      
    input {
      leaf section {
        type enumeration {
          enum all {
            description
              "Provide a comprehensive overview of various metrics and statistics related to the server.";
          }
          enum server {
            description
              "Provide detailed information about the server instance itself.";
          }
          enum clients {
            description
              "Provide information about client connections to the server.";
          }
          enum memory {
            description
              "Provide detailed information about the memory usage of the server. ";
          }
          enum persistence {
            description
              "Provide information about the persistence settings and status of the server.";
          }
          enum stats {
            description
              "Provide statistical data on server performance and usage.";
          }
          enum replication {
            description
              "Provide information about server replication status.";
          }
          enum cpu {
            description
              "Provide information about server CPU usage.";
          }
          enum module-list {
            description
              "Provide information about the modules loaded on the server.";
          }
          enum commandstats {
            description
              "Provide statistical information about executed commands.";
          }
          enum errorstats {
            description
              "Provide statistical data related to errors encountered by the server.";
          }
          enum latencystats {
            description
              "Provides detailed statistical data on operational latency.";
          }
          enum cluster {
            description
              "Provide information about cluster configuration and status.";
          }
          enum keyspace {
            description
              "Provide statistical information about the keyspace in the database.";
          }
          enum data-model {
            description
              "Provide information about the data model used by the server.";
          }
          enum debug {
            description
              " Provide debugging information about the server.";
          }
        }
        default "all";
      }
    }

    output {
      leaf data {
        type string;
        description
          "The command output buffer.";
        ntos-extensions:nc-cli-stdout;
        ntos-extensions:nc-cli-hidden;
      }
    }
    ntos-extensions:nc-cli-show "udb-info";
  }
}
