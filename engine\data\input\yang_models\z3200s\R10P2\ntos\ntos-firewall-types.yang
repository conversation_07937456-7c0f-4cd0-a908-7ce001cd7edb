module ntos-firewall-types {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:firewall-types";
  prefix ntos-firewall-types;

  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS firewall.";

  revision 2018-11-30 {
    description
      "More permissive protocol type field, more protocols in enum.";
    reference "";
  }
  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  typedef address6-types {
    type union {
      type ntos-inet:domain-name {
        ntos-extensions:nc-cli-shortdesc "<domain-name>";
      }
      type ntos-inet:ipv6-address {
        ntos-extensions:nc-cli-shortdesc "<X:X::X:X>";
      }
      type ntos-inet:ipv6-prefix {
        ntos-extensions:nc-cli-shortdesc "<X:X::X:X/M>";
      }
    }
    description
      "Address type.";
  }

  typedef icmp6-types {
    type enumeration {
      enum echo-request {
        description
          "Echo request.";
      }
      enum echo-reply {
        description
          "Echo reply.";
      }
      enum destination-unreachable {
        description
          "Destination unreachable.";
      }
      enum address-unreachable {
        description
          "Address unreachable.";
      }
      enum port-unreachable {
        description
          "Port unreachable.";
      }
      enum no-route {
        description
          "No route to destination.";
      }
      enum reject-route {
        description
          "Reject route to destination.";
      }
      enum communication-prohibited {
        description
          "Communication with destination administratively prohibited.";
      }
      enum beyond-scope {
        description
          "Beyond scope of source address.";
      }
      enum packet-too-big {
        description
          "Packet too big.";
      }
      enum failed-policy {
        description
          "Source address failed ingress/egress policy.";
      }
      enum ttl-exceeded {
        description
          "TTL exceeded.";
      }
      enum ttl-zero-during-transit {
        description
          "Hop limit exceeded in transit.";
      }
      enum ttl-zero-during-reassembly {
        description
          "Fragment reassembly time exceeded.";
      }
      enum parameter-problem {
        description
          "Parameter problem.";
      }
      enum bad-header {
        description
          "Erroneous header field encountered.";
      }
      enum unknown-header-type {
        description
          "Unrecognized Next Header type encountered.";
      }
      enum unknown-option {
        description
          "Unrecognized IPv6 option encountered.";
      }
      enum router-solicitation {
        description
          "Router solicitation.";
      }
      enum router-advertisement {
        description
          "Router advertisement.";
      }
      enum neighbor-solicitation {
        description
          "Neighbor solicitation.";
      }
      enum neighbor-advertisement {
        description
          "Neighbor advertisement.";
      }
      enum redirect {
        description
          "Redirect message.";
      }
    }
    description
      "ICMP types.";
  }

  typedef reject6-type {
    type enumeration {
      enum icmp6-no-route {
        description
          "Reject with ICMPv6 no route.";
      }
      enum icmp6-adm-prohibited {
        description
          "Reject with ICMPv6 admin prohibited.";
      }
      enum icmp6-addr-unreachable {
        description
          "Reject with ICMPv6 address unreachable.";
      }
      enum icmp6-port-unreachable {
        description
          "Reject with ICMPv6 port unreachable.";
      }
      enum tcp-reset {
        description
          "Reject with TCP RST packet. Can be used on rules which only match the TCP protocol.";
      }
    }
    description
      "Packet type when packet is rejected.";
  }

  typedef tcp-flags {
    type enumeration {
      enum syn {
        description
          "SYN flag.";
      }
      enum ack {
        description
          "ACK flag.";
      }
      enum fin {
        description
          "FIN flag.";
      }
      enum rst {
        description
          "RST flag.";
      }
      enum urg {
        description
          "URG flag.";
      }
      enum psh {
        description
          "PSH flag.";
      }
      enum all {
        description
          "All flags.";
      }
      enum none {
        description
          "No flag.";
      }
    }
    description
      "TCP flags.";
  }

  typedef address-types {
    type union {
      type ntos-inet:domain-name {
        ntos-extensions:nc-cli-shortdesc "<domain-name>";
      }
      type ntos-inet:ipv4-address {
        ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
      }
      type ntos-inet:ipv4-prefix {
        ntos-extensions:nc-cli-shortdesc "<A.B.C.D/M>";
      }
    }
    description
      "Address type.";
  }

  typedef protocol-types {
    type union {
      type enumeration {
        enum tcp {
          description
            "TCP protocol.";
        }
        enum udp {
          description
            "UDP protocol.";
        }
        enum sctp {
          description
            "SCTP protocol.";
        }
        enum icmp {
          description
            "ICMP protocol.";
        }
        enum esp {
          description
            "IPsec ESP protocol.";
        }
        enum ah {
          description
            "IPsec AH protocol.";
        }
        enum gre {
          description
            "GRE protocol.";
        }
        enum l2tp {
          description
            "L2TP protocol.";
        }
        enum ipip {
          description
            "IP-in-IP protocol.";
        }
        enum vrrp {
          description
            "VRRP protocol.";
        }
        enum all {
          description
            "All protocols.";
        }
      }
      type uint16;
      type string;
    }
    description
      "Protocol. The list can be obtained from the 'show filter protocols' command
       or the show-filter-protocols rpc.";
  }

  typedef protocol6-types {
    type union {
      type enumeration {
        enum tcp {
          description
            "TCP protocol.";
        }
        enum udp {
          description
            "UDP protocol.";
        }
        enum sctp {
          description
            "SCTP protocol.";
        }
        enum ipv6-icmp {
          description
            "ICMPv6 protocol.";
        }
        enum esp {
          description
            "IPsec ESP protocol.";
        }
        enum ah {
          description
            "IPsec AH protocol.";
        }
        enum gre {
          description
            "GRE protocol.";
        }
        enum l2tp {
          description
            "L2TP protocol.";
        }
        enum ipip {
          description
            "IP-in-IP protocol.";
        }
        enum vrrp {
          description
            "VRRP protocol.";
        }
        enum all {
          description
            "All protocols.";
        }
      }
      type uint16;
      type string;
    }
    description
      "Protocol. The list can be obtained from the 'show filter protocols' command
       or the show-filter-protocols rpc.";
  }

  typedef icmp-types {
    type enumeration {
      enum any {
        description
          "Any ICMP type.";
      }
      enum echo-request {
        description
          "Echo request.";
      }
      enum echo-reply {
        description
          "Echo reply.";
      }
      enum destination-unreachable {
        description
          "Destination unreachable.";
      }
      enum network-unreachable {
        description
          "Network unreachable.";
      }
      enum host-unreachable {
        description
          "Host unreachable.";
      }
      enum protocol-unreachable {
        description
          "Protocol unreachable.";
      }
      enum port-unreachable {
        description
          "Port unreachable.";
      }
      enum fragmentation-needed {
        description
          "Fragmentation needed.";
      }
      enum source-route-failed {
        description
          "Source route failed.";
      }
      enum network-unknown {
        description
          "Network unknown.";
      }
      enum host-unknown {
        description
          "Host unknown.";
      }
      enum network-prohibited {
        description
          "Network prohibited.";
      }
      enum host-prohibited {
        description
          "Host prohibited.";
      }
      enum TOS-network-unreachable {
        description
          "TOS network unreachable.";
      }
      enum TOS-host-unreachable {
        description
          "TOS host unreachable.";
      }
      enum communication-prohibited {
        description
          "Communication prohibited.";
      }
      enum host-precedence-violation {
        description
          "Host precedence violation.";
      }
      enum precedence-cutoff {
        description
          "Precedence cutoff.";
      }
      enum source-quench {
        description
          "Source quench.";
      }
      enum redirect {
        description
          "Redirect.";
      }
      enum network-redirect {
        description
          "Network redirect.";
      }
      enum host-redirect {
        description
          "Host redirect.";
      }
      enum TOS-network-redirect {
        description
          "TOS network redirect.";
      }
      enum TOS-host-redirect {
        description
          "TOS host redirect.";
      }
      enum router-advertisement {
        description
          "Router advertisement.";
      }
      enum router-solicitation {
        description
          "Router solicitation.";
      }
      enum ttl-exceeded {
        description
          "TTL exceeded.";
      }
      enum ttl-zero-during-transit {
        description
          "Time to Live exceeded in Transit.";
      }
      enum ttl-zero-during-reassembly {
        description
          "Fragment Reassembly Time Exceeded.";
      }
      enum parameter-problem {
        description
          "Parameter problem.";
      }
      enum ip-header-bad {
        description
          "Bad IP header.";
      }
      enum required-option-missing {
        description
          "Missing a Required Option.";
      }
      enum timestamp-request {
        description
          "Timestamp request.";
      }
      enum timestamp-reply {
        description
          "Timestamp reply.";
      }
      enum address-mask-request {
        description
          "Address mask request.";
      }
      enum address-mask-reply {
        description
          "Address mask reply.";
      }
    }
    description
      "ICMP types.";
  }

  typedef ctstatus-types {
    type enumeration {
      enum none {
        description
          "No status.";
      }
      enum expected {
        description
          "This is an expected connection (i.e. a conntrack helper set it up).";
      }
      enum seen_reply {
        description
          "Conntrack has seen packets in both directions.";
      }
      enum assured {
        description
          "Conntrack entry should never be early-expired.";
      }
      enum confirmed {
        description
          "Connection is confirmed: originating packet has left box.";
      }
    }
    description
      "Conntrack status.";
  }

  typedef ctstate-types {
    type enumeration {
      enum invalid {
        description
          "Packet is associated with no known connection.";
      }
      enum new {
        description
          "Packet started new connection or associated with one which
           has not seen packets in both directions.";
      }
      enum established {
        description
          "Packet is associated with a connection which
           has seen packets in both directions.";
      }
      enum related {
        description
          "Packet is starting a new connection, but is associated with an
           existing connection, such as an FTP data transfer or an ICMP error.";
      }
      enum untracked {
        description
          "Packet is not tracked at all, which happens if you explicitly untrack
           it by using the notrack action in the raw table.";
      }
      enum snat {
        description
          "A virtual state, matching if the original source address differs from
           the reply destination.";
      }
      enum dnat {
        description
          "A virtual state, matching if the original destination
           differs from the reply source.";
      }
    }
    description
      "Conntrack state.";
  }

  typedef unit-types {
    type enumeration {
      enum second {
        description
          "Second.";
      }
      enum minute {
        description
          "Minute.";
      }
      enum hour {
        description
          "Hour.";
      }
      enum day {
        description
          "Day.";
      }
    }
    description
      "Units.";
  }

  typedef level-types {
    type enumeration {
      enum emergency {
        description
          "Emergency level.";
      }
      enum alert {
        description
          "Alert level.";
      }
      enum critical {
        description
          "Critical level.";
      }
      enum error {
        description
          "Error level.";
      }
      enum warning {
        description
          "Warning level.";
      }
      enum notice {
        description
          "Notice level.";
      }
      enum info {
        description
          "Info level.";
      }
      enum debug {
        description
          "Debug level.";
      }
    }
    description
      "Log levels.";
  }

  typedef additional-infos-types {
    type enumeration {
      enum tcp-sequence {
        description
          "Log TCP sequence numbers.";
      }
      enum tcp-options {
        description
          "Log options from the TCP packet header.";
      }
      enum ip-options {
        description
          "Log options from the IP/IPv6 packet header.";
      }
      enum user-id {
        description
          "Log the userid of the process which generated the packet.";
      }
    }
    description
      "Additional loggable infos.";
  }

  typedef dscp-type {
    type union {
      type ntos-inet:dscp;
      type enumeration {
        enum af11 {
          description
            "AF11 (assured forwarding) class (10).";
        }
        enum af12 {
          description
            "AF12 (assured forwarding) class (12).";
        }
        enum af13 {
          description
            "AF13 (assured forwarding) class (14).";
        }
        enum af21 {
          description
            "AF21 (assured forwarding) class (18).";
        }
        enum af22 {
          description
            "AF22 (assured forwarding) class (20).";
        }
        enum af23 {
          description
            "AF23 (assured forwarding) class (22).";
        }
        enum af31 {
          description
            "AF31 (assured forwarding) class (26).";
        }
        enum af32 {
          description
            "AF32 (assured forwarding) class (28).";
        }
        enum af33 {
          description
            "AF33 (assured forwarding) class (30).";
        }
        enum af41 {
          description
            "AF41 (assured forwarding) class (34).";
        }
        enum af42 {
          description
            "AF42 (assured forwarding) class (36).";
        }
        enum af43 {
          description
            "AF43 (assured forwarding) class (38).";
        }
        enum be {
          description
            "BE (best effort) class (0).";
        }
        enum cs0 {
          description
            "CS0 (class selector) class (0).";
        }
        enum cs1 {
          description
            "CS1 (class selector) class (8).";
        }
        enum cs2 {
          description
            "CS2 (class selector) class (16).";
        }
        enum cs3 {
          description
            "CS3 (class selector) class (24).";
        }
        enum cs4 {
          description
            "CS4 (class selector) class (32).";
        }
        enum cs5 {
          description
            "CS5 (class selector) class (40).";
        }
        enum cs6 {
          description
            "CS6 (class selector) class (48).";
        }
        enum cs7 {
          description
            "CS7 (class selector) class (56).";
        }
        enum ef {
          description
            "EF (expedited forwarding) class (46).";
        }
      }
    }
    description
      "DSCP values.";
  }

  typedef reject-type {
    type enumeration {
      enum icmp-net-unreachable {
        description
          "Reject with ICMP network unreachable.";
      }
      enum icmp-host-unreachable {
        description
          "Reject with ICMP host unreachable.";
      }
      enum icmp-port-unreachable {
        description
          "Reject with ICMP port unreachable.";
      }
      enum icmp-proto-unreachable {
        description
          "Reject with ICMP prototype unreachable.";
      }
      enum icmp-net-prohibited {
        description
          "Reject with ICMP network prohibited.";
      }
      enum icmp-host-prohibited {
        description
          "Reject with ICMP host prohibited.";
      }
      enum icmp-admin-prohibited {
        description
          "Reject with ICMP admin prohibited.";
      }
      enum tcp-reset {
        description
          "Reject with TCP RST packet. Can be used on rules which only match the TCP protocol.";
      }
    }
    description
      "Packet type when packet is rejected.";
  }

  typedef port-range {
    type string {
      pattern '[0-9]+(-[0-9]+)?(,[0-9]+(-[0-9]+)?)*' {
        error-message "Invalid port range format. Example: '21,22,1024-2048'";
      }
      ntos-extensions:nc-cli-shortdesc "<port-range>";
    }
    description
      "A comma-separated list of ports or ports ranges.
       Examples: '21,22,1024-2048'.";
  }

  typedef sctp-data-flags {
    type enumeration {
      enum I {
        description
          "SACK chunk should be sent back without delay.";
      }
      enum U {
        description
          "Indicates this data is an unordered chunk
           and the stream sequence number is invalid. If an unordered
           chunk is fragmented then each fragment has this flag set.";
      }
      enum B {
        description
          "Marks the beginning fragment.
           An unfragmented chunk has this flag set.";
      }
      enum E {
        description
          "Marks the end fragment.
           An unfragmented chunk has this flag set.";
      }
    }
    description
      "SCTP data flags.";
  }

  typedef sctp-abort-flags {
    type enumeration {
      enum T {
        description
          "Means the sender sent its own Verification Tag (that receiver should check).";
      }
    }
    description
      "SCTP abort flag.";
  }
}
