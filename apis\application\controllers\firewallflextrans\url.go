package firewallflextrans

import (
	"github.com/kataras/iris/v12"
)

var Party = func(party iris.Party) {
	// 配置文件验证接口
	party.Post("/validate", VerifyConfig).Name = "配置文件验证"
	// 接口信息提取接口
	party.Post("/interfaces", ExtractInterfaces).Name = "提取接口信息"
	// 转换作业管理接口
	party.Post("/convert", CreateTransJob).Name = "创建配置转换任务"
	party.Get("/tasks", GetTransJobs).Name = "查看转换任务列表"

	// 基于job_id的任务查询和操作接口
	party.Get("/task/{job_id:string}", GetTransJobByJobID).Name = "查看任务详情"
	party.Get("/status/{job_id:string}", GetJobStatus).Name = "查询任务状态"
	party.Get("/download/{job_id:string}", DownloadTransFile).Name = "下载转换后的配置文件"
	party.Get("/log/{job_id:string}", DownloadTransLog).Name = "下载转换日志"
	party.Get("/view-log/{job_id:string}", ViewTransLog).Name = "在线查看转换日志"
	party.Get("/view-simple-log/{job_id:string}", ViewSimpleLog).Name = "简单查看转换日志"
	party.Get("/view-raw-log/{job_id:string}", ViewRawLog).Name = "查看原始日志文本"
	party.Get("/report/{job_id:string}", GetConversionReport).Name = "获取转换报告"

	// 新增API - 获取结构化的日志数据，用于前端自定义显示
	party.Get("/log-data/{job_id:string}", GetLogData).Name = "获取结构化日志数据"
}
