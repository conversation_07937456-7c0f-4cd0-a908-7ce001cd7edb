module ntos-lldp {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:lldp";
  prefix ntos-lldp;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS LLDP.";

  revision 2018-10-03 {
    description
      "Initial version.";
    reference "";
  }

  identity lldp {
    base ntos-types:SERVICE_LOG_ID;
    description
      "LLDP service.";
  }

  typedef chassis-id-type {
    type enumeration {
      enum chassis-component {
        description
          "Chassis identifier based on the value of entPhysicalAlias
           object defined in IETF RFC 2737.";
      }
      enum interface-alias {
        description
          "Chassis identifier based on the value of ifAlias object
           defined in IETF RFC 2863.";
      }
      enum port-component {
        description
          "Chassis identifier based on the value of entPhysicalAlias
           object defined in IETF RFC 2737 for a port or backplane
           component.";
      }
      enum mac-address {
        description
          "Chassis identifier based on the value of a unicast source
           address (encoded in network byte order and IEEE 802.3
           canonical bit order), of a port on the containing chassis
           as defined in IEEE Std 802-2001.";
      }
      enum network-address {
        description
          "Chassis identifier based on a network address,
           associated with a particular chassis.  The encoded address
           is composed of two fields.  The first field is a single
           octet, representing the IANA AddressFamilyNumbers value
           for the specific address type, and the second field is the
           network address value.";
      }
      enum interface-name {
        description
          "Chassis identifier based on the name of the interface,
           e.g., the value of ifName object defined in IETF RFC 2863.";
      }
      enum local {
        description
          "Chassis identifier based on a locally defined value.";
      }
    }
    description
      "Type definition with enumerations describing the source of
       the chassis identifier.";
    reference "IEEE 802.1AB LLDP MIB";
  }

  typedef port-id-type {
    type enumeration {
      enum interface-alias {
        description
          "Chassis identifier based on the value of ifAlias object
           defined in IETF RFC 2863.";
      }
      enum port-component {
        description
          "Port identifier based on the value of entPhysicalAlias
           object defined in IETF RFC 2737 for a port component.";
      }
      enum mac-address {
        description
          "Port identifier based on the value of a unicast source
           address (encoded in network byte order and IEEE 802.3
           canonical bit order) associated with a port.";
      }
      enum network-address {
        description
          "Port identifier based on a network address,
           associated with a particular port.";
      }
      enum interface-name {
        description
          "Port identifier based on the name of the interface,
           e.g., the value of ifName object defined in IETF RFC 2863.";
      }
      enum local {
        description
          "Port identifier based on a locally defined alphanumeric
           string.";
      }
    }
    description
      "Type definition with enumerations describing the basis of
       the port identifier.";
    reference "IEEE 802.1AB LLDP MIB";
  }

  grouping lldp-config {
    description
      "Configuration data for global LLDP parameters.";

    leaf enabled {
      type boolean;
      default "true";
      description
        "System level state of the LLDP protocol.";
    }

    leaf hello-timer {
      type uint64;
      units "seconds";
      description
        "System level hello timer for the LLDP protocol.";
    }
  }

  grouping lldp-state {
    description
      "Operational state data for global LLDP parameters.";

    container counters {
      description
        "Global LLDP counters.";
      uses lldp-global-counters;
    }
  }

  grouping lldp-system-info-config {
    description
      "Configuration data for system-level local and remote
       LLDP information.";

    leaf system-name {
      type string {
        length "0..255";
      }
      description
        "The system name field shall contain an alpha-numeric string
         that indicates the system's administratively assigned name.
         The system name should be the system's fully qualified domain
         name. If implementations support IETF RFC 3418, the sysName
         object should be used for this field.";
    }

    leaf system-description {
      type string {
        length "0..255";
      }
      description
        "The system description field shall contain an alpha-numeric
         string that is the textual description of the network entity.
         The system description should include the full name and
         version identification of the system's hardware type,
         software operating system, and networking software. If
         implementations support IETF RFC 3418, the sysDescr object
         should be used for this field.";
    }
  }

  grouping lldp-system-info-state {
    description
      "Operational state data reported for the local and remote
       systems.";

    leaf chassis-id {
      type string;
      description
        "The Chassis ID is a mandatory TLV which identifies the
         chassis component of the endpoint identifier associated with
         the transmitting LLDP agent.";
    }

    leaf chassis-id-type {
      type chassis-id-type;
      description
        "This field identifies the format and source of the chassis
         identifier string. It is an enumerator defined by the
         LldpChassisIdSubtype object from IEEE 802.1AB MIB.";
    }
  }

  grouping lldp-common-counters {
    description
      "Definition of global and per-interface counters.";

    leaf frame-in {
      type ntos-types:counter64;
      description
        "The number of lldp frames received.";
    }

    leaf frame-out {
      type ntos-types:counter64;
      description
        "The number of frames transmitted out.";
    }

    leaf frame-discard {
      type ntos-types:counter64;
      description
        "The number of LLDP frames received and discarded.";
    }

    leaf tlv-discard {
      type ntos-types:counter64;
      description
        "The number of TLV frames received and discarded.";
    }
  }

  grouping lldp-global-counters {
    description
      "Definition of global LLDP counters.";
    uses lldp-common-counters;

    leaf tlv-accepted {
      type ntos-types:counter64;
      description
        "The number of valid TLVs received.";
    }

    leaf entries-aged-out {
      type ntos-types:counter64;
      description
        "The number of entries aged out due to timeout.";
    }
  }

  grouping lldp-interface-counters {
    description
      "Definition of per-interface LLDP counters.";
    uses lldp-common-counters;
  }

  grouping lldp-interface-config {
    description
      "Configuration data for LLDP on each interface.";

    leaf name {
      type ntos-types:ifname;
      description
        "Reference to the LLDP Ethernet interface.";
      ntos-extensions:nc-cli-completion-xpath
        "../ntos-interface:interface/*/*[local-name()='name']";
    }

    leaf enabled {
      type boolean;
      default "true";
      description
        "Enable or disable the LLDP protocol on the interface.";
    }
  }

  grouping lldp-interface-state {
    description
      "Operational state data for LLDP on each interface.";

    container counters {
      description
        "LLDP counters on each interface.";
      uses lldp-interface-counters;
    }
  }

  grouping lldp-neighbor-state {
    description
      "Operational state data for LLDP neighbors.";

    leaf id {
      type string;
      description
        "System generated identifier for the neighbor on the
         interface.";
    }

    leaf port-id {
      type string;
      description
        "The Port ID is a mandatory TLV which identifies the port
         component of the endpoint identifier associated with the
         transmitting LLDP agent. If the specified port is an IEEE
         802.3 Repeater port, then this TLV is optional.";
    }

    leaf port-id-type {
      type port-id-type;
      description
        "This field identifies the format and source of the port
         identifier string. It is an enumerator defined by the
         PtopoPortIdType object from RFC2922.";
    }

    leaf port-description {
      type string;
      description
        "The binary string containing the actual port identifier for
         the port which this LLDP PDU was transmitted. The source and
         format of this field is defined by PtopoPortId from
         RFC2922.";
    }

    leaf management-address {
      type string;
      description
        "The Management Address is a mandatory TLV which identifies a
         network address associated with the local LLDP agent, which
         can be used to reach the agent on the port identified in the
         Port ID TLV.";
    }
  }

  grouping lldp-capabilities-state {
    description
      "Operational state data for LLDP capabilities.";

    leaf name {
      type enumeration {
        enum other {
          description
            "Other capability not specified; bit position 1.";
        }
        enum repeater {
          description
            "Repeater capability; bit position 2.";
          reference "IETF RFC 2108";
        }
        enum mac-bridge {
          description
            "MAC bridge capability; bit position 3.";
          reference "IEEE Std 802.1D";
        }
        enum wlan-access-point {
          description
            "WLAN access point capability; bit position 4.";
          reference "IEEE Std 802.11 MIB";
        }
        enum router {
          description
            "Router; bit position 5.";
          reference "IETF RFC 1812";
        }
        enum telephone {
          description
            "Telephone capability; bit position 6.";
          reference "IETF RFC 4293";
        }
        enum docsis-cable-device {
          description
            "DOCSIS cable device; bit position 7.";
          reference "IETF RFC 4639 and IETF RFC 4546";
        }
        enum station-only {
          description
            "Station only capability, for devices that implement only an
             end station capability, and for which none of the other
             capabilities apply; bit position 8.";
          reference "IETF RFC 4293";
        }
      }
      description
        "Name of the system capability advertised by the neighbor.
         Capabilities are represented in a bitmap that defines the
         primary functions of the system. The capabilities are
         defined in IEEE 802.1AB.";
    }

    leaf enabled {
      type boolean;
      description
        "Indicates whether the corresponding system capability is
         enabled on the neighbor.";
      reference "Sec ******* of IEEE 802.1AB-2009";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Top-level grouping for LLDP config and operational
       state data.";

    container lldp {
      presence "Makes lldpd available";
      description
        "Top-level container for LLDP configuration and state data.";
      ntos-extensions:feature "product";
      uses lldp-config;
      uses lldp-system-info-config;

      leaf management-address {
        type string;
        description
          "The Management Address is a mandatory TLV which identifies a
           network address associated with the local LLDP agent, which
           can be used to reach the agent on the port identified in the
           Port ID TLV.";
      }

      list interface {
        key "name";
        description
          "List of interfaces on which LLDP is enabled / available.";
        uses lldp-interface-config;
      }
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Top-level grouping for LLDP config and operational
       state data.";

    container lldp {
      description
        "Top-level container for LLDP configuration and state data.";
      ntos-extensions:feature "product";
      uses lldp-config;
      uses lldp-system-info-config;
      uses lldp-state;
      uses lldp-system-info-state;

      leaf management-address {
        type string;
        description
          "The Management Address is a mandatory TLV which identifies a
           network address associated with the local LLDP agent, which
           can be used to reach the agent on the port identified in the
           Port ID TLV.";
      }

      list interface {
        key "name";
        description
          "List of interfaces on which LLDP is enabled / available.";
        uses lldp-interface-config;
        uses lldp-interface-state;

        list neighbor {
          key "id";
          description
            "List of LLDP neighbors.";
          ntos-extensions:nc-cli-show-key-name;
          uses lldp-neighbor-state;
          uses lldp-system-info-config;
          uses lldp-system-info-state;

          list capability {
            key "name";
            description
              "List of LLDP system capabilities advertised by the
               neighbor.";
            uses lldp-capabilities-state;
          }
        }
      }
    }
  }
}
