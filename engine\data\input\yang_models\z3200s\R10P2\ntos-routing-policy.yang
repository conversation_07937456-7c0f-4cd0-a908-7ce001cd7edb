module ntos-routing-policy {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:routing-policy";
  prefix ntos-routing-policy;

  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS routing-policy module.";

  revision 2023-05-06 {
    description
      "Initial version.";
    reference
      "";
  }

  identity routing-policy {
    base ntos-types:SERVICE_LOG_ID;
    description
      "routing-policy service.";
  }

  identity route-static {
    ntos-extensions:nc-cli-identity-name "static";
    base ntos-types:ROUTE4_FRR_ID;
    base ntos-types:ROUTE6_FRR_ID;
    description
      "Static routes.";
  }

  typedef address-and-ifname {
    type string {
      ntos-extensions:nc-cli-shortdesc "<A.B.C.D>%<ifname>";
      pattern "(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])%[-A-Za-z0-9/:._@]+";
    }
    description
      "An IPv4 address followed by an interface name.";
  }

  grouping access-list-rule {
    description
      "Configuration for IPv4 access list rule.";
    leaf address {
      type union {
        type ntos-inet:ipv4-prefix {
          ntos-extensions:nc-cli-shortdesc "<A.B.C.D/M> / any";
        }
        type enumeration {
          enum "any" {
            description
              "Any prefix.";
          }
        }
      }
      description
        "Prefix to match.";
    }
    leaf exact-match {
      type boolean;
      must "../address[text() != 'any']" {
        error-message
          "Cannot set exact match if address is set to any.";
      }
      description
        "Enable or disable exact match of the prefixes.";
    }
  }

  grouping access-list-config {
    description
      "Configuration for IPv4 access list.";
    list access-list {
      key "name";
      description
        "IPv4 access list.";
      max-elements 100;
      leaf name {
        type string {
          length "1..max";
        }
        description
          "Access list name.";
      }
      leaf remark {
        type string {
          length "1..100";
        }
        description
          "Access list entry comment.";
      }
      list seq {
        ntos-extensions:nc-cli-one-liner;
        key "num";
        description
          "Specify access list to reject or accept.";
        max-elements 50;
        leaf num {
          type uint16;
          description
            "List sequence.";
        }
        list permit {
          ntos-extensions:nc-cli-one-liner;
          key "address";
          description
            "IPv4 access list deny rules.";
          uses access-list-rule;
          max-elements 1;
        }
        list deny {
          ntos-extensions:nc-cli-one-liner;
          key "address";
          description
            "IPv4 access list deny rules.";
          uses access-list-rule;
          max-elements 1;
        }
      }
    }
  }

  grouping prefix-list-seq-common-config {
    description
      "Configuration for common prefix list sequence number.";
    leaf num {
      type uint32 {
        range "1..**********";
      }
      description
        "Sequence number.";
    }
    leaf policy {
      type enumeration {
        enum "deny" {
          description
            "Specify packets to reject.";
        }
        enum "permit" {
          description
            "Specify packets to forward.";
        }
      }
      mandatory true;
      description
        "Prefix list policy.";
    }
    leaf ge {
      type uint8 {
        range "0..128";
      }
      must "count(../address) = 1" {
        error-message
          "Minimum prefix length is not applicable when address is any";
      }
      description
        "Minimum prefix length to be matched.";
    }
    leaf le {
      type uint8 {
        range "0..128";
      }
      must "count(../address) = 1" {
        error-message
          "Maximum prefix length is not applicable when address is any";
      }
      must "count(../ge) = 0 or . >= ../ge" {
        error-message
          "Maximum prefix must be >= minimum prefix";
      }
      description
        "Maximum prefix length to be matched.";
    }
  }

  grouping prefix-list-seq-config {
    description
      "Configuration for IPv4 prefix list sequence number.";
    leaf address {
      type ntos-inet:ipv4-prefix {
        ntos-extensions:nc-cli-shortdesc "<A.B.C.D/M>";
      }
      description
        "Prefix to match.wuqiang (any if not set).";
    }
    uses prefix-list-seq-common-config;
  }

  grouping route-map-seq-config {
    description
      "Configuration for route map sequence number.";
    leaf num {
      type uint16 {
        range "1..65535";
      }
      description
        "Sequence number.";
    }
    leaf policy {
      type enumeration {
        enum "deny" {
          description
            "Route map denies set operations.";
        }
        enum "permit" {
          description
            "Route map permits set operations.";
        }
      }
      mandatory true;
      description
        "Matching policy.";
    }
    leaf description {
      type string;
      description
        "Route-map description.";
    }
    container match {
      description
        "Match values from routing-policy table.";
      leaf as-path {
        type string {
          length "1..max";
        }
        description
          "Match BGP AS path list.";
      }
      container evpn {
        description
          "Ethernet Virtual Private Network.";
        leaf default-route {
          type boolean;
          description
            "If true, mark as default EVPN type-5 route.";
        }
        leaf route-type {
          type enumeration {
            enum "macip" {
              description
                "Mac-ip route.";
            }
            enum "multicast" {
              description
                "IMET route.";
            }
            enum "prefix" {
              description
                "Prefix route.";
            }
          }
          description
            "Match route type.";
        }
        leaf vni {
          type uint32 {
            range "1..********";
          }
          description
            "VNI ID.";
        }
      }
      leaf interface {
        ntos-extensions:nc-cli-completion-xpath "/ntos:config/vrf/ntos-interface:interface/*/*[local-name()='name']";
        type ntos-types:ifname;
        description
          "Match first hop interface of route.";
      }
      container ip {
        description
          "IP information.";
        container address {
          description
            "Match address of route.";
          leaf access-list {
            type string {
              length "1..max";
            }
            description
              "Matches the specified access list.";
          }
          leaf prefix-list {
            type string {
              length "1..max";
            }
            description
              "Matches the specified prefix list.";
          }
          leaf prefix-len {
            type uint8 {
              range "0..32";
            }
            description
              "Matches the specified prefix length.";
          }
        }
        container route-source {
          description
            "Match advertising source address of route.";
          leaf access-list {
            type union {
              type uint16 {
                range "1..199|1300..2699";
              }
              type string {
                length "1..max";
              }
            }
            description
              "Matches the specified access list.";
          }
          leaf prefix-list {
            type string {
              length "1..max";
            }
            description
              "Matches the specified prefix list.";
          }
        }
      }
      leaf local-preference {
        type uint32;
        description
          "Match local-preference metric value.";
      }
      leaf mac-address {
        type string {
          length "1..max";
        }
        description
          "Match MAC routing-policy name.";
      }
      leaf metric {
        type uint32;
        description
          "Match metric value.";
      }
      leaf origin {
        type enumeration {
          enum "egp" {
            description
              "Remote EGP.";
          }
          enum "igp" {
            description
              "Local IGP.";
          }
          enum "incomplete" {
            description
              "Unknown heritage.";
          }
        }
        description
          "BGP origin code.";
      }
      leaf peer {
        ntos-extensions:nc-cli-completion-xpath "/ntos:config/vrf/ntos-interface:interface/*/*[local-name()='name']";
        type union {
          type enumeration {
            enum "local" {
              description
                "Static or redistributed routes.";
            }
          }
          type ntos-inet:ipv4-address {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
          }
          type ntos-types:ifname;
        }
        description
          "Match peer address.";
      }
      leaf probability {
        type uint8 {
          range "0..100";
        }
        description
          "Match portion of routes defined by percentage value.";
      }
      leaf source-instance {
        type uint8;
        description
          "Match the protocol's instance number.";
      }
      leaf source-protocol {
        type enumeration {
          enum "babel" {
            description
              "BABEL protocol.";
          }
          enum "bgp" {
            description
              "BGP protocol.";
          }
          enum "connected" {
            description
              "Routes from directly connected peer.";
          }
          enum "eigrp" {
            description
              "EIGRP protocol.";
          }
          enum "isis" {
            description
              "ISIS protocol.";
          }
          enum "kernel" {
            description
              "Routes from kernel.";
          }
          enum "nhrp" {
            description
              "NHRP protocol.";
          }
          enum "ospf" {
            description
              "OSPF protocol.";
          }
          enum "ospf6" {
            description
              "OSPF6 protocol.";
          }
          enum "pim" {
            description
              "PIM protocol.";
          }
          enum "rip" {
            description
              "RIP protocol.";
          }
          enum "ripng" {
            description
              "RIPNG protocol.";
          }
          enum "sharp" {
            description
              "SHARP process.";
          }
          enum "static" {
            description
              "Statically configured routes.";
          }
          enum "system" {
            description
              "Routes from system configuration.";
          }
        }
        description
          "Match protocol via which the route was learnt.";
      }
      leaf tag {
        type uint32;
        description
          "Match tag of route.";
      }
    }
    container set {
      description
        "Set values in destination routing-policy protocol.";
      container aggregator {
        ntos-extensions:nc-cli-one-liner;
        presence "Makes aggregator available";
        description
          "BGP aggregator attribute.";
        leaf as {
          type uint32;
          mandatory true;
          description
            "AS number of BGP aggregator.";
        }
        leaf address {
          type ntos-inet:ipv4-address {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
          }
          mandatory true;
          description
            "IP address of aggregator.";
        }
      }
      container as-path {
        description
          "Transform BGP AS-path attribute.";
        leaf-list exclude {
          type uint32;
          description
            "AS numbers to exclude from the as-path.";
        }
        container prepend {
          ntos-extensions:nc-cli-exclusive;
          description
            "Prepend to the as-path.";
          list asn {
            key "id";
            description
              "AS number to prepend to the as-path list.";
            leaf id {
              type uint8;
              description
                "Order of the AS number.";
            }
            leaf value {
              ntos-extensions:nc-cli-no-name;
              type uint32;
              mandatory true;
              description
                "AS number.";
            }
          }
          leaf last-as {
            when "count(../asn) = 0";
            type uint8 {
              range "1..10";
            }
            description
              "Use the peer's AS-number; number of times to insert.";
          }
        }
      }
      leaf table {
        type uint32 {
          range "1..**********";
        }
        description
          "Export route to non-main table.";
      }
      leaf atomic-aggregate {
        type boolean;
        description
          "Enable or disable BGP atomic aggregate attribute.";
      }
      container ip {
        description
          "IP information.";
      }
      leaf label-index {
        type uint32 {
          range "0..1048560";
        }
        description
          "Label index value.";
      }
      leaf local-preference {
        type uint32;
        description
          "BGP local preference path attribute.";
      }
      leaf metric {
        type union {
          type uint32;
          type enumeration {
            enum "add-metric" {
              description
                "Add metric.";
            }
            enum "add-rtt" {
              description
                "Add round trip time.";
            }
            enum "substract-metric" {
              description
                "Subtract metric.";
            }
            enum "substract-rtt" {
              description
                "Subtract round trip time.";
            }
            enum "rtt" {
              description
                "Assign round trip time.";
            }
          }
        }
        description
          "Metric value for destination routing-policy protocol.";
      }
      leaf metric-type {
        type enumeration {
          enum "type-1" {
            description
              "OSPF6 external type 1 metric.";
          }
          enum "type-2" {
            description
              "OSPF6 external type 2 metric.";
          }
        }
        description
          "Type of metric.";
      }
      leaf origin {
        type enumeration {
          enum "egp" {
            description
              "Remote EGP.";
          }
          enum "igp" {
            description
              "Local IGP.";
          }
          enum "incomplete" {
            description
              "Unknown heritage.";
          }
        }
        description
          "BGP origin code.";
      }
      leaf originator-id {
        type ntos-inet:ipv4-address {
          ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
        }
        description
          "BGP originator ID attribute.";
      }
      leaf src {
        type union {
          type ntos-inet:ipv4-address {
            ntos-extensions:nc-cli-shortdesc "<A.B.C.D>";
          }
        }
        description
          "Src address for route.";
      }
      leaf tag {
        type uint32 {
          range "1..**********";
        }
        description
          "Tag value for routing-policy protocol.";
      }
      leaf weight {
        type uint32;
        description
          "BGP weight for routing-policy table.";
      }
    }
    leaf call {
      type string {
        length "1..max";
      }
      description
        "Jump to another Route-Map after match+set.";
    }
    leaf on-match {
      type union {
        type uint16 {
          range "1..65535";
        }
        type enumeration {
          enum "next" {
            description
              "Next clause.";
          }
        }
      }
      must "../policy = 'permit'" {
        error-message
          "on-match not supported under route-map deny";
      }
      description
        "Exit policy on matches.";
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "routing-policy policy global configuration.";
    uses access-list-config;
    list prefix-list {
      key "name";
      description
        "IPv4 prefix list.";
      leaf name {
        type string {
          length "1..max";
        }
        description
          "Prefix list name.";
      }
      list seq {
        ntos-extensions:nc-cli-one-liner;
        ntos-extensions:nc-cli-sort-by "num";
        key "num";
        max-elements 50;
        description
          "Prefix list sequence.";
        uses prefix-list-seq-config;
      }
    }
    list route-map {
      key "name";
      description
        "Route map list.";
      leaf name {
        type string {
          length "1..max";
        }
        description
          "Route map name.";
      }
      list seq {
        ntos-extensions:nc-cli-sort-by "num";
        max-elements 50;
        key "num";
        description
          "Route map sequence.";
        uses route-map-seq-config;
      }
    }
  }

  rpc routing-policy {
    description
      "Show state of routing-policy.";
    input {
      leaf vrf {
        type ntos-types:ntos-obj-name-type;
        description
          "Specify the VRF.";
      }

      container content {
        ntos-extensions:nc-cli-group "subcommand";
        leaf type {
          type enumeration {
            enum access-list;
            enum prefix-list;
            enum route-map;
          }

          description
            "Show type of routing-policy.";
        }

        leaf start {
          type uint32;
          description
            "Start offset of result.";
        }

        leaf end {
          type uint32;
          description
            "End offset of result.";
        }

        leaf format {
          type empty;
          description
            "Formatting display";
        }

        leaf filter {
          type string;
          description
            "show item by filter";
        }
      }
    }

    output {
      leaf json {
        description
          "The json data of result.";
        type string;
      }
    }

    ntos-extensions:nc-cli-show "routing-policy";
    ntos-api:internal;
  }
}
