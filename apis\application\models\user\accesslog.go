package user

import "irisAdminApi/application/models"

type AccessLog struct {
	models.ModelBase
	ServerName    string `gorm:"type:varchar(100)" json:"server_name" comment:"服务名称"`
	IP            string `gorm:"type:varchar(100)" json:"ip"  comment:"ip地址"`
	RequestMethod string `gorm:"type:varchar(100)" json:"request_method"  comment:"请求方式"`
	RequestPath   string `gorm:"type:varchar(255)" json:"request_path"  comment:"请求路径"`
	RequestAction string `gorm:"type:varchar(255)" json:"request_action"  comment:"请求方法"`
	RouteName     string `gorm:"type:varchar(100)" json:"route_name"  comment:"路由名称"`
	UserID        string `gorm:"type:varchar(100)" json:"user_id"  comment:"用户id"`
}
