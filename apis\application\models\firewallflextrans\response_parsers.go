package firewallflextrans

import (
	"encoding/json"
	"fmt"
	"irisAdminApi/application/logging"
)

// 实现验证响应解析器的Parse方法
func (p VerificationResponseParser) Parse(rawData map[string]interface{}) (interface{}, error) {
	response := VerificationResponse{
		Success: false,
		Message: "验证失败",
	}

	// 解析成功状态
	if success, ok := rawData["success"].(bool); ok {
		response.Success = success
	}

	// 解析消息
	if message, ok := rawData["message"].(string); ok && message != "" {
		response.Message = message
	} else {
		// 尝试从其他字段获取消息
		if response.Success {
			response.Message = "配置文件验证成功"
		} else if errMsg, ok := rawData["error"].(string); ok && errMsg != "" {
			response.Message = errMsg
		}
	}

	// 解析详细信息
	if details, ok := rawData["details"].(string); ok && details != "" {
		response.Details = details
	}

	return response, nil
}

// 实现接口提取响应解析器的Parse方法
func (p InterfaceExtractionResponseParser) Parse(rawData map[string]interface{}) (interface{}, error) {
	response := InterfaceExtractionResponse{
		Success:         false,
		Message:         "接口提取失败",
		Interfaces:      []InterfaceInfo{},
		InterfacesMap:   make(map[string]InterfaceInfo),
		InterfacesCount: 0,
	}

	// 解析成功状态
	if success, ok := rawData["success"].(bool); ok {
		response.Success = success
	}

	// 解析消息
	if message, ok := rawData["message"].(string); ok && message != "" {
		response.Message = message
	} else {
		// 尝试从其他字段获取消息
		if response.Success {
			response.Message = "接口提取成功"
		} else if errMsg, ok := rawData["error"].(string); ok && errMsg != "" {
			response.Message = errMsg
		}
	}

	// 解析接口列表
	interfaces := []InterfaceInfo{}
	interfacesMap := make(map[string]InterfaceInfo)

	// 首先尝试解析interfaces数组
	if ifaces, ok := rawData["interfaces"].([]interface{}); ok {
		for _, iface := range ifaces {
			if ifaceMap, ok := iface.(map[string]interface{}); ok {
				info := parseInterfaceInfo(ifaceMap)
				interfaces = append(interfaces, info)
				interfacesMap[info.Name] = info
			}
		}
	}

	// 然后尝试解析interfaces_map对象
	if ifacesMap, ok := rawData["interfaces"].(map[string]interface{}); ok {
		for name, iface := range ifacesMap {
			if ifaceMap, ok := iface.(map[string]interface{}); ok {
				info := parseInterfaceInfo(ifaceMap)
				// 如果名称为空，使用map的键作为名称
				if info.Name == "" {
					info.Name = name
				}
				interfaces = append(interfaces, info)
				interfacesMap[name] = info
			}
		}
	}

	response.Interfaces = interfaces
	response.InterfacesMap = interfacesMap
	response.InterfacesCount = len(interfaces)

	// 解析映射JSON
	if mappingJSON, ok := rawData["mapping_json"].(string); ok {
		response.MappingJSON = mappingJSON
	}

	// 如果Python引擎已经提供了接口数量，使用它
	if count, ok := rawData["interfaces_count"].(float64); ok {
		response.InterfacesCount = int(count)
	}

	// 如果成功提取但没有找到接口，设置适当的消息
	if response.Success && response.InterfacesCount == 0 {
		response.Message = "未找到接口信息"
	}

	return response, nil
}

// 解析接口信息
func parseInterfaceInfo(data map[string]interface{}) InterfaceInfo {
	info := InterfaceInfo{
		Attributes: make(map[string]interface{}),
	}

	// 提取基本字段
	if name, ok := data["name"].(string); ok {
		info.Name = name
	}
	if ip, ok := data["ip"].(string); ok {
		info.IP = ip
	}
	if mask, ok := data["mask"].(string); ok {
		info.Mask = mask
	}
	if ifType, ok := data["type"].(string); ok {
		info.Type = ifType
	}
	if status, ok := data["status"].(string); ok {
		info.Status = status
	}
	if desc, ok := data["description"].(string); ok {
		info.Description = desc
	}

	// 将其他字段存储到Attributes中
	for key, value := range data {
		switch key {
		case "name", "ip", "mask", "type", "status", "description":
			// 这些字段已经处理过了
		default:
			info.Attributes[key] = value
		}
	}

	return info
}

// 实现配置转换响应解析器的Parse方法
func (p ConversionResponseParser) Parse(rawData map[string]interface{}) (interface{}, error) {
	response := ConversionResponse{
		Success:      false,
		Message:      "配置转换失败",
		ErrorDetails: make(map[string]interface{}),
	}

	// 解析成功状态
	if success, ok := rawData["success"].(bool); ok {
		response.Success = success
	}

	// 解析消息
	if message, ok := rawData["message"].(string); ok && message != "" {
		response.Message = message
	} else {
		// 尝试从其他字段获取消息
		if response.Success {
			response.Message = "配置转换成功"
		} else if errMsg, ok := rawData["error"].(string); ok && errMsg != "" {
			response.Message = errMsg
		}
	}

	// 解析输出文件路径
	if outputFile, ok := rawData["output_file"].(string); ok {
		response.OutputFile = outputFile
	}

	// 解析报告文件路径
	if reportPath, ok := rawData["report_path"].(string); ok {
		response.ReportPath = reportPath
	}

	// 收集错误详情
	for key, value := range rawData {
		switch key {
		case "success", "message", "output_file", "report_path":
			// 这些字段已经处理过了
		default:
			// 其他字段作为错误详情
			if !response.Success {
				response.ErrorDetails[key] = value
			}
		}
	}

	// 如果没有错误详情，删除该字段
	if len(response.ErrorDetails) == 0 {
		response.ErrorDetails = nil
	}

	return response, nil
}

// 通用响应解析函数，根据原始数据和解析器类型解析响应
func ParseResponse(rawData map[string]interface{}, parser ResponseParser) (interface{}, error) {
	if rawData == nil {
		return nil, fmt.Errorf("原始数据为空")
	}

	// 记录原始数据的结构（仅用于调试）
	jsonData, _ := json.MarshalIndent(rawData, "", "  ")
	logging.DebugLogger.Infof("解析原始数据: %s", string(jsonData))

	// 使用解析器解析数据
	return parser.Parse(rawData)
}
