module ntos-automation {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:automation";
  prefix ntos-automation;

  import ntos {
    prefix ntos;
  }
  import ntos-api {
    prefix ntos-api;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-extensions;
  }
  import ntos-types {
    prefix ntos-types;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS automation module.";

  revision 2024-06-30 {
    description
      "Initial version.";
    reference
      "";
  }

  identity eoar {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Automation service.";
  }

  grouping adapter-info {
    description
      "Grouping for adapter info choices.";
    choice info {
      description
        "Choose adapter information option.";
      case running {
        leaf running {
          type empty;
          description
            "Adapter information for running.";
        }
      }
      case loaded {
        leaf loaded {
          type empty;
          description
            "Adapter information for loaded.";
        }
      }
      case finished {
        leaf finished {
          type empty;
          description
            "Adapter information for finished.";
        }
      }
      case name {
        leaf name {
          type string;
          description
            "Specific adapter information.";
        }
      }
    }
  }

  grouping info-filter {
    leaf start {
      type uint32;
      description
        "Start of the data.";
    }

    leaf end {
      type uint32;
      description
        "End of the data.";
    }
  }

  rpc run-automation-playbook {
    ntos-extensions:nc-cli-cmd "eoar playbook run";
    ntos-api:internal;
    description
      "RPC for executing automation playbook commands.";

    input {
      leaf name {
        type string;
        description
          "Playbook name.";
        mandatory true;
      }

      leaf params {
        type string;
        description
          "Playbook parameters in json format.";
      }

      leaf async {
        type empty;
        description
          "Wait result return or not.";
      }
    }

    output {
      leaf result {
        type string;
        description
          "The result of the command.";
      }
    }
  }

  rpc start-automation-adapter {
    ntos-extensions:nc-cli-cmd "eoar adapter start";
    ntos-api:internal;
    description
      "RPC for start automation adapter commands.";

    input {
      leaf name {
        type string;
        description
          "Adapter name.";
        mandatory true;
      }
    }

    output {
      leaf result {
        type string;
        description
          "The result of the command.";
      }
    }
  }

  rpc stop-automation-adapter {
    ntos-extensions:nc-cli-cmd "eoar adapter stop";
    ntos-api:internal;
    description
      "RPC for start automation adapter commands.";

    input {
      leaf name {
        type string;
        description
          "Adapter name.";
        mandatory true;
      }
    }

    output {
      leaf result {
        type string;
        description
          "The result of the command.";
      }
    }
  }

  rpc enable-automation-trigger {
    ntos-extensions:nc-cli-cmd "eoar trigger";
    ntos-api:internal;
    description
      "RPC for executing automation commands.";

    input {
      leaf name {
        type string;
        description
          "Trigger name.";
        mandatory true;
      }

      leaf enabled {
        type boolean;
        description
          "Enable or disable trigger of eoar.";
        mandatory true;
      }
    }

    output {
      leaf result {
        type string;
        description
          "The result of the cmd.";
      }
    }
  }

  rpc post-automation-event {
    ntos-extensions:nc-cli-cmd "eoar event post";
    ntos-api:internal;
    description
      "RPC for executing automation commands.";

    input {
      leaf name {
          type string;
          description
            "Event name.";
          mandatory true;
      }
      leaf params {
          type string;
          description
            "Event parameters in json format.";
      }
    }

    output {
      leaf result {
        type string;
        description
          "The result of the cmd.";
      }
    }
  }

  rpc upgrade-automation-library {
    ntos-extensions:nc-cli-cmd "eoar library upgrade";
    ntos-api:internal;
    description
      "RPC for upgrade automation library.";

    input {
      leaf package-path {
          type string;
          description
            "Path of library package file.";
          mandatory true;
      }
    }

    output {
      leaf id {
        type uint32;
        description
          "Id of tracing upgrade.";
      }

      leaf progress {
        type uint32;
        description
          "Progress of upgrade.";
      }

      leaf error-code {
        type uint32;
        description
          "Error code.";
      }

      leaf buf {
        type string;
        description
          "Error code information.";
      }
    }
  }

  rpc show-automation-playbook-info {
    ntos-extensions:nc-cli-show "eoar playbook";
    ntos-api:internal;
    description
      "Show information of eoar playbook.";

    input {
      leaf name {
        type string;
        description
          "Specific playbook information.";
      }

      uses info-filter;
    }

    output {
      container info {
        list playbook {
          key "name";

          leaf name {
            type string;
            description
              "Playbook name.";
          }

          leaf description {
            type string;
            description
              "Playbook description.";
          }

          leaf hit-count {
            type uint32;
            description
              "Playbook hitted counts.";
          }

          list static {
            key "name";
            leaf name {
              type string;
              description
                "Static name.";
            }

            leaf description {
              type string;
              description
                "Static description.";
            }

            leaf default {
              type string;
              description
                "Default value.";
            }

            container current {
              leaf type {
                type string;
                description
                  "Data type.";
              }

              leaf value {
                type string;
                description
                  "Data Value.";
              }
            }
          }

          list parameter {
            key "name";
            leaf name{
              type string;
              description
                "Playbook parameter name.";
            }

            leaf description {
              type string;
              description
                "Playbook parameter description.";
            }
          }
        }
      }
    }
  }

  rpc show-automation-adapter-info {
    ntos-extensions:nc-cli-show "eoar adapter";
    ntos-api:internal;
    description
      "Show information of eoar adapter.";

    input {
      uses adapter-info;
      uses info-filter;
    }

    output {
      container info {
        list adapter {
          key "name";

          leaf name {
            type string;
            description
              "Adapter name.";
          }

          leaf description {
            type string;
            description
              "Adapter description.";
          }

          leaf status {
            type string;
            description
              "Adapter status.";
          }

          leaf enabled {
            type boolean;
            description
              "Adapter enabled.";
          }
        }
      }
    }
  }

  rpc show-automation-trigger-info {
    ntos-extensions:nc-cli-show "eoar trigger";
    ntos-api:internal;
    description
      "Show information of eoar trigger.";

    input {
      leaf name {
        type string;
        description
          "Specific trigger information.";
      }

      uses info-filter;
    }

    output {
      container info {
        list trigger {
          key "name";

          leaf name {
            type string;
            description
              "Trigger name.";
          }

          leaf enabled {
            type boolean;
            description
              "Trigger enabled.";
          }

          leaf description {
            type string;
            description
              "Trigger description.";
          }

          leaf event-name {
            type string;
            description
              "Event name.";
          }

          leaf playbook {
            type string;
            description
              "Playbook name.";
          }

          container hit-count {
            leaf touched {
              type uint32;
              description
                "Touched counts.";
            }

            leaf triggered {
              type uint32;
              description
                "Triggered counts.";
            }
          }
        }
      }
    }
  }

  rpc show-automation-event-info {
    ntos-extensions:nc-cli-show "eoar event";
    ntos-api:internal;
    description
      "Show information of eoar event.";

    input {
      leaf name {
        type string;
        description
          "Specific event information.";
      }

      uses info-filter;
    }

    output {
      container info {
        list event {
          key "name";

          leaf name {
            type string;
            description
              "Event info.";
          }

          leaf description {
            type string;
            description
              "Event description.";
          }

          leaf hit-count {
            type uint32;
            description
              "Event hitted counts.";
          }

          list payload {
            key "name";

            leaf name {
              type string;
              description
                "Payload name.";
            }

            leaf description {
              type string;
              description
                "Payload description.";
            }
          }
        }
      }
    }
  }

  rpc show-automation-library-state {
    ntos-extensions:nc-cli-show "eoar library state";
    ntos-api:internal;
    description
      "Show state of eoar library.";

    input {
      leaf id {
        type uint32;
        description
          "Id of upgrade.";
      }
    }

    output {
      leaf progress {
        type uint32;
        description
          "Progress of upgrade.";
      }

      leaf error-code {
        type uint32;
        description
          "Error code.";
      }

      leaf buf {
        type string;
        description
          "Error code information.";
      }
    }
  }

  rpc show-automation-library-version {
    ntos-extensions:nc-cli-show "eoar library version";
    ntos-api:internal;
    description
      "Show version of eoar library.";

    output {
      leaf version {
        type string;
        description
          "Eoar library version.";
      }
    }
  }
}
