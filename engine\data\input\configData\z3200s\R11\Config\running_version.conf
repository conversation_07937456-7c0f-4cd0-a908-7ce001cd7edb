[{"name": "root", "version": 12621, "ts": *************}, {"name": "physical", "version": 12619, "ts": *************}, {"name": "sub_interface", "version": 12619, "ts": *************}, {"name": "routing", "version": 12618, "ts": *************}, {"name": "dhcp", "version": 12619, "ts": *************}, {"name": "security_zone", "version": 12619, "ts": *************}, {"name": "auth", "version": 12619, "ts": *************}, {"name": "devicename", "version": 12618, "ts": *************}, {"name": "discovery", "version": 12618, "ts": *************}, {"name": "timezone", "version": 12619, "ts": *************}, {"name": "security-policy", "version": 12618, "ts": *************}, {"name": "network-obj", "version": 12618, "ts": *************}, {"name": "appid", "version": 12619, "ts": *************}, {"name": "service-obj", "version": 12619, "ts": *************}, {"name": "time-range", "version": 12619, "ts": *************}, {"name": "ips-config", "version": 12618, "ts": *************}, {"name": "anti-virus", "version": 12618, "ts": *************}, {"name": "url-filter", "version": 12618, "ts": *************}, {"name": "url-category", "version": 12618, "ts": *************}, {"name": "security-defend", "version": 12619, "ts": 1750142259761}, {"name": "nat", "version": 12619, "ts": 1750142259736}, {"name": "threat-intelligence", "version": 12619, "ts": 1750142259737}, {"name": "mac-block", "version": 12619, "ts": 1750142259779}, {"name": "user-experience", "version": 12619, "ts": 1750142259730}, {"name": "mllb", "version": 12619, "ts": 1750142259734}]