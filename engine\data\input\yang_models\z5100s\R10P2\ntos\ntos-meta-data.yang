module ntos-meta-data {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:meta-data";
  prefix ntos-meta-data;

  import ietf-yang-metadata {
    prefix md;
    revision-date 2016-08-05;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS metadata annotation module.";

  revision 2024-08-14 {
    description
      "Initial revision of NTOS metadata type definition.";
  }

  md:annotation lang {
    type enumeration {
      enum "0" {
        description
          "Denotes Chinese language.";
      }
      enum "1" {
        description
          "Denotes English language.";
      }
    }

    description
      "The language of the request data.";
  }

  md:annotation src {
    type enumeration {
      enum "0" {
        description
          "Denotes configuration originated by inner component.";
      }
      enum "1" {
        description
          "Denotes configuration originated by eweb component.";
      }
      enum "2" {
        description
          "Denotes configuration originated by nc-cli component.";
      }
      enum "3" {
        description
          "Denotes configuration originated by macc cloud.";
      }
      enum "4" {
        description
          "Denotes configuration originated by netconf client.";
      }
      enum "5" {
        description
          "Denotes configuration originated by snmp component.";
      }
      enum "6" {
        description
          "Denotes configuration originated by controller(for netconf-callhome).";
      }
      enum "7" {
        description
          "Denotes configuration originated by macc cloud(sync).";
      }
    }

    description
      "The origin annotation of configuration.";
  }

  md:annotation ts {
    type string;
    description
      "The timestamp annotation of configuration.";
  }

  md:annotation usr {
    type string;
    description
      "The username that issued the configuration.";
  }
  
  md:annotation ip {
    type string;
    description
      "The ip address that the configuration originated.";
  }

  md:annotation sync {
    type enumeration {
      enum "0" {
        description
          "For this configuration deployment, apply asynchronously.";
      }
      enum "1" {
        description
          "For this configuration deployment, apply synchronously.";
      }
    }
    description
      "Used for applying configuration synchronously.";
  }
}
