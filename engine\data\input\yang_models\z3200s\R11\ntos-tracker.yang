module ntos-tracker {
  namespace "urn:ruijie:ntos:params:xml:ns:yang:tracker";
  prefix ntos-tracker;

  import ntos {
    prefix ntos;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "Ruijie NTOS tracker.";

  revision 2019-07-29 {
    description
      "Move ICMP configuration in ntos-pm routing module.";
    reference "";
  }
  revision 2019-05-27 {
    description
      "Initial version.";
    reference "";
  }

  typedef tracker-name {
    type string {
      length "1..128";
      pattern '[-A-Za-z0-9._@]+';
      ntos-ext:nc-cli-shortdesc "<tracker-name>";
    }
    description
      "An tracker name.";
  }

  augment "/ntos:config" {
    description
      "The tracker configuration.";

    container tracker {
      presence "Makes tracker available";
      description
        "Track IP addresses.";
      ntos-ext:feature "product";
    }
  }

  augment "/ntos:state" {
    description
      "The tracker state.";

    container tracker {
      description
        "The tracker operational state data.";
      ntos-ext:feature "product";
    }
  }
}
