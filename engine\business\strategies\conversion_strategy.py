# -*- coding: utf-8 -*-
"""
转换策略基类 - 定义厂商特定转换策略的标准接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
from engine.utils.logger import log
from engine.utils.i18n import _
from engine.processing.pipeline.data_flow import DataContext


class ConversionStrategy(ABC):
    """
    转换策略基类
    定义厂商特定转换策略的标准接口
    """
    
    def __init__(self, vendor: str, config_manager, template_manager, yang_manager):
        """
        初始化转换策略
        
        Args:
            vendor: 厂商名称
            config_manager: 配置管理器
            template_manager: 模板管理器
            yang_manager: YANG管理器
        """
        self.vendor = vendor
        self.config_manager = config_manager
        self.template_manager = template_manager
        self.yang_manager = yang_manager
        
        log(_("conversion_strategy.initialized"), "info", vendor=vendor)
    
    @abstractmethod
    def validate_input(self, context: DataContext) -> bool:
        """
        验证输入配置数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        pass
    
    @abstractmethod
    def prepare_conversion_data(self, context: DataContext) -> bool:
        """
        准备转换数据
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 准备是否成功
        """
        pass
    
    def validate_xml_output(self, context: DataContext) -> bool:
        """
        验证XML输出 - 默认实现
        
        Args:
            context: 数据上下文
            
        Returns:
            bool: 验证是否通过
        """
        xml_content = context.get_data("generated_xml")
        if not xml_content:
            context.add_error(_("conversion_strategy.no_xml_content"))
            return False
        
        # 基本XML格式验证
        if not xml_content.strip().startswith("<?xml") and not xml_content.strip().startswith("<"):
            context.add_error(_("conversion_strategy.invalid_xml_format"))
            return False
        
        return True
    
    def get_template_path(self, model: str, version: str) -> Optional[str]:
        """
        获取模板路径
        
        Args:
            model: 设备型号
            version: 设备版本
            
        Returns:
            Optional[str]: 模板路径
        """
        try:
            return self.template_manager.get_template_path(model, version)
        except Exception as e:
            log(_("conversion_strategy.template_path_failed"), "error", 
                vendor=self.vendor, model=model, version=version, error=str(e))
            return None
    
    def get_yang_schema(self, model: str, version: str) -> Optional[Dict[str, Any]]:
        """
        获取YANG模型架构
        
        Args:
            model: 设备型号
            version: 设备版本
            
        Returns:
            Optional[Dict[str, Any]]: YANG架构
        """
        try:
            return self.yang_manager.get_yang_schema(model, version)
        except Exception as e:
            log(_("conversion_strategy.yang_schema_failed"), "error",
                vendor=self.vendor, model=model, version=version, error=str(e))
            return None
    
    def validate_against_yang(self, xml_file: str, model: str, version: str) -> Tuple[bool, str]:
        """
        使用YANG模型验证XML
        
        Args:
            xml_file: XML文件路径
            model: 设备型号
            version: 设备版本
            
        Returns: Tuple[bool, str]: (验证是否通过, 验证消息)
        """
        try:
            return self.yang_manager.validate_xml_against_yang(xml_file, model, version)
        except Exception as e:
            error_msg = f"转换策略YANG验证失败: {str(e)}"
            log(error_msg, "error")
            return False, error_msg
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns: Dict[str, Any]: 策略信息
        """
        return {
            "vendor": self.vendor,
            "strategy_class": self.__class__.__name__,
            "template_manager_available": self.template_manager is not None,
            "yang_manager_available": self.yang_manager is not None,
            "yanglint_available": self.yang_manager.is_yanglint_available() if self.yang_manager else False
        }
