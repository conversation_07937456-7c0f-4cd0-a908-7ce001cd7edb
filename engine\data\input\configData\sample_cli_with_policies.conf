#config-version=FG100F-7.6.3-FW-build3510-250415:opmode=0:vdom=0:user=admin
config system interface
    edit "port1"
        set ip ************/24
        set allowaccess ping https ssh snmp http fgfm
    next
    edit "port2"
        set ip ***********/24
    next
    edit "port3"
        set vdom "root"                                
        set ip ************* *************     
        set allowaccess ping https ssh snmp http fgfm
        set status down                                 
        set type physical                              
        set description "port3-description"
        set alias "Ge0/3"                              
        set security-mode captive-portal
        set device-identification enable
        set role lan
        set snmp-index 7
        set secondary-IP enable
        config ipv6                                            
            set ip6-address 2003::1/96
            set ip6-allowaccess ping https ssh http
        end
        config secondaryip
            edit 1
                set ip ******* *************
            next
            edit 2
                set ip ******* *************
            next
        end
    next
end

config router static
    edit 1
        set dst 0.0.0.0 0.0.0.0
        set gateway ************
        set device "port1"
    next
    edit 2
        set dst ********** *************
        set gateway *************
        set device "port2"
        set distance 10
    next
end

config system zone
    edit "LAN"
        set interface "port2" "port3"
    next
    edit "WAN"
        set interface "port1"
    next
end

config firewall address
    edit "***********/24"
        set uuid 987da852-89c0-51ee-db79-7f4778c644b7
        set subnet *********** *************
    next
    edit "***********/24"
        set uuid 98896f70-89c0-51ee-8e3c-5be630957f68
        set subnet *********** *************
    next
    edit "**********/24"
        set subnet ********** *************
    next
end

config firewall service custom
    edit "TCP-8080"
        set tcp-portrange 8080
    next
    edit "HTTP-CUSTOM"
        set tcp-portrange 80 8080
    next
end

config firewall policy
    edit 1
        set name "LAN_to_WAN_Allow"
        set srcintf "LAN"
        set dstintf "WAN"
        set srcaddr "***********/24" "***********/24"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set nat enable
    next
    edit 2
        set name "WAN_to_LAN_Block"
        set srcintf "WAN"
        set dstintf "LAN"
        set srcaddr "all"
        set dstaddr "***********/24"
        set action deny
        set schedule "always"
        set service "ALL"
    next
    edit 3
        set name "Internal_Access"
        set srcintf "port2"
        set dstintf "port3"
        set srcaddr "***********/24"
        set dstaddr "**********/24"
        set action accept
        set schedule "always"
        set service "TCP-8080" "HTTP-CUSTOM"
    next
end
