package schedule

import (
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
	"os"
	"path/filepath"
	"time"
)

// CleanExpiredOutputFiles 清理过期的转换输出文件
func CleanExpiredOutputFiles() {
	// 获取配置中设置的文件保留小时数
	// 注意：为了兼容现有配置，我们仍使用RetentionDays配置项，但将其解释为小时
	retentionHours := libs.Config.ConfigTrans.RetentionHours
	if retentionHours <= 0 {
		retentionHours = 3 // 默认保留3小时
	}

	// 计算过期时间点（小时级别）
	expireTime := time.Now().Add(-time.Duration(retentionHours) * time.Hour)

	// 获取配置文件上传目录
	uploadDir := libs.Config.ConfigTrans.Upload
	if uploadDir == "" {
		uploadDir = "/tmp/configtrans_uploads/"
	}

	// 确保路径存在
	if _, err := os.Stat(uploadDir); os.IsNotExist(err) {
		logging.InfoLogger.Infof("配置文件上传目录不存在: %s", uploadDir)
		return
	}

	// 统计删除的文件数量和释放的空间
	deletedFiles := 0
	freedSpace := int64(0)

	// 遍历日期目录
	dateDirs, err := os.ReadDir(uploadDir)
	if err != nil {
		logging.ErrorLogger.Errorf("读取配置文件上传目录失败: %v", err)
		return
	}

	for _, dateDir := range dateDirs {
		if !dateDir.IsDir() {
			continue
		}

		dateDirPath := filepath.Join(uploadDir, dateDir.Name())

		// 遍历任务目录
		jobDirs, err := os.ReadDir(dateDirPath)
		if err != nil {
			logging.ErrorLogger.Errorf("读取日期目录失败: %s, 错误: %v", dateDirPath, err)
			continue
		}

		for _, jobDir := range jobDirs {
			if !jobDir.IsDir() {
				continue
			}

			jobDirPath := filepath.Join(dateDirPath, jobDir.Name())
			outputDirPath := filepath.Join(jobDirPath, "output")

			// 检查output目录是否存在
			if _, err := os.Stat(outputDirPath); os.IsNotExist(err) {
				continue
			}

			// 遍历output目录中的文件
			outputFiles, err := os.ReadDir(outputDirPath)
			if err != nil {
				logging.ErrorLogger.Errorf("读取输出目录失败: %s, 错误: %v", outputDirPath, err)
				continue
			}

			for _, file := range outputFiles {
				if file.IsDir() {
					continue // 跳过子目录
				}

				filePath := filepath.Join(outputDirPath, file.Name())

				// 获取文件信息
				fileInfo, err := os.Stat(filePath)
				if err != nil {
					logging.ErrorLogger.Errorf("获取文件信息失败: %s, 错误: %v", filePath, err)
					continue
				}

				// 检查文件是否为XML或加密文件
				if filepath.Ext(file.Name()) == ".xml" || file.Name() == "backup-startup.tar.gz" {
					// 检查文件修改时间是否早于过期时间
					if fileInfo.ModTime().Before(expireTime) {
						// 记录文件大小
						fileSize := fileInfo.Size()

						// 删除文件
						if err := os.Remove(filePath); err != nil {
							logging.ErrorLogger.Errorf("删除过期输出文件失败: %s, 错误: %v", filePath, err)
						} else {
							logging.DebugLogger.Infof("已删除过期输出文件: %s", filePath)
							deletedFiles++
							freedSpace += fileSize
						}
					}
				}
			}
		}
	}

	// 记录清理结果
	if deletedFiles > 0 {
		freedSpaceMB := float64(freedSpace) / (1024 * 1024)
		logging.InfoLogger.Infof("配置文件清理完成，删除了 %d 个过期文件（超过%d小时），释放了 %.2f MB 空间",
			deletedFiles, retentionHours, freedSpaceMB)
	} else {
		logging.InfoLogger.Infof("配置文件清理完成，没有找到超过%d小时的过期文件", retentionHours)
	}
}

// ScheduleConfigFilesCleaning 设置定时清理任务
func ScheduleConfigFilesCleaning() {
	// 立即执行一次清理
	CleanExpiredOutputFiles()

	// 添加每小时执行一次的定时任务
	_, err := Cron.AddFunc("0 * * * *", CleanExpiredOutputFiles)
	if err != nil {
		logging.ErrorLogger.Errorf("添加配置文件清理定时任务失败: %v", err)
	} else {
		logging.InfoLogger.Info("已添加配置文件清理定时任务，每小时执行一次")
	}
}
