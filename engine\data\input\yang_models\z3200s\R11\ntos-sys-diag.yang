module ntos-sys-diag {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:sys-diag";
  prefix ntos-sys-diag;

  import iana-timezones {
    prefix iana-tz;
  }

  import ietf-netconf-acm {
    prefix nacm;
  }

  import ntos {
    prefix ntos;
  }

  import ntos-api {
    prefix ntos-api;
  }

  import ntos-commands {
    prefix ntos-cmd;
  }

  import ntos-extensions {
    prefix ntos-ext;
  }

  import ntos-inet-types {
    prefix ntos-inet;
  }

  import ntos-types {
    prefix ntos-types;
  }

  import ntos-ip {
    prefix ntos-ip;
  }

  import ntos-if-types {
    prefix ntos-if;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
     E-mail:<EMAIL>";
  description
    "Ruijie NTOS fault diagnosis center module.";

  revision 2022-05-20 {
    description
      "Initial version.";
    reference
      "";
  }

  typedef https-url {
    type string {
      pattern 'https?://([^:]+:[^@]+@)?[^/:]+(:[0-9]+)?.*';
      ntos-ext:nc-cli-shortdesc "<http[s]://host|domain[:port]>";
    }
    description
      "An HTTP(S) login URL.";
  }

  feature ipsec {
    description
      "Advanced Ipsec diag capabilities.";

  }

  typedef fdc-diag-type {
    type enumeration {
      enum "device-health" {
        description
          "Device health.";
      }
      enum "net-diag" {
        description
          "Netd diagnosis.";
      }
      enum "sslvpn-diag" {
        description
          "sslvpn diagnosis.";
      }
      enum "ipsec-diag" {
        description
          "ipsec diagnosis.";
      }
      enum "dst-nat-diag" {
        description
          "destination nat diagnosis.";
      }
    }
    description
      "Fault diagnosis task type.";
  }

  typedef fdc-diag-result-type {
    type enumeration {
      enum "pass" {
        description
          "Diagnosis pass.";
      }
      enum "fail" {
        description
          "Diagnosis fail.";
      }
    }
    description
      "Fault diagnosis result type.";
  }

  grouping cmd-output-info {
    leaf id {
      type uint32;
      description
        "A fault diagnosis center task's id.";
    }

    leaf name {
      type string;
      description
        "A fault diagnosis center task's name.";
    }

    leaf time {
      type string;
      description
        "A fault diagnosis center task's generation time.";
    }

    leaf params {
      type string;
      description
        "A fault diagnosis center task's params information.";
    }

    leaf state {
      type string;
      description
        "A fault diagnosis center task's state.";
    }

    leaf result {
      type string;
      description
        "A fault diagnosis center task's result.";
    }

    leaf current-stage-name {
      type string;
      description
        "The current stage name of a fault diagnosis center task.";
    }

    list stages {
      key "name";
      leaf name {
        type string;
        description
          "A fault diagnosis center stage's name.";
      }

      leaf id {
        type uint32;
        description
          "A fault diagnosis center stage's id.";
      }

      leaf running-time {
        type uint32;
        description
          "A fault diagnosis center stage's runnint time.";
      }

      leaf state {
        type string;
        description
          "A fault diagnosis center stage's state.";
      }

      leaf result {
        type string;
        description
          "A fault diagnosis center stage's result.";
      }

      list results {
        key "id";
        leaf id {
          type uint32;
          description
            "A fault diagnosis center stage result's index.";
        }

        leaf typeid {
          type uint32;
          description
            "A fault diagnosis center stage result's typeid.";
        }

        leaf result {
          type string;
          description
            "A fault diagnosis center stage result's result.";
        }

        leaf ignore {
          type uint32;
          description
            "A fault diagnosis center stage result's ignore flag.";
        }

        leaf diagnosis-level {
          type string;
          description
            "A fault diagnosis center stage result's diagnosis level.";
        }

        leaf diagnosis-code {
          type uint32;
          description
            "A fault diagnosis center stage result's diagnosis code.";
        }

        leaf diagnosis-description {
          type string;
          description
            "A fault diagnosis center stage result's diagnosis description.";
        }

        leaf diagnosis-information {
          type string;
          description
            "A fault diagnosis center stage result's diagnosis information.";
        }
      }
    }

    ntos-ext:nc-cli-stdout;
    ntos-ext:nc-cli-hidden;
  }

  rpc fdc-add-type-net-diag {
    ntos-ext:nc-cli-cmd "fdc add type net-diag";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Create a fault diagnosis center's task: net diagnosis.";

    input {
      leaf in-interface {
        type string;
        description
          "Specify source interface name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name'] |
           /ntos:state/ntos:vrf[ntos:name='main'][not(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name']";
      }
      leaf proto {
        type enumeration {
          enum "ip" {
            description
              "IP protocol.";
          }
          enum "ipv6" {
            description
              "IPv6 protocol.";
          }
          enum "icmp" {
            description
              "ICMP protocol.";
          }
          enum "ipv6-icmp" {
            description
              "IPv6-ICMP protocol.";
          }
          enum "tcp" {
            description
              "TCP protocol.";
          }
          enum "udp" {
            description
              "UDP protocol.";
          }
          enum "gre" {
            description
              "GRE protocol.";
          }
        }
        mandatory true;
        description
          "Protocol type.";
      }
      leaf src-mac {
        type string {
          pattern '[0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){5}';
        }
        description
          "An IEEE 802 MAC address.";
      }
      leaf dst-mac {
        type string {
          pattern '[0-9a-fA-F]{2}(:[0-9a-fA-F]{2}){5}';
        }
        description
          "An IEEE 802 MAC address.";
      }
      leaf src-ipaddr {
        type union {
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<A.B.C.D>";
          }
          type ntos-inet:ipv6-address {
            ntos-ext:nc-cli-shortdesc "<X:X::X:X>";
          }
        }
        mandatory true;
        description
          "An IPv4 or IPv6 address.";
      }
      leaf dst-ipaddr {
        type union {
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<A.B.C.D>";
          }
          type ntos-inet:ipv6-address {
            ntos-ext:nc-cli-shortdesc "<X:X::X:X>";
          }
        }
        description
          "An IPv4 or IPv6 address.";
      }
      leaf src-port {
        type uint16;
        description
          "A 16-bit port number used by a transport protocol such as TCP or UDP.";
      }
      leaf dst-port {
        type uint16;
        description
          "A 16-bit port number used by a transport protocol such as TCP or UDP.";
      }
    }

    output {
      leaf id {
        type uint32;
        description
          "A fault diagnosis center task's id.";
      }
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  rpc fdc-add-type-dst-nat-diag {
    ntos-ext:nc-cli-cmd "fdc add type dst-nat-diag";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Create a fault diagnosis center's task: dst nat diagnosis.";

    input {
      leaf in-interface {
        type string;
        description
          "Specify source interface name.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf[ntos:name=string(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name'] |
           /ntos:state/ntos:vrf[ntos:name='main'][not(current()/ntos-system:vrf)]/ntos-interface:interface/*/*[local-name()='name']";
      }
      leaf proto {
        type enumeration {
          enum "tcp" {
            description
              "TCP protocol.";
          }
          enum "udp" {
            description
              "UDP protocol.";
          }
        }
        description
          "Protocol type.";
      }
      leaf src-ipaddr {
        type union {
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<A.B.C.D>";
          }
        }
        description
          "An IPv4 address.";
      }
      leaf is-ipaddr {
        type union {
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<A.B.C.D>";
          }
        }
        mandatory true;
        description
          "An IPv4 address.";
      }
      leaf pm-ipaddr {
        type union {
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<A.B.C.D>";
          }
        }
        mandatory true;
        description
          "An IPv4 address.";
      }
      leaf is-port {
        type uint16;
        mandatory true;
        description
          "A 16-bit port number used by a transport protocol such as TCP or UDP.";
      }
      leaf pm-port {
        type uint16;
        mandatory true;
        description
          "A 16-bit port number used by a transport protocol such as TCP or UDP.";
      }
    }

    output {
      leaf id {
        type uint32;
        description
          "A fault diagnosis center task's id.";
      }
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  rpc fdc-add-type-device-health {
    ntos-ext:nc-cli-cmd "fdc add type device-health";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Create a fault diagnosis center's task: device health diagnosis.";

    output {
      leaf id {
        type uint32;
        description
          "A fault diagnosis center task's id.";
      }
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  rpc fdc-add-type-sslvpn-diag {
    ntos-ext:nc-cli-cmd "fdc add type sslvpn-diag";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Create a fault diagnosis center's task: sslvpn diagnosis.";

    input {
      leaf vrf {
        type ntos:vrf-name;
        mandatory true;
        description
          "The name of vrf which will diagnoise sslvpn.";
        ntos-ext:nc-cli-completion-xpath
          "/ntos:state/ntos:vrf/ntos:name";
      }

      leaf gateway {
        type ntos-types:ntos-obj-name-type;
        mandatory true;
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-sslvpn:sslvpn/ntos-sslvpn:gateway/ntos-sslvpn:name";
        description
          "The gateway name which will diagnoise sslvpn.";
      }

      leaf username {
        type ntos-types:ntos-obj-name-type;
        mandatory true;
        description "User to diagnose.";
      }

      container diag_type {
        description
          "The sslvpn diag type, must be finally configured";
        choice diagtype {
          description
            "Sslvpn diagnosis subtype, include two types: login and access-resource.";
          case login {
            container login {
              description
                "Diag type: login type params.";

              leaf login_addr {
                type https-url;
                mandatory true;
                description "diag_type login addr. example:https://***********:8443";
              }
            }
          }
          case access-resource {
            container access-resource {
              description
                "Diag type: access-resource type params.";

              leaf resource_addr {
                mandatory true;
                type ntos-inet:ipv4-address;
                description "diag_type access-resource addr";
              }

              leaf resource_port {
                type uint16;
                description
                  "A 16-bit port number used by a transport protocol such as TCP or UDP.";
              }
              leaf resource_proto {
                type enumeration {
                  enum "ip" {
                    description
                      "IP protocol.";
                  }
                  enum "icmp" {
                    description
                      "ICMP protocol.";
                  }
                  enum "tcp" {
                    description
                      "TCP protocol.";
                  }
                  enum "udp" {
                    description
                      "UDP protocol.";
                  }
                }
                description
                  "The communication proto of iptunnel resource.";
              }
            }
          }
        }
      }
    }

    output {
      leaf id {
        type uint32;
        description
          "A fault diagnosis center task's id.";
      }
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  rpc fdc-add-type-ipsec-diag {
    ntos-ext:nc-cli-cmd "fdc add type ipsec-diag";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Create a fault diagnosis center's task: ipsec diagnosis.";
    input {
      leaf profile {
        if-feature ipsec;
        type string {
          length "1..64";
          pattern "[^`~!#$%^&*+/|{};:\"',\\\\<>?]*" {
            error-message 'cannot include character: `~!#%^&*+\|{};:",/<>?';
          }
        }
        ntos-ext:nc-cli-completion-xpath
          "/ntos:config/ntos:vrf/ntos-ipsec:ipsec/*[local-name()='profile']/*[local-name()='name']";
        mandatory true;
        description
          "Please input the ipsec profile name.";
      }

      leaf time {
        if-feature ipsec;
        type uint32 {
          range "30..300";
        }
        mandatory true;
        description
          "Please input the diagnosis time.";
      }

      leaf diag-type {
        if-feature ipsec;
        type enumeration {
          enum negotiate;
          enum forward;
        }
        mandatory true;
        description
          "Please input the ipsec diag type.";
      }

      leaf peer-address {
        if-feature ipsec;
        type union {
          type ntos-inet:ipv4-address;
          type ntos-inet:ipv6-address;
          type ntos-inet:domain-name;
        }
        description
          "Please input the tunnel peer-address.";
      }

      leaf proto {
        if-feature ipsec;
        type enumeration {
          enum "ip" {
            description
              "IP protocol.";
          }
          enum "ipv6" {
            description
              "IPv6 protocol.";
          }
          enum "icmp" {
            description
              "ICMP protocol.";
          }
          enum "ipv6-icmp" {
            description
              "IPv6-ICMP protocol.";
          }
          enum "tcp" {
            description
              "TCP protocol.";
          }
          enum "udp" {
            description
              "UDP protocol.";
          }
          enum "gre" {
            description
              "GRE protocol.";
          }
        }
        description
          "Please input the protocol type.";
      }

      leaf src-ipaddr {
        if-feature ipsec;
        type union {
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<A.B.C.D>";
          }
          type ntos-inet:ipv6-address {
            ntos-ext:nc-cli-shortdesc "<X:X::X:X>";
          }
        }
        description
          "Please input an IPv4 or IPv6 address.";
      }

      leaf dst-ipaddr {
        if-feature ipsec;
        type union {
          type ntos-inet:ipv4-address {
            ntos-ext:nc-cli-shortdesc "<A.B.C.D>";
          }
          type ntos-inet:ipv6-address {
            ntos-ext:nc-cli-shortdesc "<X:X::X:X>";
          }
        }
        description
          "Please input an IPv4 or IPv6 address.";
      }

      leaf src-port {
        if-feature ipsec;
        type uint16;
        description
          "Please input a 16-bit port number used by a transport protocol such as TCP or UDP.";
      }

      leaf dst-port {
        if-feature ipsec;
        type uint16;
        description
          "Please input a 16-bit port number used by a transport protocol such as TCP or UDP.";
      }

      leaf vti {
        if-feature ipsec;
        type string;
        description
          "Please input a vti name.";
      }
    }

    output {
      leaf id {
        type uint32;
        description
          "A fault diagnosis center task's id.";
      }
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  rpc fdc-add-type-name {
    ntos-ext:nc-cli-cmd "fdc add type";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Create a fault diagnosis center's task diagnosis with name.";
    input {
      leaf name {
        type string;
        mandatory true;
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-order "1";
        description
          "A task diagnosis's name.";
      }

      leaf params {
        type string {
            pattern '(([0-9a-zA-Z_]+:[0-9a-zA-Z_\.]+),){0,15}([0-9a-zA-Z_]+:[0-9a-zA-Z_\.]+)';
        }
        description
          "A task diagnosis's params, format: key1:value1,key2:value2.";
      }
    }

    output {
      leaf id {
        type uint32;
        description
          "A fault diagnosis center task's id.";
      }
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  rpc fdc-delete {
    ntos-ext:nc-cli-cmd "fdc delete";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Delete a fault diagnosis center's task.";
    input {
      leaf id {
        type uint32;
        mandatory true;
        description
          "A fault diagnosis center task's id.";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-info;
    }
  }

  rpc fdc-start {
    ntos-ext:nc-cli-cmd "fdc start";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Start a fault diagnosis center's task.";
    input {
      leaf id {
        type uint32;
        mandatory true;
        description
          "A fault diagnosis center task's id.";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-info;
    }
  }

  rpc fdc-restart {
    ntos-ext:nc-cli-cmd "fdc restart";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Restart a fault diagnosis center's task.";
    input {
      leaf id {
        type uint32;
        mandatory true;
        description
          "A fault diagnosis center task's id.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-order "1";
      }

      leaf nextstage {
        type empty;
        description
          "A fault diagnosis center task's next stage.";
      }
    }

    output {
      uses cmd-output-info;
    }
  }

  rpc fdc-status {
    ntos-ext:nc-cli-cmd "fdc status";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Get a fault diagnosis center's task status.";
    input {
      leaf id {
        type uint32;
        mandatory true;
        description
          "A fault diagnosis center task's id.";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-info;
    }
  }

  rpc fdc-result {
    ntos-ext:nc-cli-cmd "fdc result";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Get a fault diagnosis center's task rusult.";
    input {
      leaf id {
        type uint32;
        mandatory true;
        description
          "A fault diagnosis center task's id.";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      uses cmd-output-info;

      leaf data-file {
        type string;
        description
          "A fault diagnosis center task's data file name.";
      }
    }
  }

  rpc fdc-detail {
    ntos-ext:nc-cli-cmd "fdc detail";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Get a fault diagnosis center's task detail result.";
    input {
      leaf id {
        type uint32;
        mandatory true;
        description
          "A fault diagnosis center task's id.";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      leaf buffer {
        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
        type string;
        description
          "A fault diagnosis center's task detail result information.";
      }
    }
  }

  rpc fdc-data {
    ntos-ext:nc-cli-cmd "fdc data";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Get a fault diagnosis center's task all result data.";
    input {
      leaf id {
        type uint32;
        mandatory true;
        description
          "A fault diagnosis center task's id.";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      leaf file {
        type string;
        description
          "A fault diagnosis center task's data file information.";
      }
      ntos-ext:nc-cli-stdout;
      ntos-ext:nc-cli-hidden;
    }
  }

  rpc fdc-stop {
    ntos-ext:nc-cli-cmd "fdc stop";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Stop a fault diagnosis center's task.";
    input {
      leaf id {
        type uint32;
        mandatory true;
        description
          "A fault diagnosis center task's id.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-order "1";
      }

      leaf current {
        type empty;
        description
          "Stop a fault diagnosis center task in current stage.";
      }

      leaf result {
        type fdc-diag-result-type;
        description
          "Stop a fault diagnosis center task with the result.";
      }
    }

    output {
      uses cmd-output-info;
    }
  }

  rpc fdc-ignore {
    ntos-ext:nc-cli-cmd "fdc ignore";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Ignore a fault diagnosis center's stage subitem.";
    input {
      leaf id {
        type uint32;
        mandatory true;
        description
          "A fault diagnosis center task's id.";
        ntos-ext:nc-cli-no-name;
        ntos-ext:nc-cli-order "1";
      }

      leaf subitem {
        type uint32;
        mandatory true;
        description
          "A fault diagnosis center task's stage subitem.";
      }
    }

    output {
      uses cmd-output-info;
    }
  }

  rpc fdc-history-query {
    ntos-ext:nc-cli-cmd "fdc history query";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Get a fault diagnosis center's task history information.";
    input {
      leaf type {
          type union {
            type fdc-diag-type;
            type string;
          }
          mandatory true;
          description
            "A fault diagnosis center task's type.";
          ntos-ext:nc-cli-order "1";
      }

      leaf number {
        type uint32;
        description
          "The number of a fault diagnosis center task's lastest history information.";
        ntos-ext:nc-cli-no-name;
      }

      leaf detail {
        type empty;
        description
          "A fault diagnosis center's task detail history information.";
      }
    }

    output {
      list info {
        key "id";
        uses cmd-output-info;

        leaf data-file {
          type string;
          description
            "A fault diagnosis center task's data file name.";
        }

        ntos-ext:nc-cli-stdout;
        ntos-ext:nc-cli-hidden;
      }
    }
  }

  rpc fdc-history-delete-result {
    ntos-ext:nc-cli-cmd "fdc history delete result";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Delete a fault diagnosis center task's results.";
    input {
      leaf-list id {
        type uint32;
        description
          "A fault diagnosis center task's result ids.";
        ntos-ext:nc-cli-no-name;
      }
    }

    output {
      leaf-list id {
        type uint32;
        description
          "A fault diagnosis center task's result ids.";
      }
    }
  }


  rpc fdc-cloud-notify {
    ntos-ext:nc-cli-cmd "fdc cloud notify";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Notify the cloud of the log file of a packet.";
    input {
      leaf id {
          type uint32;
          description
            "A fault diagnosis center packet's number.";
      }

      leaf type {
          type string;
          description
            "A fault diagnosis center task's type.";
      }
    }

    output {
      leaf result {
          type uint32;
          description
            "The result of notify cloud.";
      }
    }
  }


  rpc fdc-usage-to-cloud {
    ntos-ext:nc-cli-cmd "fdc usage to cloud";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "Usage rate of diagnostic center function statistics.";
    input {
      leaf id {
          type uint16;
          description
            "Task id.";
      }

      leaf usage {
          type uint16;
          description
            "Use this diagnostic function.";
      }

      leaf auto-config {
          type uint16;
          description
            "Use the auto-configuration function.";
      }

      leaf type {
          type string;
          description
            "A fault diagnosis center task's type.";
      }
    }

    output {
      leaf result {
          type uint32;
          description
            "The result of notify cloud.";
      }
    }
  }


  rpc fdc-list {
    ntos-ext:nc-cli-cmd "fdc list";
    ntos-ext:nc-cli-command-no-pager;
    ntos-api:internal;
    description
      "List all fault diagnosis center task.";
    input {
      leaf type {
          type union {
            type fdc-diag-type;
            type string;
          }
          description
            "A fault diagnosis center task's type.";
      }
    }

    output {
      list task {
        key "id";
        uses cmd-output-info;
      }
    }
  }
}

