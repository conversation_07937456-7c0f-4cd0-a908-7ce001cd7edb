#!/usr/bin/env python3
"""
twice-nat44性能对比报告生成工具

本工具用于生成twice-nat44功能改进前后的性能对比报告，包括：
1. 使用率对比
2. 评估效果对比
3. IP池支持改进效果
4. 性能趋势分析

作者: FortiGate转换系统
版本: 1.0
日期: 2025-08-08
"""

import sys
import os
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine.monitoring.twice_nat44_metrics import performance_analyzer, TwiceNat44Metrics

def generate_comparison_report():
    """生成改进前后对比报告"""
    print("🚀 生成twice-nat44性能对比报告\n")
    
    # 获取性能报告
    report = performance_analyzer.generate_performance_report(days=30)
    
    print("📊 twice-nat44功能性能报告")
    print("=" * 50)
    print(f"📅 统计周期: {report['period']}")
    print(f"🔢 总会话数: {report['summary']['total_sessions']}")
    
    if report['summary']['total_sessions'] == 0:
        print("⚠️  暂无转换数据，请先运行转换测试")
        return
    
    print(f"\n📈 总体统计:")
    summary = report['summary']
    print(f"  • 总策略数: {summary['total_policies']}")
    print(f"  • 推荐使用twice-nat44: {summary['total_recommended']}")
    print(f"  • 生成twice-nat44规则: {summary['total_generated_rules']}")
    print(f"  • 总体成功率: {summary['overall_success_rate']:.1%}")
    print(f"  • 平均评分: {summary['average_score']}")
    print(f"  • 平均转换时间: {summary['average_conversion_time']:.2f}秒")
    
    print(f"\n🎯 IP池支持改进效果:")
    ippool = report['ippool_analysis']
    print(f"  • IP池策略总数: {ippool['total_ippool_policies']}")
    print(f"  • IP池策略推荐数: {ippool['ippool_recommended']}")
    print(f"  • IP池覆盖率: {ippool['ippool_coverage_rate']:.1%}")
    print(f"  • 改进效果: {ippool['improvement']}")
    
    print(f"\n📊 性能趋势:")
    trends = report['performance_trends']
    print(f"  • 近期平均评分: {trends['recent_average_score']}")
    print(f"  • 近期成功率: {trends['recent_success_rate']:.1%}")
    print(f"  • 评分趋势: {trends['score_trend']}")
    print(f"  • 成功率趋势: {trends['success_rate_trend']}")
    
    print(f"\n⚙️  阈值使用分析:")
    for threshold, data in report['threshold_analysis'].items():
        print(f"  • 阈值{threshold}: 使用{data['count']}次, 成功率{data['success_rate']:.1%}")
    
    print(f"\n💡 优化建议:")
    for i, rec in enumerate(report['recommendations'], 1):
        print(f"  {i}. {rec}")
    
    # 生成详细的JSON报告
    output_file = Path("output/reports/twice_nat44_performance_report.json")
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    detailed_report = {
        "generated_at": datetime.now().isoformat(),
        "report_type": "twice_nat44_performance_comparison",
        "version": "1.0",
        "data": report
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(detailed_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: {output_file}")

def simulate_test_data():
    """模拟测试数据以演示报告功能"""
    print("🧪 生成模拟测试数据...")
    
    from engine.monitoring.twice_nat44_metrics import metrics_collector, PolicyEvaluationResult
    
    # 模拟改进前的数据（低覆盖率）
    metrics_collector.start_conversion_session(threshold=80)
    
    # 模拟一些策略评估结果
    test_evaluations = [
        PolicyEvaluationResult(
            policy_name="policy_1_no_ippool",
            total_score=85,
            recommended=True,
            confidence=0.9,
            has_ippool=False,
            pool_format="none",
            vip_count=1,
            service_count=2,
            evaluation_time=0.1
        ),
        PolicyEvaluationResult(
            policy_name="policy_2_ippool_old",
            total_score=45,  # 改进前IP池策略评分低
            recommended=False,
            confidence=0.3,
            has_ippool=True,
            pool_format="ip_address",
            vip_count=1,
            service_count=3,
            evaluation_time=0.15
        ),
        PolicyEvaluationResult(
            policy_name="policy_3_traditional",
            total_score=78,
            recommended=False,  # 旧阈值下不推荐
            confidence=0.7,
            has_ippool=True,
            pool_format="traditional",
            vip_count=2,
            service_count=2,
            evaluation_time=0.12
        )
    ]
    
    for eval_result in test_evaluations:
        metrics_collector.record_policy_evaluation(eval_result)
    
    metrics_collector.end_conversion_session()
    
    # 模拟改进后的数据（高覆盖率）
    metrics_collector.start_conversion_session(threshold=65)
    
    improved_evaluations = [
        PolicyEvaluationResult(
            policy_name="policy_1_no_ippool_improved",
            total_score=87,
            recommended=True,
            confidence=0.95,
            has_ippool=False,
            pool_format="none",
            vip_count=1,
            service_count=2,
            evaluation_time=0.08
        ),
        PolicyEvaluationResult(
            policy_name="policy_2_ippool_improved",
            total_score=92,  # 改进后IP池策略评分高
            recommended=True,
            confidence=0.9,
            has_ippool=True,
            pool_format="ip_address",
            vip_count=1,
            service_count=3,
            evaluation_time=0.1
        ),
        PolicyEvaluationResult(
            policy_name="policy_3_traditional_improved",
            total_score=78,
            recommended=True,  # 新阈值下推荐
            confidence=0.8,
            has_ippool=True,
            pool_format="traditional",
            vip_count=2,
            service_count=2,
            evaluation_time=0.09
        ),
        PolicyEvaluationResult(
            policy_name="policy_4_mixed_format",
            total_score=85,
            recommended=True,
            confidence=0.85,
            has_ippool=True,
            pool_format="mixed",
            vip_count=1,
            service_count=4,
            evaluation_time=0.11
        )
    ]
    
    for eval_result in improved_evaluations:
        metrics_collector.record_policy_evaluation(eval_result)
        # 模拟生成的twice-nat44规则
        if eval_result.recommended:
            metrics_collector.record_generated_rule("twice-nat44", eval_result.policy_name)
    
    metrics_collector.end_conversion_session()
    
    print("✅ 模拟数据生成完成")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--simulate":
        simulate_test_data()
    
    generate_comparison_report()

if __name__ == "__main__":
    main()
