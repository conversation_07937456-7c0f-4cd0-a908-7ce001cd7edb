package response

import (
	"irisAdminApi/application/libs/i18n"
	"strings"

	"github.com/kataras/iris/v12"
)

// I18nSuccess 返回成功的国际化响应
func I18nSuccess(ctx iris.Context, data interface{}) {
	ctx.JSON(NewResponseI18n(ctx, NoErr.Code, data, "common.success"))
}

// I18nError 返回错误的国际化响应
func I18nError(ctx iris.Context, msgID string, args ...interface{}) {
	ctx.JSON(NewResponseI18n(ctx, SystemErr.Code, nil, msgID, args...))
}

// I18nErrorWithCode 返回指定错误码的国际化响应
func I18nErrorWithCode(ctx iris.Context, code int64, msgID string, args ...interface{}) {
	ctx.JSON(NewResponseI18n(ctx, code, nil, msgID, args...))
}

// I18nResponse 返回自定义数据和消息ID的国际化响应
func I18nResponse(ctx iris.Context, code int64, data interface{}, msgID string, args ...interface{}) {
	ctx.JSON(NewResponseI18n(ctx, code, data, msgID, args...))
}

// NormalizeLanguageCode 标准化语言代码格式，确保使用连字符格式
// 例如：zh_CN -> zh-CN, en_US -> en-US
func NormalizeLanguageCode(lang string) string {
	if lang == "" {
		return i18n.GetDefaultLang()
	}

	// 将下划线格式转换为连字符格式
	lang = strings.Replace(lang, "_", "-", -1)

	// 确保语言代码格式正确
	switch strings.ToLower(lang) {
	case "zh", "zh-cn", "chinese":
		return "zh-CN"
	case "en", "en-us", "english":
		return "en-US"
	default:
		// 如果是不支持的语言，返回默认语言
		if !i18n.IsSupportedLang(lang) {
			return i18n.GetDefaultLang()
		}
		return lang
	}
}

// GetLanguage 从上下文获取当前语言设置
func GetLanguage(ctx iris.Context) string {
	lang := ctx.Values().GetString("language")
	return NormalizeLanguageCode(lang)
}

// GetAcceptLanguage 从请求头获取Accept-Language
func GetAcceptLanguage(ctx iris.Context) string {
	acceptLang := ctx.GetHeader("Accept-Language")
	if acceptLang == "" {
		return i18n.GetDefaultLang()
	}

	// 使用i18n库的匹配函数获取最匹配的支持语言
	return i18n.GetMatchingLang(acceptLang)
}

// SetLanguage 设置当前请求的语言
func SetLanguage(ctx iris.Context, lang string) {
	normalizedLang := NormalizeLanguageCode(lang)
	ctx.Values().Set("language", normalizedLang)
}
