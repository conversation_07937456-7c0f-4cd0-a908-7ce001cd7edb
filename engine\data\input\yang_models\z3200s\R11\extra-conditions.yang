module extra-conditions {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:extra-conditions";
  prefix extra-conditions;

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel:4008-111-000
    E-mail:<EMAIL>";
  description
    "RUIJIE YANG extra conditions.";

  revision 2019-04-01 {
    description
      "Initial revision";
    reference "";
  }

  extension unique-values {
    argument xpath;
    description
      "This extension acts as 'must' condition that makes sure that
       there are no duplicate values in the nodes matched by the xpath
       argument.

       The argument of this extension must contain a valid xpath
       expression that represents which leaf node values will be checked
       for unicity.

       This extension may have an optional error-message substatement
       which will be displayed when a duplicate value is detected.";
  }

  extension unique-tuple {
    argument leaf-names;
    description
      "This extension acts as 'unique' constraint which takes unset
       leafs into account.

       The default 'unique' YANG statement explicitly states in RFC 7950
       Section *******.:

         List entries which do not have a value for all referenced leafs
         are not taken into account when the 'unique' constraint is
         enforced.

       This extension may have an optional error-message substatement
       which will be displayed when a list item that violates the unique
       constraint is detected.";
  }
}
