"""
FortiGate twice-nat44智能判断服务

本模块实现了基于深度分析报告的智能判断机制，用于自动评估FortiGate策略
是否适合使用twice-nat44转换。

主要功能：
- 多维度评分算法
- 置信度计算
- 详细的推荐理由分析
- 风险评估和警告
- 配置化的判断标准

版本: 1.0
作者: FortiGate转换系统
创建时间: 2025-08-06
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from engine.business.models.twice_nat44_models import TwiceNat44Recommendation
from engine.utils.logger import log
from engine.utils.i18n import _


@dataclass
class EvaluationCriteria:
    """
    评估标准配置
    
    定义了twice-nat44适用性评估的各项标准和权重。
    """
    vip_count_weight: int = 30
    ippool_usage_weight: int = 25
    vip_completeness_weight: int = 20
    version_support_weight: int = 15
    service_complexity_weight: int = 10
    
    # 阈值配置
    recommendation_threshold: int = 80
    high_confidence_threshold: int = 90
    low_confidence_threshold: int = 60
    
    # VIP数量评分标准
    optimal_vip_count: int = 1
    acceptable_vip_count: int = 3
    max_recommended_vip_count: int = 5
    
    # 服务复杂度评分标准
    simple_service_count: int = 2
    moderate_service_count: int = 5
    
    # 支持的最低版本
    min_supported_version: str = "R11"
    
    def validate(self) -> bool:
        """验证评估标准配置的有效性"""
        total_weight = (self.vip_count_weight + self.ippool_usage_weight + 
                       self.vip_completeness_weight + self.version_support_weight + 
                       self.service_complexity_weight)
        return total_weight == 100


class TwiceNat44Evaluator:
    """
    twice-nat44智能判断服务
    
    基于多维度评分算法，智能判断FortiGate策略是否适合使用twice-nat44转换。
    """
    
    def __init__(self, criteria: Optional[EvaluationCriteria] = None):
        """
        初始化评估器
        
        Args:
            criteria: 评估标准配置，如果不提供则使用默认配置
        """
        self.criteria = criteria or EvaluationCriteria()
        if not self.criteria.validate():
            raise ValueError("Invalid evaluation criteria: weights must sum to 100")
        
        log(_("twice_nat44_evaluator.initialized"), "info")

    @property
    def _optimized_evaluate(self):
        """获取优化后的评估方法"""
        if not hasattr(self, '_cached_evaluate_func'):
            from engine.business.services.twice_nat44_optimizer import cached_evaluation, performance_monitor

            # 应用性能优化装饰器
            @cached_evaluation
            @performance_monitor("twice_nat44_evaluation")
            def optimized_evaluate(policy, vip_configs, context=None):
                return self._do_evaluate(policy, vip_configs, context)

            self._cached_evaluate_func = optimized_evaluate

        return self._cached_evaluate_func
    
    def evaluate(
        self,
        policy: Dict[str, Any],
        vip_configs: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> TwiceNat44Recommendation:
        """
        评估策略是否适合使用twice-nat44（优化版本）

        Args:
            policy: FortiGate策略配置
            vip_configs: VIP配置字典
            context: 上下文信息

        Returns:
            TwiceNat44Recommendation: 详细的评估结果

        Note:
            此方法使用缓存和性能监控优化，提供更好的性能。
        """
        return self._optimized_evaluate(policy, vip_configs, context)

    def _do_evaluate(
        self,
        policy: Dict[str, Any],
        vip_configs: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> TwiceNat44Recommendation:
        """
        执行实际的评估逻辑（内部方法）

        Args:
            policy: FortiGate策略配置
            vip_configs: VIP配置字典
            context: 上下文信息

        Returns:
            TwiceNat44Recommendation: 详细的评估结果
        """
        context = context or {}
        policy_name = policy.get("name", "unknown")

        log(_("twice_nat44_evaluator.evaluation_started", policy=policy_name), "debug")

        try:
            # 基本条件检查
            basic_check_result = self._check_basic_conditions(policy, vip_configs)
            if not basic_check_result[0]:
                return self._create_negative_recommendation(basic_check_result[1])

            # 多维度评分
            evaluation_results = self._perform_multi_dimensional_evaluation(
                policy, vip_configs, context
            )

            # 生成最终建议
            recommendation = self._generate_recommendation(evaluation_results, policy_name)

            log(_("twice_nat44_evaluator.evaluation_completed",
                 policy=policy_name,
                 score=recommendation.total_score,
                 should_use=recommendation.should_use), "debug")

            return recommendation

        except Exception as e:
            log(_("twice_nat44_evaluator.evaluation_failed",
                 policy=policy_name,
                 error=str(e)), "error")
            return self._create_error_recommendation(str(e))
    
    def _check_basic_conditions(
        self, 
        policy: Dict[str, Any], 
        vip_configs: Dict[str, Any]
    ) -> Tuple[bool, Optional[str]]:
        """
        检查基本条件
        
        Args:
            policy: FortiGate策略配置
            vip_configs: VIP配置字典
            
        Returns:
            Tuple[bool, Optional[str]]: (是否满足基本条件, 失败原因)
        """
        # 检查是否有VIP对象
        dstaddr_list = policy.get("dstaddr", [])
        if isinstance(dstaddr_list, str):
            dstaddr_list = [dstaddr_list]
        
        has_vip = any(addr in vip_configs for addr in dstaddr_list)
        if not has_vip:
            return False, "策略目标地址中不包含VIP对象"
        
        # 检查是否启用NAT
        nat_enabled = policy.get("nat", "disable") == "enable"
        if not nat_enabled:
            return False, "策略未启用NAT功能"
        
        # 检查VIP配置的基本完整性
        valid_vips = []
        for addr in dstaddr_list:
            if addr in vip_configs:
                vip = vip_configs[addr]
                if "extip" in vip and "mappedip" in vip:
                    valid_vips.append(addr)
        
        if not valid_vips:
            return False, "没有找到配置完整的VIP对象"
        
        return True, None
    
    def _perform_multi_dimensional_evaluation(
        self, 
        policy: Dict[str, Any], 
        vip_configs: Dict[str, Any], 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        执行多维度评估
        
        Args:
            policy: FortiGate策略配置
            vip_configs: VIP配置字典
            context: 上下文信息
            
        Returns:
            Dict[str, Any]: 评估结果详情
        """
        results = {
            "total_score": 0,
            "dimension_scores": {},
            "reasons": [],
            "warnings": []
        }
        
        # 维度1: VIP数量评估
        vip_score, vip_reasons, vip_warnings = self._evaluate_vip_count(policy, vip_configs)
        results["total_score"] += vip_score
        results["dimension_scores"]["vip_count"] = vip_score
        results["reasons"].extend(vip_reasons)
        results["warnings"].extend(vip_warnings)
        
        # 维度2: IP池使用评估
        ippool_score, ippool_reasons, ippool_warnings = self._evaluate_ippool_usage(policy)
        results["total_score"] += ippool_score
        results["dimension_scores"]["ippool_usage"] = ippool_score
        results["reasons"].extend(ippool_reasons)
        results["warnings"].extend(ippool_warnings)
        
        # 维度3: VIP配置完整性评估
        completeness_score, completeness_reasons, completeness_warnings = self._evaluate_vip_completeness(
            policy, vip_configs
        )
        results["total_score"] += completeness_score
        results["dimension_scores"]["vip_completeness"] = completeness_score
        results["reasons"].extend(completeness_reasons)
        results["warnings"].extend(completeness_warnings)
        
        # 维度4: 版本支持评估
        version_score, version_reasons, version_warnings = self._evaluate_version_support(context)
        results["total_score"] += version_score
        results["dimension_scores"]["version_support"] = version_score
        results["reasons"].extend(version_reasons)
        results["warnings"].extend(version_warnings)
        
        # 维度5: 服务复杂度评估
        service_score, service_reasons, service_warnings = self._evaluate_service_complexity(policy)
        results["total_score"] += service_score
        results["dimension_scores"]["service_complexity"] = service_score
        results["reasons"].extend(service_reasons)
        results["warnings"].extend(service_warnings)
        
        return results
    
    def _evaluate_vip_count(
        self, 
        policy: Dict[str, Any], 
        vip_configs: Dict[str, Any]
    ) -> Tuple[int, List[str], List[str]]:
        """评估VIP数量维度"""
        dstaddr_list = policy.get("dstaddr", [])
        if isinstance(dstaddr_list, str):
            dstaddr_list = [dstaddr_list]
        
        vip_count = len([addr for addr in dstaddr_list if addr in vip_configs])
        reasons = []
        warnings = []
        
        if vip_count == self.criteria.optimal_vip_count:
            score = self.criteria.vip_count_weight
            reasons.append(f"单一VIP配置，最适合twice-nat44 (+{score}分)")
        elif vip_count <= self.criteria.acceptable_vip_count:
            score = self.criteria.vip_count_weight // 2
            reasons.append(f"VIP数量适中({vip_count}个)，适合twice-nat44 (+{score}分)")
        elif vip_count <= self.criteria.max_recommended_vip_count:
            score = self.criteria.vip_count_weight // 4
            reasons.append(f"VIP数量较多({vip_count}个)，可以使用twice-nat44 (+{score}分)")
            warnings.append(f"VIP数量较多({vip_count}个)，可能增加配置复杂度")
        else:
            score = 0
            warnings.append(f"VIP数量过多({vip_count}个)，不推荐使用twice-nat44")
        
        return score, reasons, warnings

    def _evaluate_ippool_usage(self, policy: Dict[str, Any]) -> Tuple[int, List[str], List[str]]:
        """评估IP池使用维度"""
        reasons = []
        warnings = []

        ippool_enabled = policy.get("ippool", "disable") == "enable"

        if not ippool_enabled:
            score = self.criteria.ippool_usage_weight
            reasons.append(f"不使用IP池，符合twice-nat44最佳实践 (+{score}分)")
        else:
            score = 0
            warnings.append("使用IP池，当前twice-nat44实现不完全支持")

        return score, reasons, warnings

    def _evaluate_vip_completeness(
        self,
        policy: Dict[str, Any],
        vip_configs: Dict[str, Any]
    ) -> Tuple[int, List[str], List[str]]:
        """评估VIP配置完整性维度"""
        dstaddr_list = policy.get("dstaddr", [])
        if isinstance(dstaddr_list, str):
            dstaddr_list = [dstaddr_list]

        total_vips = len([addr for addr in dstaddr_list if addr in vip_configs])
        complete_vips = 0
        incomplete_vips = []

        for addr in dstaddr_list:
            if addr in vip_configs:
                vip = vip_configs[addr]
                required_fields = ["extip", "mappedip"]
                if all(field in vip and vip[field] for field in required_fields):
                    complete_vips += 1
                else:
                    incomplete_vips.append(addr)

        reasons = []
        warnings = []

        if complete_vips == total_vips and total_vips > 0:
            score = self.criteria.vip_completeness_weight
            reasons.append(f"所有VIP配置完整 (+{score}分)")
        elif complete_vips > 0:
            score = self.criteria.vip_completeness_weight // 2
            reasons.append(f"部分VIP配置完整({complete_vips}/{total_vips}) (+{score}分)")
            warnings.append(f"存在不完整的VIP配置: {', '.join(incomplete_vips)}")
        else:
            score = 0
            warnings.append("所有VIP配置都不完整")

        return score, reasons, warnings

    def _evaluate_version_support(self, context: Dict[str, Any]) -> Tuple[int, List[str], List[str]]:
        """评估版本支持维度"""
        reasons = []
        warnings = []

        ntos_version = context.get("ntos_version", "R11")

        if ntos_version >= self.criteria.min_supported_version:
            score = self.criteria.version_support_weight
            reasons.append(f"目标设备版本({ntos_version})支持twice-nat44 (+{score}分)")
        else:
            score = 0
            warnings.append(f"目标设备版本({ntos_version})可能不支持twice-nat44")

        return score, reasons, warnings

    def _evaluate_service_complexity(self, policy: Dict[str, Any]) -> Tuple[int, List[str], List[str]]:
        """评估服务复杂度维度"""
        service_list = policy.get("service", [])
        if isinstance(service_list, str):
            service_list = [service_list]

        service_count = len(service_list)
        reasons = []
        warnings = []

        if service_count <= self.criteria.simple_service_count:
            score = self.criteria.service_complexity_weight
            reasons.append(f"服务配置简单({service_count}个服务)，适合twice-nat44 (+{score}分)")
        elif service_count <= self.criteria.moderate_service_count:
            score = self.criteria.service_complexity_weight // 2
            reasons.append(f"服务配置适中({service_count}个服务)，可以使用twice-nat44 (+{score}分)")
        else:
            score = 0
            warnings.append(f"服务配置复杂({service_count}个服务)，可能不适合twice-nat44")

        return score, reasons, warnings

    def _generate_recommendation(
        self,
        evaluation_results: Dict[str, Any],
        policy_name: str
    ) -> TwiceNat44Recommendation:
        """生成最终推荐结果"""
        total_score = evaluation_results["total_score"]
        confidence_score = total_score / 100.0

        # 判断是否推荐使用
        should_use = total_score >= self.criteria.recommendation_threshold

        # 创建推荐对象
        recommendation = TwiceNat44Recommendation(
            should_use=should_use,
            confidence_score=confidence_score,
            total_score=total_score,
            max_score=100,
            reasons=evaluation_results["reasons"],
            warnings=evaluation_results["warnings"]
        )

        # 添加总结性原因
        if should_use:
            if total_score >= self.criteria.high_confidence_threshold:
                recommendation.add_reason(f"总评分{total_score}分，强烈推荐使用twice-nat44")
            else:
                recommendation.add_reason(f"总评分{total_score}分，推荐使用twice-nat44")
        else:
            if total_score >= self.criteria.low_confidence_threshold:
                recommendation.fallback_reason = f"总评分{total_score}分，建议谨慎考虑使用twice-nat44"
            else:
                recommendation.fallback_reason = f"总评分{total_score}分，建议使用传统NAT方案"

        return recommendation

    def _create_negative_recommendation(self, reason: str) -> TwiceNat44Recommendation:
        """创建否定推荐"""
        return TwiceNat44Recommendation(
            should_use=False,
            confidence_score=0.0,
            total_score=0,
            max_score=100,
            fallback_reason=reason
        )

    def _create_error_recommendation(self, error: str) -> TwiceNat44Recommendation:
        """创建错误推荐"""
        return TwiceNat44Recommendation(
            should_use=False,
            confidence_score=0.0,
            total_score=0,
            max_score=100,
            fallback_reason=f"评估过程出错: {error}"
        )
