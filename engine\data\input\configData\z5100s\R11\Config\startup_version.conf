[{"name": "root", "version": 50759, "ts": *************}, {"name": "physical", "version": 50759, "ts": *************}, {"name": "sub_interface", "version": 50759, "ts": *************}, {"name": "routing", "version": 50759, "ts": *************}, {"name": "dhcp", "version": 50759, "ts": *************}, {"name": "security_zone", "version": 50759, "ts": *************}, {"name": "auth", "version": 50759, "ts": *************}, {"name": "devicename", "version": 50759, "ts": *************}, {"name": "discovery", "version": 50759, "ts": *************}, {"name": "timezone", "version": 50759, "ts": *************}, {"name": "security-policy", "version": 50759, "ts": *************}, {"name": "network-obj", "version": 50759, "ts": *************}, {"name": "appid", "version": 50759, "ts": *************}, {"name": "service-obj", "version": 50759, "ts": *************}, {"name": "time-range", "version": 50759, "ts": *************}, {"name": "ips-config", "version": 50759, "ts": *************}, {"name": "anti-virus", "version": 50759, "ts": *************}, {"name": "url-filter", "version": 50759, "ts": *************}, {"name": "url-category", "version": 50759, "ts": *************}, {"name": "security-defend", "version": 50759, "ts": *************}, {"name": "nat", "version": 50759, "ts": *************}, {"name": "threat-intelligence", "version": 50759, "ts": *************}, {"name": "mac-block", "version": 50759, "ts": *************}, {"name": "user-experience", "version": 50759, "ts": *************}, {"name": "mllb", "version": 50759, "ts": *************}]