package firewallflextrans

import (
	"fmt"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"time"

	"gorm.io/gorm"
)

// SeedDeviceData 初始化设备型号和版本数据
func SeedDeviceData() error {
	db := easygorm.GetEasyGormDb()
	if db == nil {
		return nil
	}

	// 定义要插入的设备型号
	modelNames := []string{"RG-WALL 1600-Z5100-S", "RG-WALL 1600-Z3200-S"}
	modelShortNames := []string{"z5100s", "z3200s"}

	// 定义要插入的设备版本
	versionNames := []string{"NGFW_NTOS 1.0R11", "NGFW_NTOS 1.0R10P2"}
	versionShortNames := []string{"R11", "R10P2"}

	// 当前时间
	now := time.Now().Format("2006-01-02 15:04:05.999")

	// 使用事务确保数据一致性
	return db.Transaction(func(tx *gorm.DB) error {
		// 处理设备型号
		for i, name := range modelNames {
			// 检查此设备型号是否已存在
			var exists int64
			tx.Model(&DeviceModel{}).Where("name = ? OR short_name = ?", name, modelShortNames[i]).Count(&exists)
			if exists > 0 {
				logging.InfoLogger.Infof("设备型号 %s (%s) 已存在，跳过", name, modelShortNames[i])
				continue
			}

			// 插入不存在的设备型号
			sql := `INSERT INTO device_models 
				(created_at, updated_at, name, short_name, vendor, status, sort_order, app) VALUES
				('%s', '%s', '%s', '%s', 'fortigate', 1, 0, 'config-trans')`

			sql = fmt.Sprintf(sql, now, now, name, modelShortNames[i])

			if err := tx.Exec(sql).Error; err != nil {
				logging.ErrorLogger.Errorf("插入设备型号 %s 失败: %v", name, err)
				return err
			}
			logging.InfoLogger.Infof("插入设备型号 %s (%s) 成功", name, modelShortNames[i])
		}

		// 处理设备版本
		for i, name := range versionNames {
			// 检查此设备版本是否已存在
			var exists int64
			tx.Model(&DeviceVersion{}).Where("name = ? OR short_name = ?", name, versionShortNames[i]).Count(&exists)
			if exists > 0 {
				logging.InfoLogger.Infof("设备版本 %s (%s) 已存在，跳过", name, versionShortNames[i])
				continue
			}

			// 插入不存在的设备版本
			sql := `INSERT INTO device_versions 
				(created_at, updated_at, name, short_name, vendor, status, sort_order, app) VALUES
				('%s', '%s', '%s', '%s', 'fortigate', 1, 0, 'config-trans')`

			sql = fmt.Sprintf(sql, now, now, name, versionShortNames[i])

			if err := tx.Exec(sql).Error; err != nil {
				logging.ErrorLogger.Errorf("插入设备版本 %s 失败: %v", name, err)
				return err
			}
			logging.InfoLogger.Infof("插入设备版本 %s (%s) 成功", name, versionShortNames[i])
		}

		return nil
	})
}
