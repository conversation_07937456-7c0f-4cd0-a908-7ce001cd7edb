module ntos-collab-disposal {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:collab-disposal";
  prefix ntos-collab-disposal;

  import ntos {
    prefix ntos;
  }
  import ntos-commands {
    prefix ntos-cmd;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-if-types{
    prefix ntos-if;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS collaboration disposal module.";

  revision 2024-08-05 {
    description
      "Initial version.";
    reference "";
  }

  identity collab-disposal {
    base ntos-types:SERVICE_LOG_ID;
    description
      "Collaboration disposal service.";
  }

  grouping collab-disposal-config {
    leaf enabled {
      description
        "Enable or disable collaboration disposal service.";
      type boolean;
      default false;
    }

    leaf identity-system {
      description
        "Set identity system name.";
      type enumeration {
        enum sam {
          description
            "Set SAM+ identity system.";
        }
        enum srun {
          description
            "Set Srun identity system.";
        }
        enum none {
          description
            "No identity system.";
        }
      }
      default none;
    }

    leaf ip-address {
      description
      "Set IPv4/6 address of identify system server.";
      when "../identity-system != 'none'";
      type union {
        type ntos-inet:ipv4-address;
        type ntos-inet:ipv6-address;
      }
    }
    leaf port {
      description
        "Set port of identity system server.";
      when "../identity-system != 'none'";
      type ntos-inet:port-number;
    }

    leaf description {
      description
        "Set description of identity system server.";
      when "../identity-system != 'none'";
      type ntos-types:ntos-obj-description-type {
        length "1..255";
      }
    }

    leaf username {
      description
        "Set username identity system server.";
      when "../identity-system != 'none'";
      type ntos-types:ntos-obj-name-type {
        length "1..127";
      }
    }
    leaf password {
      description
        "Set password of identity system server.";
      when "../identity-system != 'none'";
      type string;
    }

    container source {
      description
        "Source of sending message to identity system server.";
      when "../identity-system != 'none'";
      ntos-ext:nc-cli-one-liner;

      choice source {
        description
          "Choice of source.";

        default "auto";
        case auto {
          leaf auto {
            description
              "Auto source.";
            type empty;
          }
        }
        case interface {
          leaf interface {
            description
              "Interface source.";
            type ntos-types:ifname;
            ntos-ext:nc-cli-no-name;
            ntos-ext:nc-cli-completion-xpath
              "/ntos:config/ntos:vrf/ntos-interface:interface/physical/*[local-name()='name']";
          }
        }
      }
    }

    leaf warn-content {
      description
        "Specify disposal warning content.";
      when "../identity-system = 'sam'";
      type string {
        pattern "[^&\"'\\\\<>]*" {
          error-message "cannot include character: &\"'\\<>";
        }
        length "1..255";
      }
    }
  }

  grouping blacklist-input-type {
    choice type {
      description
        "Choice of disposal type.";

      case user {
        leaf user {
          description
            "Disposed user.";
          type ntos-types:ntos-obj-name-type {
            length "1..127";
          }
        }
      }
      case mac {
        leaf mac {
          description
            "Disposed mac address.";
          type ntos-if:mac-address;
        }
      }
    }
  }

  grouping blacklist-input-method {
    leaf block-method {
      description
        "Specify disposal method.";
      type enumeration {
        enum permanent {
          description
            "Permanet block.";
        }
        enum temporary {
          description
            "Temporary block.";
        }
      }
    }
  }

  grouping blacklist-input-extend {
    choice block-time {
      description
        "Choice of disposal time.";

      case seconds {
        leaf seconds {
          description
            "Disposed seconds.";
          type uint32 {
            range "1..1296000";
          }
        }
      }
      case minutes {
        leaf minutes {
          description
            "Disposed minutes.";
          type uint32 {
            range "3..21600";
          }
        }
      }
      case hours {
        leaf hours {
          description
            "Disposed hours";
          type uint16 {
            range "1..360";
          }
        }
      }
      case days {
        leaf days {
          description
            "Disposed days.";
          type uint8 {
            range "1..15";
          }
        }
      }
    }
  }

  grouping collab-disposal-input {
    leaf identity-system {
      description
        "Specify identity system name.";
      type enumeration {
        enum sam {
          description
            "Specify SAM+ identity system.";
        }
        enum srun {
          description
            "Specify Srun identity system.";
        }
      }
    }

    leaf ip-address {
      description
      "Specify Ipv4/6 address of identify system server.";
      type union {
        type ntos-inet:ipv4-address;
        type ntos-inet:ipv6-address;
      }
    }
    leaf port {
      description
        "Specify port of identity system server.";
      type ntos-inet:port-number;
    }

    leaf username {
      description
        "Specify username identity system server.";
      type ntos-types:ntos-obj-name-type;
    }
    leaf password {
      description
        "Specify password of identity system server.";
      type string;
    }

    container source {
      description
        "Specify source of sending message to identity system server.";
      choice source {
        description
          "Choice of source.";

        case auto {
          leaf auto {
            description
              "Auto source.";
            type empty;
          }
        }
        case interface {
          leaf interface {
            description
              "Interface source.";
            type ntos-types:ifname;
            ntos-ext:nc-cli-no-name;
          }
        }
      }
    }
  }

  grouping blacklist-output {
    leaf code {
      description
        "Return code.";
      type uint32;
    }
    leaf message {
      description
        "Return message.";
      type string;
    }
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Collaborative disposal configuration.";

    container collab-disposal {
      description
        "Collaborative disposal function.";
      uses collab-disposal-config;
    }
  }

  rpc show-collab-disposal-config {
    description
      "Show collaborative disposal configuration.";

    input {
      leaf vrf {
        description
          "Vrf name.";
        type ntos:vrf-name;
        default "main";
      }
    }

    output {
      leaf data {
        description
          "The configuration of collaborative disposal.";
        type string;
      }
      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-show "collab-disposal";
  }

  rpc show-collab-disposal-state {
    description
      "Show collaborative disposal state.";

    input {
      leaf vrf {
        description
          "Vrf name.";
        type ntos:vrf-name;
        default "main";
      }
      uses collab-disposal-input;
    }

    output {
      leaf state {
        description
          "Linkage state code of identity system server.";
        type uint32;
      }
      leaf message {
        description
          "Linkage state message of identity system server.";
        type string;
      }

      uses ntos-cmd:long-cmd-output;
    }
    ntos-ext:nc-cli-show "collab-disposal state";
  }

  rpc set-collab-disposal-blist {
    description
      "Set collaborative disposal blacklist.";

    input {
      leaf vrf {
        description
          "Vrf name.";
        type ntos:vrf-name;
        default "main";
      }

      uses blacklist-input-type;
      uses blacklist-input-method;
      uses blacklist-input-extend;
    }

    output {
      uses blacklist-output;
    }
  }

  rpc refresh-collab-disposal-blist {
    description
      "Refresh collaborative disposal blacklist.";

    input {
      leaf vrf {
        description
          "Vrf name.";
        type ntos:vrf-name;
        default "main";
      }
      uses collab-disposal-input;

      leaf warn-content {
        description
          "Specify disposal warning content.";
        type string {
          pattern "[^&\"'\\\\<>]*" {
            error-message "cannot include character: &\"'\\<>";
          }
          length "1..255";
        }
      }
    }
    ntos-ext:nc-cli-cmd "collab-disposal refresh-blacklist";
  }
}
