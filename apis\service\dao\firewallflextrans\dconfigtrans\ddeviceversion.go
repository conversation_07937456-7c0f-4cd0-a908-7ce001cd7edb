package dconfigtrans

import (
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models/firewallflextrans"
)

const DeviceVersionName = "设备版本表"

type DeviceVersion struct {
	firewallflextrans.DeviceVersion
}

type DeviceVersionListResponse struct {
	DeviceVersion
}

func (this *DeviceVersion) ModelName() string {
	return DeviceVersionName
}

func DeviceVersionModel() *firewallflextrans.DeviceVersion {
	return &firewallflextrans.DeviceVersion{}
}

// Create 创建设备版本
func (this *DeviceVersion) Create(object map[string]interface{}) error {
	object["app"] = "config-trans"
	err := easygorm.GetEasyGormDb().Model(DeviceVersionModel()).Create(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("create device version err ", err)
		return err
	}
	return nil
}

// Update 更新设备版本
func (this *DeviceVersion) Update(id uint, object map[string]interface{}) error {
	err := easygorm.GetEasyGormDb().Model(DeviceVersionModel()).Where("app = ?", "config-trans").Where("id = ?", id).Updates(object).Error
	if err != nil {
		logging.ErrorLogger.Errorf("update device version err ", err)
		return err
	}
	return nil
}

// Delete 删除设备版本
func (this *DeviceVersion) Delete(id uint) error {
	err := easygorm.GetEasyGormDb().Unscoped().Where("app = ?", "config-trans").Delete(DeviceVersionModel(), id).Error
	if err != nil {
		logging.ErrorLogger.Errorf("delete device version err ", err)
		return err
	}
	return nil
}

// Find 根据ID查找设备版本
func (this *DeviceVersion) Find(id uint) error {
	err := easygorm.GetEasyGormDb().Model(DeviceVersionModel()).Where("app = ?", "config-trans").Where("id = ?", id).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find device version err ", err)
		return err
	}
	return nil
}

// FindEx 根据指定字段查找设备版本
func (this *DeviceVersion) FindEx(col, value string) error {
	err := easygorm.GetEasyGormDb().Model(DeviceVersionModel()).Where("app = ?", "config-trans").Where(col+" = ?", value).Find(this).Error
	if err != nil {
		logging.ErrorLogger.Errorf("find device version err ", err)
		return err
	}
	return nil
}

// FindByShortName 根据简写名称查找设备版本
func (this *DeviceVersion) FindByShortName(shortName string) error {
	return this.FindEx("short_name", shortName)
}

// FindByName 根据全称查找设备版本
func (this *DeviceVersion) FindByName(name string) error {
	return this.FindEx("name", name)
}

// GetAllVersions 获取所有设备版本列表
func (this *DeviceVersion) GetAllVersions(vendor, status, sort, orderBy string, page, pageSize int) (map[string]interface{}, error) {
	var count int64
	var res []*DeviceVersionListResponse

	db := easygorm.GetEasyGormDb().Model(DeviceVersionModel()).Where("app = ?", "config-trans")

	if len(vendor) > 0 {
		db = db.Where("vendor = ?", vendor)
	}

	if len(status) > 0 {
		db = db.Where("status = ?", status)
	}

	err := db.Count(&count).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get device version list count err ", err)
		return nil, err
	}

	paginateScope := easygorm.PaginateScope(page, pageSize, sort, orderBy)
	err = db.Scopes(paginateScope).
		Find(&res).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get device version list data err ", err)
		return nil, err
	}

	// 处理结果
	items := make([]map[string]interface{}, 0, len(res))
	for _, item := range res {
		itemMap := map[string]interface{}{
			"id":          item.ID,
			"name":        item.Name,
			"short_name":  item.ShortName,
			"description": item.Description,
			"vendor":      item.Vendor,
			"status":      item.Status,
			"sort_order":  item.SortOrder,
			"created_at":  item.CreatedAt,
			"updated_at":  item.UpdatedAt,
		}
		items = append(items, itemMap)
	}

	list := map[string]interface{}{"items": items, "total": count, "limit": pageSize}
	return list, nil
}

// GetEnabledVersions 获取已启用的设备版本列表
func (this *DeviceVersion) GetEnabledVersions(vendor string) ([]map[string]interface{}, error) {
	var versions []*DeviceVersion

	db := easygorm.GetEasyGormDb().Model(DeviceVersionModel()).
		Where("app = ?", "config-trans").
		Where("status = ?", 1)

	if len(vendor) > 0 {
		db = db.Where("vendor = ?", vendor)
	}

	err := db.Order("sort_order asc").Find(&versions).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get enabled versions err ", err)
		return nil, err
	}

	// 处理结果
	result := make([]map[string]interface{}, 0, len(versions))
	for _, version := range versions {
		result = append(result, map[string]interface{}{
			"id":          version.ID,
			"name":        version.Name,
			"short_name":  version.ShortName,
			"description": version.Description,
			"vendor":      version.Vendor,
		})
	}

	return result, nil
}

// GetVersionByName 根据全称或简写获取设备版本
func GetVersionByName(name string, vendor string) (*DeviceVersion, error) {
	version := &DeviceVersion{}

	db := easygorm.GetEasyGormDb().Model(DeviceVersionModel()).
		Where("app = ?", "config-trans").
		Where("status = ?", 1)

	if len(vendor) > 0 {
		db = db.Where("vendor = ?", vendor)
	}

	// 先尝试按全称查找
	err := db.Where("name = ?", name).First(version).Error
	if err == nil {
		return version, nil
	}

	// 如果全称查找失败，尝试按简写查找
	err = db.Where("short_name = ?", name).First(version).Error
	if err != nil {
		logging.ErrorLogger.Errorf("get version by name err ", err)
		return nil, err
	}

	return version, nil
}
