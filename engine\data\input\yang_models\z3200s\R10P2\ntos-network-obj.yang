module ntos-network-obj {
  yang-version 1.1;
  namespace "urn:ruijie:ntos:params:xml:ns:yang:network-obj";
  prefix ntos-network-obj;

  import extra-conditions {
    prefix ext-cond;
  }
  import ntos {
    prefix ntos;
  }
  import ntos-types {
    prefix ntos-types;
  }
  import ntos-extensions {
    prefix ntos-ext;
  }
  import ntos-inet-types {
    prefix ntos-inet;
  }
  import ntos-api {
    prefix ntos-api;
  }

  import ntos-if-types {
    prefix ntos-if;
  }

  organization
    "Ruijie Networks Co.,Ltd";
  contact
    "Tel: 4008-111-000
    E-mail: <EMAIL>";
  description
    "Ruijie NTOS network object module.";

  revision 2022-08-02 {
    description
      "Address type support IPv6.";
    reference "";
  }

  revision 2021-09-30 {
    description
      "Initial version.";
    reference "";
  }

  typedef ipv4-address-type {
    type union {
      type ntos-inet:ipv4-address;
      type ntos-inet:ipv4-prefix;
      type ntos-inet:ipv4-range;
      type ntos-inet:ipv4-mask;
    }
  }

  typedef ip-address-type {
    type union {
      type ntos-inet:ipv4-address;
      type ntos-inet:ipv4-prefix;
      type ntos-inet:ipv4-range;
      type ntos-inet:ipv4-mask;

      /* add IPv6 Address Type */
      type ntos-inet:ipv6-address;
      type ntos-inet:ipv6-prefix;
      type ntos-inet:ipv6-range;
    }
  }

  grouping address-obj-comm-config {
    leaf name {
        type ntos-types:ntos-obj-name-type;
        description
          "Address object name.";
    }

    leaf description {
        type ntos-types:ntos-obj-description-type;
        description
          "The descrption of the address object.";
    }
  }

  grouping address-obj-config {
    description
      "Configuration of address object or address-group.";

    list address-set {
      key "name";
      ordered-by user;
      description
        "The list of address object.";

      must ' count(./ip-set) > 0' {
        error-message "Ip-set can not be zero.";
      }

      uses address-obj-comm-config;

      list ip-set {
        key "ip-address";
        description
          "The list of IP address.";
        ntos-ext:nc-cli-one-liner;

        leaf ip-address {
          type ip-address-type;
          description
            "IPv4/IPv6 address.";
        }
      }
    }

    list address-group {
      key "name";
      ordered-by user;
      description
        "The list of address group.";

      must 'count(./address-set) > 0' {
        error-message "The numeration of address-set can not be zero.";
      }

      uses address-obj-comm-config;

      list address-set {
        description
          "The list of address-set.";
        key name;
        ntos-ext:nc-cli-one-liner;

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The address-set name.";
        }
      }
    }

    list mac-address-set {
      key "name";
      ordered-by user;
      description
        "The list of mac address object.";

      must 'count(./mac-set) > 0' {
        error-message "mac-set can not be zero.";
      }

      uses address-obj-comm-config;

      list mac-set {
        key "mac-address";
        description
          "The list of mac address.";
        ntos-ext:nc-cli-one-liner;

        leaf mac-address {
          type ntos-if:mac-address;
          description
            "mac address.";
        }
      }
    }

    list mac-address-group {
      key "name";
      ordered-by user;
      description
        "The list of mac address group.";

      must 'count(./mac-address-set) > 0' {
        error-message "The numeration of mac-address-set can not be zero.";
      }

      uses address-obj-comm-config;

      list mac-address-set {
        description
          "The list of mac-address-set.";
        key name;
        ntos-ext:nc-cli-one-liner;

        leaf name {
          type ntos-types:ntos-obj-name-type;
          description
            "The mac-address-set name.";
        }
      }
    }
  }

  rpc show-network-obj {
    description
      "Show network object.";

    input {
      leaf vrf {
        type string;
        description
          "VRF.";
      }

      container content {
        description
          "The content of network object";
        ntos-ext:nc-cli-group "subcommand";
        leaf type {
          type enumeration {
            enum address;
            enum address-group;
          }
          description
            "Address-set or address-group.";
        }

        leaf obj-type {
          type enumeration {
            enum ipv4;
            enum ipv6;
            enum mac;
          }
          description
            "The net object type is ipv4, ipv6 or mac.";
        }

        leaf filter {
          type string;
          description
            "Filter.";
        }

        leaf name {
          type string;
          description
            "Address-set name, address-group name, mac-address-set name or mac-address-group name.";
        }

        leaf start {
          type uint32;
          description
            "Start offset.";
        }

        leaf end {
          type uint32;
          description
            "End offset.";
        }
        leaf with-hidden-ref {
          type empty;
          description
            "With hidden reference policy";
        }
      }

      container reference {
        description
          "The reference infomation of network object by policy";
        ntos-ext:nc-cli-group "subcommand";
        leaf name {
          type string;
          description
            "Name of network object.";
        }
        leaf with-hidden-ref {
          type empty;
          description
            "With hidden reference policy";
        }
      }
    }

    output {
      leaf buffer {
        type string;
        description
          "The command output buffer.";
          ntos-ext:nc-cli-stdout;
          ntos-ext:nc-cli-hidden;
      }
    }

    ntos-ext:nc-cli-show "network-obj";
    ntos-api:internal;
  }


  rpc show-all-network-obj {
    description
      "Show all network object and network group.";

    input {
      leaf vrf {
        type string;
        description
          "VRF.";
      }

      leaf filter {
        type string;
        description
          "Filter.";
      }
    }

    output {
      list ipv4 {
        key name;
        leaf name {
          type string;
        }
      }

      list ipv6 {
        key name;
        leaf name {
          type string;
        }
      }

      list ipv4-group {
        key name;
        leaf name {
          type string;
        }
      }

      list ipv6-group {
        key name;
        leaf name {
          type string;
        }
      }

      list mac {
        key name;
        leaf name {
          type string;
        }
      }

      list mac-group {
        key name;
        leaf name {
          type string;
        }
      }
    }

    ntos-ext:nc-cli-show "all-network-obj";
    ntos-api:internal;
  }

  augment "/ntos:config/ntos:vrf" {
    description
      "Network object configuration.";

    container network-obj {
      presence "Network object configuration.";
      description
        "Network object configuration.";
      ext-cond:unique-values "*/*[local-name()='name']" {
        error-message "The address-set and address-group name must be unique in a VRF.";
      }

      uses address-obj-config;
    }
  }

  augment "/ntos:state/ntos:vrf" {
    description
      "Network object state data.";

    container network-obj {
      description
        "State data of network object.";

      uses address-obj-config;
    }
  }
}
