package cache

import (
	"fmt"
	"irisAdminApi/application/libs/i18n"
	"strings"
	"time"

	"github.com/gomodule/redigo/redis"
	"github.com/mna/redisc"
)

// 添加一个辅助函数用于简化翻译调用
func i18n_t(key string, lang string, params ...interface{}) string {
	// 如果没有指定语言，使用默认语言
	if lang == "" {
		lang = "zh-CN"
	}

	// 构建参数映射
	templateData := make(map[string]interface{})
	for i := 0; i < len(params); i += 2 {
		if i+1 < len(params) {
			key, ok := params[i].(string)
			if ok {
				templateData[key] = params[i+1]
			}
		}
	}

	// 调用翻译函数
	return i18n.Translate(lang, key, templateData)
}

type RedisCluster struct {
	cluster       *redisc.Cluster // 集群模式使用
	singlePool    *redis.Pool     // 单实例模式使用
	isSingleMode  bool            // 是否为单实例模式
	isInitialized bool            // 是否已初始化成功
}

var rcClient *RedisCluster

// 存储重连时需要的配置信息
var (
	savedAddrs    []string
	savedPassword string
)

func createPool(addr string, opts ...redis.DialOption) (*redis.Pool, error) {
	return &redis.Pool{
		MaxIdle:     30,
		MaxActive:   100,
		IdleTimeout: 300 * time.Second,
		Dial: func() (redis.Conn, error) {
			conn, err := redis.Dial("tcp", addr, opts...)
			if err != nil {
				fmt.Println(fmt.Sprintf(i18n_t("redis.dial_error", ""), "error", err))
				return nil, err
			}
			return conn, nil
		},
		TestOnBorrow: func(c redis.Conn, t time.Time) error {
			if time.Since(t) < time.Minute {
				return nil
			}
			_, err := c.Do("PING")
			if err != nil {
				fmt.Println(fmt.Sprintf(i18n_t("redis.invalid_connection", ""), "error", err))
			}
			return err
		},
		Wait: true,
	}, nil
}

// PrewarmConnectionPool 预热Redis连接池
// 获取指定数量的连接并执行PING命令，然后归还到连接池
func PrewarmConnectionPool(poolSize int) {
	if rcClient == nil {
		fmt.Println(i18n_t("redis.client_not_initialized_for_prewarm", ""))
		return
	}

	if !rcClient.isInitialized {
		fmt.Println(i18n_t("redis.client_initialization_incomplete", ""))
		return
	}

	// 获取连接并执行PING命令，然后归还连接池
	connections := make([]redis.Conn, 0, poolSize) // 使用0初始容量，避免nil元素

	// 获取连接
	for i := 0; i < poolSize; i++ {
		conn := rcClient.Get()
		if conn == nil || conn.Err() != nil {
			fmt.Println(fmt.Sprintf(i18n_t("redis.prewarm_get_connection_failed", ""), "error", conn.Err()))
			continue
		}
		_, err := conn.Do("PING")
		if err != nil {
			fmt.Println(fmt.Sprintf(i18n_t("redis.prewarm_ping_failed", ""), "error", err))
			conn.Close()
			continue
		}
		connections = append(connections, conn)
	}

	// 归还连接
	for _, conn := range connections {
		if conn != nil {
			conn.Close()
		}
	}
}

func InitRedisCluster(addrs []string, password string) {
	// 保存连接信息，以便后续重连使用
	savedAddrs = addrs
	savedPassword = password

	if len(addrs) == 0 {
		fmt.Println(i18n_t("redis.empty_address_list", ""))
		return
	}

	// 创建新的RedisCluster实例
	rc := &RedisCluster{
		isInitialized: false, // 初始状态为未初始化
	}

	// 尝试先以单实例模式连接
	singlePool, err := createPool(addrs[0], []redis.DialOption{
		redis.DialConnectTimeout(5 * time.Second),
		redis.DialReadTimeout(5 * time.Second),
		redis.DialWriteTimeout(5 * time.Second),
		redis.DialPassword(password),
	}...)

	if err == nil {
		// 测试连接是否可用
		conn := singlePool.Get()
		_, pingErr := conn.Do("PING")
		conn.Close()

		if pingErr == nil {
			// 单实例模式连接成功
			rc.singlePool = singlePool
			rc.isSingleMode = true
			rc.isInitialized = true
			rcClient = rc

			// 预热连接池，初始化20个连接
			go PrewarmConnectionPool(20)
			return
		}

		// 如果PING失败，关闭连接池并记录错误
		fmt.Println(fmt.Sprintf(i18n_t("redis.single_mode_ping_failed", ""), "error", pingErr))
		singlePool.Close()
	} else {
		fmt.Println(fmt.Sprintf(i18n_t("redis.create_single_pool_failed", ""), "error", err))
	}

	// 尝试集群模式连接
	fmt.Println(fmt.Sprintf(i18n_t("redis.trying_cluster_mode_connection", ""), "addrs", addrs))
	cluster := &redisc.Cluster{
		StartupNodes: addrs,
		DialOptions: []redis.DialOption{
			redis.DialConnectTimeout(5 * time.Second),
			redis.DialReadTimeout(5 * time.Second),
			redis.DialWriteTimeout(5 * time.Second),
			redis.DialPassword(password),
		},
		CreatePool: createPool,
	}

	// 刷新集群信息
	err = cluster.Refresh()
	if err != nil {
		if strings.Contains(err.Error(), "cluster support disabled") {
			// 集群模式失败但错误是"集群支持已禁用"，尝试回退到单实例模式
			fmt.Println(i18n_t("redis.cluster_mode_not_supported", ""))
			singlePool, err := createPool(addrs[0], []redis.DialOption{
				redis.DialConnectTimeout(5 * time.Second),
				redis.DialReadTimeout(5 * time.Second),
				redis.DialWriteTimeout(5 * time.Second),
				redis.DialPassword(password),
			}...)

			if err != nil {
				fmt.Println(fmt.Sprintf(i18n_t("redis.single_mode_connection_failed", ""), "error", err))
				return
			}

			// 测试单实例连接是否可用
			conn := singlePool.Get()
			_, pingErr := conn.Do("PING")
			conn.Close()

			if pingErr != nil {
				fmt.Println(fmt.Sprintf(i18n_t("redis.fallback_ping_failed", ""), "error", pingErr))
				singlePool.Close()
				return
			}

			rc.singlePool = singlePool
			rc.isSingleMode = true
			rc.isInitialized = true
		} else {
			fmt.Println(fmt.Sprintf(i18n_t("redis.refresh_cluster_info_failed", ""), "error", err))
			// 重要：不要设置失败的集群连接
			return
		}
	} else {
		// 测试集群连接是否可用
		conn := cluster.Get()
		_, pingErr := conn.Do("PING")
		conn.Close()

		if pingErr != nil {
			fmt.Println(fmt.Sprintf(i18n_t("redis.cluster_ping_failed", ""), "error", pingErr))
			// 不使用失败的集群连接
			return
		}

		fmt.Println(i18n_t("redis.cluster_connection_success", ""))
		rc.cluster = cluster
		rc.isSingleMode = false
		rc.isInitialized = true
	}

	if rc.isInitialized {
		rcClient = rc
		// 预热连接池，初始化20个连接
		go PrewarmConnectionPool(20)
	} else {
		fmt.Println(i18n_t("redis.init_failed_all_modes", ""))
	}
}

func GetRedisClusterClient() *RedisCluster {
	return rcClient
}

// Get 返回一个到Redis的连接
func (rc *RedisCluster) Get() redis.Conn {
	if rc == nil {
		fmt.Println(i18n_t("redis.client_nil", ""))
		return nil
	}

	if !rc.isInitialized {
		fmt.Println(i18n_t("redis.client_not_initialized", ""))
		return nil
	}

	if rc.isSingleMode {
		if rc.singlePool == nil {
			fmt.Println(i18n_t("redis.single_pool_nil", ""))
			return nil
		}
		conn := rc.singlePool.Get()
		if conn.Err() != nil {
			fmt.Println(fmt.Sprintf(i18n_t("redis.get_connection_from_single_pool_failed", ""), "error", conn.Err()))
		}
		return conn
	} else {
		if rc.cluster == nil {
			fmt.Println(i18n_t("redis.cluster_client_nil", ""))
			return nil
		}
		conn := rc.cluster.Get()
		if conn.Err() != nil {
			fmt.Println(fmt.Sprintf(i18n_t("redis.get_connection_from_cluster_failed", ""), "error", conn.Err()))
		}
		return conn
	}
}

// 添加在Do方法内进行错误处理和自动重试的逻辑
func (rc *RedisCluster) Do(cmd string, args ...interface{}) (interface{}, error) {
	if rc == nil || !rc.isInitialized {
		return nil, fmt.Errorf(i18n_t("redis.client_not_initialized", ""))
	}

	// 获取连接
	conn := rc.Get()
	if conn == nil {
		return nil, fmt.Errorf(i18n_t("redis.cannot_get_connection", ""))
	}
	defer conn.Close()

	// 执行命令
	reply, err := conn.Do(cmd, args...)
	if err != nil {
		// 尝试区分网络错误和其他错误
		if isNetworkError(err) {
			fmt.Println(i18n_t("redis.network_error", "", "command", cmd, "error", err))
			// 这里可以触发重新连接逻辑，但不应该在此方法内重试
			// 避免无限递归调用
		} else {
			fmt.Println(i18n_t("redis.command_execution_error", "", "command", cmd, "error", err))
		}
	}
	return reply, err
}

// isNetworkError 检查是否是网络相关错误
func isNetworkError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	// 常见的网络错误字符串
	networkErrors := []string{
		"connection refused",
		"connection reset by peer",
		"broken pipe",
		"i/o timeout",
		"eof",
		"closed network connection",
		"use of closed network connection",
		"get on closed pool",
	}

	for _, ne := range networkErrors {
		if strings.Contains(strings.ToLower(errStr), ne) {
			return true
		}
	}
	return false
}

// TryReconnect 尝试重新连接Redis
// 这个方法可以在发现连接问题时调用
func TryReconnect() bool {
	// 获取之前的配置信息
	if rcClient == nil && (savedAddrs == nil || len(savedAddrs) == 0) {
		fmt.Println(i18n_t("redis.cannot_reconnect_no_saved_info", ""))
		return false
	}

	// 确保关闭旧连接
	if rcClient != nil {
		rcClient.Close()
		rcClient = nil // 清空全局客户端
	}

	// 使用保存的连接信息重新初始化
	if savedAddrs != nil && len(savedAddrs) > 0 {
		InitRedisCluster(savedAddrs, savedPassword)

		// 检查是否连接成功
		if rcClient != nil && rcClient.isInitialized {
			fmt.Println(i18n_t("redis.reconnect_success", ""))
			return true
		}
	}

	fmt.Println(i18n_t("redis.reconnect_failed", ""))
	return false
}

func (rc *RedisCluster) Send(cmd string, args ...interface{}) error {
	if rc == nil || !rc.isInitialized {
		return fmt.Errorf(i18n_t("redis.client_not_initialized", ""))
	}

	conn := rc.Get()
	if conn == nil {
		return fmt.Errorf(i18n_t("redis.cannot_get_connection", ""))
	}
	defer conn.Close()

	err := conn.Send(cmd, args...)
	if err != nil && isNetworkError(err) {
		fmt.Println(i18n_t("redis.network_error", "", "command", cmd, "error", err))
	}
	return err
}

func (rc *RedisCluster) Close() {
	if rc == nil {
		return
	}

	if rc.isSingleMode {
		if rc.singlePool != nil {
			rc.singlePool.Close()
			rc.singlePool = nil
		}
	} else {
		if rc.cluster != nil {
			rc.cluster.Close()
			rc.cluster = nil
		}
	}
	rc.isInitialized = false
}

// CheckHealth 检查Redis连接池的健康状态
// 返回是否健康和错误信息
func (rc *RedisCluster) CheckHealth() (bool, error) {
	if rc == nil {
		return false, fmt.Errorf(i18n_t("redis.client_object_nil", ""))
	}

	if !rc.isInitialized {
		return false, fmt.Errorf(i18n_t("redis.client_not_successfully_initialized", ""))
	}

	conn := rc.Get()
	if conn == nil {
		return false, fmt.Errorf(i18n_t("redis.cannot_get_connection", ""))
	}
	defer conn.Close()

	// 尝试执行PING命令
	_, err := conn.Do("PING")
	if err != nil {
		return false, fmt.Errorf(i18n_t("redis.ping_failed", ""), "error", err)
	}

	return true, nil
}

// GetRedisHealth 检查全局Redis连接池状态
// 此方法可以在应用程序中随时调用，用于监控Redis连接状态
func GetRedisHealth() (bool, error) {
	if rcClient == nil {
		return false, fmt.Errorf(i18n_t("redis.global_client_not_initialized", ""))
	}
	return rcClient.CheckHealth()
}

func (rc *RedisCluster) GetKey(key string) (interface{}, error) {
	return rc.Do("GET", key)
}

func (rc *RedisCluster) Set(key string, value interface{}, ttl ...time.Duration) (interface{}, error) {
	reply, err := rc.Do("SET", key, value)
	if len(ttl) <= 0 {
		return reply, err
	}
	if _, err := rc.Expire(key, int(ttl[0].Seconds())); err != nil {
		fmt.Println(fmt.Sprintf("%s.EXPIRE.err : %s", key, err.Error()))
	}
	return reply, err
}

func (rc *RedisCluster) SetNX(key string, value interface{}, expireSeconds int) bool {
	n, err := rc.Do("SETNX", key, value)
	if err != nil {
		fmt.Println(fmt.Sprintf("%s.SetNX.err : %s", key, err.Error()))
		return false
	}
	if n == 1 {
		rc.Expire(key, expireSeconds)
		return true
	}
	return false
}

func (rc *RedisCluster) Del(keys ...interface{}) (int, error) {
	n := 0
	t := 0
	var err error
	for _, v := range keys {
		t, err = redis.Int(rc.Do("DEL", v))
		n += t
	}
	return n, err
}

func (rc *RedisCluster) Exists(key string) bool {
	n, err := redis.Int(rc.Do("EXISTS", key))
	if err != nil {
		fmt.Println(fmt.Sprintf("%s.Exists.err : %s", key, err.Error()))
		return false
	}
	if n == 1 {
		return true
	}

	return false
}

func (rc *RedisCluster) Expire(key string, seconds int) (interface{}, error) {
	if seconds <= 0 {
		fmt.Println(fmt.Sprintf(i18n_t("redis.invalid_expire_seconds", ""), "seconds", seconds))
		return nil, nil
	}
	return rc.Do("EXPIRE", key, seconds)
}

func (rc *RedisCluster) LPush(key string, values ...interface{}) (interface{}, error) {
	return rc.Do("LPUSH", redis.Args{}.Add(key).AddFlat(values)...)
}

func (rc *RedisCluster) LPop(key string, count int) (interface{}, error) {
	return rc.Do("LPOP", key, count)
}

func (rc *RedisCluster) RPush(key string, values ...interface{}) (interface{}, error) {
	return rc.Do("RPUSH", redis.Args{}.Add(key).AddFlat(values)...)
}

func (rc *RedisCluster) RPop(key string, count int) (interface{}, error) {
	return rc.Do("RPOP", key, count)
}

func (rc *RedisCluster) LLen(key string) int {
	length, _ := redis.Int(rc.Do("LLEN", key))
	return length
}

func (rc *RedisCluster) LTrim(key string, start, end int) (interface{}, error) {
	return rc.Do("LTRIM", key, start, end)
}

func (rc *RedisCluster) LRange(key string, start, end int) (interface{}, error) {
	return rc.Do("LRANGE", key, start, end)
}

func (rc *RedisCluster) HGetAll(key string) (interface{}, error) {
	return rc.Do("HGETALL", key)
}

func (rc *RedisCluster) HMSet(key string, values ...interface{}) (interface{}, error) {
	data := make([]interface{}, 0, len(values)+1)
	data = append(data, key)
	data = append(data, values...)
	return rc.Do("HMSET", data...)
}

func (rc *RedisCluster) HIncrBy(key string, field string, incr int64) (interface{}, error) {
	return rc.Do("HINCRBY", key, field, incr)
}

func (rc *RedisCluster) Sadd(key string, members ...interface{}) (interface{}, error) {
	data := make([]interface{}, 0, len(members)+1)
	data = append(data, key)
	data = append(data, members...)
	return rc.Do("SADD", data...)
}

func (rc *RedisCluster) Scard(key string) (interface{}, error) {
	return rc.Do("SCARD", key)
}

func (rc *RedisCluster) Members(key string) (interface{}, error) {
	return rc.Do("SMEMBERS", key)
}

// LoadRedisHashToStruct 从 redis 加载数据
func (rc *RedisCluster) LoadRedisHashToStruct(sKey string, pst interface{}) error {
	vals, err := redis.Values(rc.HGetAll(sKey))
	if err != nil {
		return err
	}
	err = redis.ScanStruct(vals, pst)
	if err != nil {
		return err
	}
	return nil
}

func (rc *RedisCluster) LPopBatch(key string, count int) ([]string, error) {
	conn := rc.Get()
	defer conn.Close()

	conn.Send("MULTI")                          // 开始事务
	conn.Send("LRANGE", key, 0, count-1)        // 获取指定范围内的列表元素
	conn.Send("LTRIM", key, count, -1)          // 删除已经获取的元素
	reply, err := redis.Values(conn.Do("EXEC")) // 执行事务

	if err != nil {
		return nil, err
	}

	if len(reply) < 2 {
		return nil, fmt.Errorf("expected at least two replies; got %v", len(reply))
	}

	// 因为MULTI/EXEC返回的是一个事务回复的数组，我们需要对第一个回复（LRANGE的结果）进行类型断言
	lrangeReply, ok := reply[0].([]interface{})
	if !ok {
		return nil, fmt.Errorf("expected a slice of interfaces; got %T", reply[0])
	}

	// 将接口切片转换为字符串切片
	var items []string
	for _, item := range lrangeReply {
		itemStr, ok := item.([]byte)
		if !ok {
			return nil, fmt.Errorf("expected a byte slice; got %T", item)
		}
		items = append(items, string(itemStr))
	}

	return items, nil
}

func (rc *RedisCluster) LPopAll(key string, batchSize int) ([][]string, error) {
	var allItems [][]string // 用于存储所有批次的元素

	for {
		items, err := rc.LPopBatch(key, batchSize)
		if err != nil {
			return nil, err
		}
		if len(items) == 0 {
			break // 如果没有元素返回，表示列表已经为空
		}
		allItems = append(allItems, items) // 添加当前批次的元素到总列表

		// 如果返回的元素少于batchSize，表示这是最后一批元素
		if len(items) < batchSize {
			break
		}
	}

	return allItems, nil
}
