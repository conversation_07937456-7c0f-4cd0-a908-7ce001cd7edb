package libs

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
)

var privateKey = []byte(`
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`)

//openssl
//openssl rsa -in rsa_private_key.pem -pubout -out rsa_public_key.pem
var publicKey = []byte(`
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuWYmew19tL9pNIWv0JWS
5ec2PXUddi7iCdQhRnytZCHdbNMuQ788wHihQwXymZ5ereggkuAXKCWBI88G3M71
eVdUBan83PljEKRPy7ZETwX6t8QItdn/FcqGiQX0jQwPJorOSnwEmegIgf6LsDG4
fPqJZhDRUONgwFKnhwgcC+9Kq3SaDKsfAf/oN/njF4RU5G0CLQYCepEDsTIyFes6
/jk9zBZ81sCcBGqoe29QqZuFJQCLsru+Wu8Z4t2twOuSDXPn/Dcp9FvIus/MVsa/
cXVL2tqZCCGHTFuEmyHrHPOCiEOA6cmLkmAd4Pw9lqJbPCCpTr38VR+QuL0oG+Rb
aQIDAQAB
-----END PUBLIC KEY-----
`)

// md5
func RsaDecrypt(ciphertext string) ([]byte, error) {

	//解密
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return nil, errors.New("private key error!")
	}
	//解析PKCS1格式的私钥
	priv, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	// 解密
	c, _ := base64.StdEncoding.DecodeString(ciphertext)
	return rsa.DecryptPKCS1v15(rand.Reader, priv, c)
}

func RsaEncrypt(publicKey []byte, origData []byte) ([]byte, error) {
	//解密pem格式的公钥
	block, _ := pem.Decode(publicKey)
	if block == nil {
		return nil, errors.New("public key error")
	}
	// 解析公钥
	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	// 类型断言
	pub := pubInterface.(*rsa.PublicKey)
	//加密
	return rsa.EncryptPKCS1v15(rand.Reader, pub, origData)
}
