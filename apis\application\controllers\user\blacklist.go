package user

// func GetAllAutoAuditBlackLists(ctx iris.Context) {
// 	name := ctx.FormValue("name")
// 	page, _ := strconv.Atoi(ctx.FormValue("page"))
// 	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
// 	orderBy := ctx.FormValue("orderBy")
// 	sort := ctx.FormValue("sort")

// 	list, err := dao.All(&dbuildfarmauditblacklist.BuildfarmAuditBlackList{}, ctx, name, sort, orderBy, page, pageSize)
// 	if err != nil {
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
// 		return
// 	}
// 	ctx.JSON(response.NewResponse(response.NoErr.Code, list, response.NoErr.Msg))
// 	return
// }

// func DeleteAutoAuditWBlackList(ctx iris.Context) {
// 	err := dao.Delete(&dbuildfarmauditblacklist.BuildfarmAuditBlackList{}, ctx)
// 	if err != nil {
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
// 		return
// 	}
// 	ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
// 	return
// }

// func CreateOrUpdateAutoAuditBlackList(ctx iris.Context) {
// 	req := dbuildfarmauditblacklist.BuildfarmAuditBlackList{}
// 	if err := ctx.ReadJSON(&req); err != nil {
// 		logging.ErrorLogger.Errorf("create autoauditfile read json err ", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
// 		return
// 	}

// 	validErr := libs.Validate.Struct(req)
// 	errs := libs.ValidRequest(validErr)
// 	if len(errs) > 0 {
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, strings.Join(errs, ";")))
// 		return
// 	}

// 	var err error
// 	if req.ID == 0 {
// 		err = dao.Create(&dbuildfarmauditblacklist.BuildfarmAuditBlackList{}, ctx, map[string]interface{}{
// 			"CreatedAt": time.Now(),
// 			"Text":      req.Text,
// 			"Type":      req.Type,
// 		})
// 	} else {
// 		err = dao.Update(&dbuildfarmauditblacklist.BuildfarmAuditBlackList{}, ctx, map[string]interface{}{
// 			"UpdatedAt": time.Now(),
// 			"Text":      req.Text,
// 			"Type":      req.Type,
// 		})
// 	}

// 	if err != nil {
// 		logging.ErrorLogger.Errorf("create user get err ", err)
// 		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
// 		return
// 	}

// 	ctx.JSON(response.NewResponse(response.NoErr.Code, req, response.NoErr.Msg))
// 	return
// }
